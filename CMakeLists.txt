cmake_minimum_required(VERSION 3.10)
project(NetSpeaker)

# 设置交叉编译工具链

set(CMAKE_SYSTEM_NAME Linux)

# 交互式选择构建模式
message("\n[步骤1/2] 请选择构建模式：")
message("1. 升级版本")
message("2. 烧录版本")
message("3. PC测试版本")
message("请输入数字 [1/2/3]：")

# 使用execute_process替代read_line
execute_process(
    COMMAND sh -c "read -p '> ' BUILD_MODE; echo \${BUILD_MODE}"
    OUTPUT_VARIABLE BUILD_MODE
    OUTPUT_STRIP_TRAILING_WHITESPACE
)
while(NOT BUILD_MODE MATCHES "^[1-3]$")
    message("错误：无效输入，请重新输入！")
    execute_process(
        COMMAND sh -c "read -p '> ' BUILD_MODE; echo \${BUILD_MODE}"
        OUTPUT_VARIABLE BUILD_MODE
        OUTPUT_STRIP_TRAILING_WHITESPACE
    )
endwhile()

# 交互式选择客制化版本
message("\n[步骤2/2] 请选择客制化版本：")
message("1. 常规版本")
message("2. AIPU版本")
message("3. LZY商业版本")
message("4. YIHUI版本")
message("请输入数字 [1-4]：")

execute_process(
    COMMAND sh -c "read -p '> ' CUSTOM_VERSION; echo \${CUSTOM_VERSION}"
    OUTPUT_VARIABLE CUSTOM_VERSION
    OUTPUT_STRIP_TRAILING_WHITESPACE
)

while(NOT CUSTOM_VERSION MATCHES "^[1-4]$")
    message("错误：无效输入，请重新输入！")
    execute_process(
        COMMAND sh -c "read -p '> ' CUSTOM_VERSION; echo \${CUSTOM_VERSION}"
        OUTPUT_VARIABLE CUSTOM_VERSION
        OUTPUT_STRIP_TRAILING_WHITESPACE
    )
endwhile()


# 交互式选择屏幕类型
message("\n[步骤3/3] 请选择屏幕类型：")
message("0. 无屏")
message("1. SPI_2.1寸_320x240(AIPU)")
message("2. SPI_1.9寸_320x170(YH)")
message("3. RGB_4.3寸_480x272(YH)")
message("请输入数字 [0-3]：")

execute_process(
    COMMAND sh -c "read -p '> ' DISPLAY_TYPE; echo \${DISPLAY_TYPE}"
    OUTPUT_VARIABLE DISPLAY_TYPE
    OUTPUT_STRIP_TRAILING_WHITESPACE
)

while(NOT DISPLAY_TYPE MATCHES "^[0-3]$")
    message("错误：无效输入，请重新输入！")
    execute_process(
        COMMAND sh -c "read -p '> ' DISPLAY_TYPE; echo \${DISPLAY_TYPE}"
        OUTPUT_VARIABLE DISPLAY_TYPE
        OUTPUT_STRIP_TRAILING_WHITESPACE
    )
endwhile()


# 转换选项为变量
if(BUILD_MODE STREQUAL "1")
    set(MODE_NAME "upgrade")
    set(BUILD_UPGRADE_VERSION ON)
elseif(BUILD_MODE STREQUAL "2")
    set(MODE_NAME "burn")
    set(BUILD_UPGRADE_VERSION OFF)
elseif(BUILD_MODE STREQUAL "3")
    set(MODE_NAME "i386")
    set(BUILD_UPGRADE_VERSION OFF)
endif()

if(CUSTOM_VERSION STREQUAL "1")
    set(VERSION_NAME "default")
    set(VERSION_MACRO "")
elseif(CUSTOM_VERSION STREQUAL "2")
    set(VERSION_NAME "AIPU")
    set(VERSION_MACRO "-DAIPU_VERSION")
elseif(CUSTOM_VERSION STREQUAL "3")
    set(VERSION_NAME "LZY")
    set(VERSION_MACRO "-DLZY_COMMERCIAL_VERSION")
elseif(CUSTOM_VERSION STREQUAL "4")
    set(VERSION_NAME "YIHUI")
    set(VERSION_MACRO "-DYIHUI_VERSION")
endif()


if(DISPLAY_TYPE STREQUAL "0")
    set(DISPLAY_MACRO "-DSELECTED_DISPLAY_TYPE=0")
elseif(DISPLAY_TYPE STREQUAL "1")
    set(DISPLAY_MACRO "-DSELECTED_DISPLAY_TYPE=1")
elseif(DISPLAY_TYPE STREQUAL "2")
    set(DISPLAY_MACRO "-DSELECTED_DISPLAY_TYPE=2")
elseif(DISPLAY_TYPE STREQUAL "3")
    set(DISPLAY_MACRO "-DSELECTED_DISPLAY_TYPE=3")
endif()


# 添加宏定义
if(MODE_NAME STREQUAL "i386")
    add_definitions(-DUSE_PC_SIMULATOR)
endif()

if(BUILD_UPGRADE_VERSION)
    add_definitions(-DUPGRADE_PACKAGE)
endif()
if(VERSION_MACRO)
    add_definitions(${VERSION_MACRO})
endif()
if(DISPLAY_MACRO)
    add_definitions(${DISPLAY_MACRO})
endif()

# 交叉编译配置（保持原样）
set(CMAKE_C_COMPILER "arm-linux-gnueabihf-gcc")
set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR armv7)

# 示例输出
message(STATUS "当前配置：")
message(STATUS "  - 构建模式: ${MODE_NAME}")
message(STATUS "  - 客制化版本: ${VERSION_NAME}")
message(STATUS "  - 屏幕类型: ${DISPLAY_TYPE}")
message(STATUS "  - 客制化版本宏定义: ${VERSION_MACRO}")
message(STATUS "  - 屏幕宏定义: ${DISPLAY_MACRO}")



# 在合适位置添加日期处理
#string(TIMESTAMP BUILD_YEAR "%y")
#string(TIMESTAMP BUILD_MONTH "%m")
#string(TIMESTAMP BUILD_DAY "%d")
#math(EXPR SHORT_YEAR "${BUILD_YEAR} % 10")
#add_compile_definitions(LOCAL_FIRMWARE_VERSION="2.${SHORT_YEAR}.${BUILD_MONTH}${BUILD_DAY}")


# 定义路径变量
set(SOURCE_DIR_NAME "../../source")


if(MODE_NAME STREQUAL "i386")

    set(CMAKE_C_COMPILER "gcc")

    # 头文件目录
    include_directories(
        /mnt/hgfs/project/webrtc/WebRTC_NS_CPP
        /usr/include/freetype2
    )
    # 链接库目录
    link_directories(
        /mnt/hgfs/project/webrtc/WebRTC_NS_CPP/build_i386
    )

        # 编译选项
    add_compile_options(
        -O3 -g2 -Wno-attributes -Wno-deprecated-declarations
    )

else()

    set(CMAKE_C_COMPILER "arm-linux-gnueabihf-gcc")
    set(CMAKE_SYSTEM_PROCESSOR armv7)

    # 宏定义
    add_definitions(
        -DUSE_SSD212
        -D_GNU_SOURCE
        -D__USE_XOPEN
    )

    # 头文件目录
    include_directories(
        #${BUILD_DIR}/${SOURCE_DIR_NAME}
        /home/<USER>/project/exdisk/sigmastar/ssd212/V015/project/release/include
        /home/<USER>/project/mpg123/cross/include
        /home/<USER>/project/soxr/cross/include
        /home/<USER>/project/speex/cross/include
        /mnt/hgfs/project/webrtc/WebRTC_NS_CPP
        /home/<USER>/project/msc/cross/include
        /home/<USER>/eclipse-workspace/thirdparty/armv7_cross/include/freetype2
        /home/<USER>/project/ffmpeg/armv7/include
        /home/<USER>/project/pjsip/ssd212/cross/include
    )
    # 链接库目录
    link_directories(
        /home/<USER>/project/exdisk/sigmastar/ssd212/V015/project/release/disp/p3/common/glibc/9.1.0/mi_libs/dynamic
        /home/<USER>/project/exdisk/sigmastar/ssd212/V015/project/release/disp/p3/common/glibc/9.1.0/ex_libs/dynamic
        /home/<USER>/project/mpg123/cross/lib
        /home/<USER>/project/soxr/cross/lib
        /home/<USER>/project/speex/cross/lib
        /mnt/hgfs/project/webrtc/WebRTC_NS_CPP/build_arm
        /home/<USER>/project/msc/cross/lib
        /home/<USER>/eclipse-workspace/thirdparty/armv7_cross/lib
        /home/<USER>/project/ffmpeg/armv7/lib
        /home/<USER>/project/pjsip/ssd212/cross/lib
    )

    # 编译选项
    add_compile_options(
        -O3 -g0 -Wno-attributes
        -marm -march=armv7ve -mtune=cortex-a7 -mfpu=neon-vfpv4 -mfloat-abi=hard
    )

endif()


# 源文件配置（不能从NetSpeaker.mk加载，因为它是makefile格式）
#include(${BUILD_DIR}/${SOURCE_DIR_NAME}/NetSpeaker.mk)


# 5. 头文件
include_directories(${PROJECT_SOURCE_DIR})
# 5.1. 定义函数，用于递归添加头文件
function(include_sub_directories_recursively root_dir)
    if (IS_DIRECTORY ${root_dir})               # 当前路径是一个目录吗，是的话就加入到包含目录
        message("include dir: " ${root_dir})
        include_directories(${root_dir})
    endif()

    file(GLOB ALL_SUB RELATIVE ${root_dir} ${root_dir}/*) # 获得当前目录下的所有文件，让如ALL_SUB列表中
    foreach(sub ${ALL_SUB})
        if (IS_DIRECTORY ${root_dir}/${sub})
            include_sub_directories_recursively(${root_dir}/${sub}) # 对子目录递归调用，包含
        endif()
    endforeach()
endfunction()
# 5.2. 添加头文件
include_sub_directories_recursively(${PROJECT_SOURCE_DIR}/source) # 对子目录递归调用，包含

# 6. 源文件
#谨慎使用GLOB_RECURSE：它不会自动检测新增文件，推荐显式列出源文件或结合CONFIGURE_DEPENDS：
#​关键区别
#特性	无 CONFIGURE_DEPENDS	带 CONFIGURE_DEPENDS
#文件系统变更检测	仅在 CMake 运行时检测（如 cmake .）	在每次构建时自动检测（如 make）
#新增/删除文件后的行为	需要手动重新运行 CMake	自动触发 CMake 重新配置
#性能影响	无额外开销	每次构建增加少量文件系统扫描开销
#适用场景	稳定代码库	开发中频繁修改文件结构的项目
#file(GLOB_RECURSE SOURCES  "source/*.c")
#file(FILTER SOURCES EXCLUDE REGEX "source/pjsip/rtpMulticast.c")
#file(FILTER SOURCES EXCLUDE REGEX "source/pjsip/steamutil.c")

if(MODE_NAME STREQUAL "i386")

    file(GLOB_RECURSE SOURCES "source/*.c")
    list(FILTER SOURCES EXCLUDE REGEX "source/lv_porting_sstar")
    list(FILTER SOURCES EXCLUDE REGEX "source/pjsip")
    file(GLOB_RECURSE INCLUDES "source/*.h")

else()

    file(GLOB_RECURSE ALL_SOURCES "source/*.c")
    set(SOURCES "")
    foreach(file ${ALL_SOURCES})
        if(NOT file MATCHES "source/pjsip/rtpMulticast.c" AND
        NOT file MATCHES "source/pjsip/streamutil.c")
            list(APPEND SOURCES ${file})
        endif()
    endforeach()
    file(GLOB_RECURSE INCLUDES "source/*.h")

endif()


# 生成可执行文件
add_executable(${PROJECT_NAME} source/main.c ${SOURCES} ${INCLUDES})


# ARM生成后Strip
if(MODE_NAME STREQUAL "i386")
    # 链接库
        target_link_libraries(${PROJECT_NAME}
        PRIVATE
        m pthread mpg123 soxr webrtc_ns freetype SDL2)
else()

    # 自动收集PJSIP动态库
    file(GLOB PJSIP_LIBS "/home/<USER>/project/pjsip/ssd212/cross/lib/*.so")

    # 链接库
    target_link_libraries(${PROJECT_NAME}
        PRIVATE
        m pthread
        mi_sys mi_ao mi_ai APC_LINUX AED_LINUX AEC_LINUX
        cam_os_wrapper
        mpg123 soxr speexdsp webrtc_ns
        msc freetype
        avcodec avutil swresample avformat
        cam_fs_wrapper mi_common mi_panel mi_disp mi_gfx
        ${PJSIP_LIBS})
    # ARM生成后Strip
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND arm-linux-gnueabihf-strip $<TARGET_FILE:${PROJECT_NAME}>)
endif()
