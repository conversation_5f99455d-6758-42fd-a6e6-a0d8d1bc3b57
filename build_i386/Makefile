#
# Makefile
#
CC ?= gcc
SOURCE_DIR_NAME ?= source
BUILD_DIR ?= ${shell pwd}/../

WEBRTC_NS_INCLUDE_DIR=/mnt/hgfs/project/webrtc/WebRTC_NS_CPP
WEBRTC_NS_LIB_DIR=/mnt/hgfs/project/webrtc/WebRTC_NS_CPP/build_i386

FREETYPE_INCLUDE_DIR=/usr/include/freetype2

WARNINGS ?= -Werror -Wall -Wextra \
						-Wshadow -Wundef -Wmaybe-uninitialized -Wmissing-prototypes -Wno-discarded-qualifiers \
						-Wno-unused-function -Wno-error=strict-prototypes -Wpointer-arith -fno-strict-aliasing -Wno-error=cpp -Wuninitialized \
						-Wno-unused-parameter -Wno-missing-field-initializers -Wno-format-nonliteral -Wno-cast-qual -Wunreachable-code -Wno-switch-default  \
					  -Wreturn-type -Wmultichar -Wformat-security -Wno-ignored-qualifiers -Wno-error=pedantic -Wno-sign-compare -Wno-error=missing-prototypes -Wdouble-promotion -Wclobbered -Wdeprecated  \
						-Wempty-body  -Wstack-usage=2048 -Wno-error=unused-variable \
            -Wtype-limits -Wsizeof-pointer-memaccess -Wpointer-arith
            
CFLAGS ?= -O3 -g2 -I$(BUILD_DIR)/$(SOURCE_DIR_NAME) -I$(WEBRTC_NS_INCLUDE_DIR) -I$(FREETYPE_INCLUDE_DIR)
CFLAGS += -DUSE_PC_SIMULATOR
LDFLAGS ?= -lm -lpthread -lmpg123 -lsoxr -lwebrtc_ns -lfreetype -L$(WEBRTC_NS_LIB_DIR) -lSDL2
BIN = demo

USE_PC_SIMULATOR := 1  # ifeq 判断的是变量值，而不是宏定义,所以此处需要设置

cpu_core=$(shell grep -c processor /proc/cpuinfo)
MAKEFLAGS += --jobs=$(cpu_core)


#Collect the files to compile
#MAINSRC = $(BUILD_DIR)/$(SOURCE_DIR_NAME)/main.c

include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/NetSpeaker.mk

OBJEXT ?= .o

AOBJS = $(ASRCS:.S=$(OBJEXT))
COBJS = $(CSRCS:.c=$(OBJEXT))

#MAINOBJ = $(MAINSRC:.c=$(OBJEXT))

SRCS = $(ASRCS) $(CSRCS) $(MAINSRC)
OBJS = $(AOBJS) $(COBJS)

## MAINOBJ -> OBJFILES

all: default

ifneq ($(MAKECMDGOALS),clean)
include $(SRCS:.c=.d)
endif

%.o: %.c
	@$(CC)  $(CFLAGS) -c $< -o $@
	@echo "CC $<"

%.d: %.c
	set -e; rm -f $@; \
    $(CC) -MM $(CFLAGS) $< > $@; \
    sed -i -E 's,($*).o[ :]*,\1.o $@: ,g' $@;\

default: $(AOBJS) $(COBJS) $(MAINOBJ)
	$(CC) -o $(BIN) $(MAINOBJ) $(AOBJS) $(COBJS) $(LDFLAGS)

clean: 
	rm -f *.d $(BIN) $(AOBJS) $(COBJS) $(MAINOBJ)

