/*
 * @Author: <PERSON><PERSON>.<PERSON> 
 * @Date: 2021-09-06 18:12:45 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-02-24 12:02:03
 */

#include "sysconf.h"
#include "priority.h"

static char PriorityCurrentVal = PRIORITY_MAX_NUM-1;


pthread_mutex_t mutex_system_source=PTHREAD_MUTEX_INITIALIZER;

/**
 * [PriorityIsValid 检测一个优先级是否有效,同级视为有效]
 * @param  priority [description]
 * @return          [priority:1:有效 	0:无效]
 */
int PriorityIsValid(char priority)
{
	if(priority <= PriorityCurrentVal)
	{
		return 1;
	}
	return 0;
}


/**
 * [get_current_Priority 获取当前优先级]
 * @param  none
 * @return  Priority
 */
char get_current_Priority()
{
	return PriorityCurrentVal;
}



/*********************************************************************
 * @fn      SourceValToPriority
 *
 * @brief   根据当前音源获取优先级
 *
 * @param   source--音源
 *
 * @return  当前音源的优先级
 *********************************************************************/
char SourceValToPriority(unsigned char source)
{
	char ret=0;
	switch(source)
	{
		case SOURCE_FIRE_ALARM :			//火警信号
			ret = PRIORITY_FIRE_ALARM;
			break;
		case SOURCE_100V_INPUT		:		//100v
			ret = PRIORITY_100V;
			break;
		case SOURCE_NET_PAGING:				//网络寻呼
			ret = PRIORITY_NET_PAGING;
			break;
		case SOURCE_API_TTS_MUSIC:			//API播放TTS或者音乐
			ret = PRIORITY_API_TTS_MUSIC;
			break;
		case SOURCE_CALL	:				//对讲
			ret = PRIORITY_CALL;
			break;
		case SOURCE_SIP_CALLING :      		//SIP对讲
			ret = PRIORITY_SIP;
			break;
		case SOURCE_RTP_MULT:				//RTP MULT
			ret = PRIORITY_SIP;		
			break;
		case SOURCE_AUDIO_MIXED	:			//音频混音
			ret = PRIORITY_AUDIO_MIXED;
			break;
		case SOURCE_PHONE_GATEWAY	:			//音频混音
			ret = PRIORITY_PHONE_GATEWAY;
			break;
		case SOURCE_LOCAL_PLAY	:		//网络点播      -old本地播放
			ret = PRIORITY_NET_PLAY;
			break;
		case SOURCE_TIMING		:		//定时
			ret = PRIORITY_TIMING;
			break;
        case SOURCE_MONITOR_EV:
            ret = PRIORITY_MONITOR_EV;  //监控事件
             break;
        case SOURCE_AUX		:		    //AUX本地播放
			ret = PRIORITY_AUX;
			break;
		case SOURCE_NULL		:		//空闲
			ret = PRIORITY_NULL;
			break;
		default:
			if(SOURCE_AUDIO_COLLECTOR_BASE <= source
				&& source <= SOURCE_AUDIO_COLLECTOR_MAX)
			{
				//音频采集
				if(g_ac_source_priority == 1)
				{
					ret = PRIORITY_AUDIO_COLLECTOR;		//默认（低于定时）
				}
				else
				{
					ret = PRIORITY_AUDIO_HIGH_PRIORITY;	//高优先级（高于定时）
				}
				break;
			}else
			{
				ret = PRIORITY_NULL;
			}
			break;
	}
	return ret;
}



extern int amp_init_ok;
char set_system_source(int source)
{
  	if(source == g_media_source)
	  return 1;
    
	//加锁
	pthread_mutex_lock(&mutex_system_source);
    
  	int dest_source_priority=SourceValToPriority(source);
	int ret=0;
	if( PriorityIsValid( dest_source_priority) || source == SOURCE_NULL || source == SOURCE_AUX || source == SOURCE_100V_INPUT)
	{
        printf("set_system_source succeed:dest=0x%x,ori=0x%x\n",source,g_media_source);
		//满足优先级原则
	  	PriorityCurrentVal= dest_source_priority;
	  	g_media_source = source;
		ret = 1;
		if( g_media_source == SOURCE_NULL || g_media_source == SOURCE_AUX )
        {
			#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_AUDIO_MIXER)
			{
				if(g_signal_aux)
				{
					g_media_source = SOURCE_AUX;
					if(amp_init_ok)
					{
						#ifndef USE_PC_SIMULATOR
						//此处会造成冲击声
						Enable_Signal_Output(1);          //打开信号
						Enable_Amp_Output(1);             //打开功放
						#endif
					}
				}
				else
				{
					g_media_source = SOURCE_NULL;
					if(amp_init_ok)
					{
						#ifndef USE_PC_SIMULATOR
						Enable_Amp_Output(0);            //关闭功放
						//如果是解码终端，那么切换到空闲后不关闭信号，因为打开信号脚会有冲击
						if(!IS_MODEL_DECODER && !IS_DEVICE_AUDIO_MIXER && !IS_MODEL_SIMPLE_DECODER)
						{
							Enable_Signal_Output(0);         //关闭信号
						}
						#endif
					}
				}
			}
			#endif
        }
        else  //其他音源功放非静音
        {
			#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_AUDIO_MIXER)
			{
				if(amp_init_ok)
				{
					#ifndef USE_PC_SIMULATOR
					Enable_Signal_Output(1);          //打开信号
					Enable_Amp_Output(1);             //打开功放
					#endif
				}
			}
			#endif
        }

#ifndef USE_PC_SIMULATOR
		
		#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_AUDIO_MIXER)
		{
			#if(IS_DEVICE_DECODER_TERMINAL)
			if(g_media_source != SOURCE_100V_INPUT)
			{
				Ctrl_Relay_100V(0);	//音频继电器切换
			}
			else
			{
				Ctrl_Relay_100V(1);	//音频继电器归位
			}

			#if !DECODER_RELAY_TRIGGER_MUSIC
				if(g_media_source == SOURCE_FIRE_ALARM)
				{
					Ctrl_Relay_EMC(1);  //消防强切继电器打开
				}
				else
				{
					if(!IS_MODEL_WITH_4G)
					{
						Ctrl_Relay_EMC(0);	//消防强切继电器关闭
					}
				}
			#endif
			#endif

			if(g_media_source == SOURCE_NULL)
			{
				GPIO_OutPut_NetAudio_Led(0);		//关闭网络音频信号灯输出
				if(g_NetAudioSignal_can_close)
				{
					GPIO_OutPut_NetAudio_Signal(0);	//关闭网络音频信号IO输出
					if (DECODER_RELAY_TRIGGER_MUSIC || IS_MODEL_WITH_4G)
						Ctrl_Relay_EMC(0);  			//消防强切继电器关闭
				}
			}
			else
			{
				GPIO_OutPut_NetAudio_Led(1);		//打开网络音频信号灯输出
				GPIO_OutPut_NetAudio_Signal(1);		//打开网络音频信号IO输出
				if (DECODER_RELAY_TRIGGER_MUSIC || IS_MODEL_WITH_4G)
					Ctrl_Relay_EMC(1);  			//消防强切继电器打开
			}
		}
		#endif
#endif
	}
	else
	{
		printf("set_system_source failed:dest=0x%x,ori=0x%x\n",source,g_media_source);
		ret = 0;
	}
    
	//解锁
	pthread_mutex_unlock(&mutex_system_source);
    
	return ret;
}


unsigned char get_system_source()
{
	return g_media_source;
}