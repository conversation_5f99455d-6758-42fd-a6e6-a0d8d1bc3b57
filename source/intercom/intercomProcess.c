#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <semaphore.h>
#include "sysconf.h"
#include "intercomProcess.h"

#if(IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_DECODER_TERMINAL)

CALLDEVICECONFIG m_stCallDeviceConfig;
CALLINFO m_stCallInfo;
CALL_RECV_stream m_stCall_recv_stream;

void InitCallDevice()
{
    //首先将相关引脚设置为输入
    GPIO_Set_Call_Input();

    #if IS_DEVICE_INTERCOM_TERMINAL
    //将相关输出引脚重置，设置为低电平输出
    GPIO_OutPut_Call_Led(1,0);  //led1 off
    GPIO_OutPut_Call_Led(2,0);  //led1 off
    GPIO_OutPut_Call_Relay(1,0);//relay1 off
    GPIO_OutPut_Call_Relay(2,0);//relay2 off
    #endif
}


/*********************************************************************
 * @fn      Create_Intercom_Gpio_Event
 *
 * @brief  	对讲GPIO事件线程
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_Intercom_Gpio_Event(void *p_arg)
{
    int basic_status=0;         //系统基础状态
    int preCallStatus=m_stCallInfo.self_callStatus; //上一次的对讲状态
    int ledGpio_delay_time=0;   //ledGpio延时时间，微秒
    int ledGpio_val=0;          //ledGpio输出电平
    int ledGpio_delay_cnt=0;    //ledGpio延时计数，微秒
    int thread_sleepTime=50000; //线程休眠时间，默认50ms
    
    int cur_key1Value=1;         //0表示按下,1表示弹起
    int cur_key2Value=1;         //0表示按下,1表示弹起
    int cur_Ext1Value=1;         //0表示有信号,1表示没有信号
    int cur_Ext2Value=1;         //0表示有信号,1表示没有信号
#if 0
    printf("key1_Value=%d\n",GPIO_Get_Call_Key_Value(1));
    printf("key2_Value=%d\n",GPIO_Get_Call_Key_Value(2));
    printf("ext1_Value=%d\n",GPIO_Get_Call_Ext_Value(1));
    printf("ext2_Value=%d\n",GPIO_Get_Call_Ext_Value(2));
#endif
	while(1)
	{
        int sysSource = get_system_source();
        
        //0：没有连接上服务器,搜索中，亮0.2秒，灭2秒
        //1：连接正常但非通话状态，待机中，常亮
        //2：通话状态，传输中，亮0.2秒，灭0.2秒
        int temp_basic_status = 0;
        int imchange=0; //立即变化
        if(sysSource == SOURCE_CALL && m_stCallInfo.self_callStatus == CALL_STATUS_CONNECT) //通话状态
        {
            temp_basic_status=2;
            ledGpio_delay_time=0.2*1000*1000;
        }
        else if(IS_SERVER_CONNECTED)    //待机中
        {
            temp_basic_status=1;
            ledGpio_delay_time=1000*1000*1000;
        }
        else    //搜网中
        {
            temp_basic_status=0;
            if(ledGpio_val == 1)
            {
                ledGpio_delay_time=0.2*1000*1000;
            }
            else
            {
                ledGpio_delay_time=2*1000*1000;
            }
        }

        if(temp_basic_status!=basic_status)
        {
            basic_status = temp_basic_status;
            imchange = 1;
        }

        ledGpio_delay_cnt += thread_sleepTime;
  
        if(imchange || ledGpio_delay_cnt>=ledGpio_delay_time)
        {
            switch(basic_status)
            {
                case 0:
                    ledGpio_val = !ledGpio_val;
                    GPIO_OutPut_Call_Led(1,ledGpio_val);
                    GPIO_OutPut_Call_Led(2,ledGpio_val);
                break;
                case 1:
                    if(!ledGpio_val)
                    {
                        ledGpio_val = 1;
                        GPIO_OutPut_Call_Led(1,ledGpio_val);
                        GPIO_OutPut_Call_Led(2,ledGpio_val);
                    }
                break;
                case 2:
                    ledGpio_val = !ledGpio_val;
                    GPIO_OutPut_Call_Led(1,ledGpio_val);
                    GPIO_OutPut_Call_Led(2,ledGpio_val);
                break;
            }
            ledGpio_delay_cnt = 0;
        }

        //输入检测
        int tmp_key1Value = GPIO_Get_Call_Key_Value(1);
        #if IS_DEVICE_INTERCOM_TERMINAL
        int tmp_key2Value = GPIO_Get_Call_Key_Value(2);
        int tmp_s1Value = GPIO_Get_Call_Ext_Value(1);
        int tmp_s2Value = GPIO_Get_Call_Ext_Value(2);
        #else
        int tmp_key2Value = 1;
        int tmp_s1Value = 1;
        int tmp_s2Value = 1;
        #endif

        int key_changed=0;  //0：没有变化 1：按键1变化 2：按键2变化
        if(tmp_key1Value!=cur_key1Value)
        {
            cur_key1Value = tmp_key1Value;
            key_changed=1;
            printf("cur_key1Value=%d\n",cur_key1Value);
        }
        else if(tmp_key2Value!=cur_key2Value)
        {
            cur_key2Value = tmp_key2Value;
            key_changed=2;
            printf("cur_key2Value=%d\n",cur_key2Value);
        }

        if( key_changed && (cur_key1Value == 0 || cur_key2Value == 0 ) )
        {
            if(sysSource == SOURCE_CALL)
            {
                //如果是被叫，且正在等待接听状态，按下按键接听
                if(m_stCallInfo.isCallingParty == 0 && m_stCallInfo.self_callStatus == CALL_STATUS_WAIT_CALLED_ANSWER)
                {
                    m_stCallInfo.self_callStatus = CALL_STATUS_CONNECT;
                    Send_callStatus_feedback(CALL_STATUS_CONNECT);
                    //开启对讲
                    EnterCallMode(true);
                }
                else //其他情况挂断
                {
                    EnterCallMode(false);
                }
            }
            else if(IS_SERVER_CONNECTED)
            {
                char call_mac[6]={0};
                if(key_changed == 1)
                {
                    memcpy(call_mac,m_stCallDeviceConfig.Key1_mac,6);
                }
                else if(key_changed == 2)
                {
                    memcpy(call_mac,m_stCallDeviceConfig.Key2_mac,6);
                }
                //如果存在call_mac,则invite
                if( !(call_mac[0] == 0 && call_mac[1] == 0 && call_mac[2] == 0 && call_mac[3] == 0 && call_mac[4] == 0 && call_mac[5] == 0) )
                {
                    //unsigned char mac[6] = {0x2C,0x21,0x0F,0x0A,0x76,0x1E};
                    Initiate_call(call_mac);
                }
                else
                {
                    printf("call_mac is zero...\n");
                }
            }
        }


        #if IS_DEVICE_INTERCOM_TERMINAL
        //输出控制
        if(preCallStatus != m_stCallInfo.self_callStatus)
        {
            preCallStatus = m_stCallInfo.self_callStatus;
            if(preCallStatus == CALL_STATUS_CONNECT)
            {
                //通话中,控制继电器开
                GPIO_OutPut_Call_Relay(1,1);
                GPIO_OutPut_Call_Relay(2,1);
            }
            else
            {
                //未通话，控制继电器关
                GPIO_OutPut_Call_Relay(1,0);
                GPIO_OutPut_Call_Relay(2,0);
            }
        }
        #endif


		usleep(thread_sleepTime);
	}
}

/*********************************************************************
 * @fn      Create_Intercom_Gpio_Event_Task
 *
 * @brief  	创建对讲GPIO事件线程
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_Intercom_Gpio_Event_Task(void)
{
    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Create_Intercom_Gpio_Event, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}

/*********************************************************************
 * @fn      Clean_call_Buf
 *
 * @brief   清除对讲数据
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Clean_call_Buf(void)
{
	memset(&m_stCall_recv_stream,0,sizeof(m_stCall_recv_stream));
}

//发起对讲
void Initiate_call(unsigned char *called_mac)
{
    if(m_stCallInfo.self_callStatus != CALL_STATUS_FREE)
        return;
    if(!PriorityIsValid(PRIORITY_CALL))
        return;
    m_stCallInfo.self_callStatus = CALL_STATUS_WAIT_CALLED_ANSWER;
    #if 0
    //先将自身设置空闲状态
    Set_zone_idle_status(NULL,__func__, __LINE__,true);
    #else
    Clean_All_Audio(1,1);
    #endif
    //主叫，响铃等待时也先设置为对讲状态，避免其他音源打断
    set_system_source(SOURCE_CALL);
    //主叫发起对讲
    Send_calling_invation(called_mac);
    //开启铃声线程
    Create_AudioCall_Ring_Task();
}

bool EnterCallMode(bool bStart)
{
    int result = false;
    if(bStart)  //开始
    {
        if(PriorityIsValid(PRIORITY_CALL) &&  m_stCallInfo.self_callStatus != CALL_STATUS_FREE)
        {
            printf("EnterCallMode start!\n");
            Clean_All_Audio(1,1);
            set_system_source(SOURCE_CALL);
            
            //清空节目名称
	        memset(g_media_name,0,sizeof(g_media_name));
            pkg_query_current_status(NULL);	//发送当前状态

            Create_AudioCall_Timing_Task();

            //开启音频
            Open_Audio_Out(16000,16,1);
            Open_Audio_In(16000,16,1);

            result = true;
        }
    }
    else  //结束
    {
        printf("EnterCallMode stop!\n");
        m_stCallInfo.self_callStatus = CALL_STATUS_FREE;
        int sysSource = get_system_source();
        if(sysSource == SOURCE_CALL)
        {
            Set_zone_idle_status(NULL,__func__, __LINE__,true);
            Send_callStatus_feedback(m_stCallInfo.self_callStatus);
        }
        memset(&m_stCallInfo,0,sizeof(m_stCallInfo));   //将清除放在后面，否则挂断时对讲线程还未退出，可能改变某些参数（比如编码），导致对方异常
        result = true;
    }

    return result;
}






#if IS_DEVICE_INTERCOM_TERMINAL
void recog_command_call(char *command)
{
    int sysSource = get_system_source();
    if(IS_SERVER_CONNECTED && sysSource != SOURCE_CALL)
    {
        char call_mac[6]={0};
        memcpy(call_mac,m_stCallDeviceConfig.Key1_mac,6);

        //如果存在call_mac,则invite
        if( !(call_mac[0] == 0 && call_mac[1] == 0 && call_mac[2] == 0 && call_mac[3] == 0 && call_mac[4] == 0 && call_mac[5] == 0) )
        {
            Initiate_call(call_mac);
        }
        else
        {
            printf("call_mac is zero...\n");
        }
    }
}



#endif





#endif