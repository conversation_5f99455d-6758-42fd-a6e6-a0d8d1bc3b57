#ifndef _INTERCOM_PROCESS_H_
#define _INTERCOM_PROCESS_H_

#include "const.h"

#if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)


#define CALL_TIMESTAMP_UNIT 256         // 160/16000=10ms,256/16000=16ms,320/16000=20ms

#define DEFAULT_MIC_VOL_LEVEL    5      //对讲mic输入音量(1~9)
#define DEFAULT_FAR_OUT_LEVEL    5      //对讲远端输出音量(1~9)

typedef struct
{
    unsigned char Key1_mac[6];     //Key1对应的设备Mac
    unsigned char Key2_mac[6];     //Key2对应的设备Mac
    unsigned int AutoAnswerTime;   //自动应答时间(单位:秒)
    unsigned char micVol;                   //对讲mic输入音量(1~9)
    unsigned char farOutVol;                //对讲远端输出音量(1~9)(对讲时不以系统音量为准，应该以此为准)
}CALLDEVICECONFIG;
extern CALLDEVICECONFIG m_stCallDeviceConfig;


typedef struct
{
	unsigned char  isCallingParty;            //是否为主叫方   ( 1为主叫方  0为被叫方 )
    unsigned char  deviceMac[6];		   	  //记录主叫方或被叫方的MAC
    unsigned char  audioCoding;               //语音编码
    unsigned char  isEnableNat;                 //开启NAT后由主机转发双方音频流
    unsigned short audioPort;                 //关闭NAT后有效
	unsigned char  self_callStatus;			  //自身的对讲状态
    unsigned char  other_callStatus;          //对方的对讲状态
	unsigned int   self_mediaTimeStamp;		  //自身的媒体时间戳
    unsigned int   other_mediaTimeStamp;      //对方的媒体时间戳
}CALLINFO;
extern CALLINFO m_stCallInfo;

enum
{
	CALL_STATUS_FREE,						//空闲
	CALL_STATUS_WAIT_CALLED_ANSWER,			//被叫方响铃中
	CALL_STATUS_NO_RESPONSE,				//无人应答
	CALL_STATUS_BUSY,						//被叫方繁忙，无法接听（如寻呼）
	CALL_STATUS_REJECT,						//被叫方拒绝接听（手动挂断）	0x04
	CALL_STATUS_CODECES_NOT_SUPPORT,		//不支持的语音编码 0X05
	CALL_STATUS_CONNECT,					//被叫方已接听	0x06
	CALL_STATUS_HANGUP						//任意一方已挂断	0x07
};



#define RX_CALL_BUF_PKG_MAX       20

typedef struct
{
    char rx_call_data[RX_CALL_BUF_PKG_MAX][1400];
    int  rx_call_len[RX_CALL_BUF_PKG_MAX];
    char rx_call_valid[RX_CALL_BUF_PKG_MAX];
    char rx_call_write_pos;
    char rx_call_read_pos;
    unsigned int rx_call_timing_count;
}CALL_RECV_stream;

extern CALL_RECV_stream m_stCall_recv_stream;

void InitCallDevice();
void Create_Intercom_Gpio_Event_Task(void);
void Clean_call_Buf(void);
void Initiate_call(unsigned char *called_mac);
bool EnterCallMode(bool bStart);

#endif

#endif