/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-04 16:03:45 
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2025-01-11 10:08:55
 */

#ifndef _SYSCONF_H_
#define _SYSCONF_H_

#include <stdlib.h>
#include <stdio.h>
#include <stdbool.h>
#include <string.h>

#include <unistd.h>
#include <sys/ioctl.h>
#include <signal.h>
#include <pthread.h>
#include <sys/types.h>

#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netdb.h>
#include <net/if.h>
#include <ifaddrs.h>
#include <linux/sockios.h>
#include <linux/ethtool.h>

#include "const.h"

#include "network/netTools.h"
#include "system/sysMethod.h"
#include "system/fileOperation.h"
#include "thirdParty/minIni.h"
#include "network/multicast.h"
#include "network/network_protocol.h"
#include "network/network_process.h"
#include "network/recv_stream.h"
#include "priority/priority.h"
#include "mediaPlayer/netPlayer.h"
#include "sigmastar/mi_functions.h"
#include "sigmastar/mi_audio.h"
#include "network/tcp_client.h"
#include "sigmastar/audio_params.h"
#include "sigmastar/mi_gpio.h"
#include "mediaPlayer/audioProcess.h"
#include "network/http_client.h"
#include "thirdParty/my_md5.h"

#if ENABLE_LIBSAMPLERATE_SRC
#include "mediaPlayer/SampleRateConvert.h"
#elif ENABLE_SOXR_SRC
#include "mediaPlayer/soxr_t.h"
#endif

#include "uart/bluetooth.h"
#include "songCache/onlineSaver.h"
#include "songCache/triggerSong.h"

#if IS_DEVICE_AUDIO_COLLECTOR
#include "sigmastar/mi_audio_collector.h"
#include "audioCollector/AudioCollectorProcess.h"
#endif

#if IS_DEVICE_AUDIO_MIXER
#include "sigmastar/mi_audio_collector.h"
#include "audioMixer/AudioMixerProcess.h"
#endif

#if IS_DEVICE_PHONE_GATEWAY
#include "sigmastar/mi_audio_collector.h"
#include "phoneGateway/PhoneGatewayProcess.h"
//TODO
#endif

#if IS_DEVICE_FIRE_COLLECTOR
#include "uart/fire.h"
#endif

#if IS_DEVICE_POWER_SEQUENCE
#include "sequencePower/sequencePowerProcess.h"
#endif

#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
#include "intercom/intercomProcess.h"

  #if  IS_SUPPORT_SPEECH_RECOGNITION
  #include "uart/speech_uart.h"
  #endif

#endif

#if SUPPORT_INFORMATION_PUBLISH
  #include "informationPublish/informationPublish.h"
#endif

#if(IS_DEVICE_GPS_SYNCHRONIZER)
#include "uart/gps.h"
#endif

#include "uart/disp.h"

#include "module4G/module4G.h"

#include "uart/volumeControl.h"
#include "cJSON/cJSON.h"

#include "mongoose/mongoose.h"

#include "extension/extension.h"

#if SUPPORT_SIP
#include "pjsip/sip.h"
#endif

#if SUPPORT_TTS
#include "tts/tts.h"
#endif

#if SUPPORT_FFMPEG
#include "mediaPlayer/ffplayer.h"
#endif

#include "win/backlight.h"

#include "win/display.h"

#include "timerEx/sysTimer.h"

#include "uart/newUartDevice.h"

#if IS_DEVICE_AMP_CONTROLER
#include "ampControler/ampControler.h"
#endif

#if IS_DEVICE_NOISE_DETECTOR
#include "noiseDetector/noiseDetector.h"
#endif


#if YIHUI_VERSION
#include "thirdParty/yhBoard.h"
#endif

//功放是否经过AMP,之前版本由TPF605A输出到功放，现由于噪声问题已改板
//V25的P13先经过运放再经过运放
#define IS_AMP_WITH_AUDIO_DRIVER  ( (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL) &&\
                                    ( (IS_MODEL_DECODER || IS_MODEL_INTERCOM_NORMAL) ||\
                                        ( g_device_moduleId == POWER_P13_2x20W_OLD || g_device_moduleId == POWER_P17_2x30W_OLD ) ||\
                                        ( g_device_moduleId >= POWER_P13_2x20W_V25 && g_device_moduleId <= POWER_P13_2x10W+98 ) ||\
                                        ( g_device_moduleId >= POWER_P21_2x30W_V10 && g_device_moduleId <= POWER_P21_2x30W_V10+9) ||\
                                        ( g_device_moduleId == POWER_P15_2x33W_V10 ) ||\
                                        ( IS_MODEL_SIMPLE_AMP ) || \
                                        ( IS_MODEL_SIMPLE_DECODER ) \
                                    )\
                                  )

#define IS_SERVER_CONNECTED (g_host_device_TimeOut!=-1)

extern unsigned int sysRunTime;	//系统运行时间（秒）
extern unsigned char g_sysBootType;		//系统启动类型（1：开门狗重启 2：系统复位 3：断电重启；默认为3
extern unsigned char power_on_flag;	    //仅上电首次通知主机标志
/***************网络相关定义 START **************/
extern char g_ipAddress[20];          //IP地址
extern unsigned char g_mac_addr[6];   //MAC ADDR
extern unsigned char network_init_flag;	   //网络初始化完成标志

extern int g_network_mode;	//网络模式
extern char g_host_tcp_addr_domain[64];	//主机地址(域名或者IP)
extern char g_host_tcp_prase_ipAddress[16];		//解析后的主机IP
extern int g_host_tcp_port;				//主机TCP端口
extern int g_host_kcp_port;				//KCP端口

extern int g_current_connect_tcp_id;  //当前连接的tcp_id，默认是1-表示主服务器，2表示备用服务器
extern char g_host_tcp_addr_domain2[64];	//主机地址2(域名或者IP)
extern int g_host_tcp_port2;				//主机TCP端口2
extern int g_host_kcp_port2;        		//KCP端口2

extern int eth_link_status;				//有线网卡连接状态

/*****IP属性 ************/
extern int  g_IP_Assign;	//IP分配方式(static or DHCP)
extern char g_Static_ip_address[32];		//静态IP地址
extern char g_Subnet_Mask[32];				//子网掩码
extern char g_GateWay[32];					//网关
extern char g_Primary_DNS[32];				//主DNS服务器
extern char g_Alternative_DNS[32];			//备用DNS服务器
/***************网络相关定义 END **************/

/**************工作模式相关定义 START************/
extern unsigned int g_system_work_mode;
/**************工作模式相关定义 END************/

/**********设备信息START*****************/
extern unsigned char g_device_alias[128];	//设备别名
/*********设备信息END******************/

/**********基础信息START****************/
extern unsigned int g_system_volume;	        //系统音量
extern unsigned int g_pre_system_volume;		//系统前一音量
extern unsigned int g_sub_volume;               //子音量
extern unsigned int g_timing_volume;     //定时音量（记录正在定时播放时调节的音量）
extern unsigned int g_volumeCD_aux_volume;			//音控器本地音量(存在时才有效)
extern unsigned int g_volumeCD_net_volume;			//音控器网络音量(存在时才有效)
extern unsigned int g_aux_volume;             //本地音量
//20250305新增线路音量和麦克风音量
extern unsigned int g_lineVolume;	//线路音量
extern unsigned int g_micVolume;	//麦克风音量

extern bool bMicInsert;		//麦克风是否插入

extern signed char g_host_device_TimeOut;        	//主机离线计数
extern signed char host_ready_offline_flag;        //主机即将离线标志

extern unsigned int g_paging_status;		   // 寻呼状态
extern unsigned int g_media_source;		       // 音源状态
extern unsigned char g_media_name[128];        //当前节目名称
extern unsigned char g_media_status;            //媒体播放状态
extern unsigned char g_terminal_control_mode;   //程序控制模式

extern int concentrate_repeat_paly_enable; //是否允许播放集中模式下重新请求播放音乐
extern int paging_repeat_again_enable;     //是否允许重新寻呼
extern int mixed_source_repeat_again_enable;      //是否允许重新进入混音音源
extern int phone_gateway_source_repeat_again_enable;     //是否允许重新进入电话网关音源
extern int g_signal_100v;   //100V信号
extern int g_Is_tcp_real_internet;      //TCP模式是否处于internet公网上（主机IP是169开头或者192开头代表是内网TCP）

extern unsigned char FireAlarm_Status;	//消防告警状态
/**********基础信息END*****************/



/*******集中模式变量************/
extern unsigned char g_concentrated_song_type;	//集中模式-歌曲类型	1为MP3 2为WAV
extern unsigned int  g_concentrated_song_sample_rate;	//集中模式-歌曲采样率
extern unsigned int  g_concentrated_song_fmt;	//集中模式-歌曲采样精度
extern unsigned int  g_concentrated_song_channels;	//集中模式-歌曲声道数

extern unsigned char g_concentrated_multicast_address[16];	//集中模式-组播地址
extern unsigned int g_concentrated_multicast_port;		//集中模式-组播端口

extern unsigned int g_concentrated_need_exit_flag;	//集中模式 退出标志 0-不需要  1-需要退出
extern unsigned int g_concentrated_start;			//集中模式启动标志
extern unsigned int g_concentrated_playing;		    //集中模式歌曲正在播放

extern int g_centralized_mode_timeout_count;		//集中模式传输超时计数
extern int g_centralized_mode_timing_repeat;       //集中模式传输超时重复次数，连续5次代表确实收不到组播数据，应该停止重发
extern int g_centralized_mode_timeout_pause;	   //集中模式播歌超时检测暂停标志

extern int g_centralized_mode_is_existLocalSong;	//集中模式是否存在本地歌曲
extern bool g_IsCentralized_mode_multicast_new_cmd;		//集中模式组播播放是否采用新的命令

/*******音频采集变量************/
extern unsigned int g_ac_sample_rate;				//音频采集器采样率
extern unsigned int g_ac_channels;					//音频采集器通道数
extern unsigned char g_ac_multicast_address[16];		//音频采集-组播地址
extern unsigned int g_ac_mcast_port;					//音频采集器的组播端口
extern unsigned char g_ac_source_id;					//音频采集器音源ID
extern unsigned char g_ac_source_priority;	//音频采集音源优先级（1-默认，低于定时，2-高优先级，高于定时）

extern int g_collector_run_flag;						//音频采集器运行标志
extern int g_ac_timing_count;							//音频采集器超时计数
extern int g_ac_timing_wait;							//音频超时检测允许标志
extern int g_ac_exit_flag;								// 音频采集退出标志 0-不需要  1-需要退出

extern int g_paging_timing_count;							//寻呼数据超时检测计数


extern int g_signal_100v;   //100V信号
extern int g_signal_aux;   //Aux信号

extern char g_device_serialNum[20];    	//设备序列号

extern int g_NetAudioSignal_can_close;     //网络信号状态是否能够关闭

extern int g_allow_localSource;				//是否允许本地音源，收到在线音源指令时将其置于0，定时计数恢复，避免收到播放指令，还有短暂本地音乐

/************系统时间************/
extern unsigned char sys_date_buf[20];	//系统日期
extern unsigned char sys_time_buf[20];	//系统时间
extern unsigned char HasGotSysTime;	//是否已经获取到系统时间
/****************************/

/***********混音器**********/
#if IS_DEVICE_AUDIO_MIXER
extern int g_audio_mixer_signal_valid;	        // 音频混音器信号是否有效
extern unsigned char g_mixer_mac_addr[6];   //MAC ADDR 音频混音器mixer的MAC地址（2B开头）
#endif
extern int g_audio_mixer_stream_timing_count;   //音频混音器数据流超时检测计数
extern unsigned char g_audio_mixer_mac[6];		  //混音器MAC
extern unsigned char g_audio_mixer_priority;    //混音器优先级
extern unsigned char g_audio_mixer_volume;	    //混音器音量
extern unsigned int  g_audio_mixer_sampleRate;	//混音器采样率
extern char g_audio_mixer_broadcast_addr[32];	  //混音器广播地址
extern int g_audio_mixer_broadcast_port;		    //混音器广播端口
extern unsigned char g_audio_mixer_codecs;		  //混音器编码算法
extern unsigned char g_audio_mixer_signalType;  //混音器信号类型	1-MIC信号 2-AUX信号
/******************************/

/***********电话网关**********/
#if IS_DEVICE_PHONE_GATEWAY
extern int g_phone_gateway_signal_valid;	      // 电话网关信号是否有效
#endif

extern int g_phone_gateway_stream_timing_count;	//电话网关数据流超时检测计数

extern unsigned char g_phone_gateway_mac[6];		//电话网关MAC
extern unsigned char g_phone_gateway_volume;		//电话网关音量
extern unsigned int  g_phone_gateway_sampleRate;//电话网关采样率
extern char g_phone_gateway_broadcast_addr[32];	//电话网关广播地址
extern int g_phone_gateway_broadcast_port;		  //电话网关广播端口
extern unsigned char g_phone_gateway_codecs;		//电话网关编码算法

/******************************/


#if DECODER_LZY_VERESION_FM_C4A1
extern int g_lzy_fm_signal_detect;			//龙之音调频信号检测状态		
#endif

extern bool is_Enable_UART_LED_PLAYER;		//是否支持LED屏


extern int	g_system_language;	         //系统语言

extern int g_ApiPlayType;       //API播放类型


extern int g_remote_controler_addressCode;	//远程遥控器地址码
extern int g_remote_controler_keyCode;		//远程遥控器按键码

extern int g_isPlayIPTone;      //是否正在播报IP

extern char module4g_primary_dns[16];
extern char module4g_secondary_dns[16];
extern char mqtt_dns_url[32];

void* save_sysconf(char *section,char *key);
bool isDeviceExtraFeatureValid();

#endif