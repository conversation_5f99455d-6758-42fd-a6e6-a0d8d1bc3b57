#ifndef __TRIGGERSONG_H_
#define __TRIGGERSONG_H_

#include <stdbool.h>

typedef struct
{
    char trigger_local_play_flag;    //信号触发后进行本地播放标志为1；请求服务器在线播放时此标志为0
    char trigger_local_play_finish;  //信号触发后本地播放完成标志为1；请求服务器在线播放时此标志为0
    char trigger_switch;
    char trigger_mode;
    char trigger_song_name[128];
    char trigger_song_md5[32+1];
    int  trigger_playTimes;
    int  trigger_volume;
}triggerSong_t;

extern triggerSong_t st_triggerSong;

void InitTrigger();
#endif