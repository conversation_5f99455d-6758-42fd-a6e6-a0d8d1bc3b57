#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <dirent.h>
#include <pthread.h>
#include "const.h"
#include "sysMethod.h"
#include "fileOperation.h"
#include "onlineSaver.h"
#include "onlineDLink.h"
#include "triggerSong.h"

static unsigned int totalFileLength=0;      //歌曲总长度（字节）
static unsigned int totalFrameCnt=0;        //歌曲总帧数
static int currentFramePos=-1;              //当前的帧号,如果不是-1，说明需要重置

static int currentPlayFrame=0;                       //播放起始帧

static char songSaveName[128];              //保存的歌曲名称

static unsigned char saverBuffer[64*1024+2*1500];           //64K缓存区
static unsigned int saverMemPos=0;                  //保存缓存区位置

static FILE *audio_data_file=NULL;          //音频数据文件

static FILE *audio_pcm_file=NULL;          //正在播放的本地歌曲文件

DoubleLink online_songs_list;

flash_sapce_t flash_sapce;

void GetSaverPartitionSize();
void Scan_onlineSaver();
void onlineSaver_AddSongToList(online_song_header_t song_hearder_t,char *songName);
void onlineSaver_remove_file(char *songName);
void onlineSaver_RemoveFrontFromList();

//初始化在线保存模块
void init_onlineSaver()
{
    #ifdef USE_PC_SIMULATOR
    return
    #endif
    Dlist_Init(&online_songs_list);

	char create_path[64]={0};
	sprintf(create_path,"mkdir -p %s",ONLINE_SAVER_FLASH_DIR);
	pox_system(create_path);

    //删除tmp目录所有歌曲
    sprintf(create_path,"rm %s/* -rf",ONLINE_SAVER_MEM_DIR);
    pox_system(create_path);

    //扫描目录
    Scan_onlineSaver();
    Dlist_show_list(&online_songs_list);
    //获取磁盘空间
    GetSaverPartitionSize();
}


void SetCurrentPlayFrame(unsigned playFrame)
{
    printf("SetCurrentPlayFrame:%d\n",playFrame);
    currentPlayFrame = playFrame;
}

unsigned int GetCurrentPlayFrame()
{
    printf("GetCurrentPlayFrame:%d\n",currentPlayFrame);
    return currentPlayFrame;
}


bool onlineSaver_ready_read_localSong(char *songName)
{
    onlineSaver_close_localSong();

    char songPath[256]={0};
    sprintf(songPath,"%s/%s",ONLINE_SAVER_FLASH_DIR,songName);
    audio_pcm_file= fopen(songPath, "rb");
    if(audio_pcm_file == NULL)
    {
        return false;
    }
    //跳过头部长度
    fseek(audio_pcm_file,ONLINE_SAVER_FILE_HEADER_LENGTH,SEEK_SET);
    //跳到当前帧位置
    unsigned short FrameLen=0;
    char FrameBuf[1500]={0};
    int framePos=0;

    int currentFrame = GetCurrentPlayFrame();
    while(framePos++<currentFrame)
    {
        onlineSaver_localSong_read_frame(FrameBuf,&FrameLen);
        if(FrameLen == 0)
            return false;
    }

    return true;
}


void onlineSaver_localSong_read_frame(char *frameBuf,unsigned short *frameLen)
{
    int nRead = fread(frameLen, 1,sizeof(unsigned short), audio_pcm_file);
    if(nRead != sizeof(unsigned short))
    {
        *frameLen = 0;
        return;
    }
    //读取对应的帧大小
    nRead=fread(frameBuf, 1,*frameLen, audio_pcm_file);
    if(nRead != *frameLen)
    {
        *frameLen = 0;
        return;
    }
}



void onlineSaver_close_localSong()
{
    if(audio_pcm_file)
    {
        fclose(audio_pcm_file);
        audio_pcm_file = NULL;
    }
}

void GetSaverPartitionSize()
{
    GetFlashPartitionSize(ONLINE_SAVER_MOUNT_DIR,&flash_sapce.totalSpace,&flash_sapce.freeSpace);
}


bool IsExistOnlineSong(online_song_header_t song_hearder_t,char *songName)
{
    online_song_t online_song;
    sprintf(online_song.songName,"%s",songName);
    Node* p = Dlist_find(&online_songs_list,online_song);
    if(p)
    {
        printf("IsExistOnlineSong1,songName1=%s,songName2=%s...\n",songName,p->data.songName);
        if( p->data.song_header.channels == song_hearder_t.channels &&  p->data.song_header.fileLength == song_hearder_t.fileLength && \
            p->data.song_header.fmt == song_hearder_t.fmt && \
            p->data.song_header.sampleRate == song_hearder_t.sampleRate && p->data.song_header.songType == song_hearder_t.songType && \
            p->data.song_header.totalFrameCnt == song_hearder_t.totalFrameCnt \
            && strcmp(p->data.song_header.fileMd5,song_hearder_t.fileMd5) == 0 )
        {
            printf("IsExistOnlineSong:%s OK,Md5=%s\n",songName,song_hearder_t.fileMd5);
            return true;
        }
        else
        {
            printf("fileType=%d,songType=%d,sampleRate=%d,fmt=%d,channels=%d,fileLength=%d,totalFrameCnt=%d,timeStamp=%d\n", \
                    p->data.song_header.fileType,p->data.song_header.songType,p->data.song_header.sampleRate,p->data.song_header.fmt,p->data.song_header.channels, \
                    p->data.song_header.fileLength,p->data.song_header.totalFrameCnt,p->data.song_header.timeStamp);
        }
    }
    printf("IsExistOnlineSong:%s failed.\n",songName);
    return false;
}



online_song_t* GetExistOnlineSongByName(char *songName)
{
    online_song_t online_song;
    sprintf(online_song.songName,"%s",songName);
    Node* p = Dlist_find(&online_songs_list,online_song);
    if(p)
    {
        return &p->data;
    }
    else
    {
        return NULL;
    }
}


bool onlineSaver_Check_Flash_Song_OK(char *songName)
{
    bool ok_flag=true;
    online_song_header_t header;
    char songPath[256]={0};
    sprintf(songPath,"%s/%s",ONLINE_SAVER_FLASH_DIR,songName);
    FILE  *m_fp= fopen(songPath, "rb");
    if(m_fp == NULL)
    {
        printf("onlineSaver_Check_Flash_Song_OK:m_fp=NULL!\n");
        return false;
    }
    else
    {
        int realFileLength=GetFileLength(songPath);
        fread(&header, 1,sizeof(online_song_header_t), m_fp);
        //判断尾部标识
        fseek(m_fp,strlen(ONLINE_SAVER_FILE_END_FLAG)*-1,SEEK_END);
        char endFlag[20]={0};
        fread(&endFlag, 1,strlen(ONLINE_SAVER_FILE_END_FLAG), m_fp);
        if(strcmp(endFlag,ONLINE_SAVER_FILE_END_FLAG) != 0)
        {
            ok_flag=false;
        }
        fclose(m_fp);

        //再判断头文件是否正常
        if(ok_flag)
        {
            if( !(header.channels == 1 || header.channels == 2 ) )
            {
                printf("error1:%d\n",header.channels);
                ok_flag=false;
            }
            if( !(header.fmt == 16) )
            {
                printf("error2:%d\n",header.fmt);
                ok_flag=false;
            }
            if( !( header.sampleRate == 8000 || header.sampleRate == 11025 || header.sampleRate == 12000 ||
            header.sampleRate == 16000 || header.sampleRate == 22050 || header.sampleRate == 24000 ||
            header.sampleRate == 32000 || header.sampleRate == 44100 || header.sampleRate == 48000 ||
            header.sampleRate == 88200 || header.sampleRate == 96000 ) )
            {
                printf("error3:%d\n",header.sampleRate);
                ok_flag=false;
            }

            if( header.totalFrameCnt == 0 )
            {
                printf("error4\n");
                ok_flag=false;
            }
            //如果文件实际大小比读出来的头文件里面的大小还小的话，代表存储异常。（正常会大一点，diff_max<64KB)
            //最新测试，有些歌曲会比实际的小，比如”周杰伦 - 说了再见.mp3”,fileLength=4890859,realFileLength=4546518，为了保险起见，差距在1MB内的认为正常
            //有了尾部标识，就没必要判断总长度了
            if(header.fileLength == 0 || realFileLength <=0 )//|| abs(header.fileLength - realFileLength) > ONLINE_SAVER_FLASH_RESERVE_SPACE )
            {
                printf("error5:header.fileLength=%d,realFileLength=%d\n",header.fileLength,realFileLength);
                ok_flag=false;
            }
        }
    }

    if(ok_flag)
    {
        printf("onlineSaver_Check_Flash_Song OK:%s\n",songName);
#if 0
        printf("fileType=%d,songType=%d,sampleRate=%d,fmt=%d,channels=%d,fileLength=%d,totalFrameCnt=%d,timeStamp=%d\n", \
        header.fileType,header.songType,header.sampleRate,header.fmt,header.channels, \
        header.fileLength,header.totalFrameCnt,header.timeStamp);
#endif
        //加入到管理列表
        onlineSaver_AddSongToList(header,songName);
    }
    else
    {
        printf("onlineSaver_Check_Flash_Song failed:%s\n",songName);
        onlineSaver_remove_file(songName);
    }

    return ok_flag;
}

void onlineSaver_AddSongToList(online_song_header_t song_hearder_t,char *songName)
{
    online_song_t online_song;
    online_song.song_header = song_hearder_t;
    sprintf(online_song.songName,"%s",songName);
    //Dlist_push_back(&online_songs_list,online_song);
    //按时间戳排序，为了后面空间不够时先删除旧的
    //Dlist_sort(&online_songs_list);
    Dlist_insert_val(&online_songs_list,online_song);
    //Dlist_show_list(&online_songs_list);
    //printf("onlineSaver_AddSongToList,size=%d\n",Dlist_length(&online_songs_list));
}

void onlineSaver_RemoveFrontFromList()
{
    //获取双链表的第一个元素
    online_song_t online_song;
    //如果是触发歌曲，不能删除，需要跳到下一个元素
    Dlist_front_data(&online_songs_list,&online_song);
    if( st_triggerSong.trigger_switch && strcmp(online_song.songName,st_triggerSong.trigger_song_name) == 0 )
    {
        Dlist_front_data_next(&online_songs_list,&online_song);
        printf("found trigger song,next=%s\n",online_song.songName);
    }
    if(strlen(online_song.songName)>0)
    {
        printf("onlineSaver_RemoveFrontFromList:first Song=%s\n",online_song.songName);

        Dlist_delete_val(&online_songs_list,online_song);

        onlineSaver_remove_file(online_song.songName);

        //Dlist_show_list(&online_songs_list);
    }
    else
    {
        printf("onlineSaver_RemoveFrontFromList:Not Found!\n");
    }
}

#if 0
void onlineSaver_file_move_to_flash(char *songName,unsigned int fileLength)
{
    //预先减少可用空间，因为move需要时间，此时如果下一首播放指令过来，可用空间会计算错误
    flash_sapce.freeSpace = flash_sapce.freeSpace - fileLength - ONLINE_SAVER_FILE_LENGTH_EXTRA;
    printf("onlineSaver_file_move_to_flash:freeSpace=%d\n",flash_sapce.freeSpace);
    char srcPath[256]={0};
	char desPath[256]={0};
    sprintf(srcPath,"%s/%s",ONLINE_SAVER_MEM_DIR,songName);
	sprintf(desPath,"%s/%s",ONLINE_SAVER_FLASH_DIR,songName);
    MoveFile(srcPath,desPath);

    //检查文件是否OK
    onlineSaver_Check_Flash_Song_OK(songName);
    GetSaverPartitionSize();
}
#endif

void onlineSaver_remove_file(char *songName)
{
    char songPath[256]={0};
    sprintf(songPath,"%s/%s",ONLINE_SAVER_FLASH_DIR,songName);
    remove_file(songPath);
    //获取磁盘空间
    GetSaverPartitionSize();
}


bool onlineSaver_check_file_meet_flash_space(online_song_header_t song_hearder_t)
{
    //如果当前需要存储的文件大小超过剩余空间，那么需要删除之前的文件
    unsigned int fileLength=song_hearder_t.fileLength + ONLINE_SAVER_FILE_LENGTH_EXTRA;
    int count=20;
    while( (fileLength > flash_sapce.freeSpace) && count-- )
    {
        printf("flash space not enough:freespace=%d,fileLength=%d!\n",flash_sapce.freeSpace,fileLength);
        //删除
        onlineSaver_RemoveFrontFromList();
    }
    if(count==20)
    {
        printf("onlineSaver_check_space OK:freespace=%d,fileLength=%d!\n",flash_sapce.freeSpace,fileLength);
    }

    return true;
}

//扫描在线歌曲目录
void Scan_onlineSaver()
{
    DIR * dir;
    struct dirent *ptr;

    dir = opendir(ONLINE_SAVER_FLASH_DIR);       // 打开一个目录
    if(dir == NULL)
    {
        perror("opendir failed : ");
        return ;
    }
    while((ptr = readdir(dir)) != NULL)       // 循环读取目录数据
    {
        if(strcmp(ptr->d_name,".") == 0 || strcmp(ptr->d_name,"..") == 0)
            continue;
        else if(ptr->d_type == DT_REG)          //  file
        {
            //检查文件是否正确
            onlineSaver_Check_Flash_Song_OK(ptr->d_name);
        }
        else if(ptr->d_type == DT_DIR)          // dir
        {
            
        }
    }
}

static char* getSongSaverMemPath(char *songName)
{
    static char songPath[128]={0};
    if(songName && strlen(songName)>0)
    {
        sprintf(songPath,"%s/%s",ONLINE_SAVER_MEM_DIR,songName);
        return songPath;
    }
    else
    {
        return NULL;
    }
}

static int online_saver_reset()
{
    totalFileLength=0;
    totalFrameCnt=0;
    currentFramePos=-1;
    memset(saverBuffer,0,sizeof(saverBuffer));
    saverMemPos=0;

    if(audio_data_file)
    {
        /*关闭文件流*/
	    fclose(audio_data_file);
        audio_data_file=NULL;
    }
    //删除文件
    char *songPath=getSongSaverMemPath(songSaveName);
    if(songPath)
    {
        remove_file(songPath);
    }
    memset(songSaveName,0,sizeof(songSaveName));
}

int online_saver_init(online_song_header_t song_hearder_t,char *songName)
{
    if(!IS_ENABLE_ONLINESAVER)
    {
        printf("online_saver_init:not allow!\n");
        return ONLINE_SAVER_RESULT_NOT_ALLOW;
    }
    //首先查找歌曲文件是否已经存在
    if( IsExistOnlineSong(song_hearder_t,songName) )
    {
        printf("online_saver_init:File exist!\n");
        online_saver_reset();
        SetCurrentPlayFrame(song_hearder_t.currentFrame);
        return ONLINE_SAVER_RESULT_ALREADY_EXIST;
    }

    if( song_hearder_t.fileLength > ONLINE_SAVER_MAX_LENGTH || song_hearder_t.totalFrameCnt<=0 )
    {
        return ONLINE_SAVER_RESULT_PARAMETER_ERROR;
    }


    //如果歌曲当前播放帧不为0,则不存储
    if (song_hearder_t.currentFrame !=0 )
    {
        printf("song_hearder_t.currentFrame:%d is not zero,Not Save!\n",song_hearder_t.currentFrame);
        return ONLINE_SAVER_RESULT_PARAMETER_ERROR;
    }
    


    //判断flash是否有足够空间存储此歌曲，如果不够，需要删除旧的歌曲
    onlineSaver_check_file_meet_flash_space(song_hearder_t);

    if(currentFramePos != -1)
    {
        online_saver_reset();
    }

    sprintf(songSaveName,"%s",songName);
    totalFileLength = song_hearder_t.fileLength;
    totalFrameCnt = song_hearder_t.totalFrameCnt;

    //创建文件
    char *songPath=getSongSaverMemPath(songName);
    if((audio_data_file = fopen(songPath, "wb+")) == NULL)
    {
        perror("Create File");
        printf("online_saver_reset2.\n");
        online_saver_reset();
    }
    else
    {
        printf("Create File:%s OK!\n",songPath);

        currentFramePos=0;
        saverMemPos=0;
        memset(saverBuffer,0,sizeof(saverBuffer));

        printf("fileType=%d,songType=%d,sampleRate=%d,fmt=%d,channels=%d,fileLength=%d,totalFrameCnt=%d,timeStamp=%d,md5=%s\n", \
                song_hearder_t.fileType,song_hearder_t.songType,song_hearder_t.sampleRate,song_hearder_t.fmt,song_hearder_t.channels, \
                song_hearder_t.fileLength,song_hearder_t.totalFrameCnt,song_hearder_t.timeStamp,song_hearder_t.fileMd5);

        //写入头部文件
        fwrite(&song_hearder_t,1,sizeof(online_song_header_t), audio_data_file);

        //跳过头部长度
        fseek(audio_data_file,ONLINE_SAVER_FILE_HEADER_LENGTH,SEEK_SET);
    }
    
    return ONLINE_SAVER_RESULT_NEW_FILE;
}


bool online_saver_put_frame(unsigned int framePos,unsigned char *buf,unsigned short len)
{
    if(!IS_ENABLE_ONLINESAVER)
    {
        return false;
    }

    //保存当前的歌曲名称和长度，避免播放完成后瞬间新的歌曲信息下来改变了当前值
    static char CurSongName[128]={0};
    static unsigned int CurTotalFileLength=0;

    if(totalFrameCnt >0 && currentFramePos != totalFrameCnt-1 && audio_data_file )
    {
        if( currentFramePos+1 == framePos )
        {
            currentFramePos = framePos;
            //保存到内存里面
            //首先存储帧长度
            memcpy(saverBuffer+saverMemPos,&len,2);
            saverMemPos+=2;
            //存储实际帧
            memcpy(saverBuffer+saverMemPos,buf,len);
            saverMemPos+=len;
            if(saverMemPos>=64*1024 || (framePos == totalFrameCnt-1))   //舍弃最后一帧
            {
                /*写数据到文件*/
		        fwrite(saverBuffer,saverMemPos,sizeof(unsigned char),audio_data_file);
                saverMemPos=0;
            }
            if(totalFrameCnt>50 && framePos == totalFrameCnt-50)
            {
                sprintf(CurSongName,"%s",songSaveName);
                CurTotalFileLength = totalFileLength;
            }
            else if(totalFrameCnt <=50 && framePos == totalFrameCnt)
            {
                sprintf(CurSongName,"%s",songSaveName);
                CurTotalFileLength = totalFileLength;
            }
            
            
            if(framePos == totalFrameCnt-1)
            {
                //写入尾部标识
                fwrite(ONLINE_SAVER_FILE_END_FLAG,strlen(ONLINE_SAVER_FILE_END_FLAG),sizeof(char),audio_data_file);
#if 0
                int size = ftell(audio_data_file);
                printf("online_saver_put_frame:all finish,length=%d...\n",size);
#endif
                //重置总帧数、当前帧数
                totalFrameCnt = 0;
                currentFramePos = -1;

                //关闭文件
                fclose(audio_data_file);
                audio_data_file=NULL;

                online_song_t onlineSong;
                sprintf(onlineSong.songName,"%s",CurSongName);
                onlineSong.song_header.fileLength = CurTotalFileLength;
                MoveSongToFlash_Thread(&onlineSong);
#if 0
                //将文件移动到flash
                onlineSaver_file_move_to_flash(CurSongName,CurTotalFileLength);
                //貌似此处不重置也没关系
                if(currentFramePos>0 && (currentFramePos == totalFrameCnt-1))
                {
                    printf("online_saver_reset3.\n");
                    online_saver_reset();
                }
#endif
            }
            else
            {
                if((framePos % 100) == 0)
                    printf("online_saver_put_frame:%d...\n",framePos);
            }

            return true;
        }
        else
        {
            printf("currentFramePos=%d,framePos=%d\n",currentFramePos,framePos);
            online_saver_reset();
            return false;
        }
    }
    else
    {
        //printf("online_saver_put_frame:exit...\n");
        return false;
    }
}



online_song_header_t onlineSaver_get_songHeader(char *songName)
{
    online_song_header_t m_SongHeader;		// 歌曲文件头信息
    
    return m_SongHeader;
}


bool onlineSaver_found_song(char *songName,unsigned int fileLength,unsigned int frameCnt)
{
    char *songPath=getSongSaverMemPath(songName);
    if(songPath)
    {
        if(IsFileExist(songPath))
        {
            //读出文件
        }
    }
    return false;
}



void* MoveSongToFlash(void *p_arg)
{
    online_song_t *onlineSong = (online_song_t*)p_arg;

    char srcPath[256]={0};
	char desPath[256]={0};
    sprintf(srcPath,"%s/%s",ONLINE_SAVER_MEM_DIR,onlineSong->songName);
	sprintf(desPath,"%s/%s",ONLINE_SAVER_FLASH_DIR,onlineSong->songName);
    MoveFile(srcPath,desPath);

    //检查文件是否OK
    onlineSaver_Check_Flash_Song_OK(onlineSong->songName);
    GetSaverPartitionSize();

    //判断当前的歌曲是不是原来的歌曲
    char *songPath=getSongSaverMemPath(onlineSong->songName);
    if(songPath)
    {
        printf("Remove MemSong:%s\n",onlineSong->songName);
        remove_file(songPath);
    }

    printf("MoveSongToFlash:%s OK\n",onlineSong->songName);

    free(onlineSong);
}

void MoveSongToFlash_Thread(online_song_t *onlineSong)
{
    #if 1   //20230103 再判断一次flash是否有足够空间存储此歌曲，如果不够，需要删除旧的歌曲
    printf("MoveSongToFlash_Thread:check space=%d\n",onlineSaver_check_file_meet_flash_space(onlineSong->song_header));
    #endif

    //预先减少可用空间，因为move需要时间，此时如果下一首播放指令过来，可用空间会计算错误
    flash_sapce.freeSpace = flash_sapce.freeSpace - onlineSong->song_header.fileLength - ONLINE_SAVER_FILE_LENGTH_EXTRA;
    printf("MoveSongToFlash_Thread:freeSpace=%d\n",flash_sapce.freeSpace);
    
    //拷贝到内存结构体
    online_song_t *onlineSongReal = malloc(sizeof(online_song_t));
    memcpy(onlineSongReal,onlineSong,sizeof(online_song_t));

	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)MoveSongToFlash, onlineSongReal);
	pthread_attr_destroy(&Pthread_Attr);
}
