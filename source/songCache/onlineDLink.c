/*
 * stack.c
 *
 *  Created on: Aug 27, 2020
 *      Author: king
 */

#include <malloc.h>
#include <string.h>
#include "assert.h"
#include "onlineDLink.h"



void Dlist_Init(DoubleLink *list) {
  Node *s = (Node*)malloc(sizeof(Node));
  assert(s != NULL);
  list->first = list->last = s;
  list->first->prio = NULL;
  list->last->next = NULL;
  list->size = 0;
}

static Node* Dlist_createnode(ElemType x) {
  Node *s = (Node*)malloc(sizeof(Node));
  assert(s != NULL);
  s->data = x;
  s->prio = s->next = NULL;
  return s;
}

void Dlist_push_back(DoubleLink *list, ElemType x) {
  Node *s = Dlist_createnode(x);
  s->prio = list->last;
  list->last->next = s;
  list->last = s;
  list->size++;
}
void Dlist_push_front(DoubleLink *list,ElemType x) {
  Node *s = Dlist_createnode(x);
  if (list->first == list->last) {
    s->prio = list->first;
    list->first->next = s;
    list->last = s;
  }
  else {
    s->next = list->first->next;
    s->next->prio = s;
    s->prio = list->first;
    list->first->next = s;
  }
  list->size++;
}
void Dlist_show_list(DoubleLink *list) {
  Node *p = list->first->next;
  while (p != NULL) {
    //printf("%d->", p->data);
    printf("Song:%s,fileType=%d,songType=%d,sampleRate=%d,fmt=%d,channels=%d,fileLength=%d,totalFrameCnt=%d,timeStamp=%d\n", \
    p->data.songName,p->data.song_header.fileType,p->data.song_header.songType,p->data.song_header.sampleRate,p->data.song_header.fmt,p->data.song_header.channels, \
    p->data.song_header.fileLength,p->data.song_header.totalFrameCnt,p->data.song_header.timeStamp);
    p = p->next;
  }
  //printf("\n");
}
void Dlist_pop_back(DoubleLink *list) {
  if (list->size == 0) return;
  Node *p = list->first;
  while (p->next != list->last)
    p = p->next;
  free(list->last);
  list->last = p;
  list->last->next = NULL;
  list->size--;
}
void Dlist_pop_front(DoubleLink *list) {
  if (list->size == 0) return;
  Node *p = list->first->next;
  if (list->first->next == list->last) {
    list->last = list->first;
    list->last->next = NULL;
  }
  else {
    list->first->next = p->next;
    p->next->prio = list->first;
  }
  free(p);
  list->size--;
}

void Dlist_front_data(DoubleLink *list,ElemType *elem)
{
  memset(elem,0,sizeof(ElemType));
  if (list->size >0)
  {
    Node *p = list->first->next;
    if(p)
    {
       memcpy(elem,&p->data,sizeof(ElemType));
    }
  }
}

void Dlist_front_data_next(DoubleLink *list,ElemType *elem)
{
  memset(elem,0,sizeof(ElemType));
  if (list->size >1)
  {
    Node *p = list->first->next->next;
    if(p)
    {
       memcpy(elem,&p->data,sizeof(ElemType));
    }
  }
}

void Dlist_insert_val(DoubleLink *list, ElemType x) {
  Node *p = list->first;
  while (p->next != NULL && p->next->data.song_header.timeStamp < x.song_header.timeStamp)
    p = p->next;
  if (p->next == NULL)
	  Dlist_push_back(list, x);
  else {
    Node *s = Dlist_createnode(x);
    s->next = p->next;
    s->next->prio = s;
    s->prio = p;
    p->next = s;
    list->size++;
  }
}
Node* Dlist_find(DoubleLink *list, ElemType x) {
  Node *p = list->first->next;
  while (p!=NULL && strcmp(p->data.songName,x.songName))
    p = p->next;
  return p;
}
int Dlist_length(DoubleLink *list) {
  return list->size;
}
void Dlist_delete_val(DoubleLink *list, ElemType x) {
  if (list->size == 0) return;
  Node *p = Dlist_find(list, x);
  if (p == NULL) {
    printf("要删除的数据不存在！\n");
    return;
  }
  if (p == list->last) {
    list->last = p->prio;
    list->last->next = NULL;
  }
  else {
    p->next->prio = p->prio;
    p->prio->next = p->next;
  }
  free(p);
  list->size--;
}
void Dlist_sort(DoubleLink *list) {
  if (list->size == 0 || list->size == 1) return;
  Node *s = list->first->next;
  Node *q = s->next;
  list->last = s;
  list->last->next = NULL;
  while (q != NULL) {
    s = q;
    q = q->next;
    Node *p = list->first;
    while (p->next != NULL && p->next->data.song_header.timeStamp < s->data.song_header.timeStamp)
      p = p->next;
    if (p->next == NULL) {
      s->next = NULL;
      s->prio = list->last;
      list->last->next = s;
      list->last = s;
    }
    else {
      s->next = p->next;
      s->next->prio = s;
      s->prio = p;
      p->next = s;
    }
  }
}
void reverse(DoubleLink *list) {
  if (list->size == 0 || list->size == 1) return;
  Node *p = list->first->next;
  Node *q = p->next;
  list->last = p;
  list->last->next = NULL;
  while (q != NULL) {
    p = q;
    q = q->next;
    p->next = list->first->next;
    p->next->prio = p;
    p->prio = list->first;
    list->first->next = p;
  }
}

static void Dlist_clear(DoubleLink *list) {
  if (list->size == 0) return;
  Node *p = list->first->next;
  while (p != NULL) {
    if (p == list->last) {
      list->last = list->first;
      list->last->next = NULL;
    }
    else {
      p->next->prio = p->prio;
      p->prio->next = p->next;
    }
    free(p);
    p = list->first->next;
  }
  list->size = 0;
}

void Dlist_destroy(DoubleLink *list) {
	Dlist_clear(list);
  free(list->first);
  list->first = list->last = NULL;
}


