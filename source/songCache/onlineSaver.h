/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> 
 * @Date: 2022-06-02 10:12:51 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-06-02 10:12:51
 */


#ifndef __ONLINESAVER_H_
#define __ONLINESAVER_H_

#include <stdbool.h>

#define ONLINE_SAVER_MAX_LENGTH 20971520    //在线保存歌曲的最大长度限制20MB

#define ONLINE_SAVER_MEM_DIR    "/tmp"      //保存内存目录

#define ONLINE_SAVER_MOUNT_DIR  "/appconfigs"  //挂载目录

#define ONLINE_SAVER_FLASH_DIR  "/appconfigs/online"  //保存内存目录

#define ONLINE_SAVER_FILE_END_FLAG  "MHISONG"        //保存文件尾部标识

#define ONLINE_SAVER_FILE_HEADER_LENGTH 100  //保存文件头部长度

#define ONLINE_SAVER_FILE_LENGTH_EXTRA   128*1024     //存储文件额外占用的空间128KB
#define ONLINE_SAVER_FLASH_RESERVE_SPACE 1024*1024   //FLASH至少留下多大的空间1MB


enum
{
    ONLINE_SAVER_RESULT_NEW_FILE=0,
    ONLINE_SAVER_RESULT_ALREADY_EXIST=1,
    ONLINE_SAVER_RESULT_NOT_ALLOW=2,
    ONLINE_SAVER_RESULT_PARAMETER_ERROR=3
};



enum{
    ONLINE_SAVER_FILE_TYPE_NORMAL=0,
    ONLINE_SAVER_FILE_TYPE_TIMING=1,
    ONLINE_SAVER_FILE_TYPE_ALARM=2,
    ONLINE_SAVER_FILE_TYPE_FIXED=3
};
enum{
    ONLINE_SAVER_SONG_TYPE_MP3=1,
    ONLINE_SAVER_SONG_TYPE_WAV=2
};

// 歌曲文件头信息
typedef struct
{
    unsigned char   fileType;       // 类型（或者说是优先级，普通文件，定时文件，消防文件等等），备用
    unsigned char   songType;       // 歌曲类型(1为MP3,2为WAV)
    unsigned int	sampleRate;		// 采样频率(4bytes)
    unsigned char   fmt;		    // bits per sample (量化位数)(1byte)
    unsigned char   channels;		// 通道数，单声道为1，双声道为2,(1byte)
    unsigned int    fileLength;     // 文件长度,长度字节(4bytes)
    unsigned int    totalFrameCnt;  // 总帧数(4byte)
    unsigned int    currentFrame;   // 当前帧数(为0才保存)
    unsigned int    timeStamp;      // timeStamp,用来判断时间
    char            fileMd5[32+1];  // md5
}online_song_header_t;


// 在线歌曲集合
typedef struct
{
    char songName[128];              //歌曲名
    //unsigned int  real_fileLength;   //实际存储长度
    online_song_header_t song_header;
}online_song_t;


// FLASH挂载空间
typedef struct
{
    unsigned int totalSpace;       // 总空间
    unsigned int freeSpace;       // 剩余空间
}flash_sapce_t;

void init_onlineSaver();            //初始化在线保存模块
int online_saver_init(online_song_header_t song_hearder_t,char *songName);
bool online_saver_put_frame(unsigned int framePos,unsigned char *buf,unsigned short len);
bool onlineSaver_ready_read_localSong(char *songName);
void onlineSaver_close_localSong();
void onlineSaver_localSong_read_frame(char *frameBuf,unsigned short *frameLen);


void SetCurrentPlayFrame(unsigned playFrame);
unsigned int GetCurrentPlayFrame();

void MoveSongToFlash_Thread(online_song_t *onlineSong);

online_song_t* GetExistOnlineSongByName(char *songName);

#endif /* __ONLINESAVER_H_ */
