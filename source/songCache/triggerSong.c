#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <semaphore.h>
#include "sysconf.h"
#include "triggerSong.h"

triggerSong_t st_triggerSong;

#define TRIGGER_GPIO    PAD_KEY7

#define TRIGGER_MODE_SHORT		0x00	// 短路输入
#define TRIGGER_MODE_LEVEL		0x01	// 电平输入

void Create_Trigger_Event_Task(void);

void InitTrigger()
{
    //system("/customer/riu_w 0x103e 0x3a 0x007D");
    //首先将相关引脚设置为输入
    Set_Gpio_Input(TRIGGER_GPIO);
#if 0
    st_triggerSong.trigger_switch=1;
    sprintf(st_triggerSong.trigger_song_name,"ring8s.wav");
#endif
    Create_Trigger_Event_Task();
}


/*********************************************************************
 * @fn      Create_Trigger_Event
 *
 * @brief  	触发事件线程
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_Trigger_Event(void *p_arg)
{
    printf("Create_Trigger_Event...\n");
    int thread_sleepTime=100000; //线程休眠时间，默认50ms
    int valid_level=0;
    //设置触发模式
    int cur_triggerMode=-1;
    if(cur_triggerMode!=st_triggerSong.trigger_mode)
    {
        cur_triggerMode = st_triggerSong.trigger_mode;
        GPIO_set_trigger_mode(st_triggerSong.trigger_mode);
    }
    if(st_triggerSong.trigger_mode == TRIGGER_MODE_SHORT)
    {
        valid_level=0;
    }
    else
    {
        valid_level=1;
    }
    int cur_triggerValue=Get_Gpio_Value(TRIGGER_GPIO);
    if(cur_triggerValue == valid_level)
    {
        Set_Trigger_Led(1);
    }
    else
    {
        Set_Trigger_Led(0);
    }
	while(1)
	{
        //判断音源是否有效
        int sysSource = get_system_source();
        int tmp_key1Value = Get_Gpio_Value(TRIGGER_GPIO);
        //printf("trigger gpio=%d\n",tmp_key1Value); 
        if(cur_triggerMode!=st_triggerSong.trigger_mode)
        {
            cur_triggerMode = st_triggerSong.trigger_mode;
            GPIO_set_trigger_mode(st_triggerSong.trigger_mode);
        }
        if(st_triggerSong.trigger_mode == TRIGGER_MODE_SHORT)
        {
            valid_level=0;
        }
        else
        {
            valid_level=1;
        }
        if(tmp_key1Value != cur_triggerValue && st_triggerSong.trigger_switch)
        {
            printf("trigger status change=%d\n",tmp_key1Value);
            cur_triggerValue = tmp_key1Value;
            if(cur_triggerValue == valid_level)
            {
                Set_Trigger_Led(1);
                printf("trigger on...\n");
                if(sysSource == SOURCE_NULL || sysSource == SOURCE_AUX)
                {
                    printf("source OK...\n");
                    //判断触发歌曲是否存在？
                    online_song_t* onlineSong=GetExistOnlineSongByName(st_triggerSong.trigger_song_name);
                    if(onlineSong)
                    {
                        printf("found trigger song,ready go...\n");
                        st_triggerSong.trigger_local_play_flag = 1;
                        st_triggerSong.trigger_local_play_finish = 0;
                        //播放
                        set_system_source(SOURCE_LOCAL_PLAY);
                        g_centralized_mode_is_existLocalSong=1;
                        g_concentrated_start = 1;
                        concentrate_repeat_paly_enable = 1;  //允许重新请求播放

                        //重新计时
                        g_centralized_mode_timeout_count = 0;
                        g_centralized_mode_timeout_pause = 0;		//继续检测
                        g_concentrated_need_exit_flag = 0;

                        g_media_status = SONG_PLAYING;

                        sprintf(g_media_name,st_triggerSong.trigger_song_name);

                        //设置音量,触发音量为0时跟随
                        if(st_triggerSong.trigger_volume != 0)
                        {
                            g_system_volume = st_triggerSong.trigger_volume;
                        }

                        pkg_query_current_status(NULL);

                        struct in_addr centralized_ipAdder;
                        centralized_ipAdder.s_addr = inet_addr(g_concentrated_multicast_address); 
                        
                        g_concentrated_song_sample_rate = onlineSong->song_header.sampleRate;
                        g_concentrated_song_fmt = onlineSong->song_header.fmt;
                        g_concentrated_song_channels = onlineSong->song_header.channels;


                        Open_Audio_Out(g_concentrated_song_sample_rate,g_concentrated_song_fmt,g_concentrated_song_channels);

                        //如果存在本地歌曲,挂起超时任务
                        suspend_Centralized_Mode_Timing_Task();
                        
                        Create_Concentrated_Mode_Play_Task();


                        //需要一个变量，表示当前正在进行本地触发播放
                    }
                    else
                    {
                        //如果不存在，那么请求服务器播放
                        printf("not found trigger song,request server play...\n");
                        procHost_Request_Trigger_Play();
                    }
                }
            }
            else
            {
                Set_Trigger_Led(0);
                printf("trigger off...\n");
            }
        }

        if(st_triggerSong.trigger_local_play_finish)
        {
            st_triggerSong.trigger_local_play_finish = 0;
            //设置空闲状态
            if(sysSource == SOURCE_LOCAL_PLAY)
			    Set_zone_idle_status(NULL,  __func__, __LINE__,true);
        }

		usleep(thread_sleepTime);
	}
}

/*********************************************************************
 * @fn      Create_Trigger_Event_Task
 *
 * @brief  	创建触发事件线程
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_Trigger_Event_Task(void)
{
    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Create_Trigger_Event, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}