/*
 * stack.h
 *
 *  Created on: Aug 27, 2020
 *      Author: king
 */

#ifndef __ONLINEDLINK_H_
#define __ONLINEDLINK_H_
#include "stddef.h"
#include "onlineSaver.h"

typedef online_song_t ElemType;
typedef struct Node {
  ElemType data;
  struct Node *prio;
  struct Node *next;
}Node,*PNode;
typedef struct DoubleLink {
  PNode first;
  PNode last;
  size_t size;
}DoubleLink;
void Dlist_Init(DoubleLink *list);//初始化双链表
void Dlist_push_back(DoubleLink *list, ElemType x);//在双链表的末尾插入元素
void Dlist_push_front(DoubleLink *list, ElemType x);//在双链表的头部插入元素
void Dlist_show_list(DoubleLink *list);//打印双链表
void Dlist_pop_back(DoubleLink *list);//删除双链表的最后一个元素
void Dlist_pop_front(DoubleLink *list);//删除双链表的第一个元素
void Dlist_insert_val(DoubleLink *list, ElemType val);//将数据元素插入到双链表中（要求此时双链表中的数据元素顺序排列）
Node* Dlist_find(DoubleLink *list, ElemType x);//查找双链表中数据值为x的结点
int Dlist_length(DoubleLink *list);//求双链表的长度
void Dlist_delete_val(DoubleLink *list, ElemType x);//按值删除双链表中的某个数据元素
void Dlist_sort(DoubleLink *list);//对双链表进行排序
void Dlist_reverse(DoubleLink *list);//逆置双链表
static void Dlist_clear(DoubleLink *list);//清除双链表
void Dlist_destroy(DoubleLink *list);//摧毁双链表

static Node* Dlist_createnode(ElemType x);//创建结点

void Dlist_front_data(DoubleLink *list,ElemType *elem); //获取双链表第一个元素的数据
void Dlist_front_data_next(DoubleLink *list,ElemType *elem);  //获取双链表第二个元素的数据
#endif /* __DOUBLELINK_H_ */
