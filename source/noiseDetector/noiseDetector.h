#ifndef _NOISE_DETECTOR_H_
#define _NOISE_DETECTOR_H_

#if IS_DEVICE_NOISE_DETECTOR

#define UART_NOISE_DETECTOR_UART     "/dev/ttyS1" //uart device name
#define NOISE_NUM_CHANNELS 8
#define NOISE_NUM_SEGMENTS 8
#define POLL_INTERVAL_MS 120
#define RESPONSE_TIMEOUT_MS 80
#define MAX_RESPONSE_LEN 128

/*******噪声自适应器相关变量************/
typedef struct {
    bool isEnable;
    unsigned short channelVal[NOISE_NUM_CHANNELS]; //有效值:300~1300,对应30dB~130dB
    unsigned char segmentVol[NOISE_NUM_SEGMENTS]; //8段式噪声音量值
}st_noiseDetector_info;
extern st_noiseDetector_info g_noiseDetector_info;

extern unsigned char defaultNoiseSegmentVol[NOISE_NUM_SEGMENTS];


void uart_noise_detector_init();


#endif

#endif