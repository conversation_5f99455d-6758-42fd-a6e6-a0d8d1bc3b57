#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/time.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <termios.h>
#include <pthread.h>
#include <sys/select.h>

#include "sysconf.h"
#include "uartCommon.h"
#include "noiseDetector.h"

#if IS_DEVICE_NOISE_DETECTOR

static int g_noiseDetector_Uartfd;
st_noiseDetector_info g_noiseDetector_info;

unsigned char defaultNoiseSegmentVol[NOISE_NUM_SEGMENTS] = {30,40,50,60,70,80,90,100};

// 计算Modbus CRC16
uint16_t crc16(uint8_t *data, int length) {
    uint16_t crc = 0xFFFF;
    for (int i = 0; i < length; i++) {
        crc ^= data[i];
        for (int j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc >>= 1;
            }
        }
    }
    return crc;
}

// 设置串口
int setup_serial(const char *port, int baud) {
    int fd = open(port, O_RDWR | O_NOCTTY);
    if (fd < 0) {
        perror("Error opening serial port");
        return -1;
    }

    struct termios tty;
    memset(&tty, 0, sizeof(tty));
    if (tcgetattr(fd, &tty) != 0) {
        perror("Error getting termios");
        close(fd);
        return -1;
    }

    // 设置波特率
    speed_t speed;
    switch (baud) {
        case 2400: speed = B2400; break;
        case 4800: speed = B4800; break;
        case 9600: speed = B9600; break;
        default: speed = B4800;
    }
    cfsetospeed(&tty, speed);
    cfsetispeed(&tty, speed);

    tty.c_cflag &= ~PARENB;  // 无校验
    tty.c_cflag &= ~CSTOPB;  // 1位停止位
    tty.c_cflag &= ~CSIZE;
    tty.c_cflag |= CS8;      // 8数据位
    //tty.c_cflag &= ~CRTSCTS; // 无硬件流控
    tty.c_cflag |= CREAD | CLOCAL;

    tty.c_iflag = IGNPAR;

    tty.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG); // 原始模式
    tty.c_oflag &= ~OPOST;   // 原始输出
    
    // 设置为阻塞模式，配合select使用
    tty.c_cc[VMIN] = 1;      // 阻塞读取，至少读取1个字节
    tty.c_cc[VTIME] = 0;     // 无超时
    
    tcflush(fd, TCIOFLUSH);
    
    if (tcsetattr(fd, TCSANOW, &tty) != 0) {
        perror("Error setting termios");
        close(fd);
        return -1;
    }

    return fd;
}

// 获取当前时间（毫秒）
long long current_timestamp_ms() {
    struct timeval te;
    gettimeofday(&te, NULL);
    return te.tv_sec * 1000LL + te.tv_usec / 1000;
}

// 读取指定通道的噪声值
float read_channel(int fd, uint8_t channel_addr) {
    uint8_t request[8] = {
        channel_addr, 0x03, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00
    };
    
    uint16_t crc = crc16(request, 6);
    request[6] = crc & 0xFF;
    request[7] = (crc >> 8) & 0xFF;
#if 1
    //tcflush(fd, TCIFLUSH); // 清空输入缓冲区
    tcflush(fd, TCOFLUSH); // 清空输出缓冲区
#endif
   //printf("write start1:%d\n", channel_addr);
   // 发送前添加静默区
    usleep(20000); // 1ms总线空闲
    if (write(fd, request, 8) != 8) {
        perror("Write error");
        return -1;
    }
    usleep(20000); // 1ms总线空闲
     // 关键：等待物理层发送完成
    int ret = tcdrain(fd);
    if (ret < 0) {
        perror("tcdrain error");
        return -1;
    }
    //printf("write start2:%d\n", channel_addr);
    
    uint8_t response[MAX_RESPONSE_LEN];
    int total_bytes = 0;
    long long start_time = current_timestamp_ms();
    
    while (1) {
        fd_set fds;
        FD_ZERO(&fds);
        FD_SET(fd, &fds);
        
        struct timeval tv;
        tv.tv_sec = 0;
        tv.tv_usec = RESPONSE_TIMEOUT_MS*1000;
        //printf("select start:%d\n", channel_addr);
        int ret = select(fd+1, &fds, NULL, NULL, &tv);
        if (ret == -1) {
            perror("select error");
            return -1;
        } else if (ret == 0) {  // 超时
            if (total_bytes > 0) {
                fprintf(stderr, "Timeout: Incomplete response from channel %d (%d bytes)\n", 
                        channel_addr, total_bytes);
            }
            return -1;
        }
        //printf("select end:%d\n", channel_addr);
        int n = read(fd, response + total_bytes, MAX_RESPONSE_LEN - total_bytes);
        //printf("read %d\n", n);
        if (n > 0) {
            total_bytes += n;
            
            if (total_bytes >= 7) {
                if (response[0] == channel_addr && response[1] == 0x03) {
                    uint8_t data_len = response[2];
                    if (total_bytes >= (3 + data_len + 2)) {
                        break;
                    }
                }
            }
        } else if (n <= 0) {
            perror("Read error");
            return -1;
        }
    }

    //printf("read_channel end:%d\n", channel_addr);
    
    uint16_t recv_crc = (response[total_bytes - 1] << 8) | response[total_bytes - 2];
    uint16_t calc_crc = crc16(response, total_bytes - 2);
    if (recv_crc != calc_crc) {
        fprintf(stderr, "CRC error on channel %d: %04X vs %04X\n", 
                channel_addr, recv_crc, calc_crc);
        return -1;
    }
    
    if (response[1] != 0x03 || response[2] != 0x02) {
        fprintf(stderr, "Invalid response frame from channel %d\n", channel_addr);
        return -1;
    }
    
    int raw_value = (response[3] << 8) | response[4];
    //return raw_value / 10.0f;
	return raw_value;
}

void *noise_detector_thread(void) {
    system("/customer/riu_w 0x103e 0x3a 0x0075");
    int fd = setup_serial(UART_NOISE_DETECTOR_UART, 9600);
    if (fd < 0) {
        pthread_exit(NULL);
    }
    
    uint8_t channel_addresses[NOISE_NUM_CHANNELS] = {1, 2, 3, 4, 5, 6, 7, 8};
    float noise_values[NOISE_NUM_CHANNELS] = {-1};
    int current_channel = 0;

	bool isChannelChange=false;
    
    while (1) {
        long long poll_start = current_timestamp_ms();
        
        noise_values[current_channel] = read_channel(fd, channel_addresses[current_channel]);
        
        if (noise_values[current_channel] > 300 && noise_values[current_channel] <= 1300) {
            printf("Channel %d: %.1f dB\n", 
            	channel_addresses[current_channel], noise_values[current_channel] / 10.0f);
			if(g_noiseDetector_info.channelVal[current_channel]!=noise_values[current_channel])
			{
				isChannelChange=true;
				g_noiseDetector_info.channelVal[current_channel] = noise_values[current_channel];
			}
        } else {
            //printf("Channel %d: No response or error\n", channel_addresses[current_channel]);
        }

		if(current_channel == NOISE_NUM_CHANNELS-1)
		{
			if(isChannelChange)
			{
				isChannelChange=false;
				//发送给服务器
				Host_Set_NoiseDetector_Config(NULL);
			}
		}
        
        current_channel = (current_channel + 1) % NOISE_NUM_CHANNELS;
        
        long long elapsed = current_timestamp_ms() - poll_start;
        if (elapsed < POLL_INTERVAL_MS) {
            int sleep_time = (int)(POLL_INTERVAL_MS - elapsed);
            if (sleep_time > 0 && sleep_time <= POLL_INTERVAL_MS) {
                usleep(sleep_time * 1000);
            }
            else{
                usleep(POLL_INTERVAL_MS * 1000);
            }
        }
    }
    
    close(fd);
    pthread_exit(NULL);
}

void uart_noise_detector_init(void) {
    pthread_t t_uart_Pthread;
    pthread_attr_t Pthread_TERMINAL_Attr;
    pthread_attr_init(&Pthread_TERMINAL_Attr);
    pthread_attr_setdetachstate(&Pthread_TERMINAL_Attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&t_uart_Pthread, &Pthread_TERMINAL_Attr, (void *)noise_detector_thread, NULL);
    pthread_attr_destroy(&Pthread_TERMINAL_Attr);
}

#endif