#include "debug.h"
#include "sip.h"
#include "timerEx.h"
#include "rtpMulticast.h"
#include "voice.h"
#include "appConfig.h"
#include "priority.h"
#include "apiTest.h"
#include "pjsuaCommon.h"

#include "util.h"
#include "../log/my_log.h"

#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/sem.h>

int semid_sip_conf=-1;

appDataConfig_t appData;


// 定义信号量操作函数
void sem_signal_send(int semid) {
    struct sembuf sop;
    sop.sem_num = 0;
    sop.sem_op = 1;  // 信号量加1
    sop.sem_flg = 0;
    semop(semid, &sop, 1);
}

// 获取信号量的当前值
int get_sem_value(int semid) {
    return semctl(semid, 0, GETVAL, 0);
}

void sem_sip_conf_init()
{
	if(!IsFileExist(SEM_SIP_CONF_FILE))
	{
		FILE *file = fopen(SEM_SIP_CONF_FILE, "w");
		if (file == NULL) {
			perror("Error SEM_SIP_CONF_FILE file");
			return;
    	}
		fclose(file);
	}
	
	key_t key = ftok(SEM_SIP_CONF_FILE, 'a');  // 生成共享内存的键值
	if(key == -1)
	{
		printf("ftok error!\n");
		return;
	}
    // 创建或获取信号量集
    semid_sip_conf = semget(key, 1, IPC_CREAT | IPC_EXCL | 0666);
    if (semid_sip_conf < 0) {
        // 如果信号量集已存在，则直接获取
        semid_sip_conf = semget(key, 1, 0666);
		printf("sem_sip_conf_init:sem already exist!\n");
        if (semid_sip_conf < 0) {
            perror("semget");
            return;
        }
    } else {
		printf("semid_sip_conf semget succeed!\n");
        // 如果是新创建的信号量集，初始化信号量值为0
        if (semctl(semid_sip_conf, 0, SETVAL, 0) < 0) {
            perror("semctl");
            return;
        }
    }

	sem_sip_conf_send();
}

void sem_sip_conf_send()
{
	// 连接共享内存段
	if(semid_sip_conf == -1)
	{
		return;
	}

#if 0
	if(get_sem_value(semid_sip_conf) >=1)
	{
		printf("sem_sip_conf_send:get_sem_value=%d\n",get_sem_value(semid_sip_conf));
	}
#endif

	key_t key = ftok(SEM_SIP_CONF_FILE, 'a');  // 生成共享内存的键值

	// 尝试获取共享内存段
    int shmid = shmget(key, SHM_SIP_CONF_SIZE, IPC_CREAT | 0666);
    if (shmid < 0) {
        // 如果已存在，删除并重新创建
		perror("shmget");
		return;
    }

    st_sip_conf_Data *shmaddr = (st_sip_conf_Data *) shmat(shmid, NULL, 0);
    if (shmaddr == (void *) -1) {
        perror("shmat");
        return;
    }

	shmaddr->dacl_gain = dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L];
	shmaddr->dacr_gain = dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R];
	shmaddr->mic_gain = 4096;
	shmaddr->ouput_volume = appData.voiceInfoSettings.callVolumeTemp;

	// 发送信号量通知数据已写入
    sem_signal_send(semid_sip_conf);

	// 分离共享内存
    shmdt(shmaddr);

	printf("sem_sip_conf_send succeed!\n");
}


//ip呼叫时注册的临时用户
SipUserInfo_t ipCallUser; 
#define THIS_FILE __FILE__

//#define PJSIP_LOCAL_SIP_PORT 	5060
int setVoicePrioritySettings(unsigned char codec1,unsigned char codec2,unsigned char codec3,unsigned char codec4);
void PJSIP_CallbackConfig(pjsua_config *cfg);

static void dialNumTimeoutHandle(pj_timer_heap_t *timer_heap,
				    struct pj_timer_entry *entry);
static void hotKeyDialNumberTimeoutHandle(pj_timer_heap_t *timer_heap,
				    struct pj_timer_entry *entry);


static void hotKeyDialNumberTimeoutHandle_num2(pj_timer_heap_t *timer_heap,
				    struct pj_timer_entry *entry);

static SipUserInfo_t * findAccountByIndex(unsigned char index);
SipUserInfo_t * getSipUserInfo(pjsua_acc_id acc_id);
int pjsuaAccAdd(SipUserInfo_t *user);

static pjsip_status_code pjsipLastCode=0;

pjsua_transport_id sip_transport_id[SIP_USER_MAX]={-1,-1};


typedef void (* dialNumberTimeoutFunc_ptr)(int );
int dialNumTimeout_tid=-1;
sipCurStatus_t st_SipCurStatus;


extern int AutoAnswerTid;//自动应答定时点
extern int AutoHangupRingTimeoutTid;//自动挂断响铃超时定时定时器

static int curHotKeyLineNum2Temp=-1;
static int curHotKeySltLine=-1;
static int curHotKeyCallType=-1;

static int pjsip_init_is_finish = 0;

static functionKeysDSS_t * curActivateKeyInfo=NULL; //当前拨号的功能键信息，用于超时的第二号码拨号信息

static pj_timer_entry call_handle_Timer; //呼叫超时定时器
static pj_timer_entry call_handle_Timer2; //呼叫超时定时器

/*以下用于忙状态判断*/
static char sipCallUser[128] = {0};//比较用户
static unsigned char isTheSameUser = 0;//同用户标志
static unsigned char isDial = 0;//本端为发起端标志
unsigned char isDialNumber(void){return isDial;} //获取发起标志
void setDialNumberFlag(unsigned char flag){isDial = flag;	DBG("OAO");}//设置发起本端标志
void setTheSameUserFlag(unsigned char flag){isTheSameUser = flag;}//设置同用户信息标志
unsigned char isTheSameUserInfo(void){return isTheSameUser;}//获取同用户标志

unsigned char isTheSameUserCompare(char *userInfo)//判断发起用户是否相同
{
	if(userInfo == NULL)	return 0;
	DBG("userInfo:%s\tsipCallUser:%s",userInfo, sipCallUser);
	if(strlen(userInfo) >= strlen(sipCallUser)){
		if(strstr(userInfo, sipCallUser) != NULL){
			NOTICE("The same Sip Call User.");
			return 1;
		}else{
			NOTICE("Different Sip Call User.");
			return 0;			
		}
	}else{
		if(strstr(sipCallUser, userInfo) != NULL){
			NOTICE("The same Sip Call User.");
			return 1;
		}else{
			NOTICE("Different Sip Call User.");
			return 0;						
		}
	}
	return 0;
}

int setSipCallUserInfo(char *userInfo)//设置比较用户信息
{
	if(userInfo == NULL)	return -1;
	memset(sipCallUser, 0, sizeof(sipCallUser));
	if(strlen(userInfo) < 128){
		strcpy(sipCallUser, userInfo);
	}else{
		WARNINGP("string 'userInfo' error!");
		return -1;
	}
	DBG("UserInfo :%s",sipCallUser);
	return 0;
}

void cleanSipConversationUser(void)//清除比较用户数据
{
	isTheSameUser = 0;
	memset(sipCallUser, 0, sizeof(sipCallUser));
	WARNINGP("Clean Sip Conversation User Info.");
}
///////////////////////////////////////////////////

/*
static checkSipCallSourceIsValid_Callbak 	checkSipCallSourceIsValid=NULL;
static startSipCallSource_Callbak 			startSipCallSource=NULL;
static stopSipCallSource_Callbak 			stopSipCallSource=NULL;
static NotifyAlterSipStatus_Callbak  		NotifyAlterSipStatus=NULL;
*/

/*** SIP所需函数设置 ***/
int checkSipCallSourceIsValid(){
	return PriorityIsValid(PRIORITY_SIP) && get_system_source() != SOURCE_SIP_CALLING;
}

int checkSipRegisterAlready()
{
	int i=0;
	for ( i = 0; i < SIP_USER_MAX; ++i)
	{
		if(appData.SipUserInfo[i].registerStatus == SIP_STATUS_REGISTER_SUCCEED){
			return 1;
		}
	}
	return 0;	
}

int startSipCallSource(int line){
	//onVoipCallStatusChanged(line,SIP_LINE_CALLING_STATUS_CALLING);
	//todo 状态变化，通知服务器
	//断开AI、AO
	printf("startSipCallSource......\n");
	Clean_All_Audio(1,1);
	#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
	mi_audio_in_deinit();
	mi_audio_out_deinit();
	#endif
	set_system_source(SOURCE_SIP_CALLING);
	
	appData.voiceInfoSettings.callVolumeTemp = appData.voiceInfoSettings.callVolume;

	//清空节目名称
	memset(g_media_name,0,sizeof(g_media_name));
	pkg_query_current_status(NULL);	//发送当前状态
	return SUCCEED;
}
int stopSipCallSource(int line){
	//todo
	//用线程实现

	//20241018 强制将两条线路设置成空闲
	bool needWaitIdle=false;
	for(int i=0;i<SIP_USER_MAX;i++)
	{
		if(appData.SipUserInfo[i].callStatus!=SIP_LINE_CALLING_STATUS_IDLE)
		{
			appData.SipUserInfo[i].callStatus = SIP_LINE_CALLING_STATUS_IDLE;
			needWaitIdle=true;
		}
	}
	if(needWaitIdle)
	{
		usleep(500000);
	}
	return 0;
}

int checkRtpMultSourceIsValid(){
	return PriorityIsValid(PRIORITY_SIP);
}

int startRtpMultRecvSource(unsigned char Volume){
	//onVoipCallStatusChanged(1,SIP_LINE_CALLING_STATUS_CALLING);

	return set_system_source(SOURCE_RTP_MULT);
}

#if 0
int callStatusLedRecover()
{
	SipUserInfo_t *user;
	user = findAccountByIndex(0);
	if(user){
		if(user->registerSwitch && user->registerStatus == SIP_STATUS_REGISTER_SUCCEED){
			onVoipCallStatusChanged(1,SIP_LINE_CALLING_STATUS_IDLE);//注册开启，且注册成功
		}else if(user->registerSwitch ){
			//注册开启并且为注册失败
			onVoipRegStatusChanged(1,user->registerStatus);
			//onVoipCallStatusChanged(1,SIP_LINE_CALLING_STATUS_IDLE);
		}else{
			closeSipLineLed(1);
		}
	}
	return 0;
}
#endif

int stopRtpMultRecvSource(){
	//Set_first_enter_RTP(1);	
	//return PrioritySourceGlobalStop(SOURCE_RTP_MULT);
	Set_zone_idle_status(NULL,  __func__, __LINE__,true);
	return 0;
}

int NotifyAlterSipStatus( unsigned char status){
	//onVoipRegStatusChanged(st_SipCurStatus.curActiveCallLine,status);
	//NotifyAlterSipCurrentStatus(status);
	NOTICE("SIP状态变更通知:%s",getSipUserStatusStr(0));
	#if SUPPORT_SIP
	SEND_SIP_STATUS_TO_SERVER(status);
	#endif
}

/**
 * [SetSipRegisterStatus 设置SIP登录状态]
 * @param  status [description]
 * @return        [description]
 */
int SetSipRegisterStatus(int index,unsigned char status)
{
	int i=0;
	for ( i = 0; i < SIP_USER_MAX; ++i)
	{
		if(appData.SipUserInfo[i].index == index ){
			appData.SipUserInfo[i].registerStatus=status;
			return 1;
		}
	}
	return 0;
}

const char * getSipStatusStr(unsigned char status)
{
	switch(status){		
		case SIP_STATUS_REGISTER_SUCCEED : return  "登陆成功";
		case SIP_STATUS_REGISTER_LOADING : return "正在登陆";
		case SIP_STATUS_REGISTER_FAILED_OTHER : return "登陆错误";
		case SIP_STATUS_REGISTER_FAILED_TIMEOUT : return "登陆超时";
		case SIP_STATUS_REGISTER_FAILED_PASSWORD : return "密码错误";
		case SIP_STATUS_REGISTER_CALLING : return "正在通话";
	}
	return "未知";
}


const char *getSipUserStatusStr(int user_index)
{
	SipUserInfo_t * user = findAccountByIndex(user_index);
	if(user == NULL)return "错误";
	return getSipStatusStr(user->registerStatus);
}


void setPjsipLastCode(pjsip_status_code code)
{
	pjsipLastCode = code;
}

void setUserCallStatus(pjsua_acc_id acc_id,int call_state){
	SipUserInfo_t * user ;
	user = getSipUserInfo(acc_id);
	if(user){
		user->callStatus = call_state;
	}
}

void setUserCallStatusByLine(int line,int call_state){
	SipUserInfo_t * user ;
	user = findAccountByIndex(line-1);
	if(user){
		user->callStatus = call_state;
	}
}

void displayAllUserInfo(void)
{
	printf("Display All User Infomation:\n");
	int i = 0;	
	for (; i < SIP_USER_MAX; ++i)
	{
		printf("acc_id:%d, Index:%d, User:%s, Server:%s\n", appData.SipUserInfo[i].acc_id,
			appData.SipUserInfo[i].index, appData.SipUserInfo[i].user, appData.SipUserInfo[i].serverAddress);
	}	
}

//设置所有线路通话状态
void setAllUserCallStatus(int status)
{
	WARNINGP("Set All Sip User Status Change Status:%d",status);
	int i = 0;	
	for (; i < SIP_USER_MAX; ++i)
	{
		onSIPStatusChanged(SIP_STATUS_TYPE_INV , i+1, status);
	}	
}
#if 0
//设置所有线路LED闪烁状态
void setAllVoipUserStatusChange(int status)
{
	
	WARNINGP("Set All Voip User Status Change Status:%d",status);
	int i = 0;	
	for (; i < SIP_USER_MAX; ++i)
	{
		onVoipCallStatusChanged(i+1, status);;
	}	
}
#endif

/**
 * [checkLineIsIdle 检测所有线路是否空闲]
 * @return [description]
 */
int checkLineIsIdle()
{		
	int i = 0;	
	for (; i < SIP_USER_MAX; ++i)
	{
		if(appData.SipUserInfo[i].callStatus != SIP_LINE_CALLING_STATUS_IDLE){
			return 0;
		}
	}
	return 1;
}

int checkCurActiveLineIsRing()
{
	int i = 0;
	if(st_SipCurStatus.curActiveCallLine==0)return 0;
	for (; i < SIP_USER_MAX; ++i)
	{
		if(st_SipCurStatus.curActiveCallLine-1 == i){
			if(appData.SipUserInfo[i].callStatus == SIP_LINE_CALLING_STATUS_RING){
				return 1;
			}else{
				return 0;
			}
		}		
	}
	return 0;
}

void stopCallHandleTimer(pjsip_status_code	last_status)
{
	NOTICE("stop Call Handle Timer last_status=%d",last_status);
	if(call_handle_Timer2.id && (last_status == 200 || last_status == 486) ){
		NOTICE("cancel call handle Timer2...");
		pjsip_endpt_cancel_timer(pjsua_get_pjsip_endpt(),&call_handle_Timer2);
		call_handle_Timer2.id = 0;
	}
	if(call_handle_Timer.id){
		NOTICE("cancel call handle Timer...");
		pjsip_endpt_cancel_timer(pjsua_get_pjsip_endpt(),&call_handle_Timer);
		call_handle_Timer.id = 0;		
	}
	//如果对方忙 则检测是否需要拨打第二线路
	if(call_handle_Timer2.id == 0 && curActivateKeyInfo && last_status == 486 ){
		if(checkStringIsNum(curActivateKeyInfo->key2)? atoi(curActivateKeyInfo->key2):0){
			pj_timer_entry_init(&call_handle_Timer2, 0, NULL,&hotKeyDialNumberTimeoutHandle_num2);
			pj_time_val delay;
			delay.sec = 3;
			delay.msec = 0; /* Give 200 ms before hangup */
			call_handle_Timer2.id = 1;
				//定时器改用pjsip内部定时器，使用外部定时器曾导致bus error错误，具体原因待调试
			pjsip_endpt_schedule_timer(pjsua_get_pjsip_endpt(),&call_handle_Timer2,&delay);
			//dialNumTimeout_tid = CreateTimer(appData.sipFeatureSettingsConfig.autoHangupRingTimeout*1000,1,func);
			NOTICE("Set user busy call Number2(%s) after %ds succeed.",curActivateKeyInfo->key2,3);
		}
	}
}


/*************** sip状态获取 ***************/
/**
 * [getSipCurStatus 获取用户状态]
 * @return [description]
 */
unsigned char getSipCurStatus()
{	
	if(st_SipCurStatus.lineCallStatus == SIP_LINE_CALLING_STATUS_CALLING){
		return SIP_STATUS_REGISTER_CALLING;
	}
	NOTICE("SIP Status registering... %d",	appData.SipUserInfo[0].registerStatus);
	if(appData.SipUserInfo[0].registerStatus == 0){
		return SIP_STATUS_REGISTER_FAILED_OTHER;
	}
	return  appData.SipUserInfo[0].registerStatus;
}

void __setSipCurStatus(unsigned char state,const char* file,int line)
//void setSipCurStatus(unsigned char state)
{

	st_SipCurStatus.lineCallStatus = state;
	NOTICE("sip cur state changed from %s:%d",file,line);

	switch(state){
		case SIP_LINE_CALLING_STATUS_IDLE:
			NotifyAlterSipStatus(SIP_LINE_CALLING_STATUS_IDLE);
			//globalPlayerStop();
			break;
		case SIP_LINE_CALLING_STATUS_RING: 
			//playLocalMusic(RING_FILE_CALLING,PLAYER_START_FLAG_LOOP);
			break;
		case SIP_LINE_CALLING_STATUS_CALLING: 
			NotifyAlterSipStatus(SIP_LINE_CALLING_STATUS_CALLING);
			//globalPlayerStop();
			break;
		case SIP_LINE_CALLING_STATUS_KEEP: 
			//globalPlayerStop();
			break;
		default:
			//globalPlayerStop();
			break;
	}
}

/**
 * [getSipInformationToArray 获取sip信息到数组中]
 * @param  buf [description]
 * @return     [description]
 */
int getSipInformationToArray(char * buf)
{
	char * pbuf = buf;
	int getSipUserIndex = 0;

	*pbuf++ =  appData.SipUserInfo[getSipUserIndex].registerSwitch;
	*pbuf++ =  appData.voiceInfoSettings.callVolume;
	*pbuf++ =  appData.voiceInfoSettings.MIC_inputLevel;

	*pbuf++ = strlen(appData.SipUserInfo[getSipUserIndex].serverAddress);
	strcpy(pbuf,appData.SipUserInfo[getSipUserIndex].serverAddress);pbuf+=strlen(appData.SipUserInfo[getSipUserIndex].serverAddress);
	*pbuf++ = (appData.SipUserInfo[getSipUserIndex].serverPort>>8)&0xFF;
	*pbuf++ = (appData.SipUserInfo[getSipUserIndex].serverPort)&0xFF;

	*pbuf++ = strlen(appData.SipUserInfo[getSipUserIndex].user);
	strcpy(pbuf,appData.SipUserInfo[getSipUserIndex].user);pbuf+=strlen(appData.SipUserInfo[getSipUserIndex].user);

	*pbuf++ = strlen(appData.SipUserInfo[getSipUserIndex].password);
	strcpy(pbuf,appData.SipUserInfo[getSipUserIndex].password);
	pbuf+=strlen(appData.SipUserInfo[getSipUserIndex].password);

	*pbuf++ =  appData.SipUserInfo[getSipUserIndex].registerProtocolType;

	return pbuf - buf;
}


/**
 * [sipSourceCheckAndStart 检测sip音源优先级并且启动音源]
 * @return [description]
 */
int sipSourceCheckAndStart(int line,pjsua_call_id call_id){
	//判断音源优先级
   	//优先级检测
	if(checkSipCallSourceIsValid()<=0){
		LOG("check Sip Call Source Is Invalid!");
		if(call_id>=0){
			NOTICE("Calling Busy...486");
			pjsua_call_answer(call_id, 486, NULL, NULL);	
		}
		return -1;
	}
	
	NOTICE("VOIP音源检测成功...");
	st_SipCurStatus.curActiveCallLine = line;
	startSipCallSource(line);//音源切换
	sem_sip_conf_send();
	return SUCCEED;
}

/**
 * [getPayloadName 获取赋值类型名称]
 * @param  buffer  [description]
 * @param  payload [description]
 * @return         [description]
 */
int getPayloadName(char * buffer,int payload)
{
	switch(payload){
		case PJMEDIA_RTP_PT_PCMU: sprintf(buffer,CODEC_PCMU); return SUCCEED;
		case PJMEDIA_RTP_PT_GSM:  sprintf(buffer,CODEC_GSM); return SUCCEED;
		case PJMEDIA_RTP_PT_PCMA: sprintf(buffer,CODEC_PCMA); return SUCCEED;
		case PJMEDIA_RTP_PT_G722: sprintf(buffer,CODEC_G722); return SUCCEED;
		default:
			WARNINGP("unknown payload is %d.",payload);
			sprintf(buffer,"unknown");
			break;
	}
	return ERROR;
}


/**
 * [readSIPConfig 读取配置文件]
 * @return [description]
 */
int readSIPConfig()
{
	int i = 0;
	//读取用户配置
	for (i=0; i < SIP_USER_MAX; ++i)
	{
		char sipHeadbuf[10]={0};
		memset(&appData.SipUserInfo[i],0,sizeof(SipUserInfo_t));
		appData.SipUserInfo[i].index = i;
		appData.SipUserInfo[i].acc_id = -1;
		sprintf(sipHeadbuf,"SIP%d",i);

		ini_gets(sipHeadbuf,"user","",appData.SipUserInfo[i].user,sizeof(appData.SipUserInfo[i].user)-1,INI_SIP_INI_FILE);
		ini_gets(sipHeadbuf,"password","",appData.SipUserInfo[i].password,sizeof(appData.SipUserInfo[i].password)-1,INI_SIP_INI_FILE);
		ini_gets(sipHeadbuf,"serverip","",appData.SipUserInfo[i].serverAddress,sizeof(appData.SipUserInfo[i].serverAddress)-1,INI_SIP_INI_FILE);
		ini_gets(sipHeadbuf,"tlhNumber","",appData.SipUserInfo[i].telephoneNumber,sizeof(appData.SipUserInfo[i].telephoneNumber)-1,INI_SIP_INI_FILE);
		ini_gets(sipHeadbuf,"displayName","",appData.SipUserInfo[i].displayName,sizeof(appData.SipUserInfo[i].displayName)-1,INI_SIP_INI_FILE);

		appData.SipUserInfo[i].serverPort=ini_getl(sipHeadbuf,"serverPort",5060,INI_SIP_INI_FILE);
		appData.SipUserInfo[i].localPort = ini_getl(sipHeadbuf,"localPort",PJSIP_DEF_CONF_SIP_LOCAL_PORT,INI_SIP_INI_FILE);
		appData.SipUserInfo[i].registerSwitch = ini_getl(sipHeadbuf,"regSwitch",0,INI_SIP_INI_FILE);//默认不开启注册
		appData.SipUserInfo[i].registerTimeoutCount = ini_getl(sipHeadbuf,"regTimeout",0,INI_SIP_INI_FILE);
		appData.SipUserInfo[i].registerProtocolType = ini_getl(sipHeadbuf,"PtlType",SIP_TRANSFER_PROTOCOL_DEF,INI_SIP_INI_FILE);

		appData.SipUserInfo[i].registerStatusPrev = 0;

		appData.SipUserInfo[i].callStatus = SIP_LINE_CALLING_STATUS_IDLE;
	}
	
	//语音配置读取
	memset(&appData.voiceInfoSettings,0,sizeof(voiceInfoSettings_t));

	appData.voiceInfoSettings.en_VAD=ini_getl("voice","VAD",VOICE_VAD_DEF,INI_SIP_INI_FILE);
	appData.voiceInfoSettings.callVolume=ini_getl("voice","callVol",VOICE_CALL_VOL_DEF,INI_SIP_INI_FILE);
	appData.voiceInfoSettings.callVolumeTemp=appData.voiceInfoSettings.callVolume;
	appData.voiceInfoSettings.MIC_inputLevel=ini_getl("voice","micLevel",VOICE_MIC_INPUT_LEVEL_DEF,INI_SIP_INI_FILE);
	appData.voiceInfoSettings.mediaVolume=ini_getl("voice","mediaVol",VOICE_MIC_MEDIA_VOL_DEF,INI_SIP_INI_FILE);

	for (i=0; i < VOICE_CODEC_LIST_MAX; ++i)
	{
		char sipHeadbuf[10]={0};
		sprintf(sipHeadbuf,"codec%d",i+1);
		int temp = ini_getl("voice",sipHeadbuf,-1,INI_SIP_INI_FILE);
		if(temp != -1){
			appData.voiceInfoSettings.voiceInfoPayload[i].payload = temp;
			if(ERROR == getPayloadName(appData.voiceInfoSettings.voiceInfoPayload[i].payloadName,temp) ){
				appData.voiceInfoSettings.voiceInfoPayload[i].valid = 0;
			}else{
				appData.voiceInfoSettings.voiceInfoPayload[i].valid = 1;
			}
		}
		else
		{
			appData.voiceInfoSettings.voiceInfoPayload[i].valid = 0;
		}
	}
	if(appData.voiceInfoSettings.voiceInfoPayload[0].valid == 0){
		//设置默认语音配置
		LOG("read voice config failure set default.");
		setVoicePrioritySettings(PJMEDIA_RTP_PT_G722,PJMEDIA_RTP_PT_PCMA,PJMEDIA_RTP_PT_PCMU,PJMEDIA_RTP_PT_GSM);
	}

	//读取功能配置信息
	appData.sipFeatureSettingsConfig.DND_EN=ini_getl("Feat","DND",0,INI_SIP_INI_FILE);
	appData.sipFeatureSettingsConfig.isBanOutgoing=ini_getl("Feat","BanOut",0,INI_SIP_INI_FILE);
	appData.sipFeatureSettingsConfig.autoAnswerLineSlt=ini_getl("Feat","autoAnsSlt",0x03,INI_SIP_INI_FILE);
	appData.sipFeatureSettingsConfig.autoAnswerTimeMs=ini_getl("Feat","autoAnsTime",200,INI_SIP_INI_FILE);
	appData.sipFeatureSettingsConfig.enableIntercomMute=ini_getl("Feat","IntercomMute",0,INI_SIP_INI_FILE);
	appData.sipFeatureSettingsConfig.enableIntercomTone=ini_getl("Feat","IntercomTone",0,INI_SIP_INI_FILE);
	appData.sipFeatureSettingsConfig.enableAutoHangupRing=ini_getl("Feat","enAutoHangupRing",0,INI_SIP_INI_FILE);
	appData.sipFeatureSettingsConfig.autoHangupRingTimeout=ini_getl("Feat","autoHangupTout",0,INI_SIP_INI_FILE);
	appData.sipFeatureSettingsConfig.enableFeatureKeyAnswer=ini_getl("Feat","FeatKeyAns",1,INI_SIP_INI_FILE);	//默认开启
	appData.sipFeatureSettingsConfig.enableSpeedDialKeyHangup=ini_getl("Feat","FeatKeyHangup",1,INI_SIP_INI_FILE);	//默认开启


	//读取pjsip库信息配置
	appData.pjsipConf.console_level=ini_getl("pjsip","console_level",PJSIP_DEF_CONF_CONSOLE_LEVEL,INI_SIP_INI_FILE);
	appData.pjsipConf.clock_rate=ini_getl("pjsip","clock_rate",PJSIP_DEF_CONF_CLOCK_RATE,INI_SIP_INI_FILE);
	appData.pjsipConf.ec_options=ini_getl("pjsip","ec_options",PJSIP_DEF_CONF_EC_OPTIONS,INI_SIP_INI_FILE);
	appData.pjsipConf.ec_tail_len=ini_getl("pjsip","ec_tail_len",PJSIP_DEF_CONF_EC_TAIL_LEN,INI_SIP_INI_FILE);
	appData.pjsipConf.media_quality=ini_getl("pjsip","media_quality",PJSIP_DEF_CONF_MEDIA_QUALITY,INI_SIP_INI_FILE);
	appData.pjsipConf.en_VAD=ini_getl("pjsip","en_VAD",PJSIP_DEF_CONF_EN_VAD,INI_SIP_INI_FILE);
	appData.pjsipConf.sipLocalPort=ini_getl("pjsip","sipLocalPort",PJSIP_DEF_CONF_SIP_LOCAL_PORT,INI_SIP_INI_FILE);

#if 0
	//测试		
	appData.SipUserInfo[0].registerSwitch =  1;
	sprintf(appData.SipUserInfo[0].user,"%s","100");
	sprintf(appData.SipUserInfo[0].password,"%s","123456");
	sprintf(appData.SipUserInfo[0].serverAddress,"%s","************");		
	appData.SipUserInfo[0].serverPort = 5060;

	appData.SipUserInfo[1].registerSwitch =  1;
	sprintf(appData.SipUserInfo[1].user,"%s","101");
	sprintf(appData.SipUserInfo[1].password,"%s","123456");
	sprintf(appData.SipUserInfo[1].serverAddress,"%s","************");		
	appData.SipUserInfo[1].serverPort = 5060;

	appData.sipFeatureSettingsConfig.autoAnswerLineSlt = 0x03; 
	appData.sipFeatureSettingsConfig.autoAnswerTimeMs = 1;

	//saveSIPConfig();
#endif
	
	return SUCCEED;
}

/**
 * [saveSIPConfig 保存SIP用户等信息]
 * @return [description]
 */
int saveSIPConfig()
{
	int i = 0;
	for (; i < SIP_USER_MAX; ++i)
	{
		char sipHeadbuf[10]={0};
		sprintf(sipHeadbuf,"SIP%d",i);

		ini_puts(sipHeadbuf,"user",appData.SipUserInfo[i].user,INI_SIP_INI_FILE);
		ini_puts(sipHeadbuf,"password",appData.SipUserInfo[i].password,INI_SIP_INI_FILE);
		ini_puts(sipHeadbuf,"serverip",appData.SipUserInfo[i].serverAddress,INI_SIP_INI_FILE);
		ini_puts(sipHeadbuf,"tlhNumber",appData.SipUserInfo[i].telephoneNumber,INI_SIP_INI_FILE);
		ini_puts(sipHeadbuf,"displayName",appData.SipUserInfo[i].displayName,INI_SIP_INI_FILE);
		
		ini_putl(sipHeadbuf,"serverPort",appData.SipUserInfo[i].serverPort,INI_SIP_INI_FILE);
		ini_putl(sipHeadbuf,"localPort",appData.SipUserInfo[i].localPort,INI_SIP_INI_FILE);
		ini_putl(sipHeadbuf,"regSwitch",appData.SipUserInfo[i].registerSwitch,INI_SIP_INI_FILE);
		ini_putl(sipHeadbuf,"regTimeout",appData.SipUserInfo[i].registerTimeoutCount,INI_SIP_INI_FILE);
		ini_putl(sipHeadbuf,"PtlType",appData.SipUserInfo[i].registerProtocolType,INI_SIP_INI_FILE);
	}


	//保存语音配置
	ini_putl("voice","VAD",appData.voiceInfoSettings.en_VAD,INI_SIP_INI_FILE);
	ini_putl("voice","callVol",appData.voiceInfoSettings.callVolume,INI_SIP_INI_FILE);
	ini_putl("voice","micLevel",appData.voiceInfoSettings.MIC_inputLevel,INI_SIP_INI_FILE);
	ini_putl("voice","mediaVol",appData.voiceInfoSettings.mediaVolume,INI_SIP_INI_FILE);
	
	ini_putl("voice","codec1",appData.voiceInfoSettings.voiceInfoPayload[0].payload,INI_SIP_INI_FILE);
	ini_putl("voice","codec2",appData.voiceInfoSettings.voiceInfoPayload[1].payload,INI_SIP_INI_FILE);
	ini_putl("voice","codec3",appData.voiceInfoSettings.voiceInfoPayload[2].payload,INI_SIP_INI_FILE);
	ini_putl("voice","codec4",appData.voiceInfoSettings.voiceInfoPayload[3].payload,INI_SIP_INI_FILE);

	
	//保存功能信息
	ini_putl("Feat","DND",appData.sipFeatureSettingsConfig.DND_EN,INI_SIP_INI_FILE);
	ini_putl("Feat","BanOut",appData.sipFeatureSettingsConfig.isBanOutgoing,INI_SIP_INI_FILE);
	ini_putl("Feat","autoAnsSlt",appData.sipFeatureSettingsConfig.autoAnswerLineSlt,INI_SIP_INI_FILE);
	ini_putl("Feat","autoAnsTime",appData.sipFeatureSettingsConfig.autoAnswerTimeMs,INI_SIP_INI_FILE);
	ini_putl("Feat","IntercomMute",appData.sipFeatureSettingsConfig.enableIntercomMute,INI_SIP_INI_FILE);
	ini_putl("Feat","IntercomTone",appData.sipFeatureSettingsConfig.enableIntercomTone,INI_SIP_INI_FILE);
	ini_putl("Feat","enAutoHangupRing",appData.sipFeatureSettingsConfig.enableAutoHangupRing,INI_SIP_INI_FILE);
	ini_putl("Feat","autoHangupTout",appData.sipFeatureSettingsConfig.autoHangupRingTimeout,INI_SIP_INI_FILE);
	ini_putl("Feat","FeatKeyAns",appData.sipFeatureSettingsConfig.enableFeatureKeyAnswer,INI_SIP_INI_FILE);
	ini_putl("Feat","FeatKeyHangup",appData.sipFeatureSettingsConfig.enableSpeedDialKeyHangup,INI_SIP_INI_FILE);

	
	return SUCCEED;
}



/**
 * [setVoicePrioritySettings 语音编码设置]
 * @param  codec1 [description]
 * @param  codec2 [description]
 * @param  codec3 [description]
 * @param  codec4 [description]
 * @return        [description]
 */
int setVoicePrioritySettings(unsigned char codec1,unsigned char codec2,unsigned char codec3,unsigned char codec4){
	int pos=0;
	appData.voiceInfoSettings.voiceInfoPayload[pos].payload = codec1;
	if(ERROR !=  getPayloadName(appData.voiceInfoSettings.voiceInfoPayload[pos].payloadName,codec1) ){
		appData.voiceInfoSettings.voiceInfoPayload[pos++].valid = 1;
	}else{
		appData.voiceInfoSettings.voiceInfoPayload[pos++].valid = 0;
	}
	

	appData.voiceInfoSettings.voiceInfoPayload[pos].payload = codec2;
	if(ERROR !=  getPayloadName(appData.voiceInfoSettings.voiceInfoPayload[pos].payloadName,codec2) ){
		appData.voiceInfoSettings.voiceInfoPayload[pos++].valid = 1;
	}else{
		appData.voiceInfoSettings.voiceInfoPayload[pos++].valid = 0;
	}

	appData.voiceInfoSettings.voiceInfoPayload[pos].payload = codec3;
	if(ERROR !=  getPayloadName(appData.voiceInfoSettings.voiceInfoPayload[pos].payloadName,codec3) ){
		appData.voiceInfoSettings.voiceInfoPayload[pos++].valid = 1;
	}else{
		appData.voiceInfoSettings.voiceInfoPayload[pos++].valid = 0;
	}

	appData.voiceInfoSettings.voiceInfoPayload[pos].payload = codec4;
	if(ERROR !=  getPayloadName(appData.voiceInfoSettings.voiceInfoPayload[pos].payloadName,codec4) ){
		appData.voiceInfoSettings.voiceInfoPayload[pos++].valid = 1;
	}else{
		appData.voiceInfoSettings.voiceInfoPayload[pos++].valid = 0;
	}

	
	return SUCCEED;
}

int initPjsuaDefIpAccount()
{
	sprintf(ipCallUser.user,"888");
	sprintf(ipCallUser.password,"888");
	sprintf(ipCallUser.serverAddress,"************");
	ipCallUser.serverPort = 5065;
	if(pjsuaIpAccAdd(&ipCallUser,0) == ERROR){
		ERRORP("pjsua add acc failed!");
		return ERROR;
	}
	ipCallUser.index = 0;
	//pjsua_acc_set_online_status(ipCallUser.acc_id,0);
	//pjsua_acc_set_online_status  (pjsua_acc_id  acc_id,  pj_bool_t 
	return SUCCEED;
}


/**
 * [getUserTransportType 传输协议]
 * @param  userID [description]
 * @return        [description]
 */
pjsip_transport_type_e getUserTransportType(int userID)
{
	if(userID>=SIP_USER_MAX)return PJSIP_TRANSPORT_UDP;
	switch(appData.SipUserInfo[userID].registerProtocolType){

		case SIP_TRANSFER_PROTOCOL_UDP: NOTICE("Cur User Transfer Protocol UDP."); return PJSIP_TRANSPORT_UDP;
		case SIP_TRANSFER_PROTOCOL_TCP: NOTICE("Cur User Transfer Protocol TCP."); return PJSIP_TRANSPORT_TCP;
		case SIP_TRANSFER_PROTOCOL_TLS: NOTICE("Cur User Transfer Protocol TLS."); return PJSIP_TRANSPORT_TLS;
	}
	return PJSIP_TRANSPORT_UDP;
}

/**
 * [getSipUserInfo description]
 * @param  acc_id [description]
 * @return        [description]
 */
SipUserInfo_t * getSipUserInfo(pjsua_acc_id acc_id)
{
	int i = 0;	
	for (; i < SIP_USER_MAX; ++i)
	{
		if(appData.SipUserInfo[i].acc_id == acc_id){
			return &appData.SipUserInfo[i];
		}
	}
	if(acc_id == ipCallUser.acc_id){
		return &ipCallUser;
	}
	return NULL;
}




/**
 * [assembleSipURI sip连接组成]
 * @param  buffer [description]
 * @param  bsize  [description]
 * @param  user   [description]
 * @param  host   [description]
 * @param  port   [description]
 * @return        [description]
 */
int assembleSipURI(char *buffer,int bsize,const char* user,const char *host,int port){
	memset(buffer, 0,bsize);
	if(user==NULL){
		snprintf(buffer,bsize,"sip:%s:%d",host,port);
	}else{
		snprintf(buffer,bsize,"sip:%s@%s:%d",user,host,port);
	}
	return SUCCEED;
	
}


/**
 * [pjsuaIpAccAdd pjsua 添加IP呼叫用户]
 * @param  user   [description]
 * @param  acc_id [description]
 * @return        [description]
 */
int pjsuaIpAccAdd(SipUserInfo_t *user,pj_bool_t enReg)
{
	pj_status_t status;
	pjsua_acc_id acc_id;
	pjsua_acc_config cfg;
	char buffer[256]={0};
	pjsua_acc_config_default(&cfg);
	assembleSipURI(buffer,256,user->user,user->serverAddress,user->serverPort);
	NOTICE("assembleSipURI :%s",buffer);
	cfg.id = pj_str(buffer);
	//assembleSipURI(buffer,256,NULL,appData.SipUserInfo[i].serverAddress,appData.SipUserInfo[i].serverPort);
	
	cfg.reg_uri = pj_str(buffer);
	cfg.cred_count ++;
	cfg.cred_info[0].realm = pj_str("*");
	cfg.cred_info[0].scheme = pj_str(user->user);
	cfg.cred_info[0].username = pj_str(user->user);
	cfg.cred_info[0].data_type = 0;
	cfg.cred_info[0].data = pj_str(user->password);
	cfg.rtp_cfg.port = 4000;
	cfg.register_on_acc_add = enReg;
	cfg.rtp_cfg.port_range = 500; //rtp port: 5000 ~ 5500;
	NOTICE("pjsua Account Add:");
	NOTICE("server Address:%s",user->serverAddress);
	NOTICE("server Port:%d",user->serverPort);
	NOTICE("userName:%s",user->user);
	NOTICE("password:%s",user->password);

	status = pjsua_acc_add(&cfg, PJ_TRUE, &acc_id);
	if (status != PJ_SUCCESS){ ERRORP("Error adding account");	return ERROR;}
	else user->acc_id = acc_id;

	return SUCCEED;
}

/*
 *  创建SIP账号，注册到服务器
 *
 *  @param acc_id
 */
void PJSIP_AccountConfig()
{
	printf("账号配置...\n");
	for ( int i = 0; i < SIP_USER_MAX; ++i)
	{
		pjsuaAccAdd(&appData.SipUserInfo[i]);
	}
}

#define PJSUA_MAX_TRANSPORTS 100
pjsua_transport_id find_existing_transport(pjsip_transport_type_e type, pj_uint16_t port) {
    pjsua_transport_id transport_ids[PJSUA_MAX_TRANSPORTS];
    unsigned transport_count = PJSUA_MAX_TRANSPORTS;
    pjsua_transport_info transport_info;
    
    // 获取所有已创建的传输ID
    pj_status_t status = pjsua_enum_transports(transport_ids, &transport_count);
    if (status != PJ_SUCCESS) {
        printf("Failed to enumerate transports\n");
        return -1;  // 返回 -1 表示出错
    }

    // 遍历所有传输
    for (unsigned i = 0; i < transport_count; i++) {
        // 获取传输信息
        status = pjsua_transport_get_info(transport_ids[i], &transport_info);
        if (status != PJ_SUCCESS) {
            printf("Failed to get transport info for transport ID %d\n", transport_ids[i]);
            continue;  // 跳过错误，继续下一个传输
        }

        // 检查传输类型和端口
        if (transport_info.type == type && transport_info.local_name.port == port) {
            printf("Found transport with same type and port: %d\n", port);
            return transport_ids[i];  // 找到相同传输，返回 transport_id
        }
    }

    // 没有找到相同的传输，返回 -1
    return -1;
}

pjsua_transport_id create_transport_if_needed(pjsip_transport_type_e type, pj_uint16_t port) {
    // 检查是否已存在相同的传输
    pjsua_transport_id existing_transport_id = find_existing_transport(type, port);
    if (existing_transport_id >= 0) {
        printf("Transport already exists with ID: %d, no need to create.\n", existing_transport_id);
		return existing_transport_id;
    } else {
        // 未找到相同的传输，创建新传输
        pjsua_transport_config cfg;
        pjsua_transport_config_default(&cfg);
        cfg.port = port;
        
        pjsua_transport_id transport_id;
        pj_status_t status = pjsua_transport_create(type, &cfg, &transport_id);
        if (status == PJ_SUCCESS) {
            printf("New transport created with ID: %d on port %d\n", transport_id, port);
			return  transport_id;
        } else {
            printf("Failed to create new transport\n");
			return  -1;
        }
    }
}


/**
 * [pjsuaAccAdd description]
 * @param  user [description]
 * @return      [description]
 */
int pjsuaAccAdd(SipUserInfo_t *user)
{	
	pj_status_t status;
	pjsua_acc_config cfg;
	pjsua_acc_id acc_id = -1;

	pjsua_acc_config_default(&cfg);
	char buffer[256]={0};

	if(user->registerSwitch){
		NOTICE("Register sip user %s...",user->user);
		assembleSipURI(buffer,256,user->user,user->serverAddress,user->serverPort);
		cfg.id = pj_str(buffer);
		//assembleSipURI(buffer,256,NULL,user->serverAddress,user->serverPort);
		NOTICE("assembleSipURI :%s",buffer);
		cfg.reg_uri = pj_str(buffer);
		cfg.cred_count ++;
		cfg.cred_info[user->index].realm = pj_str("*");
		cfg.cred_info[user->index].scheme = pj_str(user->user);
		cfg.cred_info[user->index].username = pj_str(user->user);
		cfg.cred_info[user->index].data_type = 0;
		cfg.cred_info[user->index].data = pj_str(user->password);

		// 配置注册失败后的重试间隔时间（单位：秒）
		cfg.reg_retry_interval = 60;  // 默认300秒(5分钟)

#if 0
		pjsua_transport_info info;
		// 检查传输信息
		if (sip_transport_id[user->index]>=0) {
			printf("pjsua_transport_get_info is valid,need close!\n");
			pjsua_transport_close(sip_transport_id[user->index],PJ_FALSE);
			sip_transport_id[user->index]=-1;
			//pj_thread_sleep(100);  // 等待500毫秒
		}
		pjsua_transport_config transport_cfg;
		pjsua_transport_config_default(&transport_cfg);  // 初始化 transport_cfg
		transport_cfg.port=user->serverPort;
		status = pjsua_transport_create(getUserTransportType(user->index), &transport_cfg, &sip_transport_id[user->index]);
#endif

#if 0
		pjsua_transport_id new_transport_id;
		pjsua_transport_config transport_cfg;
		pjsua_transport_config_default(&transport_cfg);
		transport_cfg.port = user->serverPort;

		// 尝试创建新的传输
		status = pjsua_transport_create(getUserTransportType(user->index), &transport_cfg, &new_transport_id);
		if (status == PJ_SUCCESS) {
			// 新传输创建成功，关闭旧传输
			if (sip_transport_id[user->index] >= 0) {
				printf("Closing existing transport...\n");
				//pjsua_transport_close(sip_transport_id[user->index], PJ_FALSE);
				sip_transport_id[user->index] = -1;
			}
			// 更新当前传输ID
			sip_transport_id[user->index] = new_transport_id;
			pj_thread_sleep(500);
		} else {
			printf("Failed to create new transport: %d\n", status);
		}

		//设置传输协议
		cfg.transport_id = sip_transport_id[user->index];
#endif

		cfg.transport_id = create_transport_if_needed(getUserTransportType(user->index),user->serverPort);


		//NOTICE("User Infomation:User:%s Passwd:%s",user->user,user->password);
		//onVoipRegStatusChanged(user->index+1,SIP_STATUS_REGISTER_LOADING);
		
		//解决注册成功后，网页重新设置后导致led闪烁问题
		user->registerStatus = SIP_STATUS_REGISTER_LOADING;
		user->registerStatusPrev = user->registerStatus;
		onSIPStatusChanged(SIP_STATUS_TYPE_REG,user->index+1,SIP_STATUS_REGISTER_LOADING);
		status = pjsua_acc_add(&cfg, PJ_TRUE, &acc_id);
		if (status != PJ_SUCCESS){ ERRORP("Error adding account");	}
		else {
			user->acc_id = acc_id;
			#if 0
			status = pjsua_acc_set_transport(acc_id, sip_transport_id[user->index]);
			if (status != PJ_SUCCESS) {
				// 处理错误
				printf("pjsua_acc_set_transport error!\n");
			}
			#endif
		}
	}else{	
		//closeSipLineLed(user->index+1);
	}
}


/**
 * [userInfoCopy 用户信息复制]
 * @param  old [description]
 * @param  new [description]
 * @return     [description]
 */
int userInfoCopy(SipUserInfo_t * old,SipUserInfo_t * new)
{
	if (!old || !new)
	{
		return ERROR;
	}
	
	memcpy(new,old,sizeof(SipUserInfo_t));
	NOTICE("User Infomation Copy Done!");
/*
	new->index = old->index;								//用户索引
	new->registerSwitch = old->registerSwitch; 						//注册开关
	new->registerProtocolType  = old->registerProtocolType; 				//注册协议类型
	new->re_registrationFlag = old->re_registrationFlag; 					//主机设置信息后重
	new->telephoneNumber[SIP_USER_NAME_LEN_MAX]; //电话号码
	new->user[SIP_USER_NAME_LEN_MAX];			//用户名称
	new->password[SIP_PASSWORD_LEN_MAX]; 		//用户密码
	new->serverAddress[SERVER_ADDRESS_LEN_MAX]; //注册服务器地址
	//new->registerStatus;						//用户注册状态
	new->registerTimeoutCount = old->;					//用户注册超时计数
	new->serverPort = old->serverPort; 							//端口号
	new->localPort = old->localPort; 							//本地sip通讯端口号
	new->ProxyServerInfo = old->ProxyServerInfo; 				//代理服务器信息
	new->localDomain[SIP_USER_NAME_LEN_MAX];	//本地域名
	new->serverName[SIP_USER_NAME_LEN_MAX]; 	//服务器名称
*/

}


static SipUserInfo_t * findAccountByIndex(unsigned char index)
{
	int i =0;
	for ( i = 0; i < SIP_USER_MAX; ++i)
	{
		if(appData.SipUserInfo[i].index == index ){
			return &appData.SipUserInfo[i];
		}
	}
	return NULL;
}



/**
 * [AccountInfoModify 用户信息修改]
 * @param  user [description]
 * @return      [description]
 */
int AccountInfoModify(SipUserInfo_t *user)
{
	if (user->index >= SIP_USER_MAX){	
		return ERROR;
	}
	NOTICE("Account(%s) Infomation Modify!",user->user);

	SipUserInfo_t *oldUser;
	oldUser = findAccountByIndex(user->index);
	if (oldUser == NULL){
		//未找到用户则为新用户
		userInfoCopy( user,&appData.SipUserInfo[user->index] );
		pjsuaAccAdd(&appData.SipUserInfo[user->index]);
		saveSIPConfig();
		return SUCCEED;
	}
	if(pjsua_acc_is_valid(oldUser->acc_id)){
		//先删除用户
		NOTICE("Delete User ID: %d",oldUser->acc_id);
		pjsua_acc_del(oldUser->acc_id);//注销用户
	}

	userInfoCopy( user,oldUser );
	pjsuaAccAdd(&appData.SipUserInfo[user->index]);
	saveSIPConfig();

	LOG("Account Modify Succeed Acc Count %d.",pjsua_acc_get_count());

	return SUCCEED;
}

/**
 * [HostModifyAccountInfo 主机修改sip用户配置]
 * @param  serverip [description]
 * @param  port     [description]
 * @param  user     [description]
 * @param  password [description]
 * @return          [description]
 */
int HostModifyAccountInfo(char isEnable,int outputVolume,char *serverip,int port,char * user, char * password, char transProtocol)
{

	SipUserInfo_t SipUserInfo;
	PJ_THREAD_REGISTER_MACRO(HostModifyAccountInfo);
	userInfoCopy(&appData.SipUserInfo[0] ,&SipUserInfo); //取出默认值

	if(strcmp(SipUserInfo.serverAddress,serverip) ||\
		strcmp(SipUserInfo.user,user) ||\
		strcmp(SipUserInfo.password,password) ||\
		SipUserInfo.serverPort != port ||\
		SipUserInfo.registerSwitch != isEnable ||\
		SipUserInfo.registerProtocolType != transProtocol
	)
	{
		snprintf(SipUserInfo.user,SIP_USER_NAME_LEN_MAX,"%s",user);
		snprintf(SipUserInfo.password,SIP_PASSWORD_LEN_MAX,"%s",password);
		snprintf(SipUserInfo.serverAddress,SERVER_ADDRESS_LEN_MAX,"%s",serverip);
		SipUserInfo.registerSwitch =  isEnable;
		if(outputVolume!=255)
		{
			appData.voiceInfoSettings.callVolume = outputVolume;
		}
		SipUserInfo.serverPort =  port;
		SipUserInfo.registerProtocolType = transProtocol;
		if(ERROR ==  AccountInfoModify(&SipUserInfo) ){
			appData.SipUserInfo[0].registerStatus = SIP_STATUS_REGISTER_FAILED_OTHER;
		}else{
			if(!SipUserInfo.registerSwitch)
			{
				appData.SipUserInfo[0].registerStatus = SIP_STATUS_REGISTER_FAILED_OTHER; //修改后状态为正在注册
			}
		}
		NotifyAlterSipStatus(appData.SipUserInfo[0].registerStatus);
	}
	else
	{
		if(appData.voiceInfoSettings.callVolume != outputVolume)
		{
			if(outputVolume!=255)
			{
				appData.voiceInfoSettings.callVolume = outputVolume;
				saveSIPConfig();
			}
		}
	}
	return 0;
}



/**
 * [InitVoiceSettings 语音编码设置]
 */
void InitVoiceSettings()
{
	pj_str_t id;
		//语音编码设置
	int i=0;
	pj_status_t status;
	NOTICE("Init Voice Settings...");
	for (i = 0; i < VOICE_CODEC_LIST_MAX; ++i)
	{

		if(strlen(appData.voiceInfoSettings.voiceInfoPayload[i].payloadName)!=0
			&& appData.voiceInfoSettings.voiceInfoPayload[i].valid){
			NOTICE("初始化语音编码%d %s",i+1,appData.voiceInfoSettings.voiceInfoPayload[i].payloadName);
			status = pjsua_codec_set_priority(pj_cstr(&id,appData.voiceInfoSettings.voiceInfoPayload[i].payloadName)
			, (pj_uint8_t)(190) - 10*i );// ,(pj_uint8_t)(PJMEDIA_CODEC_PRIO_NORMAL+i+9)	
			if(status != PJ_SUCCESS){
				WARNINGP("---------------------------- init voice codec %s failure",appData.voiceInfoSettings.voiceInfoPayload[i].payloadName);
			}
		}
	}
}

#if 0
/**
 * [reloadVoiceSettingsConfig 重新加载语音配置]
 * @return [description]
 */
int reloadVoiceSettingsConfig()
{
	struct pjsua_data* pjsua_varTemp =  NULL;
	PJ_THREAD_REGISTER_MACRO(reloadVoiceSettingsConfig);
	NOTICE("reload Voice Settings Config...");
	configHandleInit();
	configHandleFree();
	NOTICE("read voice config done...");
	//pjsuaConfigReload();	
	pjsua_varTemp =  (struct pjsua_data*)pjsua_get_var();
	//pjsua_varTemp.media_cfg.clock_rate = 8000;
	//pjsua_varTemp.media_cfg.quality = 4;		//音质
	//pjsua_varTemp.media_cfg.ec_options = 3;//3 web rtc
	//pjsua_varTemp.media_cfg.ec_tail_len = 25;
	////media_cfg.no_vad = 0;		//噪音检测 0:开启噪音检测 1：关闭噪音检测
	if(pjsua_varTemp){
		NOTICE("pjsua_varTemp->media_cfg.no_vad=%d",pjsua_varTemp->media_cfg.no_vad);
		NOTICE("Set Media no_vad %s",appData.voiceInfoSettings.en_VAD?"NO":"YES");
		pjsua_varTemp->media_cfg.no_vad = appData.voiceInfoSettings.en_VAD?0:1;		//噪音检测
	}else{
		WARNINGP("pjsua_var ptr is NULL!");
	}
	NOTICE("InitVoiceSettings....")
	InitVoiceSettings();
	return SUCCEED;

}
#endif

/**
 * [findAccountAutoAnswerFlagFormAccID description]
 * @param  acc_id [description]
 * @return        [返回值小于0 不开启自动应答，大于等于0则开启自动应答]
 */
int findAccountAutoAnswerFlagFormAccID(pjsua_acc_id acc_id)
{
	int i=0;
	for (i = 0; i < SIP_USER_MAX; ++i)
	{
		if(appData.SipUserInfo[i].acc_id == acc_id ){
			//检测是否开启了自动应答
			if(appData.sipFeatureSettingsConfig.autoAnswerLineSlt == (i+1)){
				return appData.sipFeatureSettingsConfig.autoAnswerTimeMs; //返回自动应答时间
			}else if(appData.sipFeatureSettingsConfig.autoAnswerLineSlt >= 0x03)//线路开启自动应答
			{
				return appData.sipFeatureSettingsConfig.autoAnswerTimeMs; //返回自动应答时间
			}
			return -1;
		}
	}
	if(acc_id == ipCallUser.acc_id && appData.sipFeatureSettingsConfig.autoAnswerLineSlt == 0x04){
		return appData.sipFeatureSettingsConfig.autoAnswerTimeMs; //返回自动应答时间
	}
	return -1;
}


/**
 * [ResettingSipCurStateFlag 复位当前sip状态]
 */
void ResettingSipCurStateFlag()
{
	NOTICE("Resetting Sip Cur State Flag...");
	//st_SipCurStatus.lineCallStatus = SIP_LINE_CALLING_STATUS_IDLE;
	setSipCurStatus(SIP_LINE_CALLING_STATUS_IDLE);
	st_SipCurStatus.call_id = -1;
	st_SipCurStatus.curActiveCallLine = 0;
}

/**
 * [answerCurCall 接听当前呼叫电话]
 */
int answerCurCall()
{
	PJ_THREAD_REGISTER_MACRO(answerCurCall);
	if(!checkCurActiveLineIsRing()){
		//如果当前呼叫不为响铃则不处理
		ERRORP("Cur Sip Call Status Is Not Ring!");
		return -1;
	}
	if(st_SipCurStatus.call_id < 0){
		//如果当前呼叫ID无效则不处理
		ERRORP("Cur Sip Call Id Invalid!");
		return -1;
	}
	LOG("answer Current Call id %d.",st_SipCurStatus.call_id);
	//应答200
	printf("\n[%s] 应答主机 \n",__FUNCTION__);
	NotifyAlterSipStatus(SIP_STATUS_REGISTER_CALLING);
	pjsua_call_answer(st_SipCurStatus.call_id, 200, NULL, NULL);

	return 1;
}


//pjsua_call_hangup_all();//挂断所有通话
pj_status_t pjcall_thread_register(const char * str)  
{  
    if(!pjsip_init_is_finish){
    	 return -1;
    }
    pj_thread_desc  desc;  
    pj_thread_t*    thread = 0;  
    
    if (!pj_thread_is_registered())
    {
    	printf("\n\t*******pj_thread_register #%s\n\n",str);
        return pj_thread_register(NULL, desc, &thread);  
    }  
  
    return PJ_SUCCESS;
}

/**
 * [HangupAllCalling 挂断当前通话]
 */
void HangupAllCalling(pjsip_status_code	last_status)
{
	NOTICE("2. Hangup All Calling...last_status %d\n ",last_status);
	if(dialNumTimeout_tid>0){
		LOG("DeleteTimer dialNumTimeout tid:%d!",dialNumTimeout_tid);
		DeleteTimer(dialNumTimeout_tid);
		dialNumTimeout_tid  = -1;
	}

	if(AutoAnswerTid>0){
		LOG("DeleteTimer AutoAnswer tid:%d!",AutoAnswerTid);
		DeleteTimer(AutoAnswerTid);
		AutoAnswerTid  = -1;
	}

	if(AutoHangupRingTimeoutTid>0){
		LOG("DeleteTimer AutoHangupRingTimeout tid:%d!",AutoHangupRingTimeoutTid);
		DeleteTimer(AutoHangupRingTimeoutTid);
		AutoHangupRingTimeoutTid  = -1;
	}
	stopAutoAnswerTimer();
	stopCallHandleTimer(last_status);
	curHotKeyLineNum2Temp=-1;
	//curActivateKeyInfo = NULL;
	curHotKeySltLine=-1;
	curHotKeyCallType=-1;
	//pjcall_thread_register();
	PJ_THREAD_REGISTER_MACRO(HangupAllCalling);
	NOTICE("pjsua_call_hangup_all...");
	//清除所有状态
	
	ResettingSipCurStateFlag();	
	cleanSipConversationUser();
	pjsua_call_hangup_all();//挂断所有通话

	//停止视频
	//destroy_video_call_media(0);
	
	/*2019年3月7日 09:50:40新增管理软件挂断后的状态 */
	NotifyAlterSipStatus(SIP_STATUS_REGISTER_SUCCEED);
	LOG("Hangup All Calling Done...");	
}

/**
 * [HangupAllCallingStop 外部调用 停止呼叫]
 * @return [description]
 */
int HangupAllCallingStop()
{
	NOTICE("1. Hangup All Calling Stop...");
	//setAllVoipUserStatusChange(SIP_LINE_CALLING_STATUS_IDLE);
	HangupAllCalling(pjsipLastCode);
	return SUCCEED;
}

#if 0
//线程注册
/**
 * [pjcall_thread_register 
 * 如果执行pjsua_call_make_call的线程没有在pjsip中注册过，就会assert中断，提示未知线程，需要使用pj_thread_register注册才可以]
 * @return  [description]
 */
pj_status_t pjcall_thread_register(void)  
{  
	static pj_thread_desc  desc;  
	pj_thread_t*    thread = 0;  
 	//int i = 0;	
	//for ( ;i < 10; ++i)
 	//{
 	//	printf("desc[i]=%d\n",desc[i] ); 		
 	//}

	if (!pj_thread_is_registered())  
	{  
		WARNINGP("pj_thread_register正在注册线程...");
		return pj_thread_register(NULL, desc, &thread);  
	}

	WARNINGP("pj_thread_is_registered...");
	return PJ_SUCCESS;  

}
#endif



#if 0
/**
 * [pjsuaDialNumber 拨打号码]
 * @param  user    [需要使用的账号拨号]
 * @param  dialNum [需要拨打的号码]
 * @param  func    [拨号超时函数设置]
 * @return         [description]
 */
int pjsuaDialNumber(SipUserInfo_t * user,int dialNum,dialNumberTimeoutFunc_ptr func)
{
	if(!user){return -1;}
	if(user->registerStatus != SIP_STATUS_REGISTER_SUCCEED){
		WARNINGP("dial number but the user not register to serves!");
		return -1;
	}
	char buffer[50]={0};
	char numStr[10]={0};
	snprintf(numStr,10,"%d",dialNum);
	//pjcall_thread_register();
	/*
	if(!thread_registered && !pj_thread_is_registered())
    {
        if (pj_thread_register(NULL,desc,&pthread) == PJ_SUCCESS)
        {
            thread_registered = PJ_TRUE;
        }
    }
*/
	//优先级检测
	if(sipSourceCheckAndStart(-1) < 0 ) return -1;

	assembleSipURI(buffer,50,numStr,user->serverAddress,user->serverPort);
	NOTICE("assembleSipURI :%s",buffer);
	pj_str_t uri = pj_str(buffer);
	pj_status_t status;
	status = pjsua_call_make_call(user->acc_id, &uri, 0, NULL, NULL, NULL);
	if (status != PJ_SUCCESS) {
		ERRORP("Error making call (%s)",buffer);
		return -1;
	}else{
		//超时检测线程
		//呼叫成功需要设置超时检测线程响铃超时后自动挂断或者进行下一个拨号
		if(func && appData.sipFeatureSettingsConfig.autoHangupRingTimeout)//响铃超时处理
		{
			//记录当前定时点id，在接通电话后删除该定时点
			dialNumTimeout_tid = CreateTimer(appData.sipFeatureSettingsConfig.autoHangupRingTimeout*1000,1,func);
			NOTICE("Set dial timeout %ds succeed.",appData.sipFeatureSettingsConfig.autoHangupRingTimeout);
		}
		NOTICE("From user(%s) calling %d@%s...",user->user,dialNum,user->serverAddress);
	}
	return SUCCEED;
}

#endif


/**
 * [pjsuaDialNumber 拨打号码]
 * @param  user    [需要使用的账号拨号]
 * @param  dialNum [需要拨打的号码]
 * @param  call_type [拨号类型]
 * @param  func    [拨号超时函数设置]
 * @return         [description]
 */
int pjsuaDialNumber(SipUserInfo_t * user,const char * callNumOrIP,int call_type)
{
	int isIPCall=0;
	char buffer[50]={0};
	if(user == NULL){
		return -1;
	}
	if(checkStringIsNum(callNumOrIP) && strlen(callNumOrIP) > 1){		
	}else if(strstr(callNumOrIP,".")){
		isIPCall=1;	
	}
	if(isIPCall==0){
		//不为空时则需要判断用户是否注册
		if(user->registerStatus != SIP_STATUS_REGISTER_SUCCEED){
			ERRORP("dial number but the user not register to serves!");
			return -1;
		}
	}

	//检测用户acc_id是否有效
	if(!pjsua_acc_is_valid(user->acc_id)){
		if(!isIPCall){
			ERRORP("user index(%d) acc id is invalid!",user->index);		
			return -1;
		}else{
			WARNINGP("IP Call User index(%d) acc id is invalid used def acc!");
			user = &ipCallUser;
		}
	
	}
	
	//优先级检测
	if(sipSourceCheckAndStart(user->index+1,-1) < 0 ) {
		WARNINGP("Sip source start check failure!");
		return -1;
	}
	st_SipCurStatus.curActiveCallLine = user->index+1;
	if(!isIPCall){
		assembleSipURI(buffer,50,callNumOrIP,(const char*)user->serverAddress,user->serverPort);
	}else{
		//用户信息为空时默认使用ip拨号
		snprintf(buffer,50,"sip:%s",callNumOrIP);		
		//assembleSipURI(buffer,50,NULL,callNumOrIP,5060);
	}
	
	NOTICE("assembleSipURI :%s",buffer);
	pj_str_t uri = pj_str(buffer);
	pj_status_t status;
   // msg_data.content_type = pj_str("application/dtmf-relay");
    //msg_data.content_type = pj_str("application/sdp");
    //pj_ansi_snprintf(body,sizeof(body),"Call-Info: answer-after=%d",0);
    //msg_data.msg_body = pj_str(body);
	pjsua_msg_data msg_data;
	if(call_type == FUNCTION_SUB_HOT_INTERCOM){	
		pjsua_msg_data_init(&msg_data);
		pj_str_t call_Info_hdr;
		call_Info_hdr = pj_str("Call-Info");
		pj_str_t call_Info_val;
		call_Info_val = pj_str("<uri>;answer-after=0");		
		pjsip_generic_string_hdr *hdr = pjsip_call_info_hdr_create(pjsua_var.pool,&call_Info_hdr,&call_Info_val);
		pj_list_push_back(&msg_data.hdr_list, hdr);

		call_Info_hdr = pj_str("Alert-Info");
		call_Info_val = pj_str("Ring Answer");
		hdr = pjsip_call_info_hdr_create(pjsua_var.pool,&call_Info_hdr,&call_Info_val);	
		pj_list_push_back(&msg_data.hdr_list, hdr);	

		call_Info_hdr = pj_str("Remote-Party-ID");
		char str[128]={0};
		sprintf(str,"\"DSP9321 对讲面板\" <%s>;party=calling;privacy=off;screen=no",buffer);
		call_Info_val = pj_str(str);
		hdr = pjsip_call_info_hdr_create(pjsua_var.pool,&call_Info_hdr,&call_Info_val);	
		pj_list_push_back(&msg_data.hdr_list, hdr);	

		//Allow-Events: talk
		call_Info_hdr = pj_str("Allow-Events");
		call_Info_val = pj_str("talk");
		hdr = pjsip_call_info_hdr_create(pjsua_var.pool,&call_Info_hdr,&call_Info_val);	
		pj_list_push_back(&msg_data.hdr_list, hdr);	

		//Remote-Party-ID: "I12N 对讲面板" <sip:809@**************>;party=calling;privacy=off;screen=no
		/* Otherwise connect to sound device 连接本机音频设备 -- 对讲模式静音*/
		if(appData.sipFeatureSettingsConfig.enableIntercomMute){
			//对讲时外部线路静音
			//DisableSGM8904();
			
		}
	}else{
		pjsua_msg_data_init(&msg_data);
		pj_str_t call_Info_hdr;
		pj_str_t call_Info_val;
		call_Info_hdr = pj_str("Remote-Party-ID");
		char str[128]={0};
		sprintf(str,"\"DSP9321 对讲面板\" <%s>;party=calling;privacy=off;screen=no",buffer);
		call_Info_val = pj_str(str);
		pjsip_generic_string_hdr *hdr = pjsip_call_info_hdr_create(pjsua_var.pool,&call_Info_hdr,&call_Info_val);	
		pj_list_push_back(&msg_data.hdr_list, hdr);	
	}	


	status = pjsua_call_make_call(user->acc_id, &uri, 0, NULL,&msg_data, NULL);
	//status = pjsua_call_make_call(user->acc_id, &uri, 0, NULL, call_type == FUNCTION_SUB_HOT_INTERCOM ?&msg_data:NULL, NULL);
	if (status != PJ_SUCCESS) {
		ERRORP("Error making call (%s)",buffer);
		stopSipCallSource(user->index+1);//停止sip呼叫
		return -1;
	}else{	
		if(user){
			LOG("From user(%s) calling %s@%s...",user->user,callNumOrIP,user->serverAddress);
		}else{
			LOG("calling IP %s...",buffer);
		}
	}
	setDialNumberFlag(1);
	setSipCallUserInfo(buffer);
	return SUCCEED;
}


/**
 * [dialNumTimeoutHandle 拨号超时处理]
 * @param tid [description]
 * 
 */
void dialNumTimeoutHandle2(int tid)
{
	NOTICE("Dial Timeout ...");
	DeleteTimer(dialNumTimeout_tid);
	PJ_THREAD_REGISTER_MACRO(dialNumTimeoutHandle);
	dialNumTimeout_tid  = -1;
	//HangupAllCalling();//挂断所有通话
	stopSipCallSource(st_SipCurStatus.curActiveCallLine); //停止sip呼叫
}

static void dialNumTimeoutHandle(pj_timer_heap_t *timer_heap,
				    struct pj_timer_entry *entry)
{
	NOTICE("Dial Timeout ...");
	call_handle_Timer.id = 0;
	call_handle_Timer2.id = 0;
//	DeleteTimer(dialNumTimeout_tid);
//	PJ_THREAD_REGISTER_MACRO(dialNumTimeoutHandle);
//	dialNumTimeout_tid  = -1;
	//HangupAllCalling();//挂断所有通话
	stopSipCallSource(st_SipCurStatus.curActiveCallLine); //停止sip呼叫
}

/**
 * [hotKeyDialNumberTimeoutHandle 快捷键拨号超时处理]
 * @param tid [description]
 */
void hotKeyDialNumberTimeoutHandle2(int tid){
//	DeleteTimer(dialNumTimeout_tid);
//	dialNumTimeout_tid  = -1;
	NOTICE("hot Key Dial Number Timeout Handle...");
	PJ_THREAD_REGISTER_MACRO(hotKeyDialNumberTimeoutHandle);
	//if(curHotKeyLineNum2Temp<=0 ||curHotKeySltLine<0)
	if(curActivateKeyInfo == NULL)
	{
		WARNINGP("Dial Number Timeout Handle Failed!");
		stopSipCallSource(st_SipCurStatus.curActiveCallLine); //停止sip呼叫
		return ;
	}
	int line = curActivateKeyInfo->line;
	if(line>0)line-=1;
	//清除所有状态
	ResettingSipCurStateFlag();
	stopSipCallSource(st_SipCurStatus.curActiveCallLine); //停止sip呼叫
	//pjsua_call_hangup_all();//挂断所有通话	
	
}

static void hotKeyDialNumberTimeoutHandle_num2(pj_timer_heap_t *timer_heap,
				    struct pj_timer_entry *entry)
{
	call_handle_Timer2.id = 0;
	if(!curActivateKeyInfo){
		WARNINGP("hot Key Dial Number2 Timeout Handle active key info is NULL...");
		return;
	}	
	NOTICE("Dial Number2(%s) for call timeout!",curActivateKeyInfo->key2);
	int line = curActivateKeyInfo->line;
	if(line>0)line-=1;
	int ret = pjsuaDialNumber(&appData.SipUserInfo[line],curActivateKeyInfo->key2,curActivateKeyInfo->subType.type);
	curActivateKeyInfo=NULL;
	if(ret == SUCCEED && appData.sipFeatureSettingsConfig.autoHangupRingTimeout && appData.sipFeatureSettingsConfig.enableAutoHangupRing){
		//记录当前定时点id，在接通电话后删除该定时点		
		if(call_handle_Timer2.id != 1){
			pj_timer_entry_init(&call_handle_Timer2, 0, NULL,&dialNumTimeoutHandle);
			pj_time_val delay;
			delay.sec = appData.sipFeatureSettingsConfig.autoHangupRingTimeout;
   			delay.msec = 0; /* Give 200 ms before hangup */
   			call_handle_Timer2.id = 1;
   			//定时器改用pjsip内部定时器，使用外部定时器曾导致bus error错误，具体原因待调试
			pjsip_endpt_schedule_timer(pjsua_get_pjsip_endpt(),&call_handle_Timer2,&delay);
			//dialNumTimeout_tid = CreateTimer(appData.sipFeatureSettingsConfig.autoHangupRingTimeout*1000,1,func);
			NOTICE("Set dial Number2 timeout %ds succeed.",appData.sipFeatureSettingsConfig.autoHangupRingTimeout);
		}else{
			/* Timer is already active */
			WARNINGP("auto answer Timer is already active.");
		}
	}

}


static void hotKeyDialNumberTimeoutHandle(pj_timer_heap_t *timer_heap,
				    struct pj_timer_entry *entry)
{
	functionKeysDSS_t * tempKeyInfo;
	call_handle_Timer.id = 0;
	NOTICE("hot Key Dial Number Timeout Handle...");
	if(curActivateKeyInfo == NULL)
	{
		WARNINGP("Dial Number Timeout Handle Failed!");
		stopSipCallSource(st_SipCurStatus.curActiveCallLine); //停止sip呼叫
		return ;
	}
	tempKeyInfo	= curActivateKeyInfo; 
	int line = tempKeyInfo->line;
	if(line>0)line-=1;
	//清除所有状态
	ResettingSipCurStateFlag();
	HangupAllCalling(200);
	//stopSipCallSource(); //停止sip呼叫
	//pjsua_call_hangup_all();//挂断所有通话
	curActivateKeyInfo = tempKeyInfo;

	//记录当前定时点id，在接通电话后删除该定时点
	if(call_handle_Timer2.id != 1){
		pj_timer_entry_init(&call_handle_Timer2, 0, NULL,&hotKeyDialNumberTimeoutHandle_num2);
		pj_time_val delay;
		delay.sec = 5; 	//三秒后拨打第二个号码
		delay.msec = 0; /* Give 200 ms before hangup */
		call_handle_Timer2.id = 1;
			//定时器改用pjsip内部定时器，使用外部定时器曾导致bus error错误，具体原因待调试
		pjsip_endpt_schedule_timer(pjsua_get_pjsip_endpt(),&call_handle_Timer2,&delay);
		//dialNumTimeout_tid = CreateTimer(appData.sipFeatureSettingsConfig.autoHangupRingTimeout*1000,1,func);
		NOTICE("Set call Number2(%s) after %ds succeed.",curActivateKeyInfo->key2,5);
	}else{
		/* Timer is already active */
		WARNINGP("auto answer Timer is already active.");
	}
}

int isLetter(char c)
{
//     if( (c>='a' && c<='z') || (c>='A' && c<='Z'))
//        return 1;//返回1表示是字母
//    return 0;//返回0表示不是字母
    return ( (c>='a' && c<='z') || (c>='A' && c<='Z'));
}
int isNumber(char c)
{
    return (c>='0'&&c<='9');
}

int checkStringIsNum(const char *str)
{
	while(*str != '\0'){
		if(!isNumber(*str)) return 0;
		str++;
	}
	return 1;
}

#if 1
/**
 * [hotKeyIntercomDial 快捷键拨号]
 * @param  line [指定线路（目前0 or 1：线路1，2：线路2）]
 * @param  num1 [第一个号码需大于0以上的数字]
 * @param  num2 [第二个为0则只拨第一个号码,不为0时则，第一个号码拒接或响铃超时挂断则拨第二个]
 * @return      [description]
 */
int hotKeyIntercomDial(int line,const char*  num1,const char*  num2)
{
	
	if(line>SIP_USER_MAX)return -1;

	if(get_system_source() == SOURCE_SIP_CALLING){
		NOTICE("hot key Stop Sip Call...");
		stopSipCallSource(line);
		return SUCCEED;
	}
	LOG("hot Key Intercom Dial Line %d. num1 %s ,num2 %s",line,num1,num2);
	NOTICE("Check Telephone Number...");
	if(checkStringIsNum(num1) && strlen(num1) > 1){
		NOTICE("Call Type Line...");
	}else if(strstr(num1,".")){
		NOTICE("Call Type IP...");
	}else{
		ERRORP("Check Telephone Number Failed!");
		return ERROR; 
	}

	curHotKeyCallType = FUNCTION_SUB_HOT_SPEED_DIAL;
	curHotKeySltLine = line;
	if(line>0)line-=1;
	//if(num1<=0)return -1;	
	//dialNumberTimeoutFunc_ptr timeoutFunc=NULL;

	int num2Temp = checkStringIsNum(num2) ? atoi(num2):0;
	if( num2Temp <= 0 ){
		WARNINGP("Hot Key Num2 %d Wrong!",num2Temp);
	}
	//if(num2Temp>0) timeoutFunc = hotKeyDialNumberTimeoutHandle;
	//else 		   timeoutFunc = dialNumTimeoutHandle;
	//curHotKeyLineNum2Temp = num2Temp;	
	int ret = -1;
	ret = pjsuaDialNumber(&appData.SipUserInfo[line], num1,curHotKeyCallType);
	if(ret == SUCCEED){
		//超时检测线程
		//呼叫成功需要设置超时检测线程响铃超时后自动挂断或者进行下一个拨号
		if(appData.sipFeatureSettingsConfig.autoHangupRingTimeout && appData.sipFeatureSettingsConfig.enableAutoHangupRing)//响铃超时处理
		{ 
			//记录当前定时点id，在接通电话后删除该定时点		
			if(call_handle_Timer.id != 1){
				pj_timer_entry_init(&call_handle_Timer, 0, NULL,num2Temp>0?&hotKeyDialNumberTimeoutHandle:&dialNumTimeoutHandle);
				pj_time_val delay;
				delay.sec = appData.sipFeatureSettingsConfig.autoHangupRingTimeout;
	   			delay.msec = 0; /* Give 200 ms before hangup */
	   			call_handle_Timer.id = 1;
	   			//定时器改用pjsip内部定时器，使用外部定时器曾导致bus error错误，具体原因待调试
				pjsip_endpt_schedule_timer(pjsua_get_pjsip_endpt(), 
					&call_handle_Timer, 
					&delay);
				//dialNumTimeout_tid = CreateTimer(appData.sipFeatureSettingsConfig.autoHangupRingTimeout*1000,1,func);
				NOTICE("Set dial timeout %ds succeed.",appData.sipFeatureSettingsConfig.autoHangupRingTimeout);
			}else{
				/* Timer is already active */
				WARNINGP("auto answer Timer is already active.");
			}
		}
	}
	return ret;
}
#endif


/* Display error and exit application */
static void error_exit(const char *title, pj_status_t status)
{
    pjsua_perror(THIS_FILE, title, status);
    pjsua_destroy();
    exit(1);
}



/*
 *  pjusa程序初始化
 *
 *  @param acc_id
 *
 *  @param status
 */
void pjsuaLibInit(void)
{
	pj_status_t status;
	//常用配置
	pjsua_config cfg;
	//日志配置
	pjsua_logging_config log_cfg;
	//媒体设置
	pjsua_media_config media_cfg;
	
	/* Init pjsua 初始化默认配置*/
	//cfg->media_cfg.ec_options = my_atoi(pj_optarg);
	//cfg配置
	pjsua_config_default(&cfg);
	PJSIP_CallbackConfig(&cfg);
	//log_cfg配置 pj_status_t   pjsua_reconfigure_logging (const pjsua_logging_config *c) 
	pjsua_logging_config_default(&log_cfg); 
	log_cfg.console_level = appData.pjsipConf.console_level;
	if(0 !=appData.pjsip_console_level){
		log_cfg.console_level = appData.pjsip_console_level;
	}
	log_cfg.msg_logging = PJSIP_DEF_CONF_HAS_MSG_LOGGING;
	
	//meida配置
	pjsua_media_config_default(&media_cfg);
	//media_cfg.clock_rate = 16000;
	//media_cfg.quality = 8;
	//media_cfg.ec_options = 3;//3 web rtc
	//media_cfg.ec_tail_len = 25;

	//media_cfg.clock_rate = 16000;
	//media_cfg.quality = 8;		//音质
	//media_cfg.ec_options = 3;//3 web rtc 1:speex
	//media_cfg.ec_tail_len = 25;
	//media_cfg.no_vad = 0;		//噪音检测
/*
ec_tail_len,ec_delay,framesize分别代表回声的拖尾，也就是回声持续的时间；回声的延时，
就是你所说的1.5s，从扬声器播出到麦克风采集到的时延。
声音从扬声器到麦克风这个通路的能量较大时，回声就很大，
有时会比原始声音大很多，speex里面有对这个门限进行限制的，
检查一下是否超过这个限制。
影响回声消除的因素，是回声的大小(db)和回声相对原始声音的大小(ERL)，
并不是声音调小了，就容易消除了
回声消除的拖尾是什么？
答：市场上一般说回声消除的拖尾为256ms，它其实对应回声消除内部的滤波器长度，
能够滤除延时时间为多长的线性回声，
对应在内部参数为频段：0.256*48000/128=96，96个滤波处理段（其中48000为采样频率，128为帧长）。

初级的自问自答，由于个人水平有限，不当之处就指正，谢谢。
--------------------- 
作者：memath 
来源：CSDN 
原文：https://blog.csdn.net/memath/article/details/80104829 
版权声明：本文为博主原创文章，转载请附上博文链接！
 */
	
//	media_cfg.clock_rate = 16000; //22050
//	media_cfg.quality = 8;		//音质
//	media_cfg.ec_options = 3;//3 web rtc
//	media_cfg.ec_tail_len = 25;
//	media_cfg.no_vad = 0;		//噪音检测 0:开启噪音检测 1：关闭噪音检测

#if 0
	if(0!=appData.pjsip_clock_rate){
		media_cfg.clock_rate = appData.pjsip_clock_rate;
		NOTICE("Arg set clock_rate=%d",media_cfg.clock_rate);
	}
	if(0!=appData.pjsip_quality){
		media_cfg.quality =	appData.pjsip_quality;
		NOTICE("Arg set quality=%d",media_cfg.quality);
	}
#endif

	media_cfg.no_vad = appData.voiceInfoSettings.en_VAD?0:1;		//噪音检测
	

	//非0时设置
	if( 0 != appData.pjsipConf.clock_rate){
		media_cfg.clock_rate = appData.pjsipConf.clock_rate;
		//media_cfg.snd_clock_rate = appData.pjsipConf.clock_rate;

	}	
	media_cfg.ec_options = appData.pjsipConf.ec_options;
	media_cfg.ec_tail_len = appData.pjsipConf.ec_tail_len;
	media_cfg.quality = appData.pjsipConf.media_quality;
	appData.pjsipConf.en_VAD = appData.voiceInfoSettings.en_VAD;


	media_cfg.snd_auto_close_time = 0;
	


	LOG("pjsua init logging console_level=%d",log_cfg.console_level);
	LOG("pjsua init media clock_rate=%d quality=%d ec_options=%d ec_tail_len=%d no_vad=%s",
		media_cfg.clock_rate,
		media_cfg.quality,
		media_cfg.ec_options,
		media_cfg.ec_tail_len,
		media_cfg.no_vad?"YES":"NO");


	status = pjsua_init(&cfg, &log_cfg,&media_cfg);
	if (status != PJ_SUCCESS){ERRORP("Error in pjsua_init()");}

 #if 1
	//首先设置默认传输通道
	{
		//pjsua_acc_id aid;
		pjsip_transport_type_e type = getUserTransportType(0);
		pjsua_transport_config cfg;
		pjsua_transport_config_default(&cfg);

		cfg.port = appData.pjsipConf.sipLocalPort;
		status = pjsua_transport_create(type, &cfg, NULL);
		if (status != PJ_SUCCESS) {ERRORP("Error creating transport");}
	}
#if HAS_RTSP_VIDIO_SUPPORT
	init_udp_video_media();
#endif
	
	/**
	 * 配置pjsip默认IP账户 防止ip呼叫时无账户应答
	 */
	initPjsuaDefIpAccount();
	NotifyAlterSipStatus(SIP_STATUS_REGISTER_FAILED_OTHER);
	/**
	 * 配置用户信息
	 */	
	PJSIP_AccountConfig();

	/* Initialization is done, now start pjsua 开启pjsua*/
    status = pjsua_start();
	if (status != PJ_SUCCESS) {ERRORP("Error starting pjsua");}
	NOTICE("pjsua_start----------------");
	struct pjsua_data* pjsua_varTemp =  (struct pjsua_data*)pjsua_get_var();
	pjsuaCommonInit(pjsua_varTemp->pool);
	InitVoiceSettings();//语音编码优先级以及可用编码初始化
#endif
/*
	pjsua_codec_set_priority(pj_cstr(&id, CODEC_G722), (pj_uint8_t)180);
	pjsua_codec_set_priority(pj_cstr(&id, CODEC_PCMA), (pj_uint8_t)190);	// 优先级值可以设置0~255,设置0时代表不使用
	pjsua_codec_set_priority(pj_cstr(&id, CODEC_PCMU), (pj_uint8_t)170);
*/
}


/**
 * [restartSipServer 重启SIP服务]
 * @return [description]
 */
int restartSipServer()
{
	printf("restartSipServer...\n");
	//先挂断所有通话
	stopSipCallSource(0);
	//DestroyStreamMulticast();
	pjsua_destroy2(0); 	//销毁PJSUA
	NOTICE("pjsua_destroy2 done...");
	/* init PJLIB : */
	pj_init();
	NOTICE("pj_init done...");
	pjsua_create(); //创建pjsua
	pjsuaLibInit();//初始化	
	//StreaMulticastInit();//初始化rtp组播
}


int PJSIP_AccountSet(int userID,const char * serverip,int port,const char * user,const char * password){
	int i = 0;
	for (i = 0; i < SIP_USER_MAX; ++i)
	{
		if(i != userID)continue;
		memset(appData.SipUserInfo[i].serverAddress,0,SERVER_ADDRESS_LEN_MAX);
		strncpy(appData.SipUserInfo[i].serverAddress,serverip,SERVER_ADDRESS_LEN_MAX);
		appData.SipUserInfo[i].serverPort = port;		
		memset(appData.SipUserInfo[i].user,0,SIP_USER_NAME_LEN_MAX);
		strncpy(appData.SipUserInfo[i].user,user,SIP_USER_NAME_LEN_MAX);

		memset(appData.SipUserInfo[i].password,0,SIP_PASSWORD_LEN_MAX);
		strncpy(appData.SipUserInfo[i].password,password,SIP_PASSWORD_LEN_MAX);
		
		return SUCCEED;
	}
	return ERROR;
}


int reloadVoipConfig()
{
	//先挂断所有通话
	stopSipCallSource(0);
	//DestroyStreamMulticast();
	int i = 0;
	SipUserInfo_t *oldUser;
	for (i = 0; i < SIP_USER_MAX; ++i)
	{
		oldUser = findAccountByIndex(i);
		if (oldUser == NULL){
			return ERROR;
		}
		//先删除用户
		if(pjsua_acc_is_valid(oldUser->acc_id)){
			NOTICE("Delete User ID: %d",oldUser->acc_id);		
			pjsua_acc_del(oldUser->acc_id);//注销用户
			oldUser->acc_id = -1;
		}
		oldUser->registerStatus = 0; //重新设置注册状态
		pjsuaAccAdd(oldUser);
	}
	return SUCCEED;
}


/**
 * [onSIPStatusChanged sip状态改变]
 * @param type  [description]
 * @param line  [description]
 * @param state [description]
 */
void onSIPStatusChanged(int type,int line,int state)
{

	if(type == SIP_STATUS_TYPE_INV){
		NOTICE("sip INV状态改变,line-%d state:%d",line,state);
		switch(state){
			case PJSIP_INV_STATE_NULL:
			case PJSIP_INV_STATE_CALLING:
			case PJSIP_INV_STATE_INCOMING:
			case PJSIP_INV_STATE_EARLY: 
			case PJSIP_INV_STATE_CONNECTING:
			case PJSIP_INV_STATE_CONFIRMED:
				//onVoipCallStatusChanged(line,SIP_LINE_CALLING_STATUS_CALLING);
				setUserCallStatusByLine(line,SIP_LINE_CALLING_STATUS_CALLING);
				break;
			case PJSIP_INV_STATE_DISCONNECTED: 
				//onVoipCallStatusChanged(line,SIP_LINE_CALLING_STATUS_IDLE);
				setUserCallStatusByLine(line,SIP_LINE_CALLING_STATUS_IDLE);
				break;
		}		
	}else if(type == SIP_STATUS_TYPE_CALL){
		NOTICE("sip CALL状态改变,line-%d state:%d",line,state);
		switch(state){
			case SIP_LINE_CALLING_STATUS_IDLE:
			case SIP_LINE_CALLING_STATUS_RING:
			case SIP_LINE_CALLING_STATUS_CALLING: 
			case SIP_LINE_CALLING_STATUS_KEEP: 
				//onVoipCallStatusChanged(line,state);
			break;
		}
		setUserCallStatusByLine(line,state);
	}else if(type == SIP_STATUS_TYPE_REG){
		NOTICE("sip REG状态改变,line-%d state:%d",line,state);
		switch(state){
			case SIP_STATUS_REGISTER_SUCCEED:
				NotifyAlterSipStatus(SIP_STATUS_REGISTER_SUCCEED);
				//onVoipRegStatusChanged(line,SIP_STATUS_REGISTER_SUCCEED);
				break;
			case SIP_STATUS_REGISTER_LOADING:
				NotifyAlterSipStatus(SIP_STATUS_REGISTER_LOADING);
				break;
			case SIP_STATUS_REGISTER_FAILED_OTHER:
				NotifyAlterSipStatus(SIP_STATUS_REGISTER_FAILED_OTHER);
				break;
			case SIP_STATUS_REGISTER_FAILED_TIMEOUT:
				NotifyAlterSipStatus(SIP_STATUS_REGISTER_FAILED_TIMEOUT);
				break;
			case SIP_STATUS_REGISTER_FAILED_PASSWORD:
				NotifyAlterSipStatus(SIP_STATUS_REGISTER_FAILED_PASSWORD);
				//onVoipRegStatusChanged(line,state);
				break;
			case SIP_STATUS_REGISTER_CALLING: 
				NotifyAlterSipStatus(SIP_STATUS_REGISTER_CALLING);
				//onVoipCallStatusChanged(line,SIP_LINE_CALLING_STATUS_CALLING);
				break;
			default:
				break;
		}
	}

	//onVoipRegStatusChanged(st_SipCurStatus.curActiveCallLine,status);
	//onVoipCallStatusChanged(line,SIP_LINE_CALLING_STATUS_IDLE);
}


static int pjsipIsInit = 0;
int pjsipInit()
{
	//在未连接网络时初始化pjsip，以注册其他线程到pjsip使用
	//
	/* init PJLIB : */
	pj_init();
	pjsipIsInit = 1;
	pjsip_init_is_finish = 1;	
}


/**
 * [InitVoipInfomation VOIP初始化]
 * @return [description]
 */
int InitVoipInfomation()
{
	pj_status_t status;
	NOTICE("InitVoipInfomation...");
	if(pjsipIsInit == 0){
		ERRORP("pjsip is not initialize...");		
		return ERROR;
	}
	//ast_cli_register_multiple(cli_cli, ARRAY_SIZE(cli_cli));
	readSIPConfig();//读取配置文件信息
	
	status = pjsua_create();
    if (status != PJ_SUCCESS) {
    	ERRORP("Error in pjsua_create()");
    	return -1;
    }

	pjsuaLibInit();//初始化	
	pjsip_init_is_finish = 1;
	#if 0	
	StreaMulticastInit();//初始化rtp组播
	#endif
	VARIABLE_STR_ADD(appData.SipUserInfo[0].user);
	VARIABLE_STR_ADD(appData.SipUserInfo[1].user);
	VARIABLE_INT16_ADD(appData.SipUserInfo[1].serverPort);
	VARIABLE_INT8_ADD(appData.sipFeatureSettingsConfig.autoHangupRingTimeout);
	VARIABLE_INT8_ADD(appData.sipFeatureSettingsConfig.autoAnswerLineSlt);
	VARIABLE_INT8_ADD(appData.sipFeatureSettingsConfig.autoAnswerTimeMs);
	VARIABLE_INT8_ADD(appData.sipFeatureSettingsConfig.enableIntercomMute);
	VARIABLE_INT8_ADD(appData.sipFeatureSettingsConfig.enableIntercomTone);
	
	VARIABLE_INT8_ADD(appData.sipFeatureSettingsConfig.enableFeatureKeyAnswer);
	VARIABLE_INT8_ADD(appData.sipFeatureSettingsConfig.enableSpeedDialKeyHangup);
	VARIABLE_INT8_ADD(appData.sipFeatureSettingsConfig.enableAutoHangupRing);
	//pjsuaDialNumberIP(&appData.SipUserInfo[0],"***************",dialNumTimeoutHandle);
	
	return SUCCEED;
}



void *voip_set_systemSource_idle()
{
	int pre_system_source = get_system_source();
	int ready_timing_idle_cnt=0;
	while(1)
	{
		int tmp_system_source = get_system_source();
		if(tmp_system_source != pre_system_source)
		{
			//之前是SIP对讲，现在已经不是SIP对讲了，那么需要定时设置空闲
			if(pre_system_source == SOURCE_SIP_CALLING)
			{
				if(tmp_system_source!=SOURCE_100V_INPUT && tmp_system_source!=SOURCE_NULL)
				{
					ready_timing_idle_cnt = 10;
				}
			}
			else	//之前音源不是SIP对讲，现在变成了其他（可能是对讲）
			{
				ready_timing_idle_cnt = 0;
			}
			pre_system_source = tmp_system_source;
		}
		
		//线路空闲了，但是还是SIP对讲状态
		if(ready_timing_idle_cnt == 0)
		{
			if(checkLineIsIdle() && tmp_system_source == SOURCE_SIP_CALLING)
			{
				ready_timing_idle_cnt=10;
			}
		}

		if(ready_timing_idle_cnt)
		{
			ready_timing_idle_cnt--;
			if(ready_timing_idle_cnt == 0)
			{
				//线路空闲的情况下，设置空闲
				if(checkLineIsIdle())
				{
					Set_zone_idle_status(NULL,  __func__, __LINE__,true);
				}
			}
		}
		usleep(50000);
	}
}

void Voip_Set_SystemSource_Idle_THREAD()
{
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)voip_set_systemSource_idle, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}

/*
回声消除答疑
0问：为什么使用回声消除？

        答：比较常见的应用场景，a与b打电话，a端手机开启免提，这时b端传过来的声音就会通过电话的扬声器向外播放，这时手机的麦克风就可能会采集扬声器出来的声音，若是没有回声消除功能模块，b端就会听到自己的声音从a端传回来，影响了正常通话。再如微信的视频通话，都有回声消除模块起了作用。

1问：回声消除有什么开源代码？

        答：主要有webrtc的aec，aecm，aec3，还有speex。

2问：回声消除需要用到什么算法？

        答：线性回声消除用NLMS,PBFDAF,MDF,非线性处理用归一化相关性，舒适噪声生成，延时估计。

3问：为什么有时候漏回声呢（回声消除没有效果）？

        答：读文件效果仿真的话，简单的方法先自己用cooledit手动对齐远端信号，也可以自己加上延时估计；如果是pc点对点测试，测试要在两个不同的房间进行，可以参考webrtc实现方式；如果是android端自已先测试出手机端的延时（也可以参考demo请看网友的安卓demo）。

4问：为什么speex效果没有webrtc好？

        答：memath个人看法，如果远端线性好的话，speex的效果还是不错的；若是远端非线性严重的话，speex效果比较差了，对比这时用webrtc就比较好了。两者差别请看：speex与webrtc回声消除小结 

5问：webrtc aec3效果怎么样？

        答：现在的效果还比较差，以后应该会更新，以前的效果请查看webrtc aec3效果对比aec与aecm（webrtc M64 20180115版本）

6问：双讲检测都有那些方法呢？

        答：最常用是利用相关性，能量，时域特征，其中speex的双讲检测在自适应滤波器部分，也是基于能量判断的。

7问：回声消除的拖尾是什么？
答：市场上一般说回声消除的拖尾为256ms，它其实对应回声消除内部的滤波器长度，能够滤除延时时间为多长的线性回声，对应在内部参数为频段：0.256*48000/128=96，96个滤波处理段（其中48000为采样频率，128为帧长）。

初级的自问自答，由于个人水平有限，不当之处就指正，谢谢。



memath回声消除系列文章：

speex与webrtc回声消除小结 

QQ、YY与webRTC回声消除效果对比分析与展望

webrtc aec3效果对比aec与aecm（webrtc M64 20180115版本）
speex回声消除源码解读


--------------------- 
作者：memath 
来源：CSDN 
原文：https://blog.csdn.net/memath/article/details/80104829 
版权声明：本文为博主原创文章，转载请附上博文链接！

 */



//alsa_dev.c !ca_thread_func: error reading data