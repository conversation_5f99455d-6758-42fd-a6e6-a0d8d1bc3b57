#include <pthread.h>
#include <unistd.h>
// #include <pjlib.h>
// #include <pjlib-util.h>

#include "sip.h"
#include "debug.h"
#include "rtpMulticast.h"
#include "appConfig.h"
#include "priority.h"

#include "util.h"

#define HAVE_MULTICAST_SUPPORT 0

#if HAVE_MULTICAST_SUPPORT

#define THIS_FILE __FILE__

static struct pjsua_data* stPjsuaVarData =  NULL;
const char * buildCodesID(unsigned char pt,unsigned clock_rate,unsigned char channel_cnt);
static pjmedia_codec_mgr *rtpRecv_odec_mgr =NULL;
static pjmedia_codec *    rtpRecv_codec =NULL;

static pjmedia_rtp_session rtp_in_ses;

static int en_RtpMultRecv = 1;//使能rtp接收数据
static char rtpMulticastPageListen_stop = 0;

static int first_enter_RTP=1;//第一次进流接收处理，不允许再进入（服务器存在发送两次发起寻呼请求）

/**
 * [Get_first_enter_RTP 获取是否已经进入寻呼线程]
 * @return [description]
 */
int Get_first_enter_RTP()
{
	return first_enter_RTP;
}


/**
 * [Set_first_enter_RTP 设置进入RTP寻呼标志]
 * @param Status [description]
 */
void Set_first_enter_RTP(int Status)
{
	first_enter_RTP=Status;
} 


int app_perror( const char *sender, const char *title, 
			   pj_status_t status)
{
	char errmsg[PJ_ERR_MSG_SIZE];

	pj_strerror(status, errmsg, sizeof(errmsg));

	PJ_LOG(3,(sender, "%s: %s [code=%d]", title, errmsg, status));
	return 1;
}

#if 0
/** 
 * Standard RTP static payload types, as defined by RFC 3551. 
 * The header file <pjmedia-codec/types.h> also declares dynamic payload
 * type numbers that are used by PJMEDIA when advertising the capability
 * for example in SDP message.
 */
enum pjmedia_rtp_pt
{
    PJMEDIA_RTP_PT_PCMU = 0,	    /**< audio PCMU			    */
    PJMEDIA_RTP_PT_G721 = 2,	    /**< audio G721 (old def for G726-32)   */
    PJMEDIA_RTP_PT_GSM  = 3,	    /**< audio GSM			    */
    PJMEDIA_RTP_PT_G723 = 4,	    /**< audio G723			    */
    PJMEDIA_RTP_PT_DVI4_8K = 5,	    /**< audio DVI4 8KHz		    */
    PJMEDIA_RTP_PT_DVI4_16K = 6,    /**< audio DVI4 16Khz		    */
    PJMEDIA_RTP_PT_LPC = 7,	    /**< audio LPC			    */
    PJMEDIA_RTP_PT_PCMA = 8,	    /**< audio PCMA			    */
    PJMEDIA_RTP_PT_G722 = 9,	    /**< audio G722			    */
    PJMEDIA_RTP_PT_L16_2 = 10,	    /**< audio 16bit linear 44.1KHz stereo  */
    PJMEDIA_RTP_PT_L16_1 = 11,	    /**< audio 16bit linear 44.1KHz mono    */
    PJMEDIA_RTP_PT_QCELP = 12,	    /**< audio QCELP			    */
    PJMEDIA_RTP_PT_CN = 13,	    /**< audio Comfort Noise		    */
    PJMEDIA_RTP_PT_MPA = 14,	    /**< audio MPEG1/MPEG2 elemetr. streams */
    PJMEDIA_RTP_PT_G728 = 15,	    /**< audio G728			    */
    PJMEDIA_RTP_PT_DVI4_11K = 16,   /**< audio DVI4 11.025KHz mono	    */
    PJMEDIA_RTP_PT_DVI4_22K = 17,   /**< audio DVI4 22.050KHz mono	    */
    PJMEDIA_RTP_PT_G729 = 18,	    /**< audio G729			    */

    PJMEDIA_RTP_PT_CELB = 25,	    /**< video/comb Cell-B by Sun (RFC2029) */
    PJMEDIA_RTP_PT_JPEG = 26,	    /**< video JPEG			    */
    PJMEDIA_RTP_PT_NV = 28,	    /**< video NV  by nv program by Xerox   */
    PJMEDIA_RTP_PT_H261 = 31,	    /**< video H261			    */
    PJMEDIA_RTP_PT_MPV = 32,	    /**< video MPEG1 or MPEG2 elementary    */
    PJMEDIA_RTP_PT_MP2T = 33,	    /**< video MPEG2 transport		    */
    PJMEDIA_RTP_PT_H263 = 34,	    /**< video H263			    */

    PJMEDIA_RTP_PT_DYNAMIC = 96     /**< start of dynamic RTP payload	    */

};
#endif




#if 1




/*
 * This callback is called by media transport on receipt of RTP packet.
 */
static void on_rx_rtp(void *user_data, void *pkt, pj_ssize_t size)
{
    //struct media_stream *strm;
    pj_status_t status;
    const pjmedia_rtp_hdr *hdr;
    const void *payload;
    unsigned payload_len;
    NOTICE("11on_rx_rtp....size=%d",size);

    /* Check for errors */
    if (size < 0) {
	app_perror(THIS_FILE, "RTP recv() error", (pj_status_t)-size);
	return;
    }

    /* Decode RTP packet. */
    status = pjmedia_rtp_decode_rtp(&rtp_in_ses, 
				    pkt, (int)size, 
				    &hdr, &payload, &payload_len);
    if (status != PJ_SUCCESS) {
	app_perror(THIS_FILE, "RTP decode error", status);
	return;
    }

    PJ_LOG(4,(THIS_FILE, "Rx seq=%d payload_len=%d", pj_ntohs(hdr->seq),payload_len));

    /* Update the RTCP session. */
//    pjmedia_rtcp_rx_rtp(&strm->rtcp, pj_ntohs(hdr->seq),
//			pj_ntohl(hdr->ts), payload_len);
//
    /* Update RTP session */
    pjmedia_rtp_session_update(&rtp_in_ses, hdr, NULL);

}

/*
 * This callback is called by media transport on receipt of RTCP packet.
 */
static void on_rx_rtcp(void *user_data, void *pkt, pj_ssize_t size)
{
//     struct media_stream *strm;

//     strm = user_data;

//     /* Discard packet if media is inactive */
//     if (!strm->active)
// 	return;

//     /* Check for errors */
//     if (size < 0) {
// 	app_perror(THIS_FILE, "Error receiving RTCP packet",(pj_status_t)-size);
// 	return;
//     }

//     /* Update RTCP session */
//     pjmedia_rtcp_rx_rtcp(&strm->rtcp, pkt, size);
}




#if 0
/* Notification from ioqueue about incoming RTP packet */
static void on_rx_rtp( pj_ioqueue_key_t *key, 
                       pj_ioqueue_op_key_t *op_key, 
                       pj_ssize_t bytes_read)
{
	NOTICE("on_rx_rtp....key=%p",key);
	//if(key->cb.on_read_complete){
	//	NOTICE("key->cb.on_read_complete....");
	//	key->cb.on_read_complete(key,op_key,bytes_read);
	//}
	
//	if(on_rx_rtp_pjsip){
//		on_rx_rtp_pjsip(key,op_key,bytes_read);
//	}
#if 0
    transport_udp *udp;
    pj_status_t status;

    PJ_UNUSED_ARG(op_key);

    udp = (struct transport_udp*) pj_ioqueue_get_user_data(key);

    do {
	void (*cb)(void*,void*,pj_ssize_t);
	void *user_data;
	pj_bool_t discard = PJ_FALSE;

	cb = udp->rtp_cb;
	user_data = udp->user_data;

	/* Simulate packet lost on RX direction */
	if (udp->rx_drop_pct) {
	    if ((pj_rand() % 100) <= (int)udp->rx_drop_pct) {
		PJ_LOG(5,(udp->base.name, 
			  "RX RTP packet dropped because of pkt lost "
			  "simulation"));
		discard = PJ_TRUE;
	    }
	}

	/* See if source address of RTP packet is different than the 
	 * configured address, and switch RTP remote address to 
	 * source packet address after several consecutive packets
	 * have been received.
	 */
	if (bytes_read>0 && 
	    (udp->options & PJMEDIA_UDP_NO_SRC_ADDR_CHECKING)==0) 
	{
	    if (pj_sockaddr_cmp(&udp->rem_rtp_addr, &udp->rtp_src_addr) == 0) {
		/* We're still receiving from rem_rtp_addr. Don't switch. */
		udp->rtp_src_cnt = 0;
		udp->rem_rtp_cnt++;
	    } else {
		udp->rtp_src_cnt++;

		if (udp->rtp_src_cnt < PJMEDIA_RTP_NAT_PROBATION_CNT) {
		    /* Only discard if we have ever received packet from
		     * remote address (rem_rtp_addr).
		     */
		    //discard = PJ_TRUE;
		    discard = (udp->rem_rtp_cnt != 0);
		} else {
		
		    char addr_text[80];

		    /* Set remote RTP address to source address */
		    pj_memcpy(&udp->rem_rtp_addr, &udp->rtp_src_addr,
			      sizeof(pj_sockaddr));

		    /* Reset counter */
		    udp->rtp_src_cnt = 0;

		    PJ_LOG(4,(udp->base.name,
			      "Remote RTP address switched to %s",
			      pj_sockaddr_print(&udp->rtp_src_addr, addr_text,
						sizeof(addr_text), 3)));

		    /* Also update remote RTCP address if actual RTCP source
		     * address is not heard yet.
		     */
		    if (!pj_sockaddr_has_addr(&udp->rtcp_src_addr)) {
			pj_uint16_t port;

			pj_memcpy(&udp->rem_rtcp_addr, &udp->rem_rtp_addr, 
				  sizeof(pj_sockaddr));
			pj_sockaddr_copy_addr(&udp->rem_rtcp_addr,
					      &udp->rem_rtp_addr);
			port = (pj_uint16_t)
			       (pj_sockaddr_get_port(&udp->rem_rtp_addr)+1);
			pj_sockaddr_set_port(&udp->rem_rtcp_addr, port);

			pj_memcpy(&udp->rtcp_src_addr, &udp->rem_rtcp_addr, 
				  sizeof(pj_sockaddr));

			PJ_LOG(4,(udp->base.name,
				  "Remote RTCP address switched to predicted"
				  " address %s",
				  pj_sockaddr_print(&udp->rtcp_src_addr, 
						    addr_text,
						    sizeof(addr_text), 3)));

		    }
		}
	    }
	}

	//if (!discard && udp->attached && cb)
	if (!discard && cb)
	    (*cb)(user_data, udp->rtp_pkt, bytes_read);

	bytes_read = sizeof(udp->rtp_pkt);
	udp->rtp_addrlen = sizeof(udp->rtp_src_addr);
	status = pj_ioqueue_recvfrom(udp->rtp_key, &udp->rtp_read_op,
				     udp->rtp_pkt, &bytes_read, 0,
				     &udp->rtp_src_addr, 
				     &udp->rtp_addrlen);

	if (status != PJ_EPENDING && status != PJ_SUCCESS)
	    bytes_read = -status;

    } while (status != PJ_EPENDING && status != PJ_ECANCELLED);
   #endif
}


static void on_rx_rtcp(void *user_data,void *pkt,pj_ssize_t bytes_read)
{
	NOTICE("on_rx_rtcp....");
}

#endif





/* 
 * Create stream based on the codec, dir, remote address, etc. 
 */
static pj_status_t create_stream( pj_pool_t *pool,
				  pjmedia_endpt *med_endpt,
				  const pjmedia_codec_info *codec_info,
				  pjmedia_dir dir,
				  pj_uint16_t local_port,
				  const pj_sockaddr_in *rem_addr,
				  pj_bool_t mcast,
				  const pj_sockaddr_in *mcast_addr,
#if defined(PJMEDIA_HAS_SRTP) && (PJMEDIA_HAS_SRTP != 0)
				  pj_bool_t use_srtp,
				  const pj_str_t *crypto_suite,
				  const pj_str_t *srtp_tx_key,
				  const pj_str_t *srtp_rx_key,
				  pj_bool_t is_dtls_client,
				  pj_bool_t is_dtls_server,
#endif
				  pjmedia_stream **p_stream )
{
	pjmedia_stream_info info;
	pjmedia_transport *transport = NULL;
	pj_status_t status;
#if defined(PJMEDIA_HAS_SRTP) && (PJMEDIA_HAS_SRTP != 0)
	pjmedia_transport *srtp_tp = NULL;
#endif
   
	/* Reset stream info. */
	pj_bzero(&info, sizeof(info));
	int pos=0;
   

	/* Initialize stream info formats */
	info.type = PJMEDIA_TYPE_AUDIO;
	info.dir = dir;
	pj_memcpy(&info.fmt, codec_info, sizeof(pjmedia_codec_info));	
	info.tx_pt = codec_info->pt;
	info.rx_pt = codec_info->pt;
	info.ssrc = pj_rand();
	
#if PJMEDIA_HAS_RTCP_XR && PJMEDIA_STREAM_ENABLE_XR
	/* Set default RTCP XR enabled/disabled */
	info.rtcp_xr_enabled = PJ_TRUE;
#endif
   
	/* Copy remote address */
	pj_memcpy(&info.rem_addr, rem_addr, sizeof(pj_sockaddr_in));
  
	/* If remote address is not set, set to an arbitrary address
	 * (otherwise stream will assert).
	 */
	if (info.rem_addr.addr.sa_family == 0) {
	const pj_str_t addr = pj_str("127.0.0.1");
	pj_sockaddr_in_init(&info.rem_addr.ipv4, &addr, 0);
	}
   
	pj_sockaddr_cp(&info.rem_rtcp, &info.rem_addr);
	pj_sockaddr_set_port(&info.rem_rtcp,
				 pj_sockaddr_get_port(&info.rem_rtcp)+1);

	if (mcast) {
		pjmedia_sock_info si;
		int reuse = 1;

		pj_bzero(&si, sizeof(pjmedia_sock_info));
		si.rtp_sock = si.rtcp_sock = PJ_INVALID_SOCKET;

		/* Create RTP socket */
		status = pj_sock_socket(pj_AF_INET(), pj_SOCK_DGRAM(), 0,
					&si.rtp_sock);
		if (status != PJ_SUCCESS)
		return status;

		status = pj_sock_setsockopt(si.rtp_sock, pj_SOL_SOCKET(),
						pj_SO_REUSEADDR(), &reuse, sizeof(reuse));
		if (status != PJ_SUCCESS)
			return status;

		/* Bind RTP socket */
		status = pj_sockaddr_init(pj_AF_INET(), &si.rtp_addr_name,
					  NULL, local_port);
		if (status != PJ_SUCCESS)
		return status;
	
		status = pj_sock_bind(si.rtp_sock, &si.rtp_addr_name,
				  pj_sockaddr_get_len(&si.rtp_addr_name));
		if (status != PJ_SUCCESS)
		return status;

		/* Create RTCP socket */
		status = pj_sock_socket(pj_AF_INET(), pj_SOCK_DGRAM(), 0,
					&si.rtcp_sock);
		if (status != PJ_SUCCESS)
		return status;

		status = pj_sock_setsockopt(si.rtcp_sock, pj_SOL_SOCKET(),
						pj_SO_REUSEADDR(), &reuse, sizeof(reuse));
		if (status != PJ_SUCCESS)
			return status;

		/* Bind RTCP socket */
		status = pj_sockaddr_init(pj_AF_INET(), &si.rtcp_addr_name,
					  NULL, local_port+1);
		if (status != PJ_SUCCESS)
		return status;
	
		status = pj_sock_bind(si.rtcp_sock, &si.rtcp_addr_name,
							  pj_sockaddr_get_len(&si.rtcp_addr_name));
		if (status != PJ_SUCCESS)
		return status;

#ifdef HAVE_MULTICAST_SUPPORT
	{
		unsigned char loop;
		struct pj_ip_mreq imr;
	
		pj_memset(&imr, 0, sizeof(struct pj_ip_mreq));
		imr.imr_multiaddr.s_addr = mcast_addr->sin_addr.s_addr;
		imr.imr_interface.s_addr = pj_htonl(PJ_INADDR_ANY);
		status = pj_sock_setsockopt(si.rtp_sock, pj_SOL_IP(),
						pj_IP_ADD_MEMBERSHIP(),
					&imr, sizeof(struct pj_ip_mreq));
		if (status != PJ_SUCCESS)
			return status;

		status = pj_sock_setsockopt(si.rtcp_sock, pj_SOL_IP(),
						pj_IP_ADD_MEMBERSHIP(),
					&imr, sizeof(struct pj_ip_mreq));
		if (status != PJ_SUCCESS)
			return status;

		/* Disable local reception of local sent packets */
		loop = 0;
		pj_sock_setsockopt(si.rtp_sock, pj_SOL_IP(),
					   pj_IP_MULTICAST_LOOP(), &loop, sizeof(loop));
		pj_sock_setsockopt(si.rtcp_sock, pj_SOL_IP(),
					   pj_IP_MULTICAST_LOOP(), &loop, sizeof(loop));
	}
#endif
		/* Create media transport from existing sockets */
		status = pjmedia_transport_udp_attach( med_endpt, NULL, &si, 
				PJMEDIA_UDP_NO_SRC_ADDR_CHECKING, &transport);
		if (status != PJ_SUCCESS)
		return status; 	
	} else {
		/* Create media transport */
		status = pjmedia_transport_udp_create(med_endpt, NULL, local_port,
						  0, &transport);
		if (status != PJ_SUCCESS)
		return status;
	}
#if defined(PJMEDIA_HAS_SRTP) && (PJMEDIA_HAS_SRTP != 0)
	/* Check if SRTP enabled */
	if (use_srtp) {
	status = pjmedia_transport_srtp_create(med_endpt, transport, 
						   NULL, &srtp_tp);
	if (status != PJ_SUCCESS)
		return status;   
	if (is_dtls_client || is_dtls_server) {
		char fp[128];
		pj_size_t fp_len = sizeof(fp);
		pjmedia_srtp_dtls_nego_param dtls_param;
		
		pjmedia_transport_srtp_dtls_get_fingerprint(srtp_tp, "SHA-256", fp, &fp_len);
		PJ_LOG(3, (THIS_FILE, "Local cert fingerprint: %s", fp));

		pj_bzero(&dtls_param, sizeof(dtls_param));
		pj_sockaddr_cp(&dtls_param.rem_addr, rem_addr);
		pj_sockaddr_cp(&dtls_param.rem_rtcp, rem_addr);
		dtls_param.is_role_active = is_dtls_client;

		status = pjmedia_transport_srtp_dtls_start_nego(srtp_tp, &dtls_param);
		if (status != PJ_SUCCESS)
		return status;
	} else {
		pjmedia_srtp_crypto tx_plc, rx_plc;       
		pj_bzero(&tx_plc, sizeof(pjmedia_srtp_crypto));
		pj_bzero(&rx_plc, sizeof(pjmedia_srtp_crypto));

		tx_plc.key = *srtp_tx_key;
		tx_plc.name = *crypto_suite;
		rx_plc.key = *srtp_rx_key;
		rx_plc.name = *crypto_suite;
		
		status = pjmedia_transport_srtp_start(srtp_tp, &tx_plc, &rx_plc);
		if (status != PJ_SUCCESS)
		return status;
	}

	transport = srtp_tp;
	}
#endif
	/* Now that the stream info is initialized, we can create the 
	 * stream.
	 */
	if(*p_stream ==NULL){
		WARNINGP("*p_stream ==NULL");
	}

	status = pjmedia_stream_create( med_endpt, pool, &info, 
					transport, 
					NULL, p_stream);

	if (status != PJ_SUCCESS) {
	ERRORP("Error creating stream status(%d)",status);
   // app_perror(THIS_FILE, "Error creating stream", status);
	pjmedia_transport_close(transport);
	return status;
	}
//
//	NOTICE("附加媒体到传输端口");
//	//!!!附加媒体到传输端口
//	
//	if(*p_stream ==NULL){
//		WARNINGP("*p_stream ==NULL");
//	}
#if 1
	status= pjmedia_transport_attach(transport, *p_stream,
				  &info.rem_addr,
				  &info.rem_rtcp,
				  sizeof(pj_sockaddr_in),
				  &on_rx_rtp,
				  &on_rx_rtcp);
	if (status != PJ_SUCCESS){
		WARNINGP("Set pjmedia_transport_attach wrong!");
		return status;  
	}	
#endif
	WARNINGP("pjmedia_transport_attach done.");
	return PJ_SUCCESS;
}


#endif

/* 
 * Register all codecs. 
 */
static pj_status_t init_codecs(pjmedia_endpt *endpt)
{
	return pjmedia_codec_register_audio_codecs(endpt, NULL);
#if 0
	pjmedia_audio_codec_config default_cfg;
	pj_status_t status;
	const pjmedia_audio_codec_config *c;
	if (!c) {
		pjmedia_audio_codec_config_default(&default_cfg);
		c = &default_cfg;
	}
	WARNINGP("c->ilbc.mode=%d",c->ilbc.mode);
	PJ_ASSERT_RETURN(c->ilbc.mode==20 || c->ilbc.mode==30, PJ_EINVAL);

 pjmedia_codec_mgr *codec_mgr = NULL;
 codec_mgr = pjmedia_endpt_get_codec_mgr(endpt);
 WARNINGP("mgr->codec_cnt=%d",codec_mgr->codec_cnt);

#if PJMEDIA_HAS_PASSTHROUGH_CODECS
	NOTICE("pjmedia_codec_passthrough_init2");
	status = pjmedia_codec_passthrough_init2(endpt, &c->passthrough.setting);
	if (status != PJ_SUCCESS)
	return status;
#endif

#if PJMEDIA_HAS_SPEEX_CODEC
	/* Register speex. */
	NOTICE("Register speex. c->speex.option=%d",c->speex.option);
	status = pjmedia_codec_speex_init(endpt, c->speex.option,
					  c->speex.quality,
					  c->speex.complexity);
	if (status != PJ_SUCCESS)
	return status;
#endif
	 WARNINGP("mgr->codec_cnt=%d",codec_mgr->codec_cnt);

#if PJMEDIA_HAS_ILBC_CODEC
	/* Register iLBC. */
NOTICE("Register iLBC.");
	status = pjmedia_codec_ilbc_init( endpt, c->ilbc.mode);
	if (status != PJ_SUCCESS)
	return status;
#endif /* PJMEDIA_HAS_ILBC_CODEC */
 WARNINGP("mgr->codec_cnt=%d",codec_mgr->codec_cnt);
#if PJMEDIA_HAS_GSM_CODEC
	/* Register GSM */
NOTICE("Register GSM");
	status = pjmedia_codec_gsm_init(endpt);
	if (status != PJ_SUCCESS)
	return status;
#endif /* PJMEDIA_HAS_GSM_CODEC */
 WARNINGP("mgr->codec_cnt=%d",codec_mgr->codec_cnt);
#if PJMEDIA_HAS_G711_CODEC
	/* Register PCMA and PCMU */
NOTICE("Register PCMA and PCMU");
	status = pjmedia_codec_g711_init(endpt);
	if (status != PJ_SUCCESS)
	return status;
#endif  /* PJMEDIA_HAS_G711_CODEC */

#if PJMEDIA_HAS_G722_CODEC
NOTICE("pjmedia_codec_g722_init");
	status = pjmedia_codec_g722_init(endpt );
	if (status != PJ_SUCCESS)
	return status;
#endif  /* PJMEDIA_HAS_G722_CODEC */

#if PJMEDIA_HAS_INTEL_IPP
	/* Register IPP codecs */
NOTICE("Register IPP codecs");
	status = pjmedia_codec_ipp_init(endpt);
	if (status != PJ_SUCCESS)
	return status;
#endif /* PJMEDIA_HAS_INTEL_IPP */

#if PJMEDIA_HAS_G7221_CODEC
	/* Register G722.1 codecs */
NOTICE("Register G722.1 codecs");
	status = pjmedia_codec_g7221_init(endpt);
	if (status != PJ_SUCCESS)
	return status;
#endif /* PJMEDIA_HAS_G7221_CODEC */

#if PJMEDIA_HAS_L16_CODEC
	/* Register L16 family codecs */
NOTICE("Register L16 family codecs");
	status = pjmedia_codec_l16_init(endpt, 0);
	if (status != PJ_SUCCESS)
	return status;
#endif  /* PJMEDIA_HAS_L16_CODEC */

#if PJMEDIA_HAS_OPENCORE_AMRNB_CODEC || PJMEDIA_HAS_OPENCORE_AMRWB_CODEC
	/* Register OpenCORE AMR */
NOTICE(" Register OpenCORE AMR");
	status = pjmedia_codec_opencore_amr_init(endpt, 0);
	if (status != PJ_SUCCESS)
	return status;
#endif

#if PJMEDIA_HAS_SILK_CODEC
	/* Register SILK */
NOTICE("Register SILK");
	status = pjmedia_codec_silk_init(endpt);
	if (status != PJ_SUCCESS)
	return status;
#endif

#if PJMEDIA_HAS_OPUS_CODEC
	/* Register OPUS */
NOTICE("Register OPUS");
	status = pjmedia_codec_opus_init(endpt);
	if (status != PJ_SUCCESS)
	return status;
#endif

#if PJMEDIA_HAS_BCG729
	/* Register BCG729 */
NOTICE("Register BCG729");
	status = pjmedia_codec_bcg729_init(endpt);
	if (status != PJ_SUCCESS)
	return status;
#endif
	WARNINGP("init_codecs Done!");    
	return PJ_SUCCESS;
#endif

}
;

/**
 * 
 */
int analyzeAddressPortFromString(const char* str,char *addr,int *port)
{
	char rtpSendBuf[50]={0};
	strcpy(rtpSendBuf,str);
	char * p = strtok(rtpSendBuf,":");
	if(!p) return -1;
	strcpy(addr,p);
	p = strtok(NULL,":");
	if(!p) return -1;
	*port = atoi(p);

	return 1;
}



/**
 * [startStreaMulticastSendOnlyWithKey 按键启动组播发送]
 * @param  key1_multAddr [description]
 * @param  subType       [description]
 * @return               [description]
 */
int startStreaMulticastSendOnlyWithKey(const char * key1_multAddr,int subType)
{
	int payload=0;
	switch(subType){
		case FUNCTION_SUB_MULT_CODE_G711A: payload = PJMEDIA_RTP_PT_PCMA; break;
		case FUNCTION_SUB_MULT_CODE_G711U: payload = PJMEDIA_RTP_PT_PCMU; break;
		case FUNCTION_SUB_MULT_CODE_G722: payload = PJMEDIA_RTP_PT_G722; break;
		case FUNCTION_SUB_MULT_CODE_GSM: payload = PJMEDIA_RTP_PT_GSM; break;
		default:
		payload = PJMEDIA_RTP_PT_PCMU; 
		break;
	}
	#if 0
	if(getPrioritySourceStatus(SOURCE_RTP_MULT) != PRIORITY_SOURCE_STATUS_STOPED){
		//如果组播已经启动则停止
		
		stopRtpMultRecvSource();
		return SUCCEED;
	}
	#endif
	if(!checkRtpMultSourceIsValid()){
		NOTICE("check Rtp Mult Source Valid Failed!");		
		return ERROR; 
	}

	char multAddr[PJ_INET_ADDRSTRLEN]={0}; //***************
	int port;
	if(analyzeAddressPortFromString(key1_multAddr,multAddr,&port)< 0){
		//解析地址错误
		ERRORP("analyze Multicast Address Wrong!");
		return ERROR;
	}
	LOG("start Stream Multicast Send Only With Key");
	NOTICE("multAddr: %s:%d",multAddr,port);
	if( startStreaMulticast(PJMEDIA_DIR_ENCODING,multAddr,port,buildCodesID(payload,8000,1)) != PJ_SUCCESS)
	{
		stopRtpMultRecvSource();
		return ERROR;
	}
	return SUCCEED;
}
					


#if 1
/**
 * 组播流媒体信息
 */
//pj_caching_pool StreaMult_cp;
pjmedia_stream *StreaMult_stream = NULL;
pjmedia_snd_port *StreaMult_snd_port = NULL;

void en_RtpMultRecvTimeout(int tid)
{
	DeleteTimer(tid);
	en_RtpMultRecv = 1;
}

void DestroyStreamMulticastInfo()
{
	NOTICE("Destroy Stream Multicast Info...");
	if (StreaMult_snd_port) {
		pjmedia_snd_port_destroy( StreaMult_snd_port );
		StreaMult_snd_port = NULL;        
	}

	 /* Destroy stream */
	if (StreaMult_stream) {
		pjmedia_transport *tp;

		tp = pjmedia_stream_get_transport(StreaMult_stream);
		pjmedia_stream_destroy(StreaMult_stream);        
		pjmedia_transport_close(tp);
		StreaMult_stream = NULL;
	}

//rtp接收关闭
	if(rtpRecv_codec){
		pjmedia_codec_close(rtpRecv_codec);
	}
	
	/* Close codec */
	if(rtpRecv_odec_mgr){
		pjmedia_codec_mgr_dealloc_codec(rtpRecv_odec_mgr, rtpRecv_codec);
	}
	rtpRecv_codec = NULL;
	rtpRecv_odec_mgr = NULL;
	appData.rtpMulticastConf.curStatus = 0;
	appData.rtpMulticastConf.activateRtpPt = 0xFF;
	//close_dsp();//关闭DSP
	CreateTimer(3000,1,en_RtpMultRecvTimeout);
#if 0
	/* Release application pool */
	if(StreaMult_pool)pj_pool_release( StreaMult_pool );

	/* Destroy media endpoint. */
	if(StreaMult_med_endpt) pjmedia_endpt_destroy( StreaMult_med_endpt );
#endif
	/* Destroy pool factory */
   // pj_caching_pool_destroy( &StreaMult_cp );
   // StreaMult_pool=NULL;
   // StreaMult_med_endpt=NULL;
	
}

/**
 * [StopStreamMulticast 停止rtp组播流数据用于停止音源时调用]
 * @return [description]
 */
int StopStreamMulticast()
{
	//onVoipCallStatusChanged(1,SIP_LINE_CALLING_STATUS_IDLE);
	en_RtpMultRecv = 0; //接收失能
	appData.rtpMulticastConf.curStatus = 0;
	rtpMulticastPageListen_stop = 1;
	//先清除所有SIP通话状态	2019-8-22
	//setAllVoipUserStatusChange(SIP_LINE_CALLING_STATUS_IDLE);
	setAllUserCallStatus(PJSIP_INV_STATE_DISCONNECTED);
	//关闭RTP组播接收
	DestroyStreamMulticastInfo();
	//callStatusLedRecover();
	return SUCCEED;
}

/**
 * [startStreaMulticast 启动组播接收或者发送]
 * @param  dir       [组播方向]
 * @param  multIP    [组播ip]
 * @param  localPort [端口号]
 * @param  codec_id  [编码]
 * @return           [状态]
 */
pj_status_t startStreaMulticast(pjmedia_dir dir,const char * multIP,int localPort,const char *codec_id)
{
	pjmedia_master_port *master_port = NULL;
	
	pjmedia_port *stream_port;
	char tmp[10];
	char addr[PJ_INET_ADDRSTRLEN];
	pj_status_t status;

	/* Default values */
	const pjmedia_codec_info *codec_info=NULL;
	pjmedia_codec_param codec_param;
   // pjmedia_dir dir = PJMEDIA_DIR_DECODING;
	pj_sockaddr_in remote_addr;
	pj_sockaddr_in mcast_addr;
	pj_uint16_t local_port = localPort;

#if defined(PJMEDIA_HAS_SRTP) && (PJMEDIA_HAS_SRTP != 0)
	/* SRTP variables */
	pj_bool_t use_srtp = PJ_FALSE;
	char tmp_tx_key[64];
	char tmp_rx_key[64];
	pj_str_t  srtp_tx_key = {NULL, 0};
	pj_str_t  srtp_rx_key = {NULL, 0};
	pj_str_t  srtp_crypto_suite = {NULL, 0};
	pj_bool_t is_dtls_client = PJ_FALSE;
	pj_bool_t is_dtls_server = PJ_FALSE;
	int tmp_key_len;
#endif    
	pj_bzero(&remote_addr, sizeof(remote_addr));
	if(stPjsuaVarData == NULL){
		WARNINGP("PJSIP Not Initialize...");
		return -1; 		
	}

	int mcast = 1; //组播使能
	{
		pj_str_t ip = pj_str(multIP);
		status = pj_sockaddr_in_init(&mcast_addr, &ip, 0);
		if (status != PJ_SUCCESS) {
			ERRORP("Invalid mcast address:%s",multIP);
			return 1;
		}
		if(dir == PJMEDIA_DIR_ENCODING){
			//方向为编码发送
			status = pj_sockaddr_in_init(&remote_addr, &ip, localPort);
			if (status != PJ_SUCCESS) {
				ERRORP("Invalid mcast address:%s",multIP);
				return 1;
			}
		}
		
	}
#if 0
	/* Must create a pool factory before we can allocate any memory. */
	pj_caching_pool_init(&StreaMult_cp, &pj_pool_factory_default_policy, 0);
	 /* 
	 * Initialize media endpoint.
	 * This will implicitly initialize PJMEDIA too.
	 */
	status = pjmedia_endpt_create(&StreaMult_cp.factory, NULL, 1, &StreaMult_med_endpt);
	if(status != PJ_SUCCESS){
		ERRORP("pjmedia endpt create Failed!");
		DestroyStreamMulticastInfo();
		return -1;
	}   
	/* Create memory pool for application purpose */
	StreaMult_pool = pj_pool_create( &StreaMult_cp.factory,     /* pool factory     */
			   "app",       /* pool name.       */
			   4000,        /* init size        */
			   4000,        /* increment size       */
			   NULL         /* callback on error    */
			   );
#endif

	/* Register all supported codecs */
/*
//创建时已注册
	status = init_codecs(StreaMult_med_endpt);
	if(status != PJ_SUCCESS){
		ERRORP("init codecs Failed!");
		DestroyStreamMulticastInfo();
		return -1;
	}
*/

/*

pj_status_t pjmedia_codec_mgr_enum_codecs   (   pjmedia_codec_mgr *     mgr,
unsigned *  count,
pjmedia_codec_info  info[],
unsigned *  prio 
)   
*/
	if(!checkRtpMultSourceIsValid()){
		NOTICE("check Rtp Mult Source Valid Failed!");
		stopRtpMultRecvSource();
		return -1; 
	}

	//切换音音源
	if( startRtpMultRecvSource(0)<=0){
		ERRORP("start Rtp Mult Recv Source failed");
		stopRtpMultRecvSource();
		return -1;  
	}

	//先停止
	//DestroyStreamMulticastInfo();
	//stopRtpMultRecvSource();

	if(codec_id == NULL){
		ERRORP("codec info get Error!");
		stopRtpMultRecvSource();
		return -1; 
	}

	/* Find which codec to use. */
	if (codec_id) {
		unsigned count = 1;
		pj_str_t str_codec_id = pj_str(codec_id);
		pjmedia_codec_mgr *codec_mgr = pjmedia_endpt_get_codec_mgr(stPjsuaVarData->med_endpt);


		pjmedia_codec_info  info[16]={0};
		count = 16;
		NOTICE("pjmedia_codec_mgr_enum_codecs.....");
		pjmedia_codec_mgr_enum_codecs(codec_mgr, &count,info,NULL);
		printf("count===%d\n",count);
		int i = 0;
		for ( ;i < count; ++i)
		{
			printf("info[%d].encoding_name=%s\n",i,info[i].encoding_name);
		}		
		count = 1;
		status = pjmedia_codec_mgr_find_codecs_by_id( codec_mgr,
								  &str_codec_id, &count,
								  &codec_info, NULL);
		if (status != PJ_SUCCESS || count == 0) {
			ERRORP("Error: unable to find codec %s", codec_id);
			stopRtpMultRecvSource();
			 return -1;
		}
	} else {
		/* Default to pcmu */
		NOTICE("get Default codec to pcmu");
		pjmedia_codec_mgr_get_codec_info( pjmedia_endpt_get_codec_mgr(stPjsuaVarData->med_endpt),
					0, &codec_info);
		if (status != PJ_SUCCESS) {
			ERRORP("Error: unable to find codec pcmu");
			stopRtpMultRecvSource();
			 return -1;
		}
		if(codec_info == NULL){
			ERRORP("codec info get Error!");
			stopRtpMultRecvSource();
			return -1; 
		}
	}

	char socketaddrBuffer[120]={0};
	pj_sockaddr_print(&mcast_addr,socketaddrBuffer,120,3);

	//NOTICE("Create stream based on program arguments: %s",socketaddrBuffer);
	NOTICE("codec_info:");  
	printf("\tpt:%d\n",codec_info->pt );
	printf("\tencoding_name:%s\n",codec_info->encoding_name );
	printf("\tclock_rate:%d\n",codec_info->clock_rate );
	printf("\tchannel_cnt:%d\n",codec_info->channel_cnt );

	/* Create stream based on program arguments */
	status = create_stream(stPjsuaVarData->pool, stPjsuaVarData->med_endpt, codec_info, dir, local_port, 
			   &remote_addr, mcast, &mcast_addr,
#if defined(PJMEDIA_HAS_SRTP) && (PJMEDIA_HAS_SRTP != 0)
			   use_srtp, &srtp_crypto_suite, 
			   &srtp_tx_key, &srtp_rx_key,
			   is_dtls_client, is_dtls_server,
#endif
			   &StreaMult_stream);
	if (status != PJ_SUCCESS){
		stopRtpMultRecvSource();
		return -1;
	}
	NOTICE("Get codec default param for info");
	 /* Get codec default param for info */
	status = pjmedia_codec_mgr_get_default_param(
					pjmedia_endpt_get_codec_mgr(stPjsuaVarData->med_endpt), 
					codec_info, 
					&codec_param);
	/* Should be ok, as create_stream() above succeeded */

	NOTICE("Get the port interface of the stream...");
	/* Get the port interface of the stream */
	status = pjmedia_stream_get_port( StreaMult_stream, &stream_port);


	NOTICE("Create sound device port...");
	/* Create sound device port. */
	if (dir == PJMEDIA_DIR_ENCODING_DECODING)
		status = pjmedia_snd_port_create(stPjsuaVarData->pool, -1, -1, 
					PJMEDIA_PIA_SRATE(&stream_port->info),
					PJMEDIA_PIA_CCNT(&stream_port->info),
					PJMEDIA_PIA_SPF(&stream_port->info),
					PJMEDIA_PIA_BITS(&stream_port->info),
					0, &StreaMult_snd_port);
	else if (dir == PJMEDIA_DIR_ENCODING)
		status = pjmedia_snd_port_create_rec(stPjsuaVarData->pool, -1, 
					PJMEDIA_PIA_SRATE(&stream_port->info),
					PJMEDIA_PIA_CCNT(&stream_port->info),
					PJMEDIA_PIA_SPF(&stream_port->info),
					PJMEDIA_PIA_BITS(&stream_port->info),
					0, &StreaMult_snd_port);
	else
		status = pjmedia_snd_port_create_player(stPjsuaVarData->pool, -1, 
					PJMEDIA_PIA_SRATE(&stream_port->info),
					PJMEDIA_PIA_CCNT(&stream_port->info),
					PJMEDIA_PIA_SPF(&stream_port->info),
					PJMEDIA_PIA_BITS(&stream_port->info),
					0, &StreaMult_snd_port);

	if (status != PJ_SUCCESS) {
		stopRtpMultRecvSource();
		return -1;
	}

	/* Connect sound port to stream */
	status = pjmedia_snd_port_connect( StreaMult_snd_port, stream_port );
	if(status != PJ_SUCCESS){
		stopRtpMultRecvSource();
		return -1;    
	}  

	/* Start streaming */
	pjmedia_stream_start(StreaMult_stream);
	/* Start deinitialization: */


	 /* Done */

	if (dir == PJMEDIA_DIR_DECODING)
	printf("Stream is active, dir is recv-only, local port is %d\n",
		   local_port);
	else if (dir == PJMEDIA_DIR_ENCODING)
	printf("Stream is active, dir is send-only, sending to %s:%d\n",
		   pj_inet_ntop2(pj_AF_INET(), &remote_addr.sin_addr, addr,
					 sizeof(addr)),
		   pj_ntohs(remote_addr.sin_port));
	else
	printf("Stream is active, send/recv, local port is %d, "
		   "sending to %s:%d\n",
		   local_port,
		   pj_inet_ntop2(pj_AF_INET(), &remote_addr.sin_addr, addr,
					 sizeof(addr)),
		   pj_ntohs(remote_addr.sin_port));

	return 0;    
}

#endif




#if 0

pj_status_t init_local_rtp()
{
	if (m_bInitMedia)
	{
		destroy_media();
	}


	//g_local_port = local_port;


	pj_caching_pool_init(&cp, &pj_pool_factory_default_policy, 0);
		pool = pj_pool_create(&(cp.factory), "test", 1000, 512, NULL);


	int status;

	//status = pjmedia_endpt_create(&cp.factory,  pjsip_endpt_get_ioqueue(pjsua_get_pjsip_endpt()), 0, &med_endpt);


	status = pjmedia_endpt_create(&cp.factory, NULL, 1, &med_endpt);


	status = pjmedia_rtp_session_init(&video.out_sess, 97, pj_rand());
	status = pjmedia_rtp_session_init(&video.in_sess, 97, 0);


	status = pjmedia_transport_udp_create(med_endpt, NULL, g_local_port,  0, &video.transport);


	m_bInitMedia = true;
	video.active = true;
	return 0;
}



status = pjmedia_transport_attach(video.transport, &video, 
//&info.rem_addr, 
&remote_addr,
NULL, 
sizeof(pj_sockaddr_in),
&on_rx_rtp,
NULL);

struct media_stream
{
	/* Static: */
	unsigned	call_index;	   /* Call owner.	*/
	unsigned	media_index;	   /* Media index in call.	*/
	pjmedia_transport   *transport;	   /* To send/recv RTP/RTCP	*/


	/* Active? */
	pj_bool_t	active;	   /* Non-zero if is in call.	*/


	/* Current stream info: */
	pjmedia_stream_info	si;	   /* Current stream info.	*/


	/* More info: */
	unsigned	clock_rate;	   /* clock rate	*/
	unsigned	samples_per_frame; /* samples per frame	*/
	unsigned	bytes_per_frame;   /* frame size.	*/


	/* RTP session: */
	pjmedia_rtp_session	out_sess;	   /* outgoing RTP session	*/
	pjmedia_rtp_session	in_sess;	   /* incoming RTP session	*/


	/* RTCP stats: */
	pjmedia_rtcp_session rtcp;	   /* incoming RTCP session.	*/


	/* Thread: */
	pj_bool_t	thread_quit_flag;  /* Stop media thread.	*/
	pj_thread_t	*thread;	   /* Media thread.	*/
};











	struct call *call;

	struct media_stream *audio;

	const pjmedia_sdp_session *local_sdp, *remote_sdp;

	struct codec *codec_desc = NULL;

	unsigned i;

 

	call = inv->mod_data[mod_siprtp.id];

	audio =&call->media[0];
 

	//如果是呼叫中更新，则销毁已经存在的媒体

	if(audio->thread != NULL)
	destroy_call_media(call->index);
	pjmedia_sdp_neg_get_active_local(inv->neg,&local_sdp);
	pjmedia_sdp_neg_get_active_remote(inv->neg,&remote_sdp); 

	status = pjmedia_stream_info_from_sdp(&audio->si, inv->pool,app.med_endpt,

					  local_sdp, remote_sdp, 0);

	if(status!= PJ_SUCCESS) {
		app_perror(THIS_FILE, "从SDP创建流信息错误", status);
		return;
	}
	//从编解码器描述中取留存编解码器信息
 

   audio->clock_rate = audio->si.fmt.clock_rate;

   audio->samples_per_frame = audio->clock_rate *codec_desc->ptime / 1000;

   audio->bytes_per_frame = codec_desc->bit_rate *codec_desc->ptime / 1000 / 8;

 

 

	pjmedia_rtp_session_init(&audio->out_sess, audio->si.tx_pt,pj_rand());
	pjmedia_rtp_session_init(&audio->in_sess, audio->si.fmt.pt,0);
	pjmedia_rtcp_init(&audio->rtcp, "rtcp",audio->clock_rate,audio->samples_per_frame, 0); 
	//!!!附加媒体到传输端口
	status= pjmedia_transport_attach(audio->transport, audio,

					  &audio->si.rem_addr,

					  &audio->si.rem_rtcp,

					  sizeof(pj_sockaddr_in),

					  &on_rx_rtp,

					  &on_rx_rtcp);

	if(status!= PJ_SUCCESS) {

	app_perror(THIS_FILE, "错误发生于pjmedia_transport_attach()",status);

	return;

	}

 

	//启动媒体线程

   audio->thread_quit_flag = 0;

#if PJ_HAS_THREADS

	status =pj_thread_create( inv->pool, "media",&media_thread, audio,

				   0, 0, &audio->thread);

	if(status != PJ_SUCCESS) {

	app_perror(THIS_FILE,"创建媒体线程错误", status);

	return;

	}

#endif

 

	//设置媒体状态可用

   audio->active = PJ_TRUE;

#endif

#if 0
//extern struct pjmedia_stream;
pj_status_t pjmediaMulticastCreate()
{
	 pjmedia_stream **p_stream;

	pjmedia_endpt *med_endpt;

	pj_caching_pool cp;

	pj_status_t status;

	 pjmedia_stream *stream = NULL;

	pj_uint16_t local_port = 3000; //本地端口号

	pjmedia_codec_info codec_info;

	pj_sockaddr_in rem_addr;


   // p_stream = &stream;

	codec_info.type = PJMEDIA_TYPE_AUDIO; //设置类型音频
	codec_info.pt   = PJMEDIA_RTP_PT_G722;//设置负载类型 9--G.722
	codec_info.encoding_name = pj_str(CODEC_G722);
	codec_info.clock_rate = 16000;//采样率
	codec_info.channel_cnt = 2;//通道数

	rem_addr.sin_family = PJ_AF_INET;//协议类型
	rem_addr.sin_port = pj_htons(6666);;
	pj_str_t remIp = pj_str("**************");
	pj_inet_aton(&remIp, &rem_addr.sin_addr);


	WARNINGP("pjmediaMulticastCreate...");
	/* Must create a pool factory before we can allocate any memory. */

	pj_caching_pool_init(&cp, &pj_pool_factory_default_policy, 0);
	pj_pool_t       *pool ;  
	pool = pj_pool_create(&cp.factory , NULL , 512 , 512 , NULL );
	if(pool == NULL)
	{   
		ERRORP("pj_pool_create failed\n");
		return -1;
	} 
   // stream = PJ_POOL_ZALLOC_T(pool,  pjmedia_stream);
	NOTICE("pj_pool_create Succeed!");
	/*

	 * Initialize media endpoint.

	 * This will implicitly initialize PJMEDIA too.

	 */

	status = pjmedia_endpt_create(&cp.factory, NULL, 1, &med_endpt);

	PJ_ASSERT_RETURN(status == PJ_SUCCESS, 1);
   // status = codec_init(med_endpt);  
	NOTICE("pjmedia_endpt_create Succeed!");

	pjmedia_stream_info info;

	pjmedia_transport *transport = NULL;


	//Reset stream info

	pj_bzero(&info, sizeof(info));  

	//Initialize stream info formats

	info.type = PJMEDIA_TYPE_AUDIO;

	//info.dir = dir;
	pj_memcpy(&info.fmt, &codec_info, sizeof(pjmedia_codec_info));

	info.tx_pt = codec_info.pt;

	info.rx_pt = codec_info.pt;

	info.ssrc = pj_rand();
	const pj_str_t multicastAddrIpv4 = pj_str("*********");

	pj_bzero(&info, sizeof(info));
	info.type = PJMEDIA_TYPE_AUDIO;
	info.proto = PJMEDIA_TP_PROTO_RTP_AVP;
	info.dir = PJMEDIA_DIR_ENCODING_DECODING;
	pj_sockaddr_in_init(&info.rem_addr.ipv4, &multicastAddrIpv4, local_port);
	pj_sockaddr_in_init(&info.rem_rtcp.ipv4, &multicastAddrIpv4, local_port+1);
	//pj_memcpy(&info.fmt, ci[0], sizeof(pjmedia_codec_info));
	info.param = NULL;
	info.tx_pt = codec_info.pt;
	info.tx_event_pt = 101;
	info.rx_event_pt = 101;
	info.ssrc = pj_rand();
	info.jb_init = info.jb_min_pre = info.jb_max_pre = info.jb_max = -1;



	 pj_memcpy(&info.rem_addr, &rem_addr, sizeof(pj_sockaddr_in));
	 //********* multicast channel
	
	NOTICE("pj_sockaddr_in_init before!");
	status = pj_sockaddr_in_init(&info.rem_addr.ipv4, &multicastAddrIpv4, 0);

	 if(status != PJ_SUCCESS) {
		 WARNINGP("initialize IPv4 socket address failed");
		 return status;
	 }
	 NOTICE("pj_sockaddr_in_init Succeed!");
	 //Create media transport
	 status = pjmedia_transport_udp_create(med_endpt, NULL, local_port, 0, &transport);
	 if(status != PJ_SUCCESS) {
		 WARNINGP("pjmedia transport udp create failed");
		 return status;
	 }
	 NOTICE("pjmedia_transport_udp_create Succeed!");
	 status = pjmedia_stream_create(med_endpt, pool, &info, transport, NULL, &stream);
	 if (status != PJ_SUCCESS) {
		 WARNINGP("error creating stream");
		 pjmedia_transport_close(transport);
		 return status;
	 }
	 NOTICE("pjmedia_stream_create Succeed!");

/*
	 //!!!附加媒体到传输端口
	status= pjmedia_transport_attach(audio->transport, audio,

					  &audio->si.rem_addr,

					  &audio->si.rem_rtcp,

					  sizeof(pj_sockaddr_in),

					  &on_rx_rtp,

					  &on_rx_rtcp);

	if(status!= PJ_SUCCESS) {

	app_perror(THIS_FILE, "错误发生于pjmedia_transport_attach()",status);

	return;

	}
*/
	 //Start streaming
	 pjmedia_stream_start(stream);
	 NOTICE("pjmedia_stream_start Succeed!");
	 return PJ_SUCCESS;
}
#endif

#if 0
/**
 * 组播流媒体信息
 */
pj_caching_pool StreaMult_cp;
pjmedia_stream *StreaMult_stream = NULL;
pj_pool_t *StreaMult_pool;
pjmedia_endpt *StreaMult_med_endpt;
pjmedia_snd_port *StreaMult_snd_port = NULL;


void DestroyStreamMulticastInfo()
{
	NOTICE("   Destroy Stream Multicast Info...");
	if (StreaMult_snd_port) {
		pjmedia_snd_port_destroy( StreaMult_snd_port );        
	}

	 /* Destroy stream */
	if (StreaMult_stream) {
		pjmedia_transport *tp;

		tp = pjmedia_stream_get_transport(StreaMult_stream);
		pjmedia_stream_destroy(StreaMult_stream);        
		pjmedia_transport_close(tp);
		StreaMult_stream = NULL;
	}
	/* Release application pool */
	if(StreaMult_pool)pj_pool_release( StreaMult_pool );

	/* Destroy media endpoint. */
	if(StreaMult_med_endpt) pjmedia_endpt_destroy( StreaMult_med_endpt );

	/* Destroy pool factory */
	pj_caching_pool_destroy( &StreaMult_cp );
	StreaMult_pool=NULL;
	StreaMult_med_endpt=NULL;
	StreaMult_snd_port = NULL;
}
   

pj_status_t startStreaMulticast(pjmedia_dir dir,const char * multIP,int localPort,const char *codec_id)
{
	pjmedia_master_port *master_port = NULL;
	
	pjmedia_port *stream_port;
	char tmp[10];
	char addr[PJ_INET_ADDRSTRLEN];
	pj_status_t status;

	/* Default values */
	const pjmedia_codec_info *codec_info=NULL;
	pjmedia_codec_param codec_param;
   // pjmedia_dir dir = PJMEDIA_DIR_DECODING;
	pj_sockaddr_in remote_addr;
	pj_sockaddr_in mcast_addr;
	pj_uint16_t local_port = localPort;

#if defined(PJMEDIA_HAS_SRTP) && (PJMEDIA_HAS_SRTP != 0)
	/* SRTP variables */
	pj_bool_t use_srtp = PJ_FALSE;
	char tmp_tx_key[64];
	char tmp_rx_key[64];
	pj_str_t  srtp_tx_key = {NULL, 0};
	pj_str_t  srtp_rx_key = {NULL, 0};
	pj_str_t  srtp_crypto_suite = {NULL, 0};
	pj_bool_t is_dtls_client = PJ_FALSE;
	pj_bool_t is_dtls_server = PJ_FALSE;
	int tmp_key_len;
#endif    
	 pj_bzero(&remote_addr, sizeof(remote_addr));
	int mcast = 1; //组播使能
	{
		pj_str_t ip = pj_str(multIP);
		status = pj_sockaddr_in_init(&mcast_addr, &ip, 0);
		if (status != PJ_SUCCESS) {
			ERRORP("Invalid mcast address:%s",multIP);
			return 1;
		}
	}
	/* Must create a pool factory before we can allocate any memory. */
	pj_caching_pool_init(&StreaMult_cp, &pj_pool_factory_default_policy, 0);
	 /* 
	 * Initialize media endpoint.
	 * This will implicitly initialize PJMEDIA too.
	 */
	status = pjmedia_endpt_create(&StreaMult_cp.factory, NULL, 1, &StreaMult_med_endpt);
	if(status != PJ_SUCCESS){
		ERRORP("pjmedia endpt create Failed!");
		DestroyStreamMulticastInfo();
		return -1;
	}   
	/* Create memory pool for application purpose */
	StreaMult_pool = pj_pool_create( &StreaMult_cp.factory,     /* pool factory     */
			   "app",       /* pool name.       */
			   4000,        /* init size        */
			   4000,        /* increment size       */
			   NULL         /* callback on error    */
			   );

	/* Register all supported codecs */
	status = init_codecs(StreaMult_med_endpt);
	if(status != PJ_SUCCESS){
		ERRORP("init codecs Failed!");
		DestroyStreamMulticastInfo();
		return -1;
	}

/*

pj_status_t pjmedia_codec_mgr_enum_codecs   (   pjmedia_codec_mgr *     mgr,
unsigned *  count,
pjmedia_codec_info  info[],
unsigned *  prio 
)   
*/

	/* Find which codec to use. */
	if (codec_id) {
		unsigned count = 1;
		pj_str_t str_codec_id = pj_str(codec_id);
		pjmedia_codec_mgr *codec_mgr = pjmedia_endpt_get_codec_mgr(StreaMult_med_endpt);


		pjmedia_codec_info  info[16]={0};
		count = 16;
		pjmedia_codec_mgr_enum_codecs(codec_mgr, &count,info,NULL);
		printf("count===%d\n",count);
		int i = 0;
		for ( ;i < count; ++i)
		{
			printf("info[%d].encoding_name=%s\n",i,info[i].encoding_name);
		}
		if (status != PJ_SUCCESS || count == 0) {
			ERRORP("Error: unable to find codec %s", codec_id);
			DestroyStreamMulticastInfo();
			 return -1;
		}
		status = pjmedia_codec_mgr_find_codecs_by_id( codec_mgr,
								  &str_codec_id, &count,
								  &codec_info, NULL);
		
	} else {
		/* Default to pcmu */
		NOTICE("get Default codec to pcmu");
		pjmedia_codec_mgr_get_codec_info( pjmedia_endpt_get_codec_mgr(StreaMult_med_endpt),
					0, &codec_info);
		if (status != PJ_SUCCESS) {
			ERRORP("Error: unable to find codec pcmu");
			DestroyStreamMulticastInfo();
			 return -1;
		}
		if(codec_info == NULL){
			ERRORP("codec info get Error!");
			DestroyStreamMulticastInfo();
			return -1; 
		}
	}

	char socketaddrBuffer[120]={0};
	pj_sockaddr_print(&mcast_addr,socketaddrBuffer,120,3);

	NOTICE("Create stream based on program arguments::  %s",socketaddrBuffer);
	/* Create stream based on program arguments */
	status = create_stream(StreaMult_pool, StreaMult_med_endpt, codec_info, dir, local_port, 
			   &remote_addr, mcast, &mcast_addr,
#if defined(PJMEDIA_HAS_SRTP) && (PJMEDIA_HAS_SRTP != 0)
			   use_srtp, &srtp_crypto_suite, 
			   &srtp_tx_key, &srtp_rx_key,
			   is_dtls_client, is_dtls_server,
#endif
			   &StreaMult_stream);
	if (status != PJ_SUCCESS){
		DestroyStreamMulticastInfo();
		return -1;
	}
	NOTICE("Get codec default param for info");
	 /* Get codec default param for info */
	status = pjmedia_codec_mgr_get_default_param(
					pjmedia_endpt_get_codec_mgr(StreaMult_med_endpt), 
					codec_info, 
					&codec_param);
	/* Should be ok, as create_stream() above succeeded */

	NOTICE("Get the port interface of the stream...");
	/* Get the port interface of the stream */
	status = pjmedia_stream_get_port( StreaMult_stream, &stream_port);


	NOTICE("Create sound device port...");
	/* Create sound device port. */
	if (dir == PJMEDIA_DIR_ENCODING_DECODING)
		status = pjmedia_snd_port_create(StreaMult_pool, -1, -1, 
					PJMEDIA_PIA_SRATE(&stream_port->info),
					PJMEDIA_PIA_CCNT(&stream_port->info),
					PJMEDIA_PIA_SPF(&stream_port->info),
					PJMEDIA_PIA_BITS(&stream_port->info),
					0, &StreaMult_snd_port);
	else if (dir == PJMEDIA_DIR_ENCODING)
		status = pjmedia_snd_port_create_rec(StreaMult_pool, -1, 
					PJMEDIA_PIA_SRATE(&stream_port->info),
					PJMEDIA_PIA_CCNT(&stream_port->info),
					PJMEDIA_PIA_SPF(&stream_port->info),
					PJMEDIA_PIA_BITS(&stream_port->info),
					0, &StreaMult_snd_port);
	else
		status = pjmedia_snd_port_create_player(StreaMult_pool, -1, 
					PJMEDIA_PIA_SRATE(&stream_port->info),
					PJMEDIA_PIA_CCNT(&stream_port->info),
					PJMEDIA_PIA_SPF(&stream_port->info),
					PJMEDIA_PIA_BITS(&stream_port->info),
					0, &StreaMult_snd_port);

	if (status != PJ_SUCCESS) {
		DestroyStreamMulticastInfo();
		return -1;    
	}

	/* Connect sound port to stream */
	status = pjmedia_snd_port_connect( StreaMult_snd_port, stream_port );
	if(status != PJ_SUCCESS){
		DestroyStreamMulticastInfo();
		return -1;    
	}  

	/* Start streaming */
	pjmedia_stream_start(StreaMult_stream);
	/* Start deinitialization: */


	 /* Done */

	if (dir == PJMEDIA_DIR_DECODING)
	printf("Stream is active, dir is recv-only, local port is %d\n",
		   local_port);
	else if (dir == PJMEDIA_DIR_ENCODING)
	printf("Stream is active, dir is send-only, sending to %s:%d\n",
		   pj_inet_ntop2(pj_AF_INET(), &remote_addr.sin_addr, addr,
					 sizeof(addr)),
		   pj_ntohs(remote_addr.sin_port));
	else
	printf("Stream is active, send/recv, local port is %d, "
		   "sending to %s:%d\n",
		   local_port,
		   pj_inet_ntop2(pj_AF_INET(), &remote_addr.sin_addr, addr,
					 sizeof(addr)),
		   pj_ntohs(remote_addr.sin_port));


	return 0;    
	int count = 30;
	while(count){
		sleep(1);
	}
	NOTICE("DestroyStreamMulticastInfo");
	DestroyStreamMulticastInfo();
	return 0;    
}

#endif






#if 0

/*
 * main()
 */
pj_status_t startStreaMulticast(pjmedia_dir dir,const char * multIP,int localPort,char *codec_id)
{
	pj_caching_pool cp;
	pjmedia_endpt *med_endpt;
	pj_pool_t *pool;
	pjmedia_port *rec_file_port = NULL, *play_file_port = NULL;
	pjmedia_master_port *master_port = NULL;
	pjmedia_snd_port *snd_port = NULL;
	pjmedia_stream *stream = NULL;
	pjmedia_port *stream_port;
	char tmp[10];
	char addr[PJ_INET_ADDRSTRLEN];
	pj_status_t status; 

#if defined(PJMEDIA_HAS_SRTP) && (PJMEDIA_HAS_SRTP != 0)
	/* SRTP variables */
	pj_bool_t use_srtp = PJ_FALSE;
	char tmp_tx_key[64];
	char tmp_rx_key[64];
	pj_str_t  srtp_tx_key = {NULL, 0};
	pj_str_t  srtp_rx_key = {NULL, 0};
	pj_str_t  srtp_crypto_suite = {NULL, 0};
	pj_bool_t is_dtls_client = PJ_FALSE;
	pj_bool_t is_dtls_server = PJ_FALSE;
	int tmp_key_len;
#endif

	/* Default values */
	const pjmedia_codec_info *codec_info;
	pjmedia_codec_param codec_param;
//    pjmedia_dir dir = PJMEDIA_DIR_DECODING;
	pj_sockaddr_in remote_addr;
	pj_sockaddr_in mcast_addr;
	pj_uint16_t local_port = localPort;
//    char *codec_id = NULL;
	char *rec_file = NULL;
	char *play_file = NULL;
	int mcast = 1;

	{
		pj_str_t ip = pj_str(multIP);
		status = pj_sockaddr_in_init(&mcast_addr, &ip, 0);
		if (status != PJ_SUCCESS) {
			ERRORP("Invalid mcast address:%s",multIP);
			return 1;
		}
	}
	//WARNINGP("startStreaMulticast...");

	pj_bzero(&remote_addr, sizeof(remote_addr));

	/* init PJLIB : */
//    status = pj_init();
	PJ_ASSERT_RETURN(status == PJ_SUCCESS, 1);

	/* Must create a pool factory before we can allocate any memory. */
	pj_caching_pool_init(&cp, &pj_pool_factory_default_policy, 0);

	/* 
	 * Initialize media endpoint.
	 * This will implicitly initialize PJMEDIA too.
	 */
	status = pjmedia_endpt_create(&cp.factory, NULL, 1, &med_endpt);
	PJ_ASSERT_RETURN(status == PJ_SUCCESS, 1);

	/* Create memory pool for application purpose */
	pool = pj_pool_create( &cp.factory,     /* pool factory     */
			   "app",       /* pool name.       */
			   4000,        /* init size        */
			   4000,        /* increment size       */
			   NULL         /* callback on error    */
			   );


	/* Register all supported codecs */
	status = init_codecs(med_endpt);
	PJ_ASSERT_RETURN(status == PJ_SUCCESS, 1);


	/* Find which codec to use. */
	if (codec_id) {
	unsigned count = 1;
	pj_str_t str_codec_id = pj_str(codec_id);
	pjmedia_codec_mgr *codec_mgr = pjmedia_endpt_get_codec_mgr(med_endpt);
	status = pjmedia_codec_mgr_find_codecs_by_id( codec_mgr,
							  &str_codec_id, &count,
							  &codec_info, NULL);
	if (status != PJ_SUCCESS) {
		printf("Error: unable to find codec %s\n", codec_id);
		return 1;
	}
	} else {
	/* Default to pcmu */
	pjmedia_codec_mgr_get_codec_info( pjmedia_endpt_get_codec_mgr(med_endpt),
					  0, &codec_info);
	}

	/* Create stream based on program arguments */
	status = create_stream(pool, med_endpt, codec_info, dir, local_port, 
			   &remote_addr, mcast, &mcast_addr,
#if defined(PJMEDIA_HAS_SRTP) && (PJMEDIA_HAS_SRTP != 0)
			   use_srtp, &srtp_crypto_suite, 
			   &srtp_tx_key, &srtp_rx_key,
			   is_dtls_client, is_dtls_server,
#endif
			   &stream);
	if (status != PJ_SUCCESS)
	goto on_exit;

	/* Get codec default param for info */
	status = pjmedia_codec_mgr_get_default_param(
					pjmedia_endpt_get_codec_mgr(med_endpt), 
					codec_info, 
					&codec_param);
	/* Should be ok, as create_stream() above succeeded */
	pj_assert(status == PJ_SUCCESS);

	/* Get the port interface of the stream */
	status = pjmedia_stream_get_port( stream, &stream_port);
	PJ_ASSERT_RETURN(status == PJ_SUCCESS, 1);


	if (play_file) {
	unsigned wav_ptime;

	wav_ptime = PJMEDIA_PIA_PTIME(&stream_port->info);
	status = pjmedia_wav_player_port_create(pool, play_file, wav_ptime,
						0, -1, &play_file_port);
	if (status != PJ_SUCCESS) {
		app_perror(THIS_FILE, "Unable to use file", status);
		goto on_exit;
	}

	status = pjmedia_master_port_create(pool, play_file_port, stream_port,
						0, &master_port);
	if (status != PJ_SUCCESS) {
		app_perror(THIS_FILE, "Unable to create master port", status);
		goto on_exit;
	}

	status = pjmedia_master_port_start(master_port);
	if (status != PJ_SUCCESS) {
		app_perror(THIS_FILE, "Error starting master port", status);
		goto on_exit;
	}

	printf("Playing from WAV file %s..\n", play_file);

	} else if (rec_file) {

	status = pjmedia_wav_writer_port_create(pool, rec_file,
							PJMEDIA_PIA_SRATE(&stream_port->info),
							PJMEDIA_PIA_CCNT(&stream_port->info),
							PJMEDIA_PIA_SPF(&stream_port->info),
							PJMEDIA_PIA_BITS(&stream_port->info),
						0, 0, &rec_file_port);
	if (status != PJ_SUCCESS) {
		app_perror(THIS_FILE, "Unable to use file", status);
		goto on_exit;
	}

	status = pjmedia_master_port_create(pool, stream_port, rec_file_port, 
						0, &master_port);
	if (status != PJ_SUCCESS) {
		app_perror(THIS_FILE, "Unable to create master port", status);
		goto on_exit;
	}

	status = pjmedia_master_port_start(master_port);
	if (status != PJ_SUCCESS) {
		app_perror(THIS_FILE, "Error starting master port", status);
		goto on_exit;
	}

	printf("Recording to WAV file %s..\n", rec_file);
	
	} else {

	/* Create sound device port. */
	if (dir == PJMEDIA_DIR_ENCODING_DECODING)
		status = pjmedia_snd_port_create(pool, -1, -1, 
					PJMEDIA_PIA_SRATE(&stream_port->info),
					PJMEDIA_PIA_CCNT(&stream_port->info),
					PJMEDIA_PIA_SPF(&stream_port->info),
					PJMEDIA_PIA_BITS(&stream_port->info),
					0, &snd_port);
	else if (dir == PJMEDIA_DIR_ENCODING)
		status = pjmedia_snd_port_create_rec(pool, -1, 
					PJMEDIA_PIA_SRATE(&stream_port->info),
					PJMEDIA_PIA_CCNT(&stream_port->info),
					PJMEDIA_PIA_SPF(&stream_port->info),
					PJMEDIA_PIA_BITS(&stream_port->info),
					0, &snd_port);
	else
		status = pjmedia_snd_port_create_player(pool, -1, 
					PJMEDIA_PIA_SRATE(&stream_port->info),
					PJMEDIA_PIA_CCNT(&stream_port->info),
					PJMEDIA_PIA_SPF(&stream_port->info),
					PJMEDIA_PIA_BITS(&stream_port->info),
					0, &snd_port);


	if (status != PJ_SUCCESS) {
		app_perror(THIS_FILE, "Unable to create sound port", status);
		goto on_exit;
	}

	/* Connect sound port to stream */
	status = pjmedia_snd_port_connect( snd_port, stream_port );
	PJ_ASSERT_RETURN(status == PJ_SUCCESS, 1);

	}

	/* Start streaming */
	pjmedia_stream_start(stream);


	/* Done */

	if (dir == PJMEDIA_DIR_DECODING)
	printf("Stream is active, dir is recv-only, local port is %d\n",
		   local_port);
	else if (dir == PJMEDIA_DIR_ENCODING)
	printf("Stream is active, dir is send-only, sending to %s:%d\n",
		   pj_inet_ntop2(pj_AF_INET(), &remote_addr.sin_addr, addr,
					 sizeof(addr)),
		   pj_ntohs(remote_addr.sin_port));
	else
	printf("Stream is active, send/recv, local port is %d, "
		   "sending to %s:%d\n",
		   local_port,
		   pj_inet_ntop2(pj_AF_INET(), &remote_addr.sin_addr, addr,
					 sizeof(addr)),
		   pj_ntohs(remote_addr.sin_port));
	
	while(1)
	{
		sleep(1);
	}
	
	/* Start deinitialization: */
on_exit:

	/* Destroy sound device */
	if (snd_port) {
	pjmedia_snd_port_destroy( snd_port );
	PJ_ASSERT_RETURN(status == PJ_SUCCESS, 1);
	}

	/* If there is master port, then we just need to destroy master port
	 * (it will recursively destroy upstream and downstream ports, which
	 * in this case are file_port and stream_port).
	 */
	if (master_port) {
	pjmedia_master_port_destroy(master_port, PJ_TRUE);
	play_file_port = NULL;
	stream = NULL;
	}

	/* Destroy stream */
	if (stream) {
	pjmedia_transport *tp;

	tp = pjmedia_stream_get_transport(stream);
	pjmedia_stream_destroy(stream);
	
	pjmedia_transport_close(tp);
	}

	/* Destroy file ports */
	if (play_file_port)
	pjmedia_port_destroy( play_file_port );
	if (rec_file_port)
	pjmedia_port_destroy( rec_file_port );


	/* Release application pool */
	pj_pool_release( pool );

	/* Destroy media endpoint. */
	pjmedia_endpt_destroy( med_endpt );

	/* Destroy pool factory */
	pj_caching_pool_destroy( &cp );

	/* Shutdown PJLIB */
	pj_shutdown();


	return (status == PJ_SUCCESS) ? 0 : 1;
}
#endif









/**
 * [readRtpMulticastConfig 读取配置文件信息]
 */
static void readRtpMulticastConfig()
{

	#if 0
	int i = 1;
	char headBuf[10]={0};

	memset(&appData.rtpMulticastConf,0,sizeof(rtpMulticastConf_t));
	int temp = -1;
	#if 0
	temp = READ_CONFIG_INT("rtpMult","prio");	
	appData.rtpMulticastConf.prio = temp == -1? 0:temp;
	#endif
	appData.rtpMulticastConf.prio = 0;
	for (; i <= RTP_MULTICAST_URL_COUNT_MAX; ++i)
	{
		memset(headBuf,0,10);
		sprintf(headBuf,"rtpMult%d",i);
		READ_CONFIG_STR(headBuf,"name",appData.rtpMulticastConf.rtpMulticast[i-1].name);
		READ_CONFIG_STR(headBuf,"addr",appData.rtpMulticastConf.rtpMulticast[i-1].address);
		appData.rtpMulticastConf.rtpMulticast[i-1].socketfd = -1;
	}
#if 0
	//测试
	sprintf(appData.rtpMulticastConf.rtpMulticast[0].name,"%s","test");
	sprintf(appData.rtpMulticastConf.rtpMulticast[0].address,"%s","*********:13666");
	appData.rtpMulticastConf.prio = 1;	
#endif

	#endif

}

/**
 * [saveRtpMulticastConfig 保存组播配置文件信息]
 */
void saveRtpMulticastConfig()
{
	#if 0
	configHandleInit();
	int i = 1;
	char headBuf[10]={0};
	
	SAVE_CONFIG_INT("rtpMult","prio",appData.rtpMulticastConf.prio);	
	for (; i <= RTP_MULTICAST_URL_COUNT_MAX; ++i)
	{
		memset(headBuf,0,10);
		sprintf(headBuf,"rtpMult%d",i);
		if(strlen(appData.rtpMulticastConf.rtpMulticast[i-1].name)!=0){
			SAVE_CONFIG_STR(headBuf,"name",appData.rtpMulticastConf.rtpMulticast[i-1].name);
		}
		if(strlen(appData.rtpMulticastConf.rtpMulticast[i-1].address)!=0){
			SAVE_CONFIG_STR(headBuf,"addr",appData.rtpMulticastConf.rtpMulticast[i-1].address);
		}
	}
	saveConfigListToFile();
	configHandleFree();
	#endif
}


#define MAX_BUF_SIZE                 2048

int checkPayloadIsAvailability(unsigned char pt)
{
	if(pt == PJMEDIA_RTP_PT_PCMU	
		||pt == PJMEDIA_RTP_PT_GSM
		||pt == PJMEDIA_RTP_PT_PCMA
		||pt == PJMEDIA_RTP_PT_G722
		||pt == PJMEDIA_RTP_PT_G723){
		return 1;
	}
	return 0;
}

/**
 * [buildCodesID description]
 * @param  pt          [description]
 * @param  clock_rate  [description]
 * @param  channel_cnt [description]
 * @return             [description]
 */
const char * buildCodesID(unsigned char pt,unsigned clock_rate,unsigned char channel_cnt)
{
	static char codeID[20]={0};
	if(!checkPayloadIsAvailability( pt) ){
		return NULL;
	}
	if(channel_cnt != 1 && channel_cnt != 2 ){
		return NULL;
	}
	const char * codeName=NULL;
	switch(pt){
		case PJMEDIA_RTP_PT_PCMU: codeName = "pcmu";break;
		case PJMEDIA_RTP_PT_GSM: codeName = "gsm";break;
		case PJMEDIA_RTP_PT_PCMA: codeName = "pcma";break;
		case PJMEDIA_RTP_PT_G722: codeName = "g722";break;
		case PJMEDIA_RTP_PT_G723: codeName = "g723";break;	
		default:
			WARNINGP("No Found payload (%d) Def:pcmu",pt);		
			codeName = "pcmu";break;
	}

	bzero(codeID,20);
	sprintf(codeID,"%s/%d/%d",codeName,clock_rate,channel_cnt);
	return codeName;
}


#if 0

void * RtpMulticastListen_Pthread(void * args)
{
	int i = 0;	
	int port = -1;
	char address[RTP_MULTICAST_URL_LEN_MAX]={0};
	appData.rtpMulticastConf.pthreadFlag = PTHREAD_RUNING;//线程正在运行
	rtpMulticast_t * rtpMulticastPtr=NULL;
	for (; i < RTP_MULTICAST_URL_COUNT_MAX; ++i)
	{		
		if(appData.rtpMulticastConf.rtpMulticast[i].socketfd > 0){
			close(appData.rtpMulticastConf.rtpMulticast[i].socketfd);//关闭之前打开的socket
			NOTICE("Close rtp Multicast socket %d.",i+1);
		}
		if(strlen(appData.rtpMulticastConf.rtpMulticast[i].address) == 0){
			continue;
		}

		if(appData.rtpMulticastConf.prio != (i+1)){
			continue;
		}
		/* found multicase address */
		rtpMulticastPtr = &appData.rtpMulticastConf.rtpMulticast[i];
		char temp[RTP_MULTICAST_URL_LEN_MAX];
	
		bzero(temp,RTP_MULTICAST_URL_LEN_MAX);
		strcpy(temp,appData.rtpMulticastConf.rtpMulticast[i].address);
		char *p = strtok(temp,":");
		if(!p){ 
			WARNINGP("No Found Multicast Address!");
			continue;
		}
		bzero(address,RTP_MULTICAST_URL_LEN_MAX);
		strcpy(address,p);//读取地址
		if(!(p = strtok(NULL,":") ) ){continue;}
		port = atoi(p);
		NOTICE("start Multicast address[%s:%d]...",address,port);
		if(port < 0){
			ERRORP("set Multicast Port Failed!");
			continue;
		}
		break;
	}

	if(i == RTP_MULTICAST_URL_COUNT_MAX){
		WARNINGP("No Found Rtp Multicast Address Prio-%d",appData.rtpMulticastConf.prio);
		appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;//线程正在运行		
		pthread_exit(NULL);
	}
	//PJ_THREAD_REGISTER_MACRO(rtpMultListen);
	struct sockaddr_in server_addr;
	fd_set readfd;
	int rxlen = 0;
	int ret = -1;
	unsigned char RxBuf[MAX_BUF_SIZE];
	
	struct timeval timeout;	

	FD_ZERO(&readfd);        // 清空读文件描述集合
	FD_SET(rtpMulticastPtr->socketfd, &readfd); // 注册套接字文件描述符
	timeout.tv_sec = 5;//5s接收超时超时
	timeout.tv_usec = 0;// 超时时间	
	unsigned char rtpPt = 0;
	int isOpen=0;
	
	unsigned char dspBuf[1024*50]={0};
	unsigned char pcmbuf[1024*50]={0};
	int writeByteCount=0;
	
	/* Default values */
	const pjmedia_codec_info *codec_info=NULL;
	pjmedia_codec_param codec_param;
	pjmedia_codec *codec;
	pjmedia_frame frm_pcm, frm_bit, out_frm, frames[4];
	pj_status_t status;
	const pjmedia_rtp_hdr *hdr;
	const void *payload;
	unsigned payload_len;
	PJ_THREAD_REGISTER_MACRO(rtpMultListen);
	rtpMulticastPtr->socketfd = CreatSocketDGRAM();
	if(rtpMulticastPtr->socketfd  < 0){
		appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;//线程停止
		pthread_exit(NULL);
	}
	SocketBind(rtpMulticastPtr->socketfd,port);

	//注册组播发送
	if(SocketMultiaddrRegister(rtpMulticastPtr->socketfd,address,INADDR_ANY) <0 )
	{
		ERRORP("Socket Multi addr Register");
		appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;//线程停止
		pthread_exit(NULL);
	}

	while(appData.rtpMulticastConf.pthreadFlag == PTHREAD_RUNING || appData.rtpMulticastConf.pthreadFlag == PTHREAD_SLEEP)
	{
	#if 0
		if(SIP_LINE_CALLING_STATUS_IDLE != st_SipCurStatus.lineCallStatus 
			|| appData.rtpMulticastConf.pthreadFlag == PTHREAD_SLEEP){
			if(rtpMulticastPtr->socketfd > 0){
				close(rtpMulticastPtr->socketfd); //如果正在通话则不接收组播数据,或者线程需要休眠
				rtpMulticastPtr->socketfd = -1;
			}
		}
		//判断音源优先级、以及rtp负载类型,用于
		if(appData.rtpMulticastConf.curStatus 
			|| SIP_LINE_CALLING_STATUS_IDLE != st_SipCurStatus.lineCallStatus
			|| appData.rtpMulticastConf.pthreadFlag == PTHREAD_SLEEP){
			sleep(1); //如果组播开始接收数据则线程休眠等待恢复
			NOTICE("rtp multicast server sleep (cur=%d callStatus=%d pthreadFlag=%d)..."
				,appData.rtpMulticastConf.curStatus
				,st_SipCurStatus.lineCallStatus
				,appData.rtpMulticastConf.pthreadFlag);
			continue;
		}
		if(rtpMulticastPtr->socketfd < 0){
			rtpMulticastPtr->socketfd = CreatSocketDGRAM();
			if(rtpMulticastPtr->socketfd  < 0){
				appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;//线程停止
				pthread_exit(NULL);
			}
			SocketBind(rtpMulticastPtr->socketfd,port);

			//注册组播发送
			if(SocketMultiaddrRegister(rtpMulticastPtr->socketfd,address,INADDR_ANY) <0 )
			{
				ERRORP("Socket Multi addr Register");
				appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;//线程停止
				pthread_exit(NULL);
			}
		}
	#endif
		timeout.tv_sec = 0;//5s接收超时超时
		timeout.tv_usec = 1000*100;// 超时时间	
		FD_ZERO(&readfd);        // 清空读文件描述集合
		FD_SET(rtpMulticastPtr->socketfd, &readfd); // 注册套接字文件描述符
		//memset(RxBuf, 0x00,MAX_BUF_SIZE); // 清空接收缓存
		ret = select(rtpMulticastPtr->socketfd+1, &readfd, NULL, NULL, &timeout);
		switch (ret){
		case -1 :
			perror("rtp Multicast Recv call select error");
			continue;
			break;
		case 0 ://超时			
			break;
		default : // 接收数据
			if (FD_ISSET(rtpMulticastPtr->socketfd, &readfd))
			{
				rxlen = SocketRecvFrom(rtpMulticastPtr->socketfd,RxBuf,MAX_BUF_SIZE,(struct sockaddr_in*)&server_addr);
				#if 1
				//|| strcmp( (char *)inet_ntoa(server_addr.sin_addr),(char *)GetDeviceIP()) == 0 
				if(	strcmp( (char *)inet_ntoa(server_addr.sin_addr),"127.0.0.1") ==0 )
				{
					//本机自己的组播命令不处理
					continue;
				}
				#endif
				//NOTICE("rtp Multicast From(%s) len:%d(pt=%d)\n",(char *)inet_ntoa(server_addr.sin_addr),rxlen,rtpPt );
				
				//线程一直处于接收状态
				rtpPt = RxBuf[1]&0x7F;
				if(checkPayloadIsAvailability(rtpPt)){
					if(isOpen == 0){
						if(!checkRtpMultSourceIsValid()){
							NOTICE("check Rtp Mult Source Valid Failed!");
							stopRtpMultRecvSource();
							return -1; 
						}
						pjmedia_rtp_session_init(&rtp_in_ses,rtpPt,0);
						//切换音音源
						if( startRtpMultRecvSource()<=0){
							ERRORP("start Rtp Mult Recv Source failed");
							stopRtpMultRecvSource();
							continue;
						}

						{
							unsigned count = 1;
							const char * codec_id = buildCodesID(rtpPt,8000,1);
							if(rtpPt == 9){
								if( init_dsp_param(16000,16,1) <0)
								{
									ERRORP("初始化dsp参数失败...");
									PrioritySourceGlobalStop(SOURCE_RTP_MULT);//需要停止之前的寻呼,如果之前处于SIP呼叫时会造成DSP冲突
									continue;
								}
							}else{
								if( init_dsp_param(8000,16,1) <0)
								{
									ERRORP("初始化dsp参数失败...");
									PrioritySourceGlobalStop(SOURCE_RTP_MULT);//需要停止之前的寻呼,如果之前处于SIP呼叫时会造成DSP冲突
									continue;
								}	
							}
								

							pj_str_t str_codec_id = pj_str(codec_id);
							pjmedia_codec_mgr *codec_mgr = pjmedia_endpt_get_codec_mgr(stPjsuaVarData->med_endpt);
							pjmedia_codec_info  info[16]={0};
							count = 16;
							pjmedia_codec_mgr_enum_codecs(codec_mgr, &count,info,NULL);
							printf("count===%d\n",count);
							i = 0;
							for ( ;i < count; ++i)
							{
								printf("info[%d].encoding_name=%s\n",i,info[i].encoding_name);
							}		
							count = 1;
							pj_status_t status = pjmedia_codec_mgr_find_codecs_by_id( codec_mgr,
													  &str_codec_id, &count,
													  &codec_info, NULL);
							if (status != PJ_SUCCESS || count == 0) {
								ERRORP("Error: unable to find codec %s", codec_id);
								PrioritySourceGlobalStop(SOURCE_RTP_MULT);//需要停止之前的寻呼,如果之前处于SIP呼叫时会造成DSP冲突
								continue;
							}
							printf("codec_info.encoding_name=%s\n",codec_info->encoding_name);
							pjmedia_codec_mgr_get_default_param(codec_mgr, codec_info, &codec_param);
							pjmedia_codec_mgr_alloc_codec(codec_mgr, codec_info, &codec);
							pjmedia_codec_init(codec, stPjsuaVarData->pool);
							pjmedia_codec_open(codec, &codec_param);
							
							NOTICE("codec_param.info.clock_rate=%d,ptime=%d",codec_param.info.clock_rate,codec_param.info.frm_ptime);
	
						}

						isOpen = 1; //当前rtp已经开启

					}					

				    /* Decode RTP packet. */
				    status = pjmedia_rtp_decode_rtp(&rtp_in_ses, 
								    RxBuf, (int)rxlen, 
								    &hdr, &payload, &payload_len);
				    if (status != PJ_SUCCESS) {
					app_perror(THIS_FILE, "RTP decode error", status);
					return;
				    }

				    /* Parse the bitstream (not really necessary for this case
					 * since we always decode 1 frame, but it's still good
					 * for testing)
					 */
					pj_timestamp ts;
					ts.u64 = 0;
					int cnt = PJ_ARRAY_SIZE(frames);
					pjmedia_codec_parse(codec, payload, payload_len, &ts, &cnt,frames);

					//PJ_LOG(4,(THIS_FILE, "Rx seq=%d payload_len=%d", pj_ntohs(hdr->seq),payload_len));
				    /* Decode or simulate packet loss */
					out_frm.buf = (char*)pcmbuf;
					//out_frm.size = (codec_param.info.clock_rate * codec_param.info.frm_ptime / 1000) * 2;
					out_frm.size  = sizeof(pcmbuf);
					//frames[0].buf = (char*)payload;
					//frames[0].size = (codec_param.info.clock_rate * codec_param.info.frm_ptime / 1000);
					writeByteCount = 0;
					for (i = 0; i < cnt; ++i)
					{
						pjmedia_codec_decode(codec, &frames[i], sizeof(pcmbuf),&out_frm);
						memcpy(&dspBuf[writeByteCount],out_frm.buf,out_frm.size);
						writeByteCount+=out_frm.size;
					}
					/* Decode */					
					printf("out_frm.size=%d,cnt=%d,writeByteCount=%d\n",out_frm.size,cnt,writeByteCount);
					write_pcm_stream_to_dsp(dspBuf,writeByteCount);
					
					//writeByteCount+=out_frm.size;
					//if(writeByteCount > 1024){
					//	 write_pcm_stream_to_dsp(dspBuf,writeByteCount);
					//	 writeByteCount = 0;
					//}


					#if 0
					close(rtpMulticastPtr->socketfd);
					rtpMulticastPtr->socketfd = -1;
					PJ_THREAD_REGISTER_MACRO(rtpMultListen);
					
					pjmedia_rtp_session_init(&rtp_in_ses,rtpPt,0);
					if(startStreaMulticast(PJMEDIA_DIR_DECODING,address,port,buildCodesID(rtpPt,8000,1)) != PJ_SUCCESS){
						appData.rtpMulticastConf.curStatus = 0;
						ERRORP("start Stream Multicast Failed sleep 5 and continue!");
						sleep(5);
						continue;
					}
					appData.rtpMulticastConf.curStatus = 1; //当前rtp已经开启
					#endif
				}else{
					NOTICE("不支持的编码...%d",rtpPt);
				}
				
			}
		}
	}
	if(rtpMulticastPtr->socketfd > 0){
		close(rtpMulticastPtr->socketfd);
		rtpMulticastPtr->socketfd = -1;
	}
	
	if(appData.rtpMulticastConf.curStatus){
		appData.rtpMulticastConf.curStatus = 0;
	}
	appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;
	NOTICE("rtp multicast server stop...");
	pthread_exit(NULL);
}

#endif

int pjmediaCodecInit(unsigned char rtpPt,const pjmedia_codec_info **codec_info,pjmedia_codec_param *codec_param)
{
	unsigned count = 1;
	const char * codec_id = buildCodesID(rtpPt,8000,1);
	LOG("rtp Mult start at %s",codec_id);
	if(rtpPt == 9){

		//G722默认采用16K
		//if( init_dsp_param(16000,16,1) <0)
		{
			ERRORP("初始化dsp参数失败...");
			//stopRtpMultRecvSource();//需要停止之前的寻呼,如果之前处于SIP呼叫时会造成DSP冲突
			return -1;
		}
	}else{
		//if( init_dsp_param(8000,16,1) <0)
		{
			ERRORP("初始化dsp参数失败...");
			//stopRtpMultRecvSource();//需要停止之前的寻呼,如果之前处于SIP呼叫时会造成DSP冲突
			return -1;
		}	
	}
	if (!stPjsuaVarData)
	{
		LOG("PJ RTP Multicast Start Failed stPjsuaVarData=NULL");
		//stopRtpMultRecvSource();//需要停止之前的寻呼,如果之前处于SIP呼叫时会造成DSP冲突
		return -1;
	}
	pj_str_t str_codec_id = pj_str(codec_id);
	rtpRecv_odec_mgr = pjmedia_endpt_get_codec_mgr(stPjsuaVarData->med_endpt);
	//printf("pjmedia_endpt_get_codec_mgr=\n");
	//pjmedia_codec_info  info[16]={0};
	//count = 16;
	////printf("pjmedia_codec_mgr_find_codecs_by_id\n");
	//if(rtpRecv_odec_mgr == NULL){
	//	printf("rtpRecv_odec_mgr == NULL\n");
	//}
	//printf("rtpRecv_odec_mgr codec_cnt=%d\n",rtpRecv_odec_mgr->codec_cnt);
//
	//pjmedia_codec_mgr_enum_codecs(rtpRecv_odec_mgr, &count,info,NULL);
	//printf("count===%d\n",count);
	//i = 0;
	//for ( ;i < count; ++i)
	//{
	//	printf("info[%d].encoding_name=%s\n",i,info[i].encoding_name);
	//}	
	
	if (!rtpRecv_odec_mgr)
	{
		ERRORP("rtpRecv_odec_mgr = NULL Rtp Mult Stop!");
		//stopRtpMultRecvSource();//需要停止之前的寻呼,如果之前处于SIP呼叫时会造成DSP冲突
		return -1;
	}
	count = 1;

	//for (i=0; i<mgr->codec_cnt; ++i) {
		//&mgr->codec_desc[i].info;
	//rtpRecv_odec_mgr
	
	pj_status_t status = pjmedia_codec_mgr_find_codecs_by_id( rtpRecv_odec_mgr,
							  &str_codec_id, &count,
							  codec_info, NULL);
	if (status != PJ_SUCCESS || count == 0) {
		ERRORP("Error: unable to find codec %s", codec_id);
		//stopRtpMultRecvSource();//需要停止之前的寻呼,如果之前处于SIP呼叫时会造成DSP冲突
		return -1;
	}
	//printf("pjmedia_codec_mgr_find_codecs_by_id11111111\n");
	if (!(*codec_info))
	{
		ERRORP("unable to find codec %s", codec_id);
		//stopRtpMultRecvSource();//需要停止之前的寻呼,如果之前处于SIP呼叫时会造成DSP冲突
		return -1;
	}
	printf("Found rtp codec info decode name:%s\n",(*codec_info)->encoding_name);
	pjmedia_codec_mgr_get_default_param(rtpRecv_odec_mgr, (*codec_info),codec_param);
	pjmedia_codec_mgr_alloc_codec(rtpRecv_odec_mgr, (*codec_info), &rtpRecv_codec);
	pjmedia_codec_init(rtpRecv_codec, stPjsuaVarData->pool);
	pjmedia_codec_open(rtpRecv_codec, codec_param);
	NOTICE("codec_param->info.clock_rate=%d,ptime=%d",codec_param->info.clock_rate,codec_param->info.frm_ptime);
	pjmedia_rtp_session_init(&rtp_in_ses,rtpPt,0);

	return 0;
}

void * RtpMulticastListen_Pthread(void * args)
{
	int i = 0;	
	int port = -1;
	char address[RTP_MULTICAST_URL_LEN_MAX]={0};
	appData.rtpMulticastConf.pthreadFlag = PTHREAD_RUNING;//线程正在运行
	rtpMulticast_t * rtpMulticastPtr=NULL;
	for (; i < RTP_MULTICAST_URL_COUNT_MAX; ++i)
	{		
		if(appData.rtpMulticastConf.rtpMulticast[i].socketfd > 0){
			close(appData.rtpMulticastConf.rtpMulticast[i].socketfd);//关闭之前打开的socket
			NOTICE("Close rtp Multicast socket %d.",i+1);
		}
		if(strlen(appData.rtpMulticastConf.rtpMulticast[i].address) == 0){
			continue;
		}

		if(appData.rtpMulticastConf.prio != (i+1)){
			continue;
		}
		/* found multicase address */
		rtpMulticastPtr = &appData.rtpMulticastConf.rtpMulticast[i];
		char temp[RTP_MULTICAST_URL_LEN_MAX];
	
		bzero(temp,RTP_MULTICAST_URL_LEN_MAX);
		strcpy(temp,appData.rtpMulticastConf.rtpMulticast[i].address);
		char *p = strtok(temp,":");
		if(!p){ 
			WARNINGP("No Found Multicast Address!");
			continue;
		}
		bzero(address,RTP_MULTICAST_URL_LEN_MAX);
		strcpy(address,p);//读取地址
		if(!(p = strtok(NULL,":") ) ){continue;}
		port = atoi(p);
		NOTICE("start Multicast address[%s:%d]...",address,port);
		if(port < 0){
			ERRORP("set Multicast Port Failed!");
			continue;
		}
		break;
	}

	if(i == RTP_MULTICAST_URL_COUNT_MAX){
		WARNINGP("No Found Rtp Multicast Address Prio-%d",appData.rtpMulticastConf.prio);
		appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;//线程正在运行
		pthread_exit(NULL);
	}
	//PJ_THREAD_REGISTER_MACRO(rtpMultListen);
	struct sockaddr_in server_addr;
	fd_set readfd;
	int rxlen = 0;
	int ret = -1;
	unsigned char RxBuf[MAX_BUF_SIZE];
	
	struct timeval timeout;	

	FD_ZERO(&readfd);        // 清空读文件描述集合
	FD_SET(rtpMulticastPtr->socketfd, &readfd); // 注册套接字文件描述符
	timeout.tv_sec = 5;//5s接收超时超时
	timeout.tv_usec = 0;// 超时时间	
	unsigned char rtpPt = 0xFF;
	unsigned char currentRtpPt = 0;//当前使用的rtp类型
	int isOpen=0;
	
	unsigned char dspBuf[1024*4]={0};//写入dsp缓存
	unsigned char pcmbuf[1024*2]={0};//rtp解码缓存160*3
	int writeByteCount=0;
	
	/* Default values */
	const pjmedia_codec_info *codec_info=NULL;
	pjmedia_codec_param codec_param;
	pjmedia_codec *codec;
	pjmedia_frame frm_pcm, frm_bit, out_frm, frames[4];
	pj_status_t status;
	const pjmedia_rtp_hdr *hdr;
	const void *payload;
	unsigned payload_len;
	PJ_THREAD_REGISTER_MACRO(rtpMultListen);


	//rtpMulticastPtr->socketfd = CreatSocketDGRAM();
	if(rtpMulticastPtr->socketfd  < 0){
		appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;//线程停止
		pthread_exit(NULL);
	}
	//SocketBind(rtpMulticastPtr->socketfd,port);

	//注册组播发送
	//if(SocketMultiaddrRegister(rtpMulticastPtr->socketfd,address,INADDR_ANY) <0 )
	{
		ERRORP("Socket Multi addr Register");
		appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;//线程停止
		pthread_exit(NULL);
	}
	LOG("Rtp Mult Pthread Start Succeed Addr:%s:%d",address,port);
	appData.rtpMulticastConf.curStatus = 0;
	appData.rtpMulticastConf.activateRtpPt = 0xFF;
	while(appData.rtpMulticastConf.pthreadFlag == PTHREAD_RUNING || appData.rtpMulticastConf.pthreadFlag == PTHREAD_SLEEP)
	{
		timeout.tv_sec = 0;//5s接收超时超时
		timeout.tv_usec = 1000*1000;// 200ms超时时间	
		FD_ZERO(&readfd);        // 清空读文件描述集合
		FD_SET(rtpMulticastPtr->socketfd, &readfd); // 注册套接字文件描述符
		//memset(RxBuf, 0x00,MAX_BUF_SIZE); // 清空接收缓存
		ret = select(rtpMulticastPtr->socketfd+1, &readfd, NULL, NULL, &timeout);
		switch (ret){
		case -1 :
			perror("rtp Multicast Recv call select error");
			continue;
			break;
		case 0 ://超时
			if(appData.rtpMulticastConf.curStatus){
				NOTICE("Rtp recv timeout stop source!");
				//StopStreamMulticast
				appData.rtpMulticastConf.curStatus = 0;
				stopRtpMultRecvSource();
			}
			
			break;
		default : // 接收数据
			if (FD_ISSET(rtpMulticastPtr->socketfd, &readfd))
			{
				//rxlen = SocketRecvFrom(rtpMulticastPtr->socketfd,RxBuf,MAX_BUF_SIZE,(struct sockaddr_in*)&server_addr);
				if(!en_RtpMultRecv){
					printf("rtp multicast recv disabled...\n");
					continue;
				}
				#if 1
				//|| strcmp( (char *)inet_ntoa(server_addr.sin_addr),(char *)GetDeviceIP()) == 0 
				if(	strcmp( (char *)inet_ntoa(server_addr.sin_addr),"127.0.0.1") ==0 )
				{
					//本机自己的组播命令不处理
					continue;
				}
				#endif				
				//NOTICE("rtp Multicast From(%s) len:%d(pt=%d)\n",(char *)inet_ntoa(server_addr.sin_addr),rxlen,rtpPt );
				//线程一直处于接收状态
				/* Decode RTP packet. */
				status = pjmedia_rtp_decode_rtp(&rtp_in_ses, 
							    RxBuf, (int)rxlen, 
							    &hdr, &payload, &payload_len);
				if (status != PJ_SUCCESS) {
					app_perror(THIS_FILE, "RTP decode error", status);
					continue;
				}
				//rtpPt = RxBuf[1]&0x7F;
				rtpPt = hdr->pt&0x7F;
				if(appData.rtpMulticastConf.activateRtpPt != 0xFF && rtpPt != appData.rtpMulticastConf.activateRtpPt){
					printf("recv rtp pt(%d) != current rtp pt(%d)\n",rtpPt,appData.rtpMulticastConf.activateRtpPt);
					continue;
				}
				if(!en_RtpMultRecv){
					continue;
				}
				appData.rtpMulticastConf.activateRtpPt = rtpPt;//记录当前负载类型
				if(checkPayloadIsAvailability(rtpPt)){

					if(appData.rtpMulticastConf.curStatus == 0){
						if(!en_RtpMultRecv){
							continue;
						}

						if(!checkRtpMultSourceIsValid()){
							NOTICE("check Rtp Mult Source Valid Failed!");
							en_RtpMultRecv = 0; //当前音源优先级高于组播音源不处理
							CreateTimer(3000,1,en_RtpMultRecvTimeout);
							//stopRtpMultRecvSource(); 
							continue;
						}
						//切换音音源
						if( startRtpMultRecvSource(0)<=0){
							ERRORP("start Rtp Mult Recv Source failed");
							stopRtpMultRecvSource();
							continue;
						}

#if 1
						{
							unsigned count = 1;
							const char * codec_id = buildCodesID(rtpPt,8000,1);
							LOG("rtp Mult start at %s",codec_id);
							if(rtpPt == 9){

								//G722默认采用16K
								//if( init_dsp_param(16000,16,1) <0)
								{
									ERRORP("初始化dsp参数失败...");
									stopRtpMultRecvSource();//需要停止之前的寻呼,如果之前处于SIP呼叫时会造成DSP冲突
									continue;
								}
							}else{
								//if( init_dsp_param(8000,16,1) <0)
								{
									ERRORP("初始化dsp参数失败...");
									stopRtpMultRecvSource();//需要停止之前的寻呼,如果之前处于SIP呼叫时会造成DSP冲突
									continue;
								}	
							}
							if (!stPjsuaVarData)
							{
								LOG("PJ RTP Multicast Start Failed stPjsuaVarData=NULL");
								stopRtpMultRecvSource();//需要停止之前的寻呼,如果之前处于SIP呼叫时会造成DSP冲突
								continue;
							}
							
						//	printf("codec_id=%s\n",codec_id);
							pj_str_t str_codec_id = pj_str(codec_id);							
							rtpRecv_odec_mgr = pjmedia_endpt_get_codec_mgr(stPjsuaVarData->med_endpt);
							//printf("pjmedia_endpt_get_codec_mgr=\n");
							//pjmedia_codec_info  info[16]={0};
							//count = 16;
							////printf("pjmedia_codec_mgr_find_codecs_by_id\n");
							//if(rtpRecv_odec_mgr == NULL){
							//	printf("rtpRecv_odec_mgr == NULL\n");
							//}
							//printf("rtpRecv_odec_mgr codec_cnt=%d\n",rtpRecv_odec_mgr->codec_cnt);
						//
							//pjmedia_codec_mgr_enum_codecs(rtpRecv_odec_mgr, &count,info,NULL);
							//printf("count===%d\n",count);
							//i = 0;
							//for ( ;i < count; ++i)
							//{
							//	printf("info[%d].encoding_name=%s\n",i,info[i].encoding_name);
							//}	
							
							if (!rtpRecv_odec_mgr)
							{
								ERRORP("rtpRecv_odec_mgr = NULL Rtp Mult Stop!");
								stopRtpMultRecvSource();//需要停止之前的寻呼,如果之前处于SIP呼叫时会造成DSP冲突
								continue;
							}
							count = 1;

							//for (i=0; i<mgr->codec_cnt; ++i) {
 							//&mgr->codec_desc[i].info;
							//rtpRecv_odec_mgr
							
							pj_status_t status = pjmedia_codec_mgr_find_codecs_by_id( rtpRecv_odec_mgr,
													  &str_codec_id, &count,
													  &codec_info, NULL);
							if (status != PJ_SUCCESS || count == 0) {
								ERRORP("Error: unable to find codec %s", codec_id);
								stopRtpMultRecvSource();//需要停止之前的寻呼,如果之前处于SIP呼叫时会造成DSP冲突
								continue;
							}
							printf("pjmedia_codec_mgr_find_codecs_by_id11111111\n");
							if (!codec_info)
							{
								ERRORP("unable to find codec %s", codec_id);
								stopRtpMultRecvSource();//需要停止之前的寻呼,如果之前处于SIP呼叫时会造成DSP冲突
								continue;
							}
							printf("Found rtp codec info decode name:%s\n",codec_info->encoding_name);
							pjmedia_codec_mgr_get_default_param(rtpRecv_odec_mgr, codec_info, &codec_param);
							pjmedia_codec_mgr_alloc_codec(rtpRecv_odec_mgr, codec_info, &rtpRecv_codec);
							pjmedia_codec_init(rtpRecv_codec, stPjsuaVarData->pool);
							pjmedia_codec_open(rtpRecv_codec, &codec_param);
							NOTICE("codec_param.info.clock_rate=%d,ptime=%d",codec_param.info.clock_rate,codec_param.info.frm_ptime);
						}
						pjmedia_rtp_session_init(&rtp_in_ses,rtpPt,0);
#endif
						
						appData.rtpMulticastConf.curStatus = 1; //当前rtp已经开启
					}
					if(!rtpRecv_codec || !rtpRecv_odec_mgr)
					{
						ERRORP("if(!rtpRecv_codec || !rtpRecv_odec_mgr)");
						stopRtpMultRecvSource();
						continue;
					}
					
				    /* Parse the bitstream (not really necessary for this case
					 * since we always decode 1 frame, but it's still good
					 * for testing)
					 */
					pj_timestamp ts;
					ts.u64 = 0;
					int cnt = PJ_ARRAY_SIZE(frames);
					pjmedia_codec_parse(rtpRecv_codec, payload, payload_len, &ts, &cnt,frames);

					//PJ_LOG(4,(THIS_FILE, "Rx seq=%d payload_len=%d", pj_ntohs(hdr->seq),payload_len));
				    /* Decode or simulate packet loss */
					out_frm.buf = (char*)pcmbuf;
					//out_frm.size = (codec_param.info.clock_rate * codec_param.info.frm_ptime / 1000) * 2;
					out_frm.size  = sizeof(pcmbuf);
					//frames[0].buf = (char*)payload;
					//frames[0].size = (codec_param.info.clock_rate * codec_param.info.frm_ptime / 1000);
					writeByteCount = 0;
					for (i = 0; i < cnt; ++i)
					{
						/* Decode */	
						pjmedia_codec_decode(rtpRecv_codec, &frames[i], sizeof(pcmbuf),&out_frm);
						memcpy(&dspBuf[writeByteCount],out_frm.buf,out_frm.size);
						writeByteCount+=out_frm.size;
					}
										//printf("out_frm.size=%d,cnt=%d,writeByteCount=%d\n",out_frm.size,cnt,writeByteCount);
					//write_pcm_stream_to_dsp(dspBuf,writeByteCount);
					
				}else{
					NOTICE("不支持的编码...%d",rtpPt);
				}
				
			}
		}
	}
	if(rtpMulticastPtr->socketfd > 0){
		close(rtpMulticastPtr->socketfd);
		rtpMulticastPtr->socketfd = -1;
	}
	
	if(appData.rtpMulticastConf.curStatus){
		appData.rtpMulticastConf.curStatus = 0;
	}
	appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;
	NOTICE("rtp multicast server stop...");
	pthread_exit(NULL);
}

void RtpMulticastListen_PthreadStart(void)
{
	int ret = -1;
	pthread_t Mcast_Pthread;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Mcast_Pthread, &Pthread_Attr, (void *)RtpMulticastListen_Pthread, NULL);
	if (ret < 0)
	{
		ERRORP("RtpMulticastListen_Pthread create");	
	}else{
		NOTICE("Rtp Multicast Listen Pthread Start success!\n");
	}
	pthread_attr_destroy(&Pthread_Attr);
}


/**
 * [restartRtpMulticastService 重启Rtp组播服务]
 */
void restartRtpMulticastService()
{
	NOTICE("restart Rtp Multicast Service...");
	//DestroyStreamMulticastInfo();
	stopRtpMultRecvSource();
	//开启rtp组播线程
#if 1
	NOTICE("appData.rtpMulticastConf.pthreadFlag=%d",appData.rtpMulticastConf.pthreadFlag);
	if(appData.rtpMulticastConf.prio != 0){
		//if(appData.rtpMulticastConf.pthreadFlag != PTHREAD_NO_RUNING 
		//	&& appData.rtpMulticastConf.pthreadFlag != PTHREAD_EXITED ){
		if(appData.rtpMulticastConf.pthreadFlag == PTHREAD_RUNING || appData.rtpMulticastConf.pthreadFlag == PTHREAD_SLEEP){
			//需要退出线程
			appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITING;//
			int count = 30;
			while(appData.rtpMulticastConf.pthreadFlag == PTHREAD_EXITING){
				usleep(1000*100);
				count--;if(count == 0)break;
			}
			if(count == 0){
				ERRORP("rtp Multicast Pthread Exit Timeout!");
				ERRORP("restart Rtp Multicast Service Error!");
				return ;
			}
		}
		RtpMulticastListen_PthreadStart();
	}else{
		NOTICE("rtp multicast config priority 0.");
	}
#endif
	appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;//设置线程休眠
	usleep(300*1000);
}

void DestroyStreamMulticast()
{
	//先退出线程
	//if(appData.rtpMulticastConf.pthreadFlag == PTHREAD_RUNING 
	//	&& appData.rtpMulticastConf.pthreadFlag != PTHREAD_EXITED )
	//{
	//	appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITING;
	//	//需要退出线程
	//	int count = 30;
	//	while(appData.rtpMulticastConf.pthreadFlag == PTHREAD_EXITING){
	//		usleep(1000*100);
	//		count--;if(count == 0)break;
	//	}
	//	if(count == 0){
	//		ERRORP("rtp Multicast Pthread Exit Timeout!");			
	//		return ;
	//	}
	//}
	
	stopRtpMultRecvSource(); //停止rtp组播
}


#if 1
/**
 * [RtpMulticastPageListen_Pthread rtp 组播寻呼监听线程]
 * @param  args [description]
 * @return      [description]
 */
void * RtpMulticastPageListen_Pthread(void * args)
{
	int i = 0;
	rtpMulticast_t *rtpMulticastPtr = (rtpMulticast_t *)args;	
	struct sockaddr_in server_addr;
	fd_set readfd;
	int rxlen = 0;
	int ret = -1;
	unsigned char RxBuf[MAX_BUF_SIZE];
	
	struct timeval timeout;
	timeout.tv_sec = 5;//5s接收超时超时
	timeout.tv_usec = 0;// 超时时间	
	unsigned char rtpPt = 0xFF;
	unsigned char currentRtpPt = 0;//当前使用的rtp类型
	int isOpen=0;
	
	unsigned char dspBuf[1024*4]={0};//写入dsp缓存
	unsigned char pcmbuf[1024*2]={0};//rtp解码缓存160*3
	int writeByteCount=0;
	
	/* Default values */
	const pjmedia_codec_info *codec_info=NULL;
	pjmedia_codec_param codec_param;
	pjmedia_codec *codec;
	pjmedia_frame frm_pcm, frm_bit, out_frm, frames[4];
	pj_status_t status;
	const pjmedia_rtp_hdr *hdr;
	const void *payload;
	unsigned payload_len;
	PJ_THREAD_REGISTER_MACRO(RtpMulticastPageListen);
	int port = -1;
	char address[RTP_MULTICAST_URL_LEN_MAX]={0};
	{
		char temp[RTP_MULTICAST_URL_LEN_MAX];	
		bzero(temp,RTP_MULTICAST_URL_LEN_MAX);

		strcpy(temp,rtpMulticastPtr->address);
		char *p = strtok(temp,":");
		if(!p){ 
			ERRORP("No Found Multicast Address!");
			stopRtpMultRecvSource();
			pthread_exit(NULL);		
		}
		bzero(address,RTP_MULTICAST_URL_LEN_MAX);
		strcpy(address,p);//读取地址
		if(!(p = strtok(NULL,":") ) ){
			ERRORP("No Found Multicast Address!");			
			stopRtpMultRecvSource();
			pthread_exit(NULL);	
		}
		port = atoi(p);
		NOTICE("start Multicast address[%s:%d]...",address,port);
		if(port < 0){
			ERRORP("set Multicast Port Failed!");			
			stopRtpMultRecvSource();
			pthread_exit(NULL);	
		}
	}	

	//rtpMulticastPtr->socketfd = CreatSocketDGRAM();
	if(rtpMulticastPtr->socketfd  < 0){
		stopRtpMultRecvSource();
		close(rtpMulticastPtr->socketfd);
		pthread_exit(NULL);
	}
	//SocketBind(rtpMulticastPtr->socketfd,port);

	//注册组播发送
	//if(SocketMultiaddrRegister(rtpMulticastPtr->socketfd,address,INADDR_ANY) <0 )
	{
		ERRORP("Socket Multi addr Register");
		close(rtpMulticastPtr->socketfd);
		pthread_exit(NULL);
	}
	LOG("Rtp Mult Paging Pthread Start Succeed Addr:%s:%d",address,port);
	appData.rtpMulticastConf.curStatus = 0;
	appData.rtpMulticastConf.activateRtpPt = 0xFF;
	rtpMulticastPageListen_stop = 0;//
	while(!rtpMulticastPageListen_stop)
	{
		timeout.tv_sec = 5;//5s接收超时超时
		timeout.tv_usec = 0;// 200ms超时时间1000*200	
		FD_ZERO(&readfd);        // 清空读文件描述集合
		FD_SET(rtpMulticastPtr->socketfd, &readfd); // 注册套接字文件描述符
		//memset(RxBuf, 0x00,MAX_BUF_SIZE); // 清空接收缓存
		ret = select(rtpMulticastPtr->socketfd+1, &readfd, NULL, NULL, &timeout);
		switch (ret){
		case -1 :
			perror("rtp Multicast Recv call select error");
			continue;
			break;
		case 0 ://超时
			if(appData.rtpMulticastConf.curStatus){
				
				//StopStreamMulticast
			}
			WARNINGP("Rtp recv timeout stop source!");
			/*注释2019—7-10*/
			// stopRtpMultRecvSource();
			// rtpMulticastPageListen_stop = 1;
			break;
		default : // 接收数据
			if (FD_ISSET(rtpMulticastPtr->socketfd, &readfd))
			{
				//rxlen = SocketRecvFrom(rtpMulticastPtr->socketfd,RxBuf,MAX_BUF_SIZE,(struct sockaddr_in*)&server_addr);
				en_RtpMultRecv = 0; //使被动的组播监听线程接收失能
				//if(!en_RtpMultRecv){
				//	printf("rtp multicast recv disabled...\n");
				//	continue;
				//}
				#if 1
				//|| strcmp( (char *)inet_ntoa(server_addr.sin_addr),(char *)GetDeviceIP()) == 0 
				if(	strcmp( (char *)inet_ntoa(server_addr.sin_addr),"127.0.0.1") ==0 )
				{
					//本机自己的组播命令不处理
					continue;
				}
				#endif
				//NOTICE("rtp Multicast From(%s) len:%d(pt=%d)\n",(char *)inet_ntoa(server_addr.sin_addr),rxlen,rtpPt );
				//线程一直处于接收状态
				/* Decode RTP packet. */
				status = pjmedia_rtp_decode_rtp(&rtp_in_ses, 
							    RxBuf, (int)rxlen, 
							    &hdr, &payload, &payload_len);
				if (status != PJ_SUCCESS) {
					app_perror(THIS_FILE, "RTP decode error", status);
					continue;
				}
				//rtpPt = RxBuf[1]&0x7F;
				rtpPt = hdr->pt&0x7F;
				if(appData.rtpMulticastConf.activateRtpPt != 0xFF && rtpPt != appData.rtpMulticastConf.activateRtpPt){
					printf("recv rtp pt(%d) != current rtp pt(%d)\n",rtpPt,appData.rtpMulticastConf.activateRtpPt);
					continue;
				}
				if(rtpMulticastPageListen_stop == 1){
					continue;//退出
				}				
				appData.rtpMulticastConf.activateRtpPt = rtpPt;//记录当前负载类型
				if(checkPayloadIsAvailability(rtpPt)){					
					if(appData.rtpMulticastConf.curStatus == 0 ){
						if(rtpMulticastPageListen_stop == 1){
							continue;//退出
						}
						if(!checkRtpMultSourceIsValid()){
							NOTICE("check Rtp Mult Source Valid Failed!");
							//en_RtpMultRecv = 0; //当前音源优先级高于组播音源不处理
							//CreateTimer(3000,1,en_RtpMultRecvTimeout);
							stopRtpMultRecvSource(); 
							rtpMulticastPageListen_stop = 1;
							//appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;
							continue;
						}

						if(pjmediaCodecInit(rtpPt,&codec_info,&codec_param) < 0){
							LOG("pjmedia Codec Init Failed.");
							stopRtpMultRecvSource();
							rtpMulticastPageListen_stop = 1;
							//appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;
							continue;
						}
						appData.rtpMulticastConf.curStatus = 1; //当前rtp已经开启
					}
					if(!rtpRecv_codec || !rtpRecv_odec_mgr)
					{
						ERRORP("if(!rtpRecv_codec || !rtpRecv_odec_mgr)");
						stopRtpMultRecvSource();
						rtpMulticastPageListen_stop = 1;
						//appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;
						continue;
					}
					
				    /* Parse the bitstream (not really necessary for this case
					 * since we always decode 1 frame, but it's still good
					 * for testing)
					 */
					pj_timestamp ts;
					ts.u64 = 0;
					int cnt = PJ_ARRAY_SIZE(frames);
					pjmedia_codec_parse(rtpRecv_codec, payload, payload_len, &ts, &cnt,frames);

					//PJ_LOG(4,(THIS_FILE, "Rx seq=%d payload_len=%d", pj_ntohs(hdr->seq),payload_len));
				    /* Decode or simulate packet loss */
					out_frm.buf = (char*)pcmbuf;
					//out_frm.size = (codec_param.info.clock_rate * codec_param.info.frm_ptime / 1000) * 2;
					out_frm.size  = sizeof(pcmbuf);
					//frames[0].buf = (char*)payload;
					//frames[0].size = (codec_param.info.clock_rate * codec_param.info.frm_ptime / 1000);
					writeByteCount = 0;
					for (i = 0; i < cnt; ++i)
					{
						/* Decode */	
						pjmedia_codec_decode(rtpRecv_codec, &frames[i], sizeof(pcmbuf),&out_frm);
						memcpy(&dspBuf[writeByteCount],out_frm.buf,out_frm.size);
						writeByteCount+=out_frm.size;
					}
					//printf("out_frm.size=%d,cnt=%d,writeByteCount=%d\n",out_frm.size,cnt,writeByteCount);
					//write_pcm_stream_to_dsp(dspBuf,writeByteCount);
					usleep(1000);
				}else{
					NOTICE("不支持的编码...%d",rtpPt);
				}
				
			}
		}
	}
	if(rtpMulticastPtr->socketfd > 0){
		close(rtpMulticastPtr->socketfd);
		LOG("close RTP socketfd");
		rtpMulticastPtr->socketfd = -1;
	}
	
	if(appData.rtpMulticastConf.curStatus){
		appData.rtpMulticastConf.curStatus = 0;
	}
	//appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;
	NOTICE("rtp multicast paging server stop...");
	en_RtpMultRecv = 1; //使被动的组播监听线程接收使能
	Set_first_enter_RTP(1);
	pthread_exit(NULL);
}



void MulticastPageListen_Pthread(void *args)
{
	int i = 0;
	rtpMulticast_t *rtpMulticastPtr = (rtpMulticast_t *)args;	
	struct sockaddr_in server_addr;
	fd_set readfd;
	int rxlen = 0;
	int ret = -1;
	unsigned char RxBuf[MAX_BUF_SIZE];
	
	struct timeval timeout;
	timeout.tv_sec = 5;//5s接收超时超时
	timeout.tv_usec = 0;// 超时时间	
	unsigned char rtpPt = 0xFF;
	unsigned char currentRtpPt = 0;//当前使用的rtp类型
	int isOpen=0;
	
	unsigned char dspBuf[MAX_BUF_SIZE*10]={0};//写入dsp缓存
	int writeByteCount=0;
	int StreamRecvTimes=0;
	
	int port = -1;
	char address[RTP_MULTICAST_URL_LEN_MAX]={0};
	{
		char temp[RTP_MULTICAST_URL_LEN_MAX];	
		bzero(temp,RTP_MULTICAST_URL_LEN_MAX);

		strcpy(temp,rtpMulticastPtr->address);
		char *p = strtok(temp,":");
		if(!p){ 
			ERRORP("No Found Multicast Address!");
			stopRtpMultRecvSource();
			pthread_exit(NULL);		
		}
		bzero(address,RTP_MULTICAST_URL_LEN_MAX);
		strcpy(address,p);//读取地址
		if(!(p = strtok(NULL,":") ) ){
			ERRORP("No Found Multicast Address!");			
			stopRtpMultRecvSource();
			pthread_exit(NULL);	
		}
		port = atoi(p);
		NOTICE("start Multicast address[%s:%d]...",address,port);
		if(port < 0){
			ERRORP("set Multicast Port Failed!");			
			stopRtpMultRecvSource();
			pthread_exit(NULL);	
		}
	}	

	//rtpMulticastPtr->socketfd = CreatSocketDGRAM();
	if(rtpMulticastPtr->socketfd  < 0){
		stopRtpMultRecvSource();
		close(rtpMulticastPtr->socketfd);
		pthread_exit(NULL);
	}
	//SocketBind(rtpMulticastPtr->socketfd,port);

	//注册组播发送
	//if(SocketMultiaddrRegister(rtpMulticastPtr->socketfd,address,INADDR_ANY) <0 )
	{
		ERRORP("Socket Multi addr Register");
		stopRtpMultRecvSource();
		close(rtpMulticastPtr->socketfd);
		pthread_exit(NULL);
	}
	//if(init_dsp_param(16000,16,1) <0)
	{
		ERRORP("初始化dsp参数失败...");
		stopRtpMultRecvSource();
		close(rtpMulticastPtr->socketfd);
		close_dsp();
		pthread_exit(NULL);
	}
	LOG("Rtp Mult Paging Pthread Start Succeed Addr:%s:%d",address,port);

	rtpMulticastPageListen_stop = 0;//
	while(!rtpMulticastPageListen_stop)
	{
		timeout.tv_sec = 5;//5s接收超时超时
		timeout.tv_usec = 0;// 200ms超时时间1000*200	
		FD_ZERO(&readfd);        // 清空读文件描述集合
		FD_SET(rtpMulticastPtr->socketfd, &readfd); // 注册套接字文件描述符
		memset(RxBuf, 0x00,MAX_BUF_SIZE); // 清空接收缓存
		ret = select(rtpMulticastPtr->socketfd+1, &readfd, NULL, NULL, &timeout);
		switch (ret)
		{
		case -1 :
			perror("rtp Multicast Recv call select error");
			continue;
			break;
		case 0 ://超时
			WARNINGP("Rtp recv timeout stop source!");
			/*注释2019—7-10*/
			// stopRtpMultRecvSource();
			// rtpMulticastPageListen_stop = 1;
			break;
		default : // 接收数据
			if (FD_ISSET(rtpMulticastPtr->socketfd, &readfd))
			{
				//rxlen = SocketRecvFrom(rtpMulticastPtr->socketfd,RxBuf,MAX_BUF_SIZE,(struct sockaddr_in*)&server_addr);
				en_RtpMultRecv = 0; //使被动的组播监听线程接收失能
				if(	strcmp( (char *)inet_ntoa(server_addr.sin_addr),"127.0.0.1") ==0 )
				{
					//本机自己的组播命令不处理
					continue;
				}

				memcpy(&dspBuf[writeByteCount],&RxBuf,rxlen);
				StreamRecvTimes++;
				writeByteCount+=rxlen;
				//printf("recv PCM sizeof :%d  total %d \n",rxlen,writeByteCount);
					//printf("out_frm.size=%d,cnt=%d,writeByteCount=%d\n",out_frm.size,cnt,writeByteCount);		
				if(StreamRecvTimes>=6)
				{
					//memcpy(&pcmbuf,&dspBuf,dspBufSize);
					//writedspBufSize=dspBufSize;
					//sem_post(&write_sem);
					//write_pcm_stream_to_dsp(dspBuf,writeByteCount);
					StreamRecvTimes=0;
					writeByteCount=0;
					//memset(&pcmbuf,0,sizeof(pcmbuf));
					memset(&dspBuf,0,sizeof(dspBuf));
				}
				
			}
		}
	}
		if(rtpMulticastPtr->socketfd > 0)
		{
			close(rtpMulticastPtr->socketfd);
			LOG("close RTP socketfd");
			rtpMulticastPtr->socketfd = -1;
		}
	//appData.rtpMulticastConf.pthreadFlag = PTHREAD_EXITED;
		NOTICE("rtp multicast paging server stop...");
		en_RtpMultRecv = 1; //使被动的组播监听线程接收使能
		Set_first_enter_RTP(1);
		//close_dsp();
		pthread_exit(NULL);
}

#endif

void MulticastPageListen_PthreadStart(void * args)
{
	int ret = -1;
	pthread_t Mcast_Pthread;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Mcast_Pthread, &Pthread_Attr, (void *)MulticastPageListen_Pthread, args);
	if (ret < 0)
	{
		ERRORP("Rtp Multicast Page Listen Pthread create");	
	}else{
		NOTICE("Rtp Multicast Page Listen Pthread Start success!");
	}
	pthread_attr_destroy(&Pthread_Attr);
}

void RtpMulticastPageListen_PthreadStart(void * args)
{
	int ret = -1;
	pthread_t Mcast_Pthread;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Mcast_Pthread, &Pthread_Attr, (void *)RtpMulticastPageListen_Pthread, args);
	if (ret < 0)
	{
		ERRORP("Rtp Multicast Page Listen Pthread create");	
	}else{
		NOTICE("Rtp Multicast Page Listen Pthread Start success!");
	}
	pthread_attr_destroy(&Pthread_Attr);
}




/**
 * [multicastRtpPagingStart description]
 * @param  address [description]
 * @param  port    [description]
 * @param  pt      [description]
 * @return         [description]
 */
int multicastRtpPagingStart(const char *address,int port,unsigned char pt,unsigned char Volume)
{
	if(Get_first_enter_RTP())
	{
		Set_first_enter_RTP(0);
		static rtpMulticast_t rtpMulticastPtr;
		strncpy(rtpMulticastPtr.name,address,RTP_MULTICAST_NAME_LEN_MAX);
		sprintf(rtpMulticastPtr.address,"%s:%d",address,port);
		//pt rtp负载类型自动识别
		//切换音音源
		if (!PriorityIsValid(SOURCE_RTP_MULT))
		{
			Set_first_enter_RTP(1);
			return ERROR;
		}
		if (get_system_source() == SOURCE_RTP_MULT)
		{
			Set_first_enter_RTP(1);
			stopRtpMultRecvSource();
		}

		if( startRtpMultRecvSource(Volume)<=0){
			ERRORP("start Rtp Mult Recv Source failed");
			//stopRtpMultRecvSource();
			//continue;
			Set_first_enter_RTP(1);
			return ERROR;
		}
		if(pt==1)
		MulticastPageListen_PthreadStart((void*)&rtpMulticastPtr);
		else
		RtpMulticastPageListen_PthreadStart((void*)&rtpMulticastPtr);
	}
		return SUCCEED;
}



/**
 * [StreaMulticastInit 初始化组播配置]
 */
void StreaMulticastInit()
{
	//由于之前已经建立了内存池,和媒体节点需要获取之前的
	stPjsuaVarData =  (struct pjsua_data*)pjsua_get_var();
	readRtpMulticastConfig();
	restartRtpMulticastService();
}

/**
 * [restartMulticastStream 重启组播流媒体]
 */
//void restartMulticastStream()
//{
//	DestroyStreamMulticast();
//	StreaMulticastInit();
//}
//


#endif