#ifndef __PJSUA_COMMON_H__
#define __PJSUA_COMMON_H__
#include <pjsua-lib/pjsua.h>
#include <pjsua-lib/pjsua_internal.h>
#include "typedef.h"



/* Call specific data */
typedef struct app_call_data
{
    pj_timer_entry	    timer;
    pj_bool_t		    ringback_on;
    pj_bool_t		    ring_on;
} app_call_data;

/* Video settings */
typedef struct app_vid
{
    unsigned            vid_cnt;
    int             vcapture_dev;
    int             vrender_dev;
    pj_bool_t           in_auto_show;
    pj_bool_t           out_auto_transmit;
} app_vid;

/* Pjsua application data */
typedef struct pjsua_app_config
{
    pjsua_config	    cfg;
    pjsua_logging_config    log_cfg;
    pjsua_media_config	    media_cfg;
    pj_bool_t		    no_refersub;
    pj_bool_t		    ipv6;
    pj_bool_t		    enable_qos;
    pj_bool_t		    no_tcp;
    pj_bool_t		    no_udp;
    pj_bool_t		    use_tls;
    pjsua_transport_config  udp_cfg;
    pjsua_transport_config  rtp_cfg;
    pjsip_redirect_op	    redir_op;

    unsigned		    acc_cnt;
    pjsua_acc_config	    acc_cfg[PJSUA_MAX_ACC];

    unsigned		    buddy_cnt;
    pjsua_buddy_config	    buddy_cfg[PJSUA_MAX_BUDDIES];

	app_call_data	    call_data[PJSUA_MAX_CALLS];

    pj_pool_t		   *pool;
    /* Compatibility with older pjsua */

    unsigned		    codec_cnt;
    pj_str_t		    codec_arg[32];
    unsigned		    codec_dis_cnt;
    pj_str_t                codec_dis[32];
    pj_bool_t		    null_audio;
    unsigned		    wav_count;
    pj_str_t		    wav_files[32];
    unsigned		    tone_count;
    pjmedia_tone_desc	    tones[32];
    pjsua_conf_port_id	    tone_slots[32];
    pjsua_player_id	    wav_id[32];
    pjsua_conf_port_id 	wav_port[32];
    pj_bool_t		    auto_play;
    pj_bool_t		    auto_play_hangup;
    pj_timer_entry	    auto_hangup_timer;
    pj_bool_t		    auto_loop;
    pj_bool_t		    auto_conf;
    pj_str_t		    rec_file;
    pj_bool_t		    auto_rec;
    pjsua_recorder_id	    rec_id;
    pjsua_conf_port_id	    rec_port;
    unsigned		    auto_answer;
    unsigned		    duration;

#ifdef STEREO_DEMO
    pjmedia_snd_port	   *snd;
    pjmedia_port	   *sc, *sc_ch1;
    pjsua_conf_port_id	    sc_ch1_slot;
#endif

    float		    mic_level,
			    speaker_level;

    int			    capture_dev, playback_dev;
    unsigned		    capture_lat, playback_lat;

    pj_bool_t		    no_tones;
    int			    ringback_slot;
    int			    ringback_cnt;
    pjmedia_port	   *ringback_port;
    int			    ring_slot;
    int			    ring_cnt;
    pjmedia_port	   *ring_port;

    app_vid		    vid;
    unsigned		    aud_cnt;

#if 0

    /* AVI to play */
    unsigned                avi_cnt;
    struct {
	pj_str_t		path;
	pjmedia_vid_dev_index	dev_id;
	pjsua_conf_port_id	slot;
    } avi[PJSUA_APP_MAX_AVI];
    pj_bool_t               avi_auto_play;
    int			    avi_def_idx;

    /* CLI setting */
    pj_bool_t		    use_cli;
    cli_cfg_t		    cli_cfg;
#endif

} pjsua_app_config;

#define LOCK_CODEC_MAX_RETRY         5
#define LOCK_CODEC_RETRY_INTERVAL   200
pj_status_t process_pending_reinvite(pjsua_call *call);

void pjsua_on_call_media_state(pjsua_call_id call_id);
void ring_stop(pjsua_call_id call_id);
void ring_start(pjsua_call_id call_id,int ri);
void pjsua_on_call_state(pjsua_call_id call_id, pjsip_event *e);
pj_bool_t is_non_av_fmt(const pjmedia_sdp_media *m,
			       const pj_str_t *fmt);
void pjsua_on_incoming_call(pjsua_acc_id acc_id, pjsua_call_id call_id,
			     pjsip_rx_data *rdata);
pj_status_t pjsuaCommonInit(pj_pool_t *pool);
void pjsua_on_Intercom_call(pjsua_acc_id acc_id, pjsua_call_id call_id,
			     pjsip_rx_data *rdata);
#endif