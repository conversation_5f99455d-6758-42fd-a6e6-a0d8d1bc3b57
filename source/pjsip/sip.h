
#ifndef __SIP_H__
#define __SIP_H__

#include <pjsua-lib/pjsua.h>
#include <pjsua-lib/pjsua_internal.h>
#include "sysConfig.h"
#include "sysconf.h"


// 定义结构体 sem_sip_conf_Data
typedef struct{
    unsigned short dacl_gain;	//dac增益
    unsigned short dacr_gain;	//dacR增益
	unsigned short mic_gain;	//mic增益
	unsigned char ouput_volume;	//输出音量(0~100)
}st_sip_conf_Data;

#define SHM_SIP_CONF_SIZE sizeof(st_sip_conf_Data)  // 结构体 sem_sip_conf_Data 的大小
#define SEM_SIP_CONF_FILE	"/tmp/shm_sip_conf"




#define VOIP_DEVELOP_TEST 	1 	

#define SIP_DEBUG 	1

#ifdef SIP_DEBUG
#define SIP_DBG(...) {fprintf(stderr, " SIP_DBG(%s(), %d): ",__FUNCTION__, __LINE__); fprintf(stderr, __VA_ARGS__);}
#else
#define SIP_DBG(...)
#endif


#include "typedef.h"

//#define 	CODEC_GSM  		"GSM"
//#define 	CODEC_PCMU 		"PCMU"
//#define 	CODEC_PCMA 		"PCMA"
//#define 	CODEC_G722 		"G722"

#define SIP_TRANSFER_PROTOCOL_UDP 	0
#define SIP_TRANSFER_PROTOCOL_TCP 	1
#define SIP_TRANSFER_PROTOCOL_TLS 	2

#define SIP_USER_MAX 				2

#define LOCAL_SIP_PORT_DEF 			5060
#define SIP_TRANSFER_PROTOCOL_DEF  	SIP_TRANSFER_PROTOCOL_UDP	

#define INVITE_STATUS_NORMAL 		0 	//邀请状态正常
#define INVITE_STATUS_INVITED 		1   //已经邀请
#define INVITE_STATUS_AUTH_PROCESS	2 	//等待用户处理
#define INVITE_STATUS_SUCCEED  		3   //邀请成功


#define SIP_LINE_CALLING_STATUS_IDLE 	0 //空闲
#define SIP_LINE_CALLING_STATUS_RING 	1 //响铃
#define SIP_LINE_CALLING_STATUS_CALLING	2 //正在通话
#define SIP_LINE_CALLING_STATUS_KEEP 	3 //保持

#define AUTO_HANGUP_RING_TIMEOUT_MAX 	60


#if 1
#define PJSIP_DEF_CONF_HAS_MSG_LOGGING  0		//默认关闭pjsip消息打印
#define PJSIP_DEF_CONF_CONSOLE_LEVEL 	4 		//pjsip logging输出等级
#define PJSIP_DEF_CONF_CLOCK_RATE		16000 	//Override conference bridge clock rate（0：def 8000，16000）
#define PJSIP_DEF_CONF_EC_OPTIONS		3 		//0=default 1=speex, 2=suppressor, 3=WebRtc 
#define PJSIP_DEF_CONF_EC_TAIL_LEN		20 		//Set echo canceller tail length (default=200)
#define PJSIP_DEF_CONF_MEDIA_QUALITY	8		//Specify media quality (0-10, default=4)
#define PJSIP_DEF_CONF_EN_VAD			0
#define PJSIP_DEF_CONF_SIP_LOCAL_PORT	5060

#else

//测试
#define PJSIP_DEF_CONF_HAS_MSG_LOGGING  1		//默认关闭pjsip消息打印
#define PJSIP_DEF_CONF_CONSOLE_LEVEL 	5 		//pjsip logging输出等级
#define PJSIP_DEF_CONF_CLOCK_RATE		8000 	//Override conference bridge clock rate（0：def 8000，16000）
#define PJSIP_DEF_CONF_EC_OPTIONS		3 		//0=default 1=speex, 2=suppressor, 3=WebRtc 
#define PJSIP_DEF_CONF_EC_TAIL_LEN		20 		//Set echo canceller tail length (default=200)
#define PJSIP_DEF_CONF_MEDIA_QUALITY	8		//Specify media quality (0-10, default=4)
#define PJSIP_DEF_CONF_EN_VAD			0
#define PJSIP_DEF_CONF_SIP_LOCAL_PORT	5060
#endif

/**
 * pjsip库信息配置结构体
 */
typedef struct pjsipLibConfig_t
{
	int 			console_level; //pjsip 终端logg输出等级
	unsigned		clock_rate; 	//媒体时钟
	unsigned		ec_options; 	//
    unsigned		ec_tail_len;	//
   	unsigned 		media_quality;	//
   	unsigned 		en_VAD;			//
   	int 			sipLocalPort;	//
}pjsipLibConfig_t;


/**
 * 
 */
enum sip_status_type
{
	SIP_STATUS_TYPE_NULL,  	//空
	SIP_STATUS_TYPE_INV, 	//邀请状态、pjsip回调
	SIP_STATUS_TYPE_CALL, 	//呼叫状态
	SIP_STATUS_TYPE_REG, 	//注册状态
};


/**
 * [PJ_THREAD_REGISTER_MACRO pjsip 线程注册]
 * @param  type [description]
 * @return      [description]
 */
#if 0
#define PJ_THREAD_REGISTER_MACRO(type) {\
	pj_thread_desc type;\
	pj_thread_t*    thread = 0;\
	if (!pj_thread_is_registered()){ printf("\n\tpj_thread_register #%s \n\n",#type); pj_thread_register(NULL, type, &thread);}\
}
#endif

#define PJ_THREAD_REGISTER_MACRO(type) pjcall_thread_register(#type)

#define DEFFACTORY_SIP_PASSWORD	 "123"

pj_status_t pjcall_thread_register(const char * str);
/**
 * 代理服务器信息
 */
typedef struct ProxyServer_t
{
	char serverip[SERVER_ADDRESS_LEN_MAX];//代理服务器地址
	int serverPort;//代理服务器端口号
	char userAccount[SIP_USER_NAME_LEN_MAX];//账号
	char password[SIP_PASSWORD_LEN_MAX];//密码
	char backupServer[SERVER_ADDRESS_LEN_MAX];//备份服务器地址
	int backupServerPort; //备份服务器端口号
	 
}ProxyServer_t;

/**
 * sip用户基本信息
 */
typedef struct SipUserInfo_t
{
	int index;								//用户索引
	int registerSwitch; 						//注册开关
	int registerProtocolType ; 				//注册协议类型
	int re_registrationFlag; 					//主机设置信息后重新注册标志
	char telephoneNumber[SIP_USER_NAME_LEN_MAX+1]; //电话号码
	char displayName[SIP_USER_NAME_LEN_MAX+1]; 	//显示名称
	char user[SIP_USER_NAME_LEN_MAX+1];			//用户名称
	char password[SIP_PASSWORD_LEN_MAX+1]; 		//用户密码
	char serverAddress[SERVER_ADDRESS_LEN_MAX+1]; //注册服务器地址
	unsigned char   registerStatus;						//用户注册状态
	unsigned char   registerStatusPrev;						//用户注册状态
	int reg_last_code;							//用户注册状态码
	int  registerTimeoutCount;					//用户注册超时计数
	int  serverPort; 							//端口号
	int  localPort; 							//本地sip通讯端口号

	int callStatus; 							//当前线路呼叫状态
	ProxyServer_t ProxyServerInfo; 				//代理服务器信息
	char localDomain[SIP_USER_NAME_LEN_MAX+1];	//本地域名
	char serverName[SIP_USER_NAME_LEN_MAX+1]; 	//服务器名称
	
	pjsua_acc_id acc_id; 						//用户账号ID添加账户时分配
//	pjsua_call_id call_id;						//用户呼叫时分配的id号

}SipUserInfo_t;



/**
 * SIP功能设置
 */
typedef struct SipFeatureSettingsConfig_t
{
	unsigned char DND_EN;//DND (Do Not Disturb)免打扰模式 0:正常 1：免打扰模式开启
	unsigned char isBanOutgoing;//禁止呼出 0:可呼出 1：不可呼出

	//自动应答线路选择 0:不开启自动应答 
	//0x01:线路1自动应答
	//0x02:线路2开启0x03
	//0x03:线路开启自动应答
	//0x04:线路和IP直拨自动应答
	unsigned char autoAnswerLineSlt;
	unsigned char autoAnswerTimeMs; 	 //0-60：自动应答 默认0 单位：毫秒
	unsigned char enableIntercomMute;//使能对讲模式静音 1：使能
	unsigned char enableIntercomTone;//使能对讲模式铃声 1：使能
	unsigned char enableAutoHangupRing;//使能自动超时自动挂断
	unsigned char autoHangupRingTimeout;//响铃超时自动挂断 0:无效 1~60（默认：0） 单位：秒
	unsigned char enableFeatureKeyAnswer; //功能键接听
	unsigned char enableSpeedDialKeyHangup;//Speed Dial键挂断

}SipFeatureSettingsConfig_t;

//extern SipFeatureSettingsConfig_t st_SipFeatureSettingsConfig; //设备SIP功能配置结构体

typedef struct sipCurStatus_t
{
	unsigned char curActiveCallLine;//当前活动的线路
	unsigned char lineCallStatus;//呼叫状态
	pjsua_call_id call_id; //当前呼叫ID
}sipCurStatus_t;
extern sipCurStatus_t st_SipCurStatus;


/**
 * [int  description]
 * @param  void [description]
 * @return      [description]
 */
typedef int (*checkSipCallSourceIsValid_Callbak)(void);
typedef int (*startSipCallSource_Callbak)(void);
typedef int (*stopSipCallSource_Callbak)(void);
typedef int (*NotifyAlterSipStatus_Callbak)(unsigned char status);


void SetSipCallBack(
	checkSipCallSourceIsValid_Callbak SourceIsValid_Callbak,
	startSipCallSource_Callbak 			StartCallSource_Callbak,
	stopSipCallSource_Callbak 			StopCallSource_Callbak, 
	NotifyAlterSipStatus_Callbak 		NotifySipStatus_Callbak);


unsigned char getSipCurStatus();

#define setSipCurStatus(s) __setSipCurStatus(s,__FILE__,__LINE__)
void __setSipCurStatus(unsigned char state,const char* file,int line);

int getSipInformationToArray(char * buf);
int checkSipCallSourceIsValid();
int startSipCallSource(int line);
int stopSipCallSource(int line);
int checkRtpMultSourceIsValid();
int startRtpMultRecvSource(unsigned char Volume);
int stopRtpMultRecvSource();
int NotifyAlterSipStatus( unsigned char status);


const char * getSipStatusStr(unsigned char status);
const char *getSipUserStatusStr(int user_index);

int reloadVoiceSettingsConfig();

int findAccountAutoAnswerFlagFormAccID(pjsua_acc_id acc_id);
void ResettingSipCurStateFlag();
int answerCurCall();
void HangupAllCalling(pjsip_status_code	last_status);

int HangupAllCallingStop();
int StopStreamMulticast();

//void dialNumTimeoutHandle(int tid);
//void hotKeyDialNumberTimeoutHandle(int tid);
int hotKeyIntercomDial(int line,const char* num1,const char*  num2);
int restartSipServer();
int reloadVoipConfig();
int PJSIP_AccountSet(int userID,const char * serverip,int port,const char * user,const char * password);
void DestroyStreamMulticast();
int InitVoipInfomation();
int pjsipInit();

int HostModifyAccountInfo(char isEnable,int outputVolume,char *serverip,int port,char * user, char * password, char transProtocol);
void saveRtpMulticastConfig();

unsigned char isDialNumber(void);
void setDialNumberFlag(unsigned char flag);
void setTheSameUserFlag(unsigned char flag);
unsigned char isTheSameUserInfo(void);
unsigned char isTheSameUserCompare(char *userInfo);
int setSipCallUserInfo(char *userInfo);
void cleanSipConversationUser(void);


SipUserInfo_t * getSipUserInfo(pjsua_acc_id acc_id);
void setAllUserCallStatus(int status);
void setPjsipLastCode(pjsip_status_code code);
void stopCallHandleTimer(pjsip_status_code	last_status);
int sipSourceCheckAndStart(int line,pjsua_call_id call_id);
void onSIPStatusChanged(int type,int line,int state);
int checkStringIsNum(const char *str);
int pjsuaIpAccAdd(SipUserInfo_t *user,pj_bool_t enReg);
void stopAutoAnswerTimer();
void displayAllUserInfo(void);
int checkSipRegisterAlready();
int checkLineIsIdle();


void Voip_Set_SystemSource_Idle_THREAD();

void sem_sip_conf_init();
void sem_sip_conf_send();


int saveSIPConfig();
#endif
