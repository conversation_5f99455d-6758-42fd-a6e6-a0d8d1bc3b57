#ifndef __API_TEST_H__
#define __API_TEST_H__ 

#define VAR_STR_TYPE_CHAR 	0
#define VAR_STR_TYPE_INT8 	1
#define VAR_STR_TYPE_INT16 	2
#define VAR_STR_TYPE_INT32 	3

typedef struct variableStr_t
{
	char type;
	void * address;
	char *varStr;
}variableStr_t;
void varStrListAdd(char type,void * address,char *varStr);

#define VARIABLE_STR_ADD(v)  varStrListAdd(VAR_STR_TYPE_CHAR,v,#v)
#define VARIABLE_INT8_ADD(v)  varStrListAdd(VAR_STR_TYPE_INT8,&v,#v)
#define VARIABLE_INT16_ADD(v)  varStrListAdd(VAR_STR_TYPE_INT16,&v,#v)
#define VARIABLE_INT32_ADD(v)  varStrListAdd(VAR_STR_TYPE_INT32,&v,#v)


#endif