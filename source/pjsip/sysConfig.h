
#ifndef __SYS_CONFIG_H__
#define __SYS_CONFIG_H__


#define CURRENT_DEVICE_MTD_NAME 	"ubi0:rootfs"

#define WAN_NET_IFNAME 		"eth0" 	//WAN 接口名称
#define BRNAME				"ethr"	//默认网桥名
#
/********** SIP参数 ********/
#define SIP_USER_NAME_LEN_MAX 		32
#define SIP_PASSWORD_LEN_MAX 		16

#define SIP_IDENTITY_LEN_MAX 		32
#define SIP_CONTACT_URL_LEN_MAX 	40
#define SERVER_ADDRESS_LEN_MAX 		20
#define SIP_SUPPORT 1

#define Heartbeat_Timeout_unregister	15

#define Heartbeat_Timeout_BASE 60	//注册间隔

#define Heartbeat_Timeout_VAILD (360+10)	//有效周期 3min 避免因网络造成的分机下线问题
#

#define SIP_STATUS_REGISTER_SUCCEED 1
#define SIP_STATUS_REGISTER_LOADING 2
#define SIP_STATUS_REGISTER_FAILED_OTHER  3
#define SIP_STATUS_REGISTER_FAILED_TIMEOUT 4
#define SIP_STATUS_REGISTER_FAILED_PASSWORD 5
#define SIP_STATUS_REGISTER_CALLING 6

#define VOICE_VAD_DEF 				0 	//默认VAD (0 | 1)
#define VOICE_CALL_VOL_DEF 			80 //默认通话音量(0~100)
#define VOICE_MIC_INPUT_LEVEL_DEF 	5  //默认麦克风输入级别（1~9，默认5）
#define VOICE_MIC_MEDIA_VOL_DEF 	80 //未用到


#define HAS_CALL_TONES 		1 //呼叫时铃声

#define SYSTEM_CALL_RING_DIR "/customer/App/ring/"
#define RING_FILE_NO_ANSWER 	SYSTEM_CALL_RING_DIR "no_answer.wav" //未应答、或者未接通提示
#define RING_FILE_CALLING	 	SYSTEM_CALL_RING_DIR "calling_ring.wav"//拨号时提示音
#define RING_FILE_INCOMING	 	SYSTEM_CALL_RING_DIR "incoming_ring.wav"//拨号时提示音
#define RING_FILE_INTERCOM_TONE	 	SYSTEM_CALL_RING_DIR "intercomTone.wav"//对讲模式响铃

#define RING_FILE_BAN_OUTGOING 	SYSTEM_CALL_RING_DIR "ban_outgoing.wav"//禁止呼出
#define RING_FILE_NOT_ADDRESS 	SYSTEM_CALL_RING_DIR "ipAddress_not.wav"//未获取ip地址
#define RING_FILE_FACTORY_RESET SYSTEM_CALL_RING_DIR "Initializing.wav"//恢复出厂设置
#define RING_FILE_BUSY SYSTEM_CALL_RING_DIR "busy.wav"//忙音

#endif