#ifndef __IO_DETECTION_H__
#define __IO_DETECTION_H__ 

#define IO_INPUT_MAX_COUNT 7 	//输入口总数

#define KEY_PRESS_LONG_TIMEOUT_MAX	3 	//按键长按超时时间3s

#define FUNCTION_KEYS_COUNT_MAX 	4 	//最大功能键个数

#define FUNCTION_KEYS_DSS_KEY_LEN_MAX 	32
#define FUNCTION_KEYS_TYPE_NAME_LEN_MAX 	16

/**
 * 功能键类型
 */
enum functionKeyType_e
{
	FUNCTION_NONE=0,//无效类型
	FUNCTION_HOT_KEY,//热键-快捷键
	FUNCTION_LINE_SLT,//线路选择
	FUNCTION_KEY_EVENT,//按键事件
	FUNCTION_TYPE_MULTICAST,//发送组播
	FUNCTION_TYPE_MAX, 		//按键类型最大值
};

enum functionKeySubType_e
{
	FUNCTION_SUB_NONE=0,//无效类型
	FUNCTION_SUB_HOT_SPEED_DIAL,//速拨号
	FUNCTION_SUB_HOT_INTERCOM,//对讲
	FUNCTION_SUB_EVENT_RELEASE,//挂断
	FUNCTION_SUB_EVENT_OK,//确认或者接听
	FUNCTION_SUB_EVENT_HANDFREE,//免提	
	FUNCTION_SUB_MULT_CODE_G711A,//组播编码
	FUNCTION_SUB_MULT_CODE_G711U,//组播编码
	FUNCTION_SUB_MULT_CODE_G722,//组播编码
	FUNCTION_SUB_MULT_CODE_GSM,//组播编码
	FUNCTION_SUB_TYPE_MAX,

};

/**
 * 按键类型定义
 */
typedef struct functionKeyType_t
{
	unsigned char type; //按键类型
	char typeName[FUNCTION_KEYS_TYPE_NAME_LEN_MAX]; //类型名称
}functionKeyType_t;

/**
 * 功能键配置
 */
typedef struct functionKeysDSS_t
{
	unsigned char index;			//功能键编号
	functionKeyType_t type;	//功能类型
	functionKeyType_t subType;//功能子类型
	char key1[FUNCTION_KEYS_DSS_KEY_LEN_MAX];
	char key2[FUNCTION_KEYS_DSS_KEY_LEN_MAX];
	unsigned char line; 	//选择的线路：0：无效 1：选择线路1 2：
	void (*action_cb)(void*user_data); //触发后的回调函数
}functionKeysDSS_t;


enum IO_trig_mode_e
{
	IO_TRIG_MODE_CLOSE=0,
	IO_TRIG_MODE_OPEN,
};

enum GPIO_status_e
{
	GPIO_STATUS_LOW=0,
	GPIO_STATUS_HIGHT,
	GPIO_STATUS_UNKNOWN
};

enum GPIO_trig_type_e
{
	IO_TRIG_TYPE_PRESS=0,//按下触发
	IO_TRIG_TYPE_RELEASE,//松开触发
	IO_TRIG_TYPE_PRESS_LONG,//长按触发
};

enum GPIO_input_enable_e
{
	GPIO_INPUT_DISABLE,//
	GPIO_INPUT_ENABLE,
};
enum GPIO_output_enable_e
{
	GPIO_OUTPUT_DISABLE,//
	GPIO_OUTPUT_ENABLE,
};

//typedef int (*on_statusChange_cb)(unsigned char );
//typedef void (*on_action_cb)(void *userData);
/**
 * io输入检测配置
 */
typedef struct io_Input_t
{
	unsigned char index;
	unsigned char enable; 	//是否启用该io检测 	
	unsigned char triggerMode; //触发模式 常开触发、短路触发
	unsigned char trigType ;// 触发类型 按下触发、松开触发、长按触发
	unsigned char curStatus; //当前状态
	unsigned char prvStatus; //上一次状态
	unsigned char downTime;//长按触发秒数
	unsigned char pressLongTimeout;//长按触发秒数
	int dispelTid; //消抖超时

	char name[FUNCTION_KEYS_DSS_KEY_LEN_MAX]; //输入名称
	int keyCode; 	//键码
	int tid;
	int  (*on_statusChange_cb)(unsigned char status);//状态改变回调
	void (*on_action_cb)(void *userData);//io触发后执行的动作回调
	functionKeysDSS_t * funcKeyInfo;
}io_Input_t; 


//io_Input_t io_Input[IO_INPUT_MAX_COUNT];




enum IO_out_mode_e
{
	IO_OUT_MODE_CLOSE=0,//常闭
	IO_OUT_MODE_OPEN,//常开
};

enum IO_trig_call_status_e
{
	IO_TRIG_CALL_STATUS_CALLING,
	IO_TRIG_CALL_STATUS_CALLING_AND_RING,
	IO_TRIG_CALL_STATUS_RING,	
};


#define GPIO_OUT_TRIG_WAY_IO_INPUT 	0x01 //输入口触发
#define GPIO_OUT_TRIG_WAY_IO_DTMF 	0x02//DTMF触发
#define GPIO_OUT_TRIG_WAY_IO_SMS 	0x04//短消息触发
#define GPIO_OUT_TRIG_WAY_CALL_STATUS 	0x08 //通话状态触发
// #define GPIO_OUT_TRIG_WAY_IO_INPUT 	0x10
// #define GPIO_OUT_TRIG_WAY_IO_INPUT 	0x20

//IO输出口在输入IO口的最大值，2 则为可实现两个IO口输入触发
#define IO_INPUT_QUEUE_MAX 	2

//IO输出最大个数
#define IO_OUT_QUEUE_MAX 	2
#define IO_OUT_DTMF_STR_LEN 	11


/**
 * io输出触发配置
 */
typedef struct io_output_t
{
	unsigned char enable; 	//是否启用该io检测 	
	unsigned char gpioNo;	//gpio 序号,用于驱动控制
	unsigned char curStatus; //当前状态
	unsigned char prevStatus;//上一次状态
	unsigned char outMode;//输出模式
	unsigned char prevOutMode;//上一次的触发模式
	unsigned char outKeepTime;//输出持续时间 (0~600秒)--0为保持当前状态
	unsigned char outTrigWay;//输出触发方式
	unsigned char IoInputIndex[IO_INPUT_QUEUE_MAX]; //所选择的IO输入触发端口index IoInputIndex[0]=4;IoInputIndex[1]=5
	unsigned char callStatusTrigSlt;//	通话触发状态选中
	int tid; //定时器id 用于计数保持输出时间
	void (* ioStatusCtl_cb)(unsigned char status); //io输出状态控制回调
	char dtmf[IO_OUT_DTMF_STR_LEN];
	char sms[IO_OUT_DTMF_STR_LEN];
	char name[FUNCTION_KEYS_DSS_KEY_LEN_MAX]; //输出名称
/*
	io_Input_t *ioInputQueue[IO_INPUT_QUEUE_MAX];//选择的IO输入
	void(* on_ioInputTrig_cb)(void);//输入触发回调,可执行其他功能命令
	void(* on_dtmfTrig_cb)(const char * dtmf);//dtmf触发回调
	void(* on_smsTrig_cb)(const char * sms);//sms触发回调
*/
}io_output_t;


#endif