#include "sip.h"
#include "timerEx.h"
#include "appConfig.h"
#include "util.h"
#include "../log/my_log.h"
#include "pjsuaCommon.h"

#define THIS_FILE __FILE__

int AutoAnswerTid =-1;//自动应答定时点
int AutoHangupRingTimeoutTid =-1;//自动挂断响铃超时定时定时器
extern int dialNumTimeout_tid;
extern SipUserInfo_t ipCallUser;
static int entertimes=0;
static const pjmedia_sdp_session *rem_sdp_session=NULL;
static int Remote_Vedio_Port=0;
static pj_timer_entry auto_hangup_timer; //自动挂断定时器,目前未使用 2018年11月6日10:12:26
static pj_timer_entry auto_answer_timer; //自动应答定时器
 int local_SDP_Port=0;
 int g_local_port=5008;
int inv_rem_video_sdp_get = 0;
static int audio_way=0;
/**
 * [Set_Audio_Way description]
 * @return [description]
 */
void Set_Audio_Way(int values)
{
	NOTICE("[%d]设置为语音接听%d",__LINE__,values);
	//if(GetCurrentDeviceMode()==DEVICE_MODEL_DSP9326)
	audio_way=values;
	
}

static void hangupTimeout_callback(pj_timer_heap_t *timer_heap,
				    struct pj_timer_entry *entry)
{
	PJ_UNUSED_ARG(timer_heap);
    PJ_UNUSED_ARG(entry);

    NOTICE("hangup Timeout callback...");
    auto_hangup_timer.id = 0;
    pjsua_call_hangup_all();
}

static void autoAnswerTimeout_callback(pj_timer_heap_t *timer_heap,
				struct pj_timer_entry *entry)
{
	PJ_UNUSED_ARG(timer_heap);
	PJ_UNUSED_ARG(entry);
	NOTICE("auto Answer Timeout callback...");
	if(st_SipCurStatus.lineCallStatus != SIP_LINE_CALLING_STATUS_RING){
		WARNINGP("Auto Answer Handle But Cur Status Is Not Ring!");		
		return ;
	}
	auto_answer_timer.id = 0;
	//优先级检测,无呼叫ID
	if( sipSourceCheckAndStart(st_SipCurStatus.curActiveCallLine,-1) < 0 ){
    	return ;
    }
	//DestroyStreamMulticastInfo();
	PJ_LOG(3 , ("timer" , "incoming_auto_answer_callback")) ;
	NOTICE("auto answer check call_id=%d...",st_SipCurStatus.call_id);
	if(st_SipCurStatus.call_id<0){
		WARNINGP("Cur sip call_id<0 ")
		return ;
	}

	//应答200
	pjsua_call_answer(st_SipCurStatus.call_id, 200, NULL, NULL);

	//st_SipCurStatus.lineCallStatus = SIP_LINE_CALLING_STATUS_CALLING;
	//setSipCurStatus(SIP_LINE_CALLING_STATUS_CALLING);
	//SIP当前状态通知
	//NotifyAlterSipStatus(SIP_STATUS_REGISTER_CALLING);
}

/**
 * [AutoAnswerHandle 自动应答处理,如果手动接听后则清除该计时]
 * @param tid [description]
 */
static  void AutoAnswerHandle(int tid)
{
	//注册线程	
	PJ_THREAD_REGISTER_MACRO(AutoAnswerHandle);
	DeleteTimer(tid);
	AutoAnswerTid = -1;
	if(st_SipCurStatus.lineCallStatus != SIP_LINE_CALLING_STATUS_RING){
		WARNINGP("Auto Answer Handle But Cur Status Is Not Ring!");		
		return ;
	}

	//优先级检测,无呼叫ID
	if( sipSourceCheckAndStart(st_SipCurStatus.curActiveCallLine,-1) < 0 ){
    	return ;
    }   
	//DestroyStreamMulticastInfo();
	PJ_LOG(3 , ("timer" , "incoming_auto_answer_callback")) ;
	NOTICE("auto_answer_callback");
	if(st_SipCurStatus.call_id<0){
		WARNINGP("Cur sip call_id<0 ")
		return ;
	}
	setSipCurStatus(SIP_LINE_CALLING_STATUS_CALLING);
	//SIP当前状态通知
	NotifyAlterSipStatus(SIP_STATUS_REGISTER_CALLING);
	//应答200
	pjsua_call_answer(st_SipCurStatus.call_id, 200, NULL, NULL);
	//st_SipCurStatus.lineCallStatus = SIP_LINE_CALLING_STATUS_CALLING;

}


/**
 * [autoHangupRingTimeoutHandle 响铃超时自动挂断处理]
 * @param tid [description]
 */
static void autoHangupRingTimeoutHandle(int tid)
{
	NOTICE("auto Hangup Ring Timeout Handle...");
	DeleteTimer(tid);
	AutoHangupRingTimeoutTid =-1;
	//st_SipCurStatus.lineCallStatus = SIP_LINE_CALLING_STATUS_IDLE;
	//注册线程
	PJ_THREAD_REGISTER_MACRO(autoHangupRingTimeout);
	HangupAllCalling(200);//超时挂断
}



//--------------------------PJSUA回调函数-------------------------------//

#if 1

/**
 * [pjmsgCheckCallInfo 检测sdp携带的呼叫信息，用于自动接听]
 * @param  msg [sip msg]
 * @return     [-1:未找到call-info; >=0 成功]
 */
static int pjmsgCheckCallInfo(const pjsip_msg *msg)
{
	pj_str_t name = pj_str("Call-Info"); //查找Call-Info头
	struct pjsip_hdr *hdr = pjsip_msg_find_hdr_by_name(msg,&name,NULL);
	if(hdr){
		if(hdr->vptr){
			if(hdr->vptr->print_on){
				char buf[200]={0};
				hdr->vptr->print_on((void*)hdr,buf,200);
				//printf("pjsip_msg_print buf=[%s]##\n",buf );
				//处理消息体中带有 answer-after=0 自动应答选项时可则直接应答
				if(strstr(buf,"answer-after=")){
					LOG("pjsip msg <%s> auto answer!",buf);
					return SUCCEED;
				}
			}
		}
	}
	return -1;	
}


/* Callback called by the library upon receiving incoming call */
static void on_incoming_call(pjsua_acc_id acc_id, pjsua_call_id call_id,
			     		pjsip_rx_data *rdata)
{		
	pjsua_call_info call_info;
    pj_time_val delay;
	SipUserInfo_t * user =NULL;
	WARNINGP("-------------------------on_incoming_call");
	//printf("g_chating_flag:%d\n", g_chating_flag);
	//char option[10];

//	g_make_call_be_canceled_flag = 0;
	//g_incoming_call_cancle_flag_when_no_call = 0;
	
  
  
   	// PJ_UNUSED_ARG(rdata);
   
/*   
    NOTICE("pjsip_rx_data_get_info=%s",pjsip_rx_data_get_info(rdata));
    if(rdata->msg_info.msg_buf){
    	printf("rdata->msg_info.msg_buf=[%s]##\n",rdata->msg_info.msg_buf);
    }
*/   
   // pjsip_msg_print(rdata->msg_info.msg,buf,2000);
   
    /*
    if(rdata->msg_info.require){
    	NOTICE("rdata->msg_info.require->count=%d",rdata->msg_info.require->count);
		int i = 0;
    	for (i = 0; i < rdata->msg_info.require->count; ++i)
    	{
    		NOTICE("rdata->msg_info.require->values[%d]=%s",i,rdata->msg_info.require->values[i].ptr);
    	}
    	
    	
    }*/

    pjsua_call_get_info(call_id, &call_info);

    //是否为本端发起且未挂断
    if(!isDialNumber()){
    	//对端发起，记录发起端
    	setSipCallUserInfo(call_info.remote_info.ptr);
    }else if(isTheSameUserCompare(call_info.remote_info.ptr)){
    	//发起端相同，设置同端标志 本号拨打本号
    	setTheSameUserFlag(1);
    }else{
    	//本端发起且未挂断
    	setTheSameUserFlag(0);
    }

    PJ_LOG(3,(THIS_FILE, 
    	  "Incoming call for account %d!\n"
		  "Media count: %d audio & %d video\n"
		  "From: %s\n"
		  "To: %s \n---end!\n",
		  acc_id,
		  call_info.rem_aud_cnt,
		  call_info.rem_vid_cnt,
		  call_info.remote_info.ptr,
		  call_info.local_info.ptr));
    LOG("Incoming call for account %d,From %s to %s.", acc_id, call_info.remote_info.ptr,call_info.local_info.ptr);
    user = getSipUserInfo(acc_id);
    if(!user){
    	if( ipCallUser.acc_id == acc_id){
    		user=&ipCallUser;
    	}
    }

    if(!user) {
    	ERRORP("acc_id %d no found!",acc_id);
    	return;
    }

    DBG("Index:%d, User:%s, Server:%s",user->index, user->user, user->serverAddress);
    displayAllUserInfo();
	//488 not acceptable here 这里请求不可接受
	//如果状态不为正常则放回繁忙
	//if (SIP_LINE_CALLING_STATUS_IDLE != st_SipCurStatus.lineCallStatus)
	if ( !checkLineIsIdle())
	{
		LOG("Calling Busy...486");
		pjsua_call_answer(call_id, 486, NULL, NULL);
	}else if(appData.sipFeatureSettingsConfig.DND_EN){
		LOG("Device DND Mode Is Enable...400");
		pjsua_call_answer(call_id, 403, NULL, NULL);
	}else if(checkSipRegisterAlready() && !strstr(call_info.local_info.ptr,"@")){
		//2019-8-26 修改已注册SIP帐号，IP直拨不可用
		LOG("Device SIP Account Registered, IP Dialing Disabled.");
		pjsua_call_answer(call_id, 403, NULL, NULL);
	}else{
		if( !checkSipCallSourceIsValid()){
			ERRORP("sip Source Check Start Wrong Busy...486");
			pjsua_call_answer(call_id, 486, NULL, NULL);
			return ;
		}
		if(user){
			st_SipCurStatus.curActiveCallLine = user->index+1;			
			onSIPStatusChanged(SIP_STATUS_TYPE_CALL,user->index+1,SIP_LINE_CALLING_STATUS_RING);
		}
		//来电标志
		//st_SipCurStatus.lineCallStatus = SIP_LINE_CALLING_STATUS_RING;
		setSipCurStatus(SIP_LINE_CALLING_STATUS_RING);
		st_SipCurStatus.call_id = call_id; //设置当前状态

		if(pjmsgCheckCallInfo(rdata->msg_info.msg) >=0 ){

			if(appData.sipFeatureSettingsConfig.enableIntercomTone == 1){
				pjsua_on_Intercom_call(acc_id,call_id,rdata);
				//应答200
				delay.sec = 2; //响铃4秒
	   			delay.msec = 0; /* Give 200 ms before hangup */
	   			 /* Timer is already active */
	   			if(auto_answer_timer.id == 1){
	   				WARNINGP("auto answer Timer is already active.");
	   				return ;
	   			}
	   			auto_answer_timer.id = 1;
	   			//定时器改用pjsip内部定时器，使用外部定时器曾导致bus error错误，具体原因待调试
				pjsip_endpt_schedule_timer(pjsua_get_pjsip_endpt(), 
				       &auto_answer_timer, 
				       &delay);
			}else{
				//优先级检测,无呼叫ID
				if( sipSourceCheckAndStart(st_SipCurStatus.curActiveCallLine,-1) < 0 ){
					pjsua_call_answer(call_id, 486, NULL, NULL);
				}
				else
				{
					pjsua_call_answer(st_SipCurStatus.call_id, 200, NULL, NULL);
				}
			}
			
			///
			return;
		}

		int autoAnswerTimeMs = findAccountAutoAnswerFlagFormAccID(acc_id); //查找当前用户ID是否开启自动应答
		if(autoAnswerTimeMs > 0 ){
			//开启自动应答
			NOTICE("Calling ...Auto Answer(%d ms)",autoAnswerTimeMs);
			
			//pjsua_call_setting opt;

/*视频应答*/
			// pjsua_call_setting_default(&opt);
			// opt.vid_cnt = 1;
			// pjsua_call_answer2(call_id, &opt, 200, NULL,NULL);
			// 
			
			//pjsua_call_answer(st_SipCurStatus.call_id, 200, NULL, NULL);			
			//AutoAnswerTid = CreateTimer(autoAnswerTimeMs*1000,1,AutoAnswerHandle);
				//应答200
			//TODO ********暂时固定时间为200ms
			delay.sec = 0;//autoAnswerTimeMs;
   			delay.msec = autoAnswerTimeMs; /* Give 200 ms before hangup */

   			 /* Timer is already active */
   			if(auto_answer_timer.id == 1){
   				WARNINGP("auto answer Timer is already active.");
   				return ;
   			}  					
			
   			auto_answer_timer.id = 1;
   			//定时器改用pjsip内部定时器，使用外部定时器曾导致bus error错误，具体原因待调试
			pjsip_endpt_schedule_timer(pjsua_get_pjsip_endpt(), 
			       &auto_answer_timer, 
			       &delay);
			//pjsua_call_answer(st_SipCurStatus.call_id, 200, NULL, NULL);
			//st_SipCurStatus.lineCallStatus = SIP_LINE_CALLING_STATUS_CALLING;
			////SIP当前状态通知
			//NotifyAlterSipStatus(SIP_STATUS_REGISTER_CALLING);
		}else if(autoAnswerTimeMs == 0){
			//优先级检测,无呼叫ID
			if( sipSourceCheckAndStart(st_SipCurStatus.curActiveCallLine,-1) < 0 ){
				pjsua_call_answer(call_id, 486, NULL, NULL);
			}
			else
			{
				pjsua_call_answer(st_SipCurStatus.call_id, 200, NULL, NULL);
			}
			return;
		}else{
			//不为自动应答则判断响铃超时自动挂断,被叫时无效
			//if(appData.sipFeatureSettingsConfig.autoHangupRingTimeout)//响铃超时处理
			//{
			//	NOTICE("Ring timeout(%d) auto hangup enable!",appData.sipFeatureSettingsConfig.autoHangupRingTimeout);
			//	AutoHangupRingTimeoutTid = CreateTimer(appData.sipFeatureSettingsConfig.autoHangupRingTimeout*1000,1,autoHangupRingTimeoutHandle);
			//}
			//
			NOTICE("Wait User Answer Call...");
		}

		pjsua_on_incoming_call(acc_id,call_id,rdata);
	}
}

/* Callback called by the library when call's state has changed */
static void on_call_state(pjsua_call_id call_id, pjsip_event *e)
{
	printf("-------------on_call_state----------");
#if 0
typedef enum pjsip_inv_state
{
    PJSIP_INV_STATE_NULL,	    /**< Before INVITE is sent or received  */
    PJSIP_INV_STATE_CALLING,	    /**< After INVITE is sent		    */
    PJSIP_INV_STATE_INCOMING,	    /**< After INVITE is received.	    */
    PJSIP_INV_STATE_EARLY,	    /**< After response with To tag.	    */
    PJSIP_INV_STATE_CONNECTING,	    /**< After 2xx is sent/received.	    */
    PJSIP_INV_STATE_CONFIRMED,	    /**< After ACK is sent/received.	    */
    PJSIP_INV_STATE_DISCONNECTED,   /**< Session is terminated.		    */
} pjsip_inv_state;
#endif

	pjsua_call_info call_info;

	PJ_UNUSED_ARG(e);
	pjsua_on_call_state(call_id,NULL); //第二级处理

    pjsua_call_get_info(call_id, &call_info);
    NOTICE("## acc_id <%d> Calling Status Changed to %s,code=%d",call_info.acc_id,call_info.state_text.ptr,call_info.last_status);
	pj_parsed_time connPtime,totalPtime;
	pj_time_decode( &call_info.connect_duration, &connPtime);
	pj_time_decode( &call_info.total_duration, &totalPtime);

	NOTICE("connect duration:%d-%02d-%02d %02d:%02d:%02d",connPtime.year,connPtime.mon,connPtime.day,connPtime.hour,connPtime.min,connPtime.sec);
	NOTICE("total   duration:%d-%02d-%02d %02d:%02d:%02d",totalPtime.year,totalPtime.mon,totalPtime.day,totalPtime.hour,totalPtime.min,totalPtime.sec);
 
    //NOTICE("connect duration:%ds,total duration %ds.",call_info.connect_duration.sec,call_info.total_duration.sec);

//    call_info.connect_duration.sec
//	call_info.connect_duration.msec
//	call_info.total_duration.sec
//	call_info.total_duration.msec

/*
    PJ_LOG(3,(THIS_FILE, "Call %d state=%.*s", call_id,
			 (int)call_info.state_text.slen,
			 call_info.state_text.ptr));
*/
//    pjsip_rx_data_get_info(rdata);
//	printf("rdata->msg_info.msg->line.req.method.name.ptr:%s\n", rdata->msg_info.msg->line.req.method.name.ptr);
	//rdata->msg_info.msg->line.req.method.name.ptr

    //当对方挂断时，将标志清空
		//if(call_info.state == PJSIP_INV_STATE_DISCONNECTED)
 	//if(!strcmp(call_info.state_text.ptr, "DISCONNCTD"))
 	//{	
 	//	
 	//}
/*
 	//建立通话
 	if(!strcmp(call_info.state_text.ptr, "CONFIRMED"))
 	{	
 		//当对方接听时，设置标志
 		if(g_make_call_flag == 1)
 		{	
 			g_make_call_flag = 0;			 //清除打电话标志
 			g_make_call_be_answered_flag = 1;//这个也可以代表拨号后正在通话中的状态，主要用于判断对方接起电话			
 		}

 		g_current_call = incoming_id;		//当前通话的电话值
 		g_chating_flag = 1;				//这个是拨号或来电正在通话的状态
 	}
*/
	int timeVal = connPtime.hour+connPtime.min+connPtime.sec;
 	SipUserInfo_t * user = getSipUserInfo(call_info.acc_id);
	if(user){
		st_SipCurStatus.curActiveCallLine = user->index+1;
	}else{
		WARNINGP("Calling find sip user failure!");
	}
 	switch(call_info.state){
 		case PJSIP_INV_STATE_NULL:
 			Set_Audio_Way(0);
 			WARNINGP("Before INVITE is sent or received...");
 			break;
		case PJSIP_INV_STATE_CALLING:
			WARNINGP("After INVITE is sent...");
			setSipCurStatus(SIP_LINE_CALLING_STATUS_RING);

			break;
		case PJSIP_INV_STATE_INCOMING:
			WARNINGP("After INVITE is received...");
			setSipCurStatus(SIP_LINE_CALLING_STATUS_RING);
			break;
		case PJSIP_INV_STATE_EARLY:
			WARNINGP("After response with To tag...");
			 /* Start ringback */
			ring_start(call_id,0);	
			DBG("is Dial Number %s",isDialNumber()?"YES":"NO");
		   	if(isTheSameUserCompare(call_info.remote_info.ptr)){
		    	setTheSameUserFlag(1);
		    }else{
		    	setTheSameUserFlag(0);
		    }				
			break;
		case PJSIP_INV_STATE_CONNECTING:
			WARNINGP("After 2xx is sent/received...");
		    if(isTheSameUserCompare(call_info.remote_info.ptr)){
		    	setTheSameUserFlag(1);
		    }else{
		    	setTheSameUserFlag(0);
		    }	
			break;
		case PJSIP_INV_STATE_CONFIRMED:
			WARNINGP("After ACK is sent/received...");
			//st_SipCurStatus.lineCallStatus = SIP_LINE_CALLING_STATUS_CALLING;
			setSipCurStatus(SIP_LINE_CALLING_STATUS_CALLING);
			st_SipCurStatus.call_id = call_id;
			stopCallHandleTimer(call_info.last_status);
			//SIP当前状态通知
			NotifyAlterSipStatus(SIP_STATUS_REGISTER_CALLING);
			break;
		case PJSIP_INV_STATE_DISCONNECTED:
			Set_Audio_Way(0);
			local_SDP_Port=0;
			entertimes=0;
			NOTICE("Session is terminated...");

			if(call_info.last_status == 486 && timeVal == 0){
				//需要判别发起方
				printf("isDialNumber() %d  isTheSameUserInfo() %d getSipCurStatus %d \n",isDialNumber(),isTheSameUserInfo(),getSipCurStatus());
				if((isDialNumber() || isTheSameUserInfo()) && getSipCurStatus()==SIP_STATUS_REGISTER_SUCCEED ) {
					//本端发起繁忙挂断
					LOG("Call Busy. Stop Current Calling.");
					ring_start(call_id,3);
					sleep(3);
					ring_stop(call_id);	
					setPjsipLastCode(call_info.last_status);
					stopSipCallSource(st_SipCurStatus.curActiveCallLine);//停止sip呼叫
					cleanSipConversationUser();	
					
				}else{
					//对端发起繁忙保持
					if(getSipCurStatus()==SIP_STATUS_REGISTER_CALLING)
					{
						LOG("Call Busy. Keep Current Calling.");
						setPjsipLastCode(SIP_LINE_CALLING_STATUS_CALLING);
					}
					else
					{
						setPjsipLastCode(SIP_LINE_CALLING_STATUS_IDLE);
						stopSipCallSource(st_SipCurStatus.curActiveCallLine);//停止sip呼叫	
						cleanSipConversationUser();
						setDialNumberFlag(0);//清空主叫标志，防止重复操作
					}
				}
			}else{
				//正常挂断
				LOG("Session terminated connect duration %02d:%02d:%02d.",connPtime.hour,connPtime.min,connPtime.sec);
				setPjsipLastCode(call_info.last_status);
				stopSipCallSource(st_SipCurStatus.curActiveCallLine);//停止sip呼叫	
				cleanSipConversationUser();
				setDialNumberFlag(0);//清空主叫标志，防止重复操作
				setAllUserCallStatus(call_info.state);
				ResettingSipCurStateFlag();
			}
			break;
		default:
			WARNINGP("No Handle call state %d",call_info.state);
			break;
 	}
	#if 0
 	if(user){
 		if(call_info.last_status == 486 && timeVal == 0){
 			printf("[502]isDialNumber  %d isTheSameUserInfo() %d call_info.state %d \n",isDialNumber(),isTheSameUserInfo(),call_info.state);
 			if(isDialNumber() || isTheSameUserInfo()){
 				printf("call_info.state %d \n",call_info.state);
 				onSIPStatusChanged(SIP_STATUS_TYPE_INV ,user->index+1,call_info.state);
 				setDialNumberFlag(0);//清空主叫标志，防止重复操作 			
 			}else{
				
 			}		
 		}else{
 			if(call_info.state == PJSIP_INV_STATE_DISCONNECTED){
 				setAllUserCallStatus(call_info.state);
				ResettingSipCurStateFlag();
 			}else{
 				onSIPStatusChanged(SIP_STATUS_TYPE_INV ,user->index+1,call_info.state);
 			}
 		}
 	}
	#endif
}



/* 媒体状态回调 */
static void on_call_media_state(pjsua_call_id call_id)
{
	printf("on_call_media_state-------------------------\n");

    pjsua_call_info call_info;
    pjsua_on_call_media_state(call_id);
    pjsua_call_get_info(call_id, &call_info);

	printf("call_info.conf_slot:%d\n", call_info.conf_slot);

	if (call_info.media_status == PJSUA_CALL_MEDIA_ACTIVE)
	{
		pjsua_conf_connect(call_info.conf_slot, 0);//call单向连接本地
		pjsua_conf_connect(0, call_info.conf_slot);//本地单向连接到call
		//提高输出音量
		pjsua_conf_adjust_rx_level(0, 5);//调节数字输出等级(int)((level-1) * 128)
		//pjsua_conf_adjust_tx_level(0, 3.0);//调节数字输出等级(int)((level-1) * 128)
#if HAS_RTSP_VIDIO_SUPPORT	
		printf("stream start media_cnt=%d rem_vid_cnt=%d........\n",call_info.media_cnt,call_info.rem_vid_cnt);
		/**
		 * 如果当前以广播方式发起
		 */ 
		if(audio_way)
		{
			return; 
		}
		//临时call_info.rem_vid_cnt==0  原为call_info.rem_vid_cnt
		if(call_info.rem_vid_cnt){
			on_call_media_state_vid(call_id);
			//sdp->media[1].desc.media.;
			//rem_sdp_session = rem_sdp;
			//if(rem_sdp_session){
			//	char sdpBuf;
			//	bzero(sdpBuf,1024);
			//	pjmedia_sdp_print(rem_sdp_session,sdpBuf,1000);
			//	printf("===============rem_sdp:\n%s ##\n",sdpBuf);
			//	//获取远端视频rtp端口
			//	printf("===============rem_sdp video prot =%d,transport=%s",rem_sdp_session->media[1]->desc.port,rem_sdp_session->media[1]->desc.transport.ptr);
			//}
			return;
		}else{			
			pjsua_call *call;
			call = &pjsua_var.calls[call_id];
			pjmedia_sdp_session *remote=NULL;
			pjmedia_sdp_session *local=NULL;
			//pjmedia_sdp_neg_get_active_remote			
			if(call->inv)
			if(call->inv->neg){
				pjmedia_sdp_neg_get_active_remote( call->inv->neg,&remote);	
				pjmedia_sdp_neg_get_active_local( call->inv->neg,&local);
				if(remote->media_count<=1)
				{
					return ;//音频发起
				}	
				char sdpBuf[1024]={0};
				char localsdp[1024]={0};
				if(remote){			
					pjmedia_sdp_print(remote,sdpBuf,1000);
					pjmedia_sdp_print(local,localsdp,1000);
					Remote_Vedio_Port=remote->media[1]->desc.port;
					printf("neg active_remote_sdp:\n%s ##\n",sdpBuf);
					printf("local->media_count %d neg active_local_sdp:\n%s ##\n",local->media_count,localsdp);
					printf("local->media[1]->desc.port %d \n",local->media[1]->desc.port);
						if(local->media_count>1)
						if(local->media[1]->desc.port!=0)
						{
							printf("[%d %s]local->media[1]->desc.port 赋值 %d\n",__LINE__,__FUNCTION__,local->media[1]->desc.port );
							local_SDP_Port=local->media[1]->desc.port;
						}
					
#if HAS_RTSP_VIDIO_SUPPORT
						if(local->media_count>1){
							
							on_call_sdp_created_vid(call_id,local,call->inv->pool,remote);
							//return;
						}
#endif

					on_call_media_state_vid(call_id);				
				}
			}else{
				printf("neg active_remote_sdp is NULL\n");
			}
		}

		//结束时将复制出来的
		//video_sdp_media = NULL;
#endif //HAS_RTSP_VIDIO_SUPPORT
	
		/* Put call in conference with other calls, if desired 将所媒体通道连接起来*/

	  //   pjsua_call_id call_ids[PJSUA_MAX_CALLS];
	  //   unsigned call_cnt=PJ_ARRAY_SIZE(call_ids);
	  //   unsigned i;

	  //   /* Get all calls, and establish media connection between
	  //    * this call and other calls. 
	  //    */
	  //   pjsua_enum_calls(call_ids, &call_cnt);

	  //   for(i=0; i<call_cnt; ++i) 
	  //   {	
	  //   	if(call_info.conf_slot == pjsua_call_get_conf_port(call_ids[i]))
	  //   		continue;
			
			// if (!pjsua_call_has_media(call_ids[i]))
			//     continue;

			// pjsua_conf_connect(call_info.conf_slot,
			// 		   pjsua_call_get_conf_port(call_ids[i]));
			// pjsua_conf_connect(pjsua_call_get_conf_port(call_ids[i]),
			//                    call_info.conf_slot);


	  //   }
    }
}



#else



/* Callback called by the library upon receiving incoming call */
static void on_incoming_call(pjsua_acc_id acc_id, pjsua_call_id call_id,
			     pjsip_rx_data *rdata)
{
    pjsua_call_info ci;

    PJ_UNUSED_ARG(acc_id);
    PJ_UNUSED_ARG(rdata);

    pjsua_call_get_info(call_id, &ci);

    PJ_LOG(3,(THIS_FILE, "Incoming call from %.*s!!",
			 (int)ci.remote_info.slen,
			 ci.remote_info.ptr));

    /* Automatically answer incoming calls with 200/OK */
    pjsua_call_answer(call_id, 200, NULL, NULL);
}

/* Callback called by the library when call's state has changed */
static void on_call_state(pjsua_call_id call_id, pjsip_event *e)
{
    pjsua_call_info ci;

    PJ_UNUSED_ARG(e);

    pjsua_call_get_info(call_id, &ci);
    PJ_LOG(3,(THIS_FILE, "Call %d state=%.*s", call_id,
			 (int)ci.state_text.slen,
			 ci.state_text.ptr));
}

/* Callback called by the library when call's media state has changed */
static void on_call_media_state(pjsua_call_id call_id)
{
    pjsua_call_info ci;

    pjsua_call_get_info(call_id, &ci);

    if (ci.media_status == PJSUA_CALL_MEDIA_ACTIVE) {
	// When media is active, connect call to sound device.
	pjsua_conf_connect(ci.conf_slot, 0);
	pjsua_conf_connect(0, ci.conf_slot);
    }
}



#endif

/*
 * Handler when a transaction within a call has changed state.
 */
static void on_call_tsx_state(pjsua_call_id call_id,
			      pjsip_transaction *tsx,
			      pjsip_event *e)
{
	WARNINGP(" ----on_call_tsx_state");
	//pj_status_t status;
	pjsip_rx_data *my_rdata;

	my_rdata = e->body.tsx_state.src.rdata;
	
	//通话未建立时，对方自己取消
	if (tsx->role == PJSIP_ROLE_UAS && tsx->state == PJSIP_TSX_STATE_TRYING)	//不加这个会断错误，是个限制条件，用call挂断状态会进入
	{	
		WARNINGP("name =%s .......\n",my_rdata->msg_info.msg->line.req.method.name.ptr)
		if(!strcmp(my_rdata->msg_info.msg->line.req.method.name.ptr, "CANCEL"))
		{	
			//g_incoming_call_flag = 0;		//来电自己取消时，标志清零
			//g_incoming_call_cancle_flag = 1;
			//g_incoming_call_cancle_flag_when_no_call= 1;	//目前没通话时的来电取消标志
			pjsua_call_info call_info;
 			pjsua_call_get_info(call_id, &call_info);
 			LOG("No connection cancel from user state:%s,code:%d",call_info.state_text.ptr,call_info.last_status);
 			SipUserInfo_t * user = getSipUserInfo(call_info.acc_id);
			if(user){
				st_SipCurStatus.curActiveCallLine = user->index+1;	
				onSIPStatusChanged(SIP_STATUS_TYPE_CALL ,user->index+1, SIP_LINE_CALLING_STATUS_IDLE);
			}
			HangupAllCalling(call_info.last_status);//超时挂断			
			NOTICE("rdata:%s.", my_rdata->msg_info.msg->line.req.method.name.ptr);
			NOTICE("未通话，对方自己挂断.");
		}
	}
/*--------------------------SIP_INFO DTMF接受------------------------------------*/
    const pjsip_method info_method = 
    {
	PJSIP_OTHER_METHOD,
	{ "INFO", 4 }
    };

    if (pjsip_method_cmp(&tsx->method, &info_method)==0) {
	/*
	 * Handle INFO method.
	 */
	const pj_str_t STR_APPLICATION = { "application", 11};
	const pj_str_t STR_DTMF_RELAY  = { "dtmf-relay", 10 };
	pjsip_msg_body *body = NULL;
	pj_bool_t dtmf_info = PJ_FALSE;
	
	if (tsx->role == PJSIP_ROLE_UAC) {
	    if (e->body.tsx_state.type == PJSIP_EVENT_TX_MSG)
		body = e->body.tsx_state.src.tdata->msg->body;
	    else
		body = e->body.tsx_state.tsx->last_tx->msg->body;
	} else {
	    if (e->body.tsx_state.type == PJSIP_EVENT_RX_MSG)
		body = e->body.tsx_state.src.rdata->msg_info.msg->body;
	}
	
	/* Check DTMF content in the INFO message */
	if (body && body->len &&
	    pj_stricmp(&body->content_type.type, &STR_APPLICATION)==0 &&
	    pj_stricmp(&body->content_type.subtype, &STR_DTMF_RELAY)==0)
	{
	    dtmf_info = PJ_TRUE;
	}

	if(dtmf_info && tsx->role == PJSIP_ROLE_UAS &&
		   tsx->state == PJSIP_TSX_STATE_TRYING)
	{
	   /* Answer incoming INFO with 200/OK */
	    pjsip_rx_data *rdata;
	    pjsip_tx_data *tdata;
	    pj_status_t status;

	    rdata = e->body.tsx_state.src.rdata;

	    if (rdata->msg_info.msg->body) {
		status = pjsip_endpt_create_response(tsx->endpt, rdata,
						     200, NULL, &tdata);
		if (status == PJ_SUCCESS)
		    status = pjsip_tsx_send_msg(tsx, tdata);

		PJ_LOG(3,(THIS_FILE, "Call %d: incoming INFO:\n%.*s", 
			  call_id,
			  (int)rdata->msg_info.msg->body->len,
			  rdata->msg_info.msg->body->data));
	    } else {
		status = pjsip_endpt_create_response(tsx->endpt, rdata,
						     400, NULL, &tdata);
		if (status == PJ_SUCCESS)
		    status = pjsip_tsx_send_msg(tsx, tdata);
	    }
	}
    }
}



/* 
* DTMF callback. 
*/  
static void call_on_dtmf_callback(pjsua_call_id call_id, int dtmf)  
{  
    PJ_LOG(3,(THIS_FILE, "Incoming DTMF on call %d: %c", call_id, dtmf));  
    //recvDtmf(dtmf);
}  

void printfPJsuaAccInfo(pjsua_acc_info *info)
{
	/** 
     * The account ID. 
     */
   // pjsua_acc_id	id;
    printf("\n\npjsua acc info:\n");
    printf("id: %d\n",info->id);

    /**
     * Flag to indicate whether this is the default account.
     */
   // pj_bool_t		is_default;
    printf("is_default: %d\n",info->is_default);

    /** 
     * Account URI 
     */
    //pj_str_t		acc_uri;
    printf("acc_uri: %s\n",info->acc_uri.ptr);


    /** 
     * Flag to tell whether this account has registration setting
     * (reg_uri is not empty).
     */
   // pj_bool_t		has_registration;

    /**
     * An up to date expiration interval for account registration session.
     */
    //int			expires;
	printf("expires: %d\n",info->expires);

    /**
     * Last registration status code. If status code is zero, the account
     * is currently not registered. Any other value indicates the SIP
     * status code of the registration.
     */
    //pjsip_status_code	status;
    printf("status Code: %d\n",info->status);

    /**
     * Last registration error code. When the status field contains a SIP
     * status code that indicates a registration failure, last registration
     * error code contains the error code that causes the failure. In any
     * other case, its value is zero.
     */
    //pj_status_t	        reg_last_err;
	printf("reg last err: %d\n",info->reg_last_err);

    /**
     * String describing the registration status.
     */
    //pj_str_t		status_text;
    printf("status text: %s\n",info->status_text.ptr);

    /**
     * Presence online status for this account.
     */
   // pj_bool_t		online_status;
	printf("online status: %d\n",info->online_status);

    /**
     * Presence online status text.
     */
    //pj_str_t		online_status_text;
    printf("online_status_text: %s\n",info->online_status_text.ptr);

    /**
     * Extended RPID online status information.
     */
    //pjrpid_element	rpid;

    /**
     * Buffer that is used internally to store the status text.
     */
    //char		buf_[PJ_ERR_MSG_SIZE];
    
    printf("--------------------end acc info!\n\n");
}

/*
 * Handler registration status has changed.
 */
static void on_reg_state(pjsua_acc_id acc_id)
{
   // PJ_UNUSED_ARG(acc_id);
  
    // Log already written.	
	SipUserInfo_t * userinfo = NULL;
	userinfo = (SipUserInfo_t * )getSipUserInfo(acc_id);
	struct pjsua_data* pjsua_varTemp =  NULL;

	pjsua_acc_info info;
	pj_status_t pj_state = pjsua_acc_get_info(acc_id,&info);
	//NOTICE("pj_state :%d ",pj_state);
	if (pj_state == PJ_SUCCESS){
		
	}

	printfPJsuaAccInfo(&info);

	pjsua_varTemp =  (struct pjsua_data*)pjsua_get_var();
	if(!userinfo) {
		printf("No Found User from acc_id:%d\n",acc_id);
		return ;
	}
	printf("acc_cnt=%d\n", pjsua_varTemp->acc_cnt);
	printf("acc last code:%d onlineStatus:%d\n",pjsua_varTemp->acc[acc_id].reg_last_code,pjsua_varTemp->acc[acc_id].online_status);

	if(pjsua_varTemp->acc[acc_id].reg_last_code == 200){
		userinfo->registerStatus = SIP_STATUS_REGISTER_SUCCEED;		
	}else if(pjsua_varTemp->acc[acc_id].reg_last_code == 403){
		WARNINGP("User %s Login Failed password Wrong!",userinfo->user);
		userinfo->registerStatus = SIP_STATUS_REGISTER_FAILED_PASSWORD;
	}else{		
		userinfo->registerStatus = SIP_STATUS_REGISTER_FAILED_OTHER;
	}
	if(userinfo->reg_last_code != pjsua_varTemp->acc[acc_id].reg_last_code ){
		if(pjsua_varTemp->acc[acc_id].reg_last_code == 200){
			LOG("user %s Login Succeed.",userinfo->user);
			
		}else{
			LOG("user %s Login Wrong code=%d.",userinfo->user,pjsua_varTemp->acc[acc_id].reg_last_code);
		}		
		userinfo->reg_last_code = pjsua_varTemp->acc[acc_id].reg_last_code;
	}
	
	if(userinfo->acc_id != ipCallUser.acc_id){
		//WARNINGP("==================Login User Index %d",userinfo->index);
		st_SipCurStatus.curActiveCallLine = userinfo->index+1;
		NotifyAlterSipStatus(appData.SipUserInfo[0].registerStatus);

/* 2018年12月26日08:50:26 解决通话过程中发送了注册信息而导致led停止了通话闪烁*/
		if(userinfo->registerStatusPrev != userinfo->registerStatus){
			userinfo->registerStatusPrev = userinfo->registerStatus;
			onSIPStatusChanged(SIP_STATUS_TYPE_REG ,userinfo->index+1,userinfo->registerStatus);
		}
		
	}	
		
	//[acc_id].reg_last_code
	//pjsua_varTemp.acc[acc_id];
	//printf("reg_last_code=%d\n",);
	
}
pj_pool_t Clone_pool;
/**
 * [on_call_sdp_created description]
 * @param call_id [description]
 * @param sdp     [description]
 * @param pool    [description]
 * @param rem_sdp [description]
 */
static void on_call_sdp_created(pjsua_call_id call_id,
                                   pjmedia_sdp_session *sdp,
                                   pj_pool_t *pool,
                                   const pjmedia_sdp_session *rem_sdp)
{	

	memcpy(&Clone_pool,pool,sizeof(pj_pool_t));
	int i=0;
	char sdpBuf[2024]={0};
	char rem_sdpBuf[1024]={0};

	// pjsua_call_info call_info;
	// pjsua_call_get_info(call_id, &call_info);	
	// DBG("local acc_id:%d", call_info.acc_id);

	if(audio_way)
	{
		sdp->media_count=1;
		return; 
	}


	//sdp->media[i]->desc.port=5000;//测试固定端口

	//printf("===============locl_sdp:\n%s ##\n",sdpBuf);
	//sdp->media[1].desc.media.;
	//rem_sdp_session = rem_sdp;
	if(rem_sdp){
		//bzero(sdpBuf,1024);
		//pjmedia_sdp_print(rem_sdp,sdpBuf,1000);
		NOTICE("Display dsp information...");
		printf("=======sdp->media_count %d ======\n ##\n",sdp->media_count);
		//获取远端视频rtp端口		

		// for (i = 0; i < sdp->media_count; ++i)
		// {
		// 	printf("\n===============locl_sdp %s prot=%d,transport=%s\n"
		// 		,sdp->media[i]->desc.media.ptr
		// 		,sdp->media[i]->desc.port
		// 		,sdp->media[i]->desc.transport.ptr);
		// 	printf("\n===============rem_sdp %s prot =%d,transport=%s\n"
		// 		,rem_sdp->media[i]->desc.media.ptr
		// 		,rem_sdp->media[i]->desc.port
		// 		,rem_sdp->media[i]->desc.transport.ptr);
			
		// 	if(strlen(sdp->media[i]->desc.media.ptr)>6)
		// 	{
		// 		if(strstr(sdp->media[i]->desc.media.ptr,"audio")!=0)
		// 		{
		// 			memset(sdp->media[i]->desc.media.ptr,0,sdp->media[i]->desc.media.slen);
		// 			//snprintf(sdp->media[i]->desc.media.ptr,5,"audio");
		// 			strncpy(sdp->media[i]->desc.media.ptr,"audio",6);
		// 		}
		// 		else if(strstr(sdp->media[i]->desc.media.ptr,"video")!=0)
		// 		{
		// 			memset(sdp->media[i]->desc.media.ptr,0,sdp->media[i]->desc.media.slen);
		// 			//snprintf(sdp->media[i]->desc.media.ptr,5,"video");
		// 			strncpy(sdp->media[i]->desc.media.ptr,"video",6);
		// 		}
				
		// 	}
		// 	if (strlen(rem_sdp->media[i]->desc.media.ptr)>6)
		// 	{
		// 		if(strstr(rem_sdp->media[i]->desc.media.ptr,"audio")!=0)
		// 		{
		// 			memset(rem_sdp->media[i]->desc.media.ptr,0,rem_sdp->media[i]->desc.media.slen);
		// 			strncpy(rem_sdp->media[i]->desc.media.ptr,"audio",6);
		// 			//snprintf(rem_sdp->media[i]->desc.media.ptr,5,"audio");
		// 		}
		// 		else if(strstr(rem_sdp->media[i]->desc.media.ptr,"video")!=0)
		// 		{
		// 			memset(rem_sdp->media[i]->desc.media.ptr,0,rem_sdp->media[i]->desc.media.slen);
		// 			strncpy(rem_sdp->media[i]->desc.media.ptr,"video",6);
		// 			//snprintf(rem_sdp->media[i]->desc.media.ptr,5,"video");
		// 			printf("修正后rem_sdp->media[i]->desc.media.ptr %s sizeof %d\n",rem_sdp->media[i]->desc.media.ptr
		// 				,rem_sdp->media[i]->desc.media.slen);
		// 		}

		// 	}
		// 	if(strlen(sdp->media[i]->desc.transport.ptr)>8)
		// 	{
		// 		if(strstr(sdp->media[i]->desc.transport.ptr,"RTP/AVP")!=0)
		// 		{
		// 			memset(sdp->media[i]->desc.transport.ptr,0,rem_sdp->media[i]->desc.transport.slen);
		// 			strncpy(sdp->media[i]->desc.transport.ptr,"RTP/AVP",8);
		// 			//snprintf(sdp->media[i]->desc.transport.ptr,7,"RTP/AVP");
		// 		}
		// 	}
		// 	if(strlen(rem_sdp->media[i]->desc.transport.ptr)>8)
		// 	{
		// 		if(strstr(rem_sdp->media[i]->desc.transport.ptr,"RTP/AVP")!=0)
		// 		{
		// 			memset(rem_sdp->media[i]->desc.transport.ptr,0,rem_sdp->media[i]->desc.transport.slen);
		// 			strncpy(rem_sdp->media[i]->desc.transport.ptr,"RTP/AVP",8);
		// 			//snprintf(rem_sdp->media[i]->desc.transport.ptr,7,"RTP/AVP");
		// 			printf("修正后rem_sdp->transport.ptr=%s sizeof %d \n",rem_sdp->media[i]->desc.transport.ptr
		// 				,rem_sdp->media[i]->desc.transport.slen);
		// 		}
		// 	}

		// }
		printf("222222...media_count %d\n",sdp->media_count);
		for (i = 0; i < sdp->media_count; ++i)
		{

			printf("222222...locl_sdp %s,prot=%d,transport=%s\n"
				,sdp->media[i]->desc.media.ptr
				,sdp->media[i]->desc.port
				,sdp->media[i]->desc.transport.ptr);
			printf("222222...rem_sdp %s,prot =%d,transport=%s\n"
				,rem_sdp->media[i]->desc.media.ptr
				,rem_sdp->media[i]->desc.port
				,rem_sdp->media[i]->desc.transport.ptr);
		}	
#if HAS_RTSP_VIDIO_SUPPORT			
			if(sdp->media_count>1)
			if(sdp->media[1]->desc.port!=0)
			{
				printf("[%d] [%s]sdp->media[1]->desc.port 赋值 %d\n",__LINE__,__FUNCTION__,sdp->media[1]->desc.port);
				sdp->media[1]->desc.port=g_local_port;
				local_SDP_Port=sdp->media[1]->desc.port;
			}

				if(sdp->media_count>1){
					on_call_sdp_created_vid(call_id,sdp,pool,rem_sdp);
					//return;
				}
#else//9321音频对讲
				// pjmedia_sdp_media_deactivate(pool,sdp->media[1]); //禁用视频媒体
				// sdp->media_count=1;//改为音频对讲
				// return;
#endif
	}
	pjmedia_sdp_attr   *attr = NULL,*attr_t = NULL;
#if ENABLE_DEVICE_MODEL_DSP9326
	//查找视频属性
	if(sdp->media_count>1){
		printf("%s sdp->media[1]->attr_count %d\n",__FUNCTION__,sdp->media[1]->attr_count);
		attr_t = pjmedia_sdp_attr_find2(sdp->media[1]->attr_count,sdp->media[1]->attr,"sendrecv",NULL);
		printf("%s %d\n",__FUNCTION__,__LINE__);
	}


	static pjmedia_sdp_session  video_sdp_media;
	if (attr_t)
	{
		
		if(sdp->media[1]->desc.port != 0){
			//video_sdp_attr = pjmedia_sdp_attr_clone(pool,attr);
			printf("%s %d\n",__FUNCTION__,__LINE__);
			//video_sdp_media=malloc(sizeof(pjmedia_sdp_session));
			//video_sdp_media =  pjmedia_sdp_media_clone(pool,&sdp->media[1]);
			//video_sdp_media=pjmedia_sdp_session_clone(pool,sdp);
			memset(&video_sdp_media,0,sizeof(pjmedia_sdp_session));
			memcpy(&video_sdp_media,sdp,sizeof(pjmedia_sdp_session));
			g_local_port=sdp->media[1]->desc.port;
				
			//clone_rem_sdp=pjmedia_sdp_session_clone(pool,rem_sdp);
		}
		// m=video 5002 RTP/AVP 97
		// c=IN IP4 ***************
		// b=TIAS:256000
		// a=rtcp:5003 IN IP4 ***************
		// a=sendrecv
		// a=rtpmap:97 H264/90000
		// a=fmtp:97 profile-level-id=42e01e; packetization-mode=1
		
		//    pjmedia_sdp_bandw  *bandw[PJMEDIA_MAX_SDP_BANDW]; /**< Bandwidth info.  */

		

		//PJ_DEF(pj_status_t) pjmedia_sdp_media_add_attr(sdp->media,attr
		//				pjmedia_sdp_attr *attr)
		///* code */
		//pjmedia_sdp_session_add_attr(sdp,);
		//pjmedia_sdp_media_add_attr();
	}

	if(!rem_sdp &&sdp->media_count > 1 ){
		printf("!rem_sdp && sdp->media_count > 1  local media_count=%d sdp->media[1]->desc.port=%d \n", sdp->media_count,sdp->media[1]->desc.port);
	
		#if 1	
		pjsua_call *call;
		call = &pjsua_var.calls[call_id];
		call->media[call->audio_idx].dir = PJMEDIA_DIR_ENCODING_DECODING;
		pjsua_call_info call_info;
		pjsua_call_get_info(call_id, &call_info);
		printf("call_info.media_dir=%d\n", call_info.media_dir);
		bzero(sdpBuf,1024);
		pjmedia_sdp_print(sdp,sdpBuf,1000);
		printf("locl_sdp mod:\n%s ##\n",sdpBuf);
		
			//process_pending_reinvite(call);
		
		#endif
//
//
		//pjmedia_sdp_session *remote=NULL;
		////pjmedia_sdp_neg_get_active_remote
		//if(call->inv)
		//if(call->inv->neg){
		//	pjmedia_sdp_neg_get_active_remote( call->inv->neg,&remote);
		//}
		//if(remote){
		//	on_call_sdp_created_vid(call_id,sdp,pool,remote);
		//	inv_rem_video_sdp_get = 1;
		//	DBG("inv_rem_video_sdp_get yes");
		//}else{
		//	inv_rem_video_sdp_get = 0;
		//	DBG("inv_rem_video_sdp_get no");
		//}
		if (sdp->media[1]->desc.port == 0)
		{
			//sdp->media[1]->desc.port=g_local_port;
			memset(sdp,0,sizeof(pjmedia_sdp_session));
			memcpy(sdp,&video_sdp_media,sizeof(pjmedia_sdp_session));
			printf("[%d]copy last sdp\n",__LINE__);
			//free(video_sdp_media);
			//video_sdp_media=NULL;
			// memset(rem_sdp,0,sizeof(pjmedia_sdp_session));
			// memcpy(rem_sdp,clone_rem_sdp,sizeof(pjmedia_sdp_session));
		}
		#if 0
		//临时
		if (sdp->media[1]->desc.port == 0 )
		{
			//带有远端dsp且本地dsp视频未开启时
			//const char * video_sdp_attr_name[]={"rtcp","sendrecv","rtpmap","fmtp"};
			char video_sdp_attr_name[4][10]={"rtcp","sendrecv","rtpmap","fmtp"};
			int removedCnt = 0;
			pjmedia_sdp_attr  *attr1 = NULL;
			if(Remote_Vedio_Port==0)
			{
				return ;
			}
			Remote_Vedio_Port=0;
			
			#if 1//
			printf("**** ARRAY_SIZE(video_sdp_attr_name) %d \n",ARRAY_SIZE(video_sdp_attr_name) );
			for (i = 0; i < ARRAY_SIZE(video_sdp_attr_name); ++i)
			{
				printf("%d %s  sdp->media[1]->attr_count %d \n",__LINE__,video_sdp_attr_name[i],sdp->media[1]->attr_count);
				//removedCnt = pjmedia_sdp_media_remove_all_attr(&sdp->media[1],video_sdp_attr_name[i]);
				attr = pjmedia_sdp_attr_find2(sdp->media[1]->attr_count,sdp->media[1]->attr,video_sdp_attr_name[i],NULL);
				printf("\n[%d] %s\n",__LINE__,__FUNCTION__ );
				if(attr){
					printf("attr->name.ptr=%s\n", attr->name.ptr);
					if( pjmedia_sdp_media_remove_attr(sdp->media[1],attr)  == PJ_SUCCESS){
						printf("removed %s...\n",video_sdp_attr_name[i]);
					}else{
						printf("remove attr %s Wrong!\n",video_sdp_attr_name[i]);
					}
				}else{
					printf("remove Not Found attr %s\n",video_sdp_attr_name[i]);
					//return ;
				}
			}

			for (i = 0; i < ARRAY_SIZE(video_sdp_attr_name); ++i)
			{
				printf("\n[%d] video_sdp_media->attr_count %d\n",__LINE__,video_sdp_media->attr_count );
				//if(video_sdp_media->attr_count==0)
					//video_sdp_media->attr_count=4;
				attr1 = pjmedia_sdp_attr_find2(video_sdp_media->attr_count,video_sdp_media->attr,video_sdp_attr_name[i],NULL);
				
				if(attr1){
					printf("%d\n",__LINE__);
					pjmedia_sdp_media_add_attr(sdp->media[1],attr1);
					printf("add attr %s\n",video_sdp_attr_name[i]);
				}else{
					printf("Not Found attr %s\n",video_sdp_attr_name[i]);
				}

			}
			printf("\n#############video_sdp_media->bandw_count %d##########\n",video_bandw_count);
			memcpy(&sdp->media[1]->desc,&video_sdp_media->desc,sizeof(video_sdp_media->desc));
			memcpy(&sdp->conn,video_conn,sizeof(pjmedia_sdp_conn));
			//for (i = 0; i < video_bandw_count; ++i)
			//{
				printf("\n#############%d##########\n",__LINE__);
				memcpy(&sdp->bandw,video_bandw,sizeof(pjmedia_sdp_bandw));
				printf("\n#############%d##########\n",__LINE__);
			//}
			#endif
		
			pjsua_call *call;
 			call = &pjsua_var.calls[call_id];
 			call->media[call->audio_idx].dir = PJMEDIA_DIR_ENCODING_DECODING;
 			pjsua_call_info call_info;
			pjsua_call_get_info(call_id, &call_info);
			printf("call_info.media_dir=%d\n", call_info.media_dir);
			bzero(sdpBuf,1024);
			pjmedia_sdp_print(sdp,sdpBuf,1000);
			printf("locl_sdp mod:\n%s ##\n",sdpBuf);


			pjmedia_sdp_session *remote=NULL;
			

			//pjmedia_sdp_neg_get_active_remote
			if(call->inv)
			{
				printf("[%d]进入呼叫邀请 %s\n",__LINE__,remote );
				if(call->inv->neg){
					pjmedia_sdp_neg_get_active_remote( call->inv->neg,&remote);
				}
				if(remote){
					on_call_sdp_created_vid(call_id,sdp,pool,remote);
					inv_rem_video_sdp_get = 1;
					DBG("inv_rem_video_sdp_get yes port %d",sdp->media[1]->desc.port);
					if(sdp->media_count>1)
					if(sdp->media[1]->desc.port!=0)
					{
						local_SDP_Port=sdp->media[1]->desc.port;
					}
				}else{
					//新增调试
					//on_call_sdp_created_vid(call_id,sdp,pool,sdp);
					printf("邀请远端的本地SDP %s\n",sdp);
					inv_rem_video_sdp_get = 0;
					DBG("inv_rem_video_sdp_get no");
					
				}
			}
		}
		#endif
	}
#endif

	if(attr_t && rem_sdp){
		//printf("attr->name.ptr=%s\n", attr_t->name.ptr);
		//printf("attr->value.ptr=%s\n", attr_t->value.ptr);
		// pjmedia_sdp_attr   *rtcpattr = NULL;
		// rtcpattr = pjmedia_sdp_attr_find2(sdp->media[1]->attr_count,sdp->media[1]->attr,"sendrecv",NULL);
		// pjmedia_sdp_media_remove_attr(sdp->media[1],rtcpattr);
		// // //attr_t = pjmedia_sdp_attr_create(pool,"recvonly",NULL);
		// attr_t = pjmedia_sdp_attr_create(pool,"sendrecv",NULL);
		// pjmedia_sdp_media_add_attr(sdp->media[1],attr_t);
		// // bzero(sdpBuf,1024);
		//  pjmedia_sdp_media_deactivate(pool,sdp->media[1]); //禁用视频媒体
		//on_call_sdp_created_vid(call_id,sdp,pool,rem_sdp);
		// pjmedia_sdp_print(sdp,sdpBuf,1000);
		// printf("__%d locl_sdp:\n%s ##\n",__LINE__,sdpBuf);
		inv_rem_video_sdp_get = 0;
	}

	
	
#if ENABLE_DEVICE_MODEL_DSP9321//清除视频属性
	if(sdp->media_count>1)
	{
		pjmedia_sdp_attr   *rtcpattr = NULL;
		pjmedia_sdp_attr   *rtcprtpmap = NULL;
		pjmedia_sdp_attr   *rtcprtcp=NULL;
		rtcpattr = pjmedia_sdp_attr_find2(sdp->media[1]->attr_count,sdp->media[1]->attr,"sendrecv",NULL);
		if(rtcpattr)
		pjmedia_sdp_media_remove_attr(sdp->media[1],rtcpattr);
		
		rtcprtpmap = pjmedia_sdp_attr_find2(sdp->media[1]->attr_count,sdp->media[1]->attr,"rtpmap",NULL);
		if(rtcprtpmap)
		pjmedia_sdp_media_remove_attr(sdp->media[1],rtcprtpmap);
		
		rtcprtcp = pjmedia_sdp_attr_find2(sdp->media[1]->attr_count,sdp->media[1]->attr,"rtcp",NULL);
		if(rtcprtcp)
		pjmedia_sdp_media_remove_attr(sdp->media[1],rtcprtcp);

		pjmedia_sdp_media_deactivate(pool,sdp->media[1]); //禁用视频媒体
		//sdp->media_count=1;//改为音频对讲
	}
#endif	
	pjmedia_sdp_print(sdp,sdpBuf,2000);
	if(rem_sdp){
		pjmedia_sdp_print(rem_sdp,rem_sdpBuf,2000);
	}
	printf("on_call_sdp_created... call_id %d\n",call_id);
	printf("entertimes %d locl_sdp:\n%s\n##\n",entertimes++,sdpBuf );
	printf("rem_sdp: \n%s\n##\n",rem_sdpBuf );
	return;
}

void stopAutoAnswerTimer(){
	if(auto_answer_timer.id ){
		pjsip_endpt_cancel_timer(pjsua_get_pjsip_endpt(),&auto_answer_timer);
		auto_answer_timer.id = 0;
	}
	
}


/**
 * [PJSIP_CallbackConfig 配置回调函数]
 * @param cfg [description]
 */
void PJSIP_CallbackConfig(pjsua_config *cfg)
{
	cfg->cb.on_incoming_call = &on_incoming_call;		//来电回调		（调用顺序2）
	cfg->cb.on_call_media_state = &on_call_media_state;	//媒体状态改变回调 （调用顺序3）
	cfg->cb.on_call_state = &on_call_state;				
	cfg->cb.on_call_sdp_created = &on_call_sdp_created;	//SDP设置回调	(发送邀请或者应答 调用顺序1)
	cfg->cb.on_call_tsx_state = &on_call_tsx_state;		//通话状态改变回调
	cfg->cb.on_dtmf_digit = &call_on_dtmf_callback;		//DTMF回调
	cfg->cb.on_reg_state = &on_reg_state; 				//注册


	pj_timer_entry_init(&auto_hangup_timer, 0, NULL, 
				    &hangupTimeout_callback);
	pj_timer_entry_init(&auto_answer_timer, 0, NULL, 
				    &autoAnswerTimeout_callback);
}