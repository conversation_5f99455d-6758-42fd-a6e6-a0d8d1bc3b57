#include <pthread.h>
#include <unistd.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

#include "apiTest.h"
#include "sip.h"
#include "debug.h"

#define VARIABLE_STR_LIST_MAX 	200

static variableStr_t variableStrList[VARIABLE_STR_LIST_MAX];
static int varStrCurAddPos=0;

static void initVariableStrList()
{
	int i = 0;
	for (i = 0; i < VARIABLE_STR_LIST_MAX; ++i)
	{
		memset(&variableStrList[i],0,sizeof(variableStr_t));
	}
}

static int varAddressIsExist(void*address){
	int i = 0;
	for (i = 0; i < VARIABLE_STR_LIST_MAX; ++i){

	}
	return 0;
}

variableStr_t * getVarAddressPtr(const char *varStr){
	int i = 0;
	for (i = 0; i < VARIABLE_STR_LIST_MAX; ++i){
		if(0 == strcmp(variableStrList[i].varStr,varStr) ){
			return &variableStrList[i];
		}
	}
	return NULL;
}


void varStrListAdd(char type,void * address,char *varStr){
	variableStrList[varStrCurAddPos].type = type;
	variableStrList[varStrCurAddPos].address = address;
	variableStrList[varStrCurAddPos].varStr = varStr;
	varStrCurAddPos++;
}




/**
 * [getVariableStrList 获取变量值]
 * @return [description]
 */
char * getVariableStrList(){
	static char VariableStrListBuf[2048]={0};
	bzero(VariableStrListBuf,2048);
	char * pbuf=VariableStrListBuf;
	int i = 0;
	int len = 0;
	len = sprintf(pbuf,"VariableList:\n");
	pbuf+=len;
	for (i = 0; i < VARIABLE_STR_LIST_MAX; ++i)
	{
		if(!variableStrList[i].address)break;
		if(variableStrList[i].type == VAR_STR_TYPE_CHAR){
			len = sprintf(pbuf,"%s=%s\n",variableStrList[i].varStr,(char*)variableStrList[i].address);
		}else if(variableStrList[i].type == VAR_STR_TYPE_INT8){
			len = sprintf(pbuf,"%s=%d\n",variableStrList[i].varStr,*(unsigned char*)variableStrList[i].address );
		}else if(variableStrList[i].type == VAR_STR_TYPE_INT16){
			len = sprintf(pbuf,"%s=%d\n",variableStrList[i].varStr,*(unsigned int*)variableStrList[i].address );
		}else if(variableStrList[i].type == VAR_STR_TYPE_INT32){
			len = sprintf(pbuf,"%s=%ld\n",variableStrList[i].varStr,*(unsigned long*)variableStrList[i].address );
		}
		pbuf+=len;
	}
	return VariableStrListBuf;
}

/**
 * [setVariableStrList 设置变量]
 * @param  strList [description]
 * @return         [description]
 */
int setVariableStrList(char * strList)
{
	variableStr_t * varPtr;//getVarAddressPtr
	char * delim = "\n";
	char lineBuf[128]={0};
	char * p = strtok(strList,delim);
	char * p1;
	if(!p)return -1;
	//WARNINGP("setVariableStrList...");
	if(strcmp("SetVariable:",p)) { WARNINGP("Check SetVariable:(%s) Failed!",p); return -1;}
	p = strtok(NULL,delim);
	while(p){
		bzero(lineBuf,128);
		strcpy(lineBuf,p);
		p1 = strtok(lineBuf,"=");
		varPtr = getVarAddressPtr(p1);
		if(varPtr){
			//printf("Set Key:%s.",p1);
			p1 = strtok(NULL,"=");
			if(p1){
				//printf("Val=%s",p1);
				switch(varPtr->type ){
					case VAR_STR_TYPE_CHAR: strcpy(varPtr->address,p1);
					case VAR_STR_TYPE_INT8: *(unsigned char*) (varPtr->address) = atoi(p1) ;
					case VAR_STR_TYPE_INT16:*(unsigned short*)(varPtr->address) = atoi(p1) ;
					case VAR_STR_TYPE_INT32:*(unsigned int*)(varPtr->address) = atoi(p1) ;
				}
			}
		}
		p = strtok(NULL,delim);
	}
	return 0;
}
