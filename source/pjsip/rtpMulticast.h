#ifndef __RTP_MULTICAST_H__
#define __RTP_MULTICAST_H__
#include "typedef.h"


#define RTP_MULTICAST_NAME_LEN_MAX 32
#define RTP_MULTICAST_URL_LEN_MAX 32 //***************:55555

#define RTP_MULTICAST_URL_COUNT_MAX 	10 //最多支持的组播设置


typedef struct rtpMulticast_t{
	char name[RTP_MULTICAST_NAME_LEN_MAX];
	char address[RTP_MULTICAST_URL_LEN_MAX];	
	int socketfd ;
}rtpMulticast_t;

typedef struct rtpMulticastConf_t{
	//当前优先级
	//选择0则组播功能无效，1：对应组播启用
	unsigned char prio;//
	unsigned char pthreadFlag; //线程状态标志 0：线程未启动
	unsigned char curStatus;//当前组播状态：0：空闲 1：接收数据启用
	unsigned char rtpRecvTimeout;//rtp组播接收超时时间设置 (1~10秒)
	unsigned char activateRtpPt;//当前使用的rtp类型
	rtpMulticast_t rtpMulticast[RTP_MULTICAST_URL_COUNT_MAX];
}rtpMulticastConf_t;
void saveRtpMulticastConfig();
void StreaMulticastInit();
void restartMulticastStream();
/**
 * [startStreaMulticastSendOnlyWithKey 按键启动组播发送]
 * @param  key1_multAddr [description]
 * @param  subType       [description]
 * @return               [description]
 */
int startStreaMulticastSendOnlyWithKey(const char * key1_multAddr,int subType);
int analyzeAddressPortFromString(const char* str,char *addr,int *port);

#endif