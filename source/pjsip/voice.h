#ifndef __VOICE_H__
#define __VOICE_H__

#include "pjmedia/codec.h"

#define VOICE_CODEC_LIST_MAX 		4 //支持的最大编码优先级设置


#define VOICE_CODEC_PAYLOAD_NAME_LEN 	12
/**
 * 语音编码设置配置
 */

#define 	CODEC_GSM  		"GSM"
#define 	CODEC_PCMU 		"PCMU"
#define 	CODEC_PCMA 		"PCMA"
#define 	CODEC_G722 		"G722"


typedef struct voiceInfoPayload_t
{
	unsigned char valid;
	unsigned char payload;	//负载类型
	char payloadName[VOICE_CODEC_PAYLOAD_NAME_LEN];	
}voiceInfoPayload_t;

/**
 * 媒体信息配置
 */
typedef struct voiceInfoSettings_t
{

	unsigned char en_VAD; //使能VAD 静音检测
	unsigned char callVolume;//通话音量（0~100,255）255代表跟随设备音量
	unsigned char callVolumeTemp;//通话音量（临时）
	unsigned char MIC_inputLevel;//麦克风输入级别
	unsigned char mediaVolume;//媒体音量 -- 包含组播音量广播音量
	
	voiceInfoPayload_t voiceInfoPayload[VOICE_CODEC_LIST_MAX];
}voiceInfoSettings_t;




#endif