#ifndef __APP_CONFIG_H__
#define __APP_CONFIG_H__

#include "sip.h"
#include "voice.h"
#include "rtpMulticast.h"
#include "ioDetection.h"

/**
 * 应用程序配置信息
 */
typedef struct appDataConfig_t
{
	//sip账号配置
	SipUserInfo_t SipUserInfo[SIP_USER_MAX];	
	//APP_CONFIG_RW_CB(sipUser);

	//设备SIP功能配置结构体	
	SipFeatureSettingsConfig_t sipFeatureSettingsConfig; 
	//APP_CONFIG_RW_CB(FeatureSet);

	//功能键配置
	functionKeysDSS_t functionKeysDSS[FUNCTION_KEYS_COUNT_MAX];
	//io输入配置
	io_Input_t IO_KeyInput[IO_INPUT_MAX_COUNT]; 
	io_output_t io_output[IO_OUT_QUEUE_MAX];

	//语音设置
	voiceInfoSettings_t voiceInfoSettings;

	//rtp组播设置
	rtpMulticastConf_t rtpMulticastConf;


	pjsipLibConfig_t pjsipConf;
	int pjsip_clock_rate;
	int pjsip_quality;
	int pjsip_console_level;

}appDataConfig_t;

extern appDataConfig_t appData;


#endif