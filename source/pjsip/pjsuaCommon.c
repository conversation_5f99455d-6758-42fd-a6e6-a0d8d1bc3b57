

#include "pjsuaCommon.h"
#include "sysConfig.h"
#include "debug.h"
#include "util.h"
#define THIS_FILE __FILE__

/* Ringtones		    US	       UK  */
#define RINGBACK_FREQ1	    440	    /* 400 */
#define RINGBACK_FREQ2	    480	    /* 450 */
#define RINGBACK_ON	    2000    /* 400 */
#define RINGBACK_OFF	    4000    /* 200 */
#define RINGBACK_CNT	    1	    /* 2   */
#define RINGBACK_INTERVAL   4000    /* 2000 */

#define RING_FREQ1	    800
#define RING_FREQ2	    640
#define RING_ON		    200
#define RING_OFF	    100
#define RING_CNT	    3
#define RING_INTERVAL	    3000


#define PJSUA_APP_NO_LIMIT_DURATION	(int)0x7FFFFFFF
#define PJSUA_APP_MAX_AVI		4
#define PJSUA_APP_NO_NB			-2

pjsua_app_config app_config;



/* Set default config. */
static void default_config(pj_pool_t *pool)
{
    char tmp[80];
    unsigned i;
    pjsua_app_config *cfg = &app_config;

    pjsua_config_default(&cfg->cfg);
    pj_ansi_sprintf(tmp, "PJSUA v%s %s", pj_get_version(),
		    pj_get_sys_info()->info.ptr);
    pj_strdup2_with_null(pool, &cfg->cfg.user_agent, tmp);

    pjsua_logging_config_default(&cfg->log_cfg);
    pjsua_media_config_default(&cfg->media_cfg);
    pjsua_transport_config_default(&cfg->udp_cfg);
    cfg->udp_cfg.port = 5060;
    pjsua_transport_config_default(&cfg->rtp_cfg);
    cfg->rtp_cfg.port = 4000;
    cfg->redir_op = PJSIP_REDIRECT_ACCEPT_REPLACE;
    cfg->duration = PJSUA_APP_NO_LIMIT_DURATION; 
    cfg->rec_id = PJSUA_INVALID_ID;  
    cfg->rec_port = PJSUA_INVALID_ID;
    cfg->mic_level = cfg->speaker_level = 1.0;
    cfg->capture_dev = PJSUA_INVALID_ID;
    cfg->playback_dev = PJSUA_INVALID_ID;
    cfg->capture_lat = PJMEDIA_SND_DEFAULT_REC_LATENCY;
    cfg->playback_lat = PJMEDIA_SND_DEFAULT_PLAY_LATENCY;
    cfg->ringback_slot = PJSUA_INVALID_ID;
    cfg->ring_slot = PJSUA_INVALID_ID;

    for (i=0; i<PJ_ARRAY_SIZE(cfg->wav_id); ++i)
	{
		cfg->wav_id[i] = PJSUA_INVALID_ID;
	}
	
	for (i=0; i<PJ_ARRAY_SIZE(cfg->wav_port); ++i)
	{
		cfg->wav_port[i] = PJSUA_INVALID_ID;
	}	

//    for (i=0; i<PJ_ARRAY_SIZE(cfg->acc_cfg); ++i)
//	pjsua_acc_config_default(&cfg->acc_cfg[i]);
//
//    for (i=0; i<PJ_ARRAY_SIZE(cfg->buddy_cfg); ++i)
//	pjsua_buddy_config_default(&cfg->buddy_cfg[i]);
#if HAS_RTSP_VIDIO_SUPPORT	
/*新增视频开启调试*/
   cfg->vid.vcapture_dev = PJMEDIA_VID_DEFAULT_CAPTURE_DEV;
   cfg->vid.vrender_dev = PJMEDIA_VID_DEFAULT_RENDER_DEV;
   cfg->vid.vid_cnt = 1;
#endif
   for (i=0; i<PJ_ARRAY_SIZE(cfg->acc_cfg); ++i)
	pjsua_acc_config_default(&cfg->acc_cfg[i]);
//
//    cfg->avi_def_idx = PJSUA_INVALID_ID;
//
//    cfg->use_cli = PJ_FALSE;
//    cfg->cli_cfg.cli_fe = CLI_FE_CONSOLE;
//    cfg->cli_cfg.telnet_cfg.port = 0;


}


/* Playfile done notification, set timer to hangup calls */
pj_status_t on_playfile_done(pjmedia_port *port, void *usr_data)
{
    pj_time_val delay;

    PJ_UNUSED_ARG(port);
    PJ_UNUSED_ARG(usr_data);

    /* Just rewind WAV when it is played outside of call */
    if (pjsua_call_get_count() == 0) {
	pjsua_player_set_pos(app_config.wav_id[0], 0);
	return PJ_SUCCESS;
    }

    /* Timer is already active */
    if (app_config.auto_hangup_timer.id == 1)
	return PJ_SUCCESS;

    app_config.auto_hangup_timer.id = 1;
    delay.sec = 0;
    delay.msec = 200; /* Give 200 ms before hangup */
    pjsip_endpt_schedule_timer(pjsua_get_pjsip_endpt(), 
			       &app_config.auto_hangup_timer, 
			       &delay);

    return PJ_SUCCESS;
}



/*****************************************************************************
 * Configuration manipulation
 */

/*****************************************************************************
 * Callback 
 */
void ringback_start(pjsua_call_id call_id)
{
    if (app_config.no_tones)
	return;

    if (app_config.call_data[call_id].ringback_on)
	return;

    app_config.call_data[call_id].ringback_on = PJ_TRUE;

    if (++app_config.ringback_cnt==1 && 
	app_config.ringback_slot!=PJSUA_INVALID_ID) 
    {
	pjsua_conf_connect(app_config.ringback_slot, 0);
    }
}

void ring_stop(pjsua_call_id call_id)
{


	if (app_config.no_tones)
	return;

#if 0
    if (app_config.call_data[call_id].ringback_on) {
	app_config.call_data[call_id].ringback_on = PJ_FALSE;


	pj_assert(app_config.ringback_cnt>0);
	if (--app_config.ringback_cnt == 0 && 
	    app_config.ringback_slot!=PJSUA_INVALID_ID) 
	{
	    pjsua_conf_disconnect(app_config.ringback_slot, 0);
	    pjmedia_tonegen_rewind(app_config.ringback_port);
	}
    }


#endif
    if (app_config.call_data[call_id].ring_on) {
	app_config.call_data[call_id].ring_on = PJ_FALSE;

	if(app_config.ring_cnt<=0)return;
	//pj_assert(app_config.ring_cnt>0);
	//if (--app_config.ring_cnt == 0 && 
	//    app_config.ring_slot!=PJSUA_INVALID_ID) 
	//{
	//    pjsua_conf_disconnect(app_config.ring_slot, 0);
	//    pjmedia_tonegen_rewind(app_config.ring_port);
	//}

	if (--app_config.ring_cnt == 0)
    {
    	int i = 0;
    	for (i = 0; i < PJ_ARRAY_SIZE(app_config.wav_port); ++i)
    	{
    		if(app_config.wav_port[i] != PJSUA_INVALID_ID){
				NOTICE("pjsua conf disconnect wav_port[%d]...",i);
				pjsua_conf_disconnect(app_config.wav_port[i], 0);
				pjsua_player_set_pos(app_config.wav_id[i] ,0);
    		}
    	}
    }

    }
    
}

void ring_start(pjsua_call_id call_id,int ri)
{	
	if (app_config.no_tones)
	return;

	if (app_config.call_data[call_id].ring_on)
	return;

	app_config.call_data[call_id].ring_on = PJ_TRUE;
/*
	if (++app_config.ring_cnt==1 && 
	app_config.ring_slot!=PJSUA_INVALID_ID) 
	{
	//	pjsua_conf_connect(app_config.ring_slot, 0);
	}
*/

	/* Stream a file, if desired */
	if ( ++app_config.ring_cnt==1 &&  app_config.wav_port[ri] != PJSUA_INVALID_ID)
	{
		NOTICE("pjsua conf connect wav_port[%d]...",ri);
		pjsua_conf_connect(app_config.wav_port[ri], 0);	   
	}
}

/* Callback from timer when the maximum call duration has been
 * exceeded.
 */
static void call_timeout_callback(pj_timer_heap_t *timer_heap,
				  struct pj_timer_entry *entry)
{
    pjsua_call_id call_id = entry->id;
    pjsua_msg_data msg_data_;
    pjsip_generic_string_hdr warn;
    pj_str_t hname = pj_str("Warning");
    pj_str_t hvalue = pj_str("399 pjsua \"Call duration exceeded\"");

    PJ_UNUSED_ARG(timer_heap);

    if (call_id == PJSUA_INVALID_ID) {
	PJ_LOG(1,(THIS_FILE, "Invalid call ID in timer callback"));
	return;
    }
    
    /* Add warning header */
    pjsua_msg_data_init(&msg_data_);
    pjsip_generic_string_hdr_init2(&warn, &hname, &hvalue);
    pj_list_push_back(&msg_data_.hdr_list, &warn);

    /* Call duration has been exceeded; disconnect the call */
    PJ_LOG(3,(THIS_FILE, "Duration (%d seconds) has been exceeded "
			 "for call %d, disconnecting the call",
			 app_config.duration, call_id));
    entry->id = PJSUA_INVALID_ID;
    pjsua_call_hangup(call_id, 200, NULL, &msg_data_);
}

/* General processing for media state. "mi" is the media index */
static void on_call_generic_media_state(pjsua_call_info *ci, unsigned mi,
                                        pj_bool_t *has_error)
{
    const char *status_name[] = {
        "None",
        "Active",
        "Local hold",
        "Remote hold",
        "Error"
    };

    PJ_UNUSED_ARG(has_error);

    pj_assert(ci->media[mi].status <= PJ_ARRAY_SIZE(status_name));
    pj_assert(PJSUA_CALL_MEDIA_ERROR == 4);

    PJ_LOG(4,(THIS_FILE, "Call %d media %d [type=%s], status is %s",
	      ci->id, mi, pjmedia_type_name(ci->media[mi].type),
	      status_name[ci->media[mi].status]));
}

/* Process audio media state. "mi" is the media index. */
static void on_call_audio_state(pjsua_call_info *ci, unsigned mi,
                                pj_bool_t *has_error)
{
    PJ_UNUSED_ARG(has_error);
    printf("\n\n\n==========================================================on_call_audio_state....\n");
    /* Stop ringback */
	ring_stop(ci->id);
#if 0
    /* Connect ports appropriately when media status is ACTIVE or REMOTE HOLD,
     * otherwise we should NOT connect the ports.
     */
    if (ci->media[mi].status == PJSUA_CALL_MEDIA_ACTIVE ||
	ci->media[mi].status == PJSUA_CALL_MEDIA_REMOTE_HOLD)
    {
	pj_bool_t connect_sound = PJ_TRUE;
	pj_bool_t disconnect_mic = PJ_FALSE;
	pjsua_conf_port_id call_conf_slot;

	call_conf_slot = ci->media[mi].stream.aud.conf_slot;

	/* Loopback sound, if desired */
	if (app_config.auto_loop) {
		pjsua_conf_connect(call_conf_slot, call_conf_slot);
		connect_sound = PJ_FALSE;
	}

	/* Automatically record conversation, if desired */
	if (app_config.auto_rec && app_config.rec_port != PJSUA_INVALID_ID) {
		pjsua_conf_connect(call_conf_slot, app_config.rec_port);
	}

	/* Stream a file, if desired */
	if ((app_config.auto_play || app_config.auto_play_hangup) && 
	    app_config.wav_port != PJSUA_INVALID_ID)
	{
	    pjsua_conf_connect(app_config.wav_port, call_conf_slot);
	    connect_sound = PJ_FALSE;
	}

	/* Stream AVI, if desired */
	if (app_config.avi_auto_play &&
	    app_config.avi_def_idx != PJSUA_INVALID_ID &&
	    app_config.avi[app_config.avi_def_idx].slot != PJSUA_INVALID_ID)
	{
	    pjsua_conf_connect(app_config.avi[app_config.avi_def_idx].slot,
			       call_conf_slot);
	    disconnect_mic = PJ_TRUE;
	}

	/* Put call in conference with other calls, if desired */
	if (app_config.auto_conf) {
	    pjsua_call_id call_ids[PJSUA_MAX_CALLS];
	    unsigned call_cnt=PJ_ARRAY_SIZE(call_ids);
	    unsigned i;

	    /* Get all calls, and establish media connection between
	     * this call and other calls.
	     */
	    pjsua_enum_calls(call_ids, &call_cnt);

	    for (i=0; i<call_cnt; ++i) {
		if (call_ids[i] == ci->id)
		    continue;
		
		if (!pjsua_call_has_media(call_ids[i]))
		    continue;

		pjsua_conf_connect(call_conf_slot,
				   pjsua_call_get_conf_port(call_ids[i]));
		pjsua_conf_connect(pjsua_call_get_conf_port(call_ids[i]),
		                   call_conf_slot);

		/* Automatically record conversation, if desired */
		if (app_config.auto_rec && app_config.rec_port !=
					   PJSUA_INVALID_ID)
		{
		    pjsua_conf_connect(pjsua_call_get_conf_port(call_ids[i]), 
				       app_config.rec_port);
		}

	    }

	    /* Also connect call to local sound device */
	    connect_sound = PJ_TRUE;
	}

	/* Otherwise connect to sound device */
	if (connect_sound) {
	    pjsua_conf_connect(call_conf_slot, 0);
	    if (!disconnect_mic)
		pjsua_conf_connect(0, call_conf_slot);

	    /* Automatically record conversation, if desired */
	    if (app_config.auto_rec && app_config.rec_port != PJSUA_INVALID_ID)
	    {
		pjsua_conf_connect(call_conf_slot, app_config.rec_port);
		pjsua_conf_connect(0, app_config.rec_port);
	    }
	}


    }
    #endif
}


/*
 * Callback on media state changed event.
 * The action may connect the call to sound device, to file, or
 * to loop the call.
 */
void pjsua_on_call_media_state(pjsua_call_id call_id)
{
    pjsua_call_info call_info;
    unsigned mi;
    pj_bool_t has_error = PJ_FALSE;

    pjsua_call_get_info(call_id, &call_info);

    for (mi=0; mi<call_info.media_cnt; ++mi) {
	on_call_generic_media_state(&call_info, mi, &has_error);

	switch (call_info.media[mi].type) {
	case PJMEDIA_TYPE_AUDIO:
	    on_call_audio_state(&call_info, mi, &has_error);
	    break;	
	default:
	    /* Make gcc happy about enum not handled by switch/case */
	    break;
	}
    }
}
/*
 * Handler when invite state has changed.
 */
void pjsua_on_call_state(pjsua_call_id call_id, pjsip_event *e)
{
    pjsua_call_info call_info;

    PJ_UNUSED_ARG(e);

    pjsua_call_get_info(call_id, &call_info);

	if (call_info.state == PJSIP_INV_STATE_DISCONNECTED) {

	/* Stop all ringback for this call */
	ring_stop(call_id);
#if 0
	/* Cancel duration timer, if any */
	if (app_config.call_data[call_id].timer.id != PJSUA_INVALID_ID) {
	    app_call_data *cd = &app_config.call_data[call_id];
	    pjsip_endpoint *endpt = pjsua_get_pjsip_endpt();

	    cd->timer.id = PJSUA_INVALID_ID;
	    pjsip_endpt_cancel_timer(endpt, &cd->timer);
	}

	/* Rewind play file when hangup automatically, 
	 * since file is not looped
	 */
	if (app_config.auto_play_hangup)
	    pjsua_player_set_pos(app_config.wav_id, 0);


	PJ_LOG(3,(THIS_FILE, "Call %d is DISCONNECTED [reason=%d (%s)]", 
		  call_id,
		  call_info.last_status,
		  call_info.last_status_text.ptr));

	//if (call_id == current_call) {
	//    find_next_call();
	//}

	/* Dump media state upon disconnected */
	if (1) {
	    PJ_LOG(5,(THIS_FILE, 
		      "Call %d disconnected, dumping media stats..", 
		      call_id));
	    //log_call_dump(call_id);
	}

    } else {

	if (app_config.duration != PJSUA_APP_NO_LIMIT_DURATION && 
	    call_info.state == PJSIP_INV_STATE_CONFIRMED) 
	{
	    /* Schedule timer to hangup call after the specified duration */
	    app_call_data *cd = &app_config.call_data[call_id];
	    pjsip_endpoint *endpt = pjsua_get_pjsip_endpt();
	    pj_time_val delay;

	    cd->timer.id = call_id;
	    delay.sec = app_config.duration;
	    delay.msec = 0;
	    pjsip_endpt_schedule_timer(endpt, &cd->timer, &delay);
	}

	if (call_info.state == PJSIP_INV_STATE_EARLY) {
	    int code;
	    pj_str_t reason;
	    pjsip_msg *msg;

	    /* This can only occur because of TX or RX message */
	    pj_assert(e->type == PJSIP_EVENT_TSX_STATE);

	    if (e->body.tsx_state.type == PJSIP_EVENT_RX_MSG) {
		msg = e->body.tsx_state.src.rdata->msg_info.msg;
	    } else {
		msg = e->body.tsx_state.src.tdata->msg;
	    }

	    code = msg->line.status.code;
	    reason = msg->line.status.reason;

	    /* Start ringback for 180 for UAC unless there's SDP in 180 */
	    if (call_info.role==PJSIP_ROLE_UAC && code==180 && 
		msg->body == NULL && 
		call_info.media_status==PJSUA_CALL_MEDIA_NONE) 
	    {
			ringback_start(call_id);
	    }

	    PJ_LOG(3,(THIS_FILE, "Call %d state changed to %s (%d %.*s)", 
		      call_id, call_info.state_text.ptr,
		      code, (int)reason.slen, reason.ptr));
	} else {
	    PJ_LOG(3,(THIS_FILE, "Call %d state changed to %s", 
		      call_id,
		      call_info.state_text.ptr));
	}
#endif
	}

	

}



/**
 * Handler when there is incoming call.
 */
void pjsua_on_incoming_call(pjsua_acc_id acc_id, pjsua_call_id call_id,
			     pjsip_rx_data *rdata)
{
    pjsua_call_info call_info;

    PJ_UNUSED_ARG(acc_id);
    PJ_UNUSED_ARG(rdata);

    pjsua_call_get_info(call_id, &call_info);

    /* Start ringback */
    ring_start(call_id,1);
}

/**
 * 对讲模式响铃.
 */
void pjsua_on_Intercom_call(pjsua_acc_id acc_id, pjsua_call_id call_id,
			     pjsip_rx_data *rdata)
{
    pjsua_call_info call_info;

    PJ_UNUSED_ARG(acc_id);
    PJ_UNUSED_ARG(rdata);

    pjsua_call_get_info(call_id, &call_info);

    /* Start ringback */
    ring_start(call_id,2);
}

/*
 * Handler when a transaction within a call has changed state.
 */
void pjsua_on_call_tsx_state(pjsua_call_id call_id,
			      pjsip_transaction *tsx,
			      pjsip_event *e)
{
    const pjsip_method info_method = 
    {
	PJSIP_OTHER_METHOD,
	{ "INFO", 4 }
    };

    if (pjsip_method_cmp(&tsx->method, &info_method)==0) {
	/*
	 * Handle INFO method.
	 */
	const pj_str_t STR_APPLICATION = { "application", 11};
	const pj_str_t STR_DTMF_RELAY  = { "dtmf-relay", 10 };
	pjsip_msg_body *body = NULL;
	pj_bool_t dtmf_info = PJ_FALSE;
	
	if (tsx->role == PJSIP_ROLE_UAC) {
	    if (e->body.tsx_state.type == PJSIP_EVENT_TX_MSG)
		body = e->body.tsx_state.src.tdata->msg->body;
	    else
		body = e->body.tsx_state.tsx->last_tx->msg->body;
	} else {
	    if (e->body.tsx_state.type == PJSIP_EVENT_RX_MSG)
		body = e->body.tsx_state.src.rdata->msg_info.msg->body;
	}
	
	/* Check DTMF content in the INFO message */
	if (body && body->len &&
	    pj_stricmp(&body->content_type.type, &STR_APPLICATION)==0 &&
	    pj_stricmp(&body->content_type.subtype, &STR_DTMF_RELAY)==0)
	{
	    dtmf_info = PJ_TRUE;
	}

	if (dtmf_info && tsx->role == PJSIP_ROLE_UAC && 
	    (tsx->state == PJSIP_TSX_STATE_COMPLETED ||
	       (tsx->state == PJSIP_TSX_STATE_TERMINATED &&
	        e->body.tsx_state.prev_state != PJSIP_TSX_STATE_COMPLETED))) 
	{
	    /* Status of outgoing INFO request */
	    if (tsx->status_code >= 200 && tsx->status_code < 300) {
		PJ_LOG(4,(THIS_FILE, 
			  "Call %d: DTMF sent successfully with INFO",
			  call_id));
	    } else if (tsx->status_code >= 300) {
		PJ_LOG(4,(THIS_FILE, 
			  "Call %d: Failed to send DTMF with INFO: %d/%.*s",
			  call_id,
		          tsx->status_code,
			  (int)tsx->status_text.slen,
			  tsx->status_text.ptr));
	    }
	} else if (dtmf_info && tsx->role == PJSIP_ROLE_UAS &&
		   tsx->state == PJSIP_TSX_STATE_TRYING)
	{
	    /* Answer incoming INFO with 200/OK */
	    pjsip_rx_data *rdata;
	    pjsip_tx_data *tdata;
	    pj_status_t status;

	    rdata = e->body.tsx_state.src.rdata;

	    if (rdata->msg_info.msg->body) {
		status = pjsip_endpt_create_response(tsx->endpt, rdata,
						     200, NULL, &tdata);
		if (status == PJ_SUCCESS)
		    status = pjsip_tsx_send_msg(tsx, tdata);

		PJ_LOG(3,(THIS_FILE, "Call %d: incoming INFO:\n%.*s", 
			  call_id,
			  (int)rdata->msg_info.msg->body->len,
			  rdata->msg_info.msg->body->data));
	    } else {
		status = pjsip_endpt_create_response(tsx->endpt, rdata,
						     400, NULL, &tdata);
		if (status == PJ_SUCCESS)
		    status = pjsip_tsx_send_msg(tsx, tdata);
	    }
	}
    }
}




pj_status_t pjsuaCommonInit(pj_pool_t *pool)
{
	int i=0;
	pj_status_t status;
	//app_config.no_tones = 1;
	app_config.no_tones = HAS_CALL_TONES? 0: 1;
	if(app_config.no_tones){
		return status;
	}

	NOTICE("pjsua Common Init...");
	default_config(pool);

	app_config.wav_count = 1;
	app_config.wav_files[0] = pj_str(RING_FILE_CALLING);
	app_config.wav_count = 2;
	app_config.wav_files[1] = pj_str(RING_FILE_INCOMING);
	app_config.wav_count = 3;
	app_config.wav_files[2] = pj_str(RING_FILE_INTERCOM_TONE);
	app_config.wav_count = 4;
	app_config.wav_files[3] = pj_str(RING_FILE_BUSY);

	/* Optionally registers WAV file */
    for (i=0; i<app_config.wav_count; ++i) {
		pjsua_player_id wav_id;
		unsigned play_options = 0;

		if (app_config.auto_play_hangup)
		    play_options |= PJMEDIA_FILE_NO_LOOP;

		status = pjsua_player_create(&app_config.wav_files[i], play_options, 
					     &wav_id);
		if (status != PJ_SUCCESS)
		    return  status;

		if (app_config.wav_id[i] == PJSUA_INVALID_ID) {
		    app_config.wav_id[i] = wav_id;
		    app_config.wav_port[i] = pjsua_player_get_conf_port(app_config.wav_id[i]);

	#if 0
		    if (app_config.auto_play_hangup) {
			pjmedia_port *port;

			pjsua_player_get_port(app_config.wav_id, &port);
			status = pjmedia_wav_player_set_eof_cb(port, NULL, 
							       &on_playfile_done);
			if (status != PJ_SUCCESS)
			    return  status;

			//pj_timer_entry_init(&app_config.auto_hangup_timer, 0, NULL, 
			//		    &hangup_timeout_callback);
		    
		    }
	#endif
		}
    }

	/* Optionally registers tone players */
    for (i=0; i<app_config.tone_count; ++i) {
	pjmedia_port *tport;
	char name[80];
	pj_str_t label;
	pj_status_t status2;

	pj_ansi_snprintf(name, sizeof(name), "tone-%d,%d",
			 app_config.tones[i].freq1, 
			 app_config.tones[i].freq2);
	label = pj_str(name);
	status2 = pjmedia_tonegen_create2(pool, &label,
					  8000, 1, 160, 16, 
					  PJMEDIA_TONEGEN_LOOP,  &tport);
	if (status2 != PJ_SUCCESS) {
	    pjsua_perror(THIS_FILE, "Unable to create tone generator", status);
	    return  status2;
	}

	status2 = pjsua_conf_add_port(pool, tport,
				     &app_config.tone_slots[i]);
	pj_assert(status2 == PJ_SUCCESS);

	status2 = pjmedia_tonegen_play(tport, 1, &app_config.tones[i], 0);
	pj_assert(status2 == PJ_SUCCESS);
    }



    /* Create ringback tones */
    if (app_config.no_tones == PJ_FALSE) {
	unsigned samples_per_frame;
	pjmedia_tone_desc tone[RING_CNT+RINGBACK_CNT];
	pj_str_t name;

	samples_per_frame = app_config.media_cfg.audio_frame_ptime * 
			    app_config.media_cfg.clock_rate *
			    app_config.media_cfg.channel_count / 1000;
    /* Ringback tone (call is ringing) */
	name = pj_str("ringback");
	status = pjmedia_tonegen_create2(pool, &name, 
					 app_config.media_cfg.clock_rate,
					 app_config.media_cfg.channel_count, 
					 samples_per_frame,
					 16, PJMEDIA_TONEGEN_LOOP, 
					 &app_config.ringback_port);
	if (status != PJ_SUCCESS)
	    return status;

	pj_bzero(&tone, sizeof(tone));
	for (i=0; i<RINGBACK_CNT; ++i) {
	    tone[i].freq1 = RINGBACK_FREQ1;
	    tone[i].freq2 = RINGBACK_FREQ2;
	    tone[i].on_msec = RINGBACK_ON;
	    tone[i].off_msec = RINGBACK_OFF;
	}
	tone[RINGBACK_CNT-1].off_msec = RINGBACK_INTERVAL;

	pjmedia_tonegen_play(app_config.ringback_port, RINGBACK_CNT, tone,
			     PJMEDIA_TONEGEN_LOOP);


	status = pjsua_conf_add_port(pool, app_config.ringback_port,
				     &app_config.ringback_slot);
	if (status != PJ_SUCCESS)
	    return status;

	/* Ring (to alert incoming call) */
	name = pj_str("ring");
	status = pjmedia_tonegen_create2(pool, &name, 
					 app_config.media_cfg.clock_rate,
					 app_config.media_cfg.channel_count, 
					 samples_per_frame,
					 16, PJMEDIA_TONEGEN_LOOP, 
					 &app_config.ring_port);
	if (status != PJ_SUCCESS)
	    return status;

	for (i=0; i<RING_CNT; ++i) {
	    tone[i].freq1 = RING_FREQ1;
	    tone[i].freq2 = RING_FREQ2;
	    tone[i].on_msec = RING_ON;
	    tone[i].off_msec = RING_OFF;
	}
	tone[RING_CNT-1].off_msec = RING_INTERVAL;

	pjmedia_tonegen_play(app_config.ring_port, RING_CNT, 
			     tone, PJMEDIA_TONEGEN_LOOP);

	status = pjsua_conf_add_port(pool, app_config.ring_port,
				     &app_config.ring_slot);
	if (status != PJ_SUCCESS)
	   return status;
    }


} 



#if 1

/* Check if lock codec is needed */
static pj_bool_t check_lock_codec(pjsua_call *call)
{
    const pjmedia_sdp_session *local_sdp, *remote_sdp;
    pj_bool_t has_mult_fmt = PJ_FALSE;
    unsigned i;
    pj_status_t status;

    /* Check if lock codec is disabled */
    if (!pjsua_var.acc[call->acc_id].cfg.lock_codec)
	return PJ_FALSE;

    /* Check lock codec retry count */
    if (call->lock_codec.retry_cnt >= LOCK_CODEC_MAX_RETRY)
        return PJ_FALSE;

    /* Check if we are the answerer, we shouldn't need to lock codec */
    if (!call->inv->neg || !pjmedia_sdp_neg_was_answer_remote(call->inv->neg))
        return PJ_FALSE;

    /* Check if remote answerer has given us more than one codecs. */
    status = pjmedia_sdp_neg_get_active_local(call->inv->neg, &local_sdp);
    if (status != PJ_SUCCESS)
	return PJ_FALSE;
    status = pjmedia_sdp_neg_get_active_remote(call->inv->neg, &remote_sdp);
    if (status != PJ_SUCCESS)
	return PJ_FALSE;

    for (i = 0; i < call->med_cnt && !has_mult_fmt; ++i) {
	pjsua_call_media *call_med = &call->media[i];
	const pjmedia_sdp_media *rem_m, *loc_m;
	unsigned codec_cnt = 0;
	unsigned j;

	/* Skip this if the media is inactive or error */
	if (call_med->state == PJSUA_CALL_MEDIA_NONE ||
	    call_med->state == PJSUA_CALL_MEDIA_ERROR ||
	    call_med->dir == PJMEDIA_DIR_NONE)
	{
	    continue;
	}

	/* Remote may answer with less media lines. */
	if (i >= remote_sdp->media_count)
	    continue;

	rem_m = remote_sdp->media[i];
	loc_m = local_sdp->media[i];

	/* Verify that media must be active. */
	pj_assert(loc_m->desc.port && rem_m->desc.port);
	PJ_UNUSED_ARG(loc_m);

	/* Count the formats in the answer. */
	for (j=0; j<rem_m->desc.fmt_count && codec_cnt <= 1; ++j) {
	    if (!is_non_av_fmt(rem_m, &rem_m->desc.fmt[j]) && ++codec_cnt > 1)
		has_mult_fmt = PJ_TRUE;
	}
    }

    /* Reset retry count when remote answer has one codec */
    if (!has_mult_fmt)
	call->lock_codec.retry_cnt = 0;

    return has_mult_fmt;
}


/* Check if ICE setup is complete and if it needs to send reinvite */
pj_bool_t check_ice_complete(pjsua_call *call, pj_bool_t *need_reinv)
{
    pj_bool_t ice_need_reinv = PJ_FALSE;
    pj_bool_t ice_complete = PJ_TRUE;
    unsigned i;

    /* Check if ICE setup is complete and if it needs reinvite */
    for (i = 0; i < call->med_cnt; ++i) {
	pjsua_call_media *call_med = &call->media[i];
	pjmedia_transport_info tpinfo;
	pjmedia_ice_transport_info *ice_info;

	if (call_med->tp_st == PJSUA_MED_TP_NULL ||
	    call_med->tp_st == PJSUA_MED_TP_DISABLED ||
	    call_med->state == PJSUA_CALL_MEDIA_ERROR)
	{
	    continue;
	}

	pjmedia_transport_info_init(&tpinfo);
	pjmedia_transport_get_info(call_med->tp, &tpinfo);
	ice_info = (pjmedia_ice_transport_info*)
		   pjmedia_transport_info_get_spc_info(
					&tpinfo, PJMEDIA_TRANSPORT_TYPE_ICE);

	/* Check if ICE is active */
	if (!ice_info || !ice_info->active)
	    continue;

	/* Check if ICE setup not completed yet */
	if (ice_info->sess_state < PJ_ICE_STRANS_STATE_RUNNING)	{
	    ice_complete = PJ_FALSE;
	    break;
	}

	/* Check if ICE needs to send reinvite */
	if (!ice_need_reinv &&
	    ice_info->sess_state == PJ_ICE_STRANS_STATE_RUNNING &&
	    ice_info->role == PJ_ICE_SESS_ROLE_CONTROLLING)
	{
	    pjsua_ice_config *cfg=&pjsua_var.acc[call->acc_id].cfg.ice_cfg;
	    if ((cfg->ice_always_update && !call->reinv_ice_sent) ||
		pj_sockaddr_cmp(&tpinfo.sock_info.rtp_addr_name,
				&call_med->rtp_addr))
	    {
		ice_need_reinv = PJ_TRUE;
	    }
	}
    }

    if (ice_complete && need_reinv)
	*need_reinv = ice_need_reinv;

    return ice_complete;
}


/* Check if the specified format can be skipped in counting codecs */
pj_bool_t is_non_av_fmt(const pjmedia_sdp_media *m,
			       const pj_str_t *fmt)
{
    const pj_str_t STR_TEL = {"telephone-event", 15};
    unsigned pt;

    pt = pj_strtoul(fmt);

    /* Check for comfort noise */
    if (pt == PJMEDIA_RTP_PT_CN)
	return PJ_TRUE;

    /* Dynamic PT, check the format name */
    if (pt >= 96) {
	pjmedia_sdp_attr *a;
	pjmedia_sdp_rtpmap rtpmap;

	/* Get the format name */
	a = pjmedia_sdp_attr_find2(m->attr_count, m->attr, "rtpmap", fmt);
	if (a && pjmedia_sdp_attr_get_rtpmap(a, &rtpmap)==PJ_SUCCESS) {
	    /* Check for telephone-event */
	    if (pj_stricmp(&rtpmap.enc_name, &STR_TEL)==0)
		return PJ_TRUE;
	} else {
	    /* Invalid SDP, should not reach here */
	    pj_assert(!"SDP should have been validated!");
	    return PJ_TRUE;
	}
    }

    return PJ_FALSE;
}


/* Check and send reinvite for lock codec and ICE update */
pj_status_t process_pending_reinvite(pjsua_call *call)
{
    const pj_str_t ST_UPDATE = {"UPDATE", 6};
    pj_pool_t *pool = call->inv->pool_prov;
    pjsip_inv_session *inv = call->inv;
    pj_bool_t ice_need_reinv;
    pj_bool_t ice_completed;
    pj_bool_t need_lock_codec;
    pj_bool_t rem_can_update;
    pjmedia_sdp_session *new_offer;
    pjsip_tx_data *tdata;
    unsigned i;
    pj_status_t status;

    /* Verify if another SDP negotiation is in progress, e.g: session timer
     * or another re-INVITE.
     */
    if (inv==NULL || inv->neg==NULL ||
	pjmedia_sdp_neg_get_state(inv->neg)!=PJMEDIA_SDP_NEG_STATE_DONE)
    {
	return PJMEDIA_SDPNEG_EINSTATE;
    }

    /* Don't do this if call is disconnecting! */
    if (inv->state > PJSIP_INV_STATE_CONFIRMED || inv->cause >= 200)
    {
	return PJ_EINVALIDOP;
    }

    /* Delay this when the SDP negotiation done in call state EARLY and
     * remote does not support UPDATE method.
     */
    if (inv->state == PJSIP_INV_STATE_EARLY &&
	pjsip_dlg_remote_has_cap(inv->dlg, PJSIP_H_ALLOW, NULL, &ST_UPDATE)!=
	PJSIP_DIALOG_CAP_SUPPORTED)
    {
        call->reinv_pending = PJ_TRUE;
        return PJ_EPENDING;
    }

    /* Check if ICE setup is complete and if it needs reinvite */
    ice_completed = check_ice_complete(call, &ice_need_reinv);
    if (!ice_completed)
	return PJ_EPENDING;

    /* Check if we need to lock codec */
    need_lock_codec = check_lock_codec(call);

    /* Check if reinvite is really needed */
    if (!need_lock_codec && !ice_need_reinv)
	return PJ_SUCCESS;


    /* Okay! So we need to send re-INVITE/UPDATE */

    /* Check if remote support UPDATE */
    rem_can_update = pjsip_dlg_remote_has_cap(inv->dlg, PJSIP_H_ALLOW, NULL,
					      &ST_UPDATE) ==
						PJSIP_DIALOG_CAP_SUPPORTED;

    /* Logging stuff */
    {
	const char *ST_ICE_UPDATE = "ICE transport address after "
				    "ICE negotiation";
	const char *ST_LOCK_CODEC = "media session to use only one codec";
	PJ_LOG(4,(THIS_FILE, "Call %d sending %s for updating %s%s%s",
		  call->index,
		  (rem_can_update? "UPDATE" : "re-INVITE"),
		  (ice_need_reinv? ST_ICE_UPDATE : ST_LOCK_CODEC),
		  (ice_need_reinv && need_lock_codec? " and " : ""),
		  (ice_need_reinv && need_lock_codec? ST_LOCK_CODEC : "")
		  ));
    }

    /* Generate SDP re-offer */
    status = pjsua_media_channel_create_sdp(call->index, pool, NULL,
					    &new_offer, NULL);
    if (status != PJ_SUCCESS) {
	pjsua_perror(THIS_FILE, "Unable to create local SDP", status);
	return status;
    }

    /* Update the new offer so it contains only a codec. Note that
     * SDP nego has removed unmatched codecs from the offer and the codec
     * order in the offer has been matched to the answer, so we'll override
     * the codecs in the just generated SDP with the ones from the active
     * local SDP and leave just one codec for the next SDP re-offer.
     */
    if (need_lock_codec) {
	const pjmedia_sdp_session *ref_sdp;

	/* Get local active SDP as reference */
	status = pjmedia_sdp_neg_get_active_local(call->inv->neg, &ref_sdp);
	if (status != PJ_SUCCESS)
	    return status;

	/* Verify media count. Note that remote may add/remove media line
	 * in the answer. When answer has less media, it must have been
	 * handled by pjsua_media_channel_update() as disabled media.
	 * When answer has more media, it must have been ignored (treated
	 * as non-exist) anywhere. Local media count should not be updated
	 * at this point, as modifying media count operation (i.e: reinvite,
	 * update, vid_set_strm) is currently blocking, protected with
	 * dialog mutex, and eventually reset SDP nego state to LOCAL OFFER.
	 */
	if (call->med_cnt != ref_sdp->media_count ||
	    ref_sdp->media_count != new_offer->media_count)
	{
	    /* Anyway, just in case, let's just return error */
	    return PJMEDIA_SDPNEG_EINSTATE;
	}

	for (i = 0; i < call->med_cnt; ++i) {
	    unsigned j, codec_cnt = 0;
	    const pjmedia_sdp_media *ref_m = ref_sdp->media[i];
	    pjmedia_sdp_media *m = new_offer->media[i];
	    pjsua_call_media *call_med = &call->media[i];

	    /* Verify if media is deactivated */
	    if (call_med->state == PJSUA_CALL_MEDIA_NONE ||
		call_med->state == PJSUA_CALL_MEDIA_ERROR ||
		call_med->dir == PJMEDIA_DIR_NONE)
	    {
		continue;
	    }

	    /* Reset formats */
	    m->desc.fmt_count = 0;
	    pjmedia_sdp_attr_remove_all(&m->attr_count, m->attr, "rtpmap");
	    pjmedia_sdp_attr_remove_all(&m->attr_count, m->attr, "fmtp");

	    /* Copy only the first format + any non-AV formats from
	     * the active local SDP.
	     */
	    for (j = 0; j < ref_m->desc.fmt_count; ++j) {
		const pj_str_t *fmt = &ref_m->desc.fmt[j];

		if (is_non_av_fmt(ref_m, fmt) || (++codec_cnt == 1)) {
		    pjmedia_sdp_attr *a;

		    m->desc.fmt[m->desc.fmt_count++] = *fmt;
		    a = pjmedia_sdp_attr_find2(ref_m->attr_count, ref_m->attr,
					       "rtpmap", fmt);
		    if (a) {
		        pjmedia_sdp_attr_add(&m->attr_count, m->attr,
		    			     pjmedia_sdp_attr_clone(pool, a));
		    }
		    a = pjmedia_sdp_attr_find2(ref_m->attr_count, ref_m->attr,
					       "fmtp", fmt);
		    if (a) {
		        pjmedia_sdp_attr_add(&m->attr_count, m->attr,
		        		     pjmedia_sdp_attr_clone(pool, a));
		    }
		}
	    }
	}
    }

    /* Put back original direction and "c=0.0.0.0" line */
    {
	const pjmedia_sdp_session *cur_sdp;

	/* Get local active SDP */
	status = pjmedia_sdp_neg_get_active_local(call->inv->neg, &cur_sdp);
	if (status != PJ_SUCCESS)
	    return status;

	/* Make sure media count has not been changed */
	if (call->med_cnt != cur_sdp->media_count)
	    return PJMEDIA_SDPNEG_EINSTATE;

	for (i = 0; i < call->med_cnt; ++i) {
	    const pjmedia_sdp_media *m = cur_sdp->media[i];
	    pjmedia_sdp_media *new_m = new_offer->media[i];
	    pjsua_call_media *call_med = &call->media[i];
	    pjmedia_sdp_attr *a = NULL;

	    /* Update direction to the current dir */
	    pjmedia_sdp_media_remove_all_attr(new_m, "sendrecv");
	    pjmedia_sdp_media_remove_all_attr(new_m, "sendonly");
	    pjmedia_sdp_media_remove_all_attr(new_m, "recvonly");
	    pjmedia_sdp_media_remove_all_attr(new_m, "inactive");

	    if (call_med->dir == PJMEDIA_DIR_ENCODING_DECODING) {
		a = pjmedia_sdp_attr_create(pool, "sendrecv", NULL);
	    } else if (call_med->dir == PJMEDIA_DIR_ENCODING) {
		a = pjmedia_sdp_attr_create(pool, "sendonly", NULL);
	    } else if (call_med->dir == PJMEDIA_DIR_DECODING) {
		a = pjmedia_sdp_attr_create(pool, "recvonly", NULL);
	    } else {
		const pjmedia_sdp_conn *conn;
		a = pjmedia_sdp_attr_create(pool, "inactive", NULL);

		/* Also check if the original c= line address is zero */
		conn = m->conn;
		if (!conn)
		    conn = cur_sdp->conn;
		if (pj_strcmp2(&conn->addr, "0.0.0.0")==0 ||
		    pj_strcmp2(&conn->addr, "0")==0)
		{
		    if (!new_m->conn) {
			new_m->conn = PJ_POOL_ZALLOC_T(pool, pjmedia_sdp_conn);
		    }

		    if (pj_strcmp2(&new_m->conn->addr, "0.0.0.0")) {
			new_m->conn->net_type = pj_str("IN");
			new_m->conn->addr_type = pj_str("IP4");
			new_m->conn->addr = pj_str("0.0.0.0");
		    }
		}
	    }

	    pj_assert(a);
	    pjmedia_sdp_media_add_attr(new_m, a);
	}
    }


    if (rem_can_update) {
	status = pjsip_inv_update(inv, NULL, new_offer, &tdata);
    } else {
	status = pjsip_inv_reinvite(inv, NULL, new_offer, &tdata);
    }

    if (status==PJ_EINVALIDOP &&
	++call->lock_codec.retry_cnt < LOCK_CODEC_MAX_RETRY)
    {
	/* Ups, let's reschedule again */
	pjsua_call_schedule_reinvite_check(call, LOCK_CODEC_RETRY_INTERVAL);
	return PJ_SUCCESS;
    } else if (status != PJ_SUCCESS) {
	pjsua_perror(THIS_FILE, "Error creating UPDATE/re-INVITE",
		     status);
	return status;
    }

    /* Send the UPDATE/re-INVITE request */
    status = pjsip_inv_send_msg(inv, tdata);
    if (status != PJ_SUCCESS) {
	pjsua_perror(THIS_FILE, "Error sending UPDATE/re-INVITE",
		     status);
	return status;
    }

    /* Update flags */
    if (ice_need_reinv)
	call->reinv_ice_sent = PJ_TRUE;
    if (need_lock_codec)
	++call->lock_codec.retry_cnt;

    return PJ_SUCCESS;
}

#endif