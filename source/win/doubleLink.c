/*
 * stack.c
 *
 *  Created on: Aug 27, 2020
 *      Author: king
 */

#include <malloc.h>
#include "assert.h"
#include "doubleLink.h"



void Win_Dlist_Init(DoubleLink *list) {
  Node *s = (Node*)malloc(sizeof(Node));
  assert(s != NULL);
  list->first = list->last = s;
  list->first->prio = NULL;
  list->last->next = NULL;
  list->size = 0;
}

static Node* Win_Dlist_createnode(ElemType x) {
  Node *s = (Node*)malloc(sizeof(Node));
  assert(s != NULL);
  s->data = x;
  s->prio = s->next = NULL;
  return s;
}

void Win_Dlist_push_back(DoubleLink *list, ElemType x) {
  Node *s = Win_Dlist_createnode(x);
  s->prio = list->last;
  list->last->next = s;
  list->last = s;
  list->size++;
}
void Win_Dlist_push_front(DoubleLink *list,ElemType x) {
  Node *s = Win_Dlist_createnode(x);
  if (list->first == list->last) {
    s->prio = list->first;
    list->first->next = s;
    list->last = s;
  }
  else {
    s->next = list->first->next;
    s->next->prio = s;
    s->prio = list->first;
    list->first->next = s;
  }
  list->size++;
}
void Win_Dlist_show_list(DoubleLink *list) {
  Node *p = list->first->next;
  while (p != NULL) {
    printf("%d->", p->data);
    p = p->next;
  }
  printf("Nul.\n");
}
void Win_Dlist_pop_back(DoubleLink *list) {
  if (list->size == 0) return;
  Node *p = list->first;
  while (p->next != list->last)
    p = p->next;
  free(list->last);
  list->last = p;
  list->last->next = NULL;
  list->size--;
}
void Win_Dlist_pop_front(DoubleLink *list) {
  if (list->size == 0) return;
  Node *p = list->first->next;
  if (list->first->next == list->last) {
    list->last = list->first;
    list->last->next = NULL;
  }
  else {
    list->first->next = p->next;
    p->next->prio = list->first;
  }
  free(p);
  list->size--;
}
void Win_Dlist_insert_val(DoubleLink *list, ElemType x) {
  Node *p = list->first;
  while (p->next != NULL && p->next->data < x)
    p = p->next;
  if (p->next == NULL)
	  Win_Dlist_push_back(list, x);
  else {
    Node *s = Win_Dlist_createnode(x);
    s->next = p->next;
    s->next->prio = s;
    s->prio = p;
    p->next = s;
    list->size++;
  }
}
Node* Win_Dlist_find(DoubleLink *list, ElemType x) {
  Node *p = list->first->next;
  while (p!=NULL && p->data != x)
    p = p->next;
  return p;
}
int Win_Dlist_length(DoubleLink *list) {
  return list->size;
}
void Win_Dlist_delete_val(DoubleLink *list, ElemType x) {
  if (list->size == 0) return;
  Node *p = Win_Dlist_find(list, x);
  if (p == NULL) {
    printf("要删除的数据不存在:%d\n",x);
    return;
  }
  if (p == list->last) {
    list->last = p->prio;
    list->last->next = NULL;
  }
  else {
    p->next->prio = p->prio;
    p->prio->next = p->next;
  }
  free(p);
  list->size--;
}
void Win_Dlist_sort(DoubleLink *list) {
  if (list->size == 0 || list->size == 1) return;
  Node *s = list->first->next;
  Node *q = s->next;
  list->last = s;
  list->last->next = NULL;
  while (q != NULL) {
    s = q;
    q = q->next;
    Node *p = list->first;
    while (p->next != NULL && p->next->data < s->data)
      p = p->next;
    if (p->next == NULL) {
      s->next = NULL;
      s->prio = list->last;
      list->last->next = s;
      list->last = s;
    }
    else {
      s->next = p->next;
      s->next->prio = s;
      s->prio = p;
      p->next = s;
    }
  }
}

static void Win_Dlist_clear(DoubleLink *list) {
  if (list->size == 0) return;
  Node *p = list->first->next;
  while (p != NULL) {
    if (p == list->last) {
      list->last = list->first;
      list->last->next = NULL;
    }
    else {
      p->next->prio = p->prio;
      p->prio->next = p->next;
    }
    free(p);
    p = list->first->next;
  }
  list->size = 0;
}

void Win_Dlist_destroy(DoubleLink *list) {
	Win_Dlist_clear(list);
  free(list->first);
  list->first = list->last = NULL;
}


