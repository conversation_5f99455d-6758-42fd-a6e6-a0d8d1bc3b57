#include "sysconf.h"
#include "rotaryEncoder.h"
#include "theme.h"
#include "yihui_settingsWindow.h"

lv_obj_t *screen_settings;  //暂未用到
lv_obj_t *settings_all_cont;
lv_obj_t *settings_info_cont,*settings_ip_cont;
lv_obj_t *settings_head_title_label;

lv_obj_t *settings_head_pre_btn,*settings_head_next_btn;

lv_obj_t *settings_textArea_brightness,*settings_textArea_micVolume,*settings_textArea_LineVolume;
lv_obj_t *settings_brightness_slider,*settings_micVolume_slider,*settings_lineVolume_slider;

#if SUPPORT_BASS_TREBLE_CONTROL
lv_obj_t *settings_textArea_bass,*settings_textArea_treble;
lv_obj_t *settings_bass_slider,*settings_treble_slider;
#endif

// IP地址、掩码、网关的数值存储
static uint8_t ip_parts[4] = {0};
static uint8_t mask_parts[4] = {0};
static uint8_t gateway_parts[4] = {0};

// LVGL对象指针
static lv_obj_t* ip_items[4];
static lv_obj_t* mask_items[4];
static lv_obj_t* gateway_items[4];

static lv_obj_t *ipsettings_save_btn;
static lv_obj_t *main_settings_ipsettings_btn, *main_settings_remote_addrCode_btn, *main_settings_quit_btn;
static lv_obj_t *remoteAddrCode_save_btn,*remoteAddrCode_quit_btn;
static lv_obj_t *remoteAddrCode_TextArea;


bool settings_editMode=false;
static int current_focus_ip = -1;         // 当前焦点项索引（0-12，12为保存）

#if(SELECTED_DISPLAY_TYPE==DISPLAY_TYPE_RGB_4P3_480X272)
#define SETTINGS_HEAD_CONT_HEIGHT 45

#define SETTINGS_IP_LABEL_POS_X 60
#define SETTINGS_IP_LABEL_POS_Y 25
#define SETTINGS_IP_LABEL_SPACING_Y 50
#define SETTINGS_IP_TEXTAREA_WIDTH 50
#define SETTINGS_IP_TEXTAREA_HEIGHT 30
#define SETTINGS_IP_TEXTAREA_POS_X 172
#define SETTINGS_IP_TEXTAREA_POS_Y 20
#define SETTINGS_IP_TEXTAREA_SPACING_X 60
#define SETTINGS_IP_TEXTAREA_SPACING_Y 50

#define SETTINGS_IP_LABEL_STYLE_FONT    style_font_24
#define SETTINGS_IP_TEXTAREA_STYLE_FONT style_font_20

#define SETTINGS_IP_SAVE_BTN_POS_X 60
#define SETTINGS_IP_SAVE_BTN_POS_Y 175
#define SETTINGS_IP_SAVE_BTN_WIDTH 340
#define SETTINGS_IP_SAVE_BTN_HEIGHT 38
#define SETTINGS_IP_SAVE_BTN_STYLE_FONT    style_font_24

#else
#define SETTINGS_HEAD_CONT_HEIGHT 35

#define SETTINGS_IP_LABEL_POS_X 25
#define SETTINGS_IP_LABEL_POS_Y 10
#define SETTINGS_IP_LABEL_SPACING_Y 34
#define SETTINGS_IP_TEXTAREA_WIDTH 42
#define SETTINGS_IP_TEXTAREA_HEIGHT 10
#define SETTINGS_IP_TEXTAREA_POS_X 120
#define SETTINGS_IP_TEXTAREA_POS_Y 5
#define SETTINGS_IP_TEXTAREA_SPACING_X 45
#define SETTINGS_IP_TEXTAREA_SPACING_Y 32

#define SETTINGS_IP_LABEL_STYLE_FONT    style_font_20
#define SETTINGS_IP_TEXTAREA_STYLE_FONT style_font_16

#define SETTINGS_IP_SAVE_BTN_POS_X 25
#define SETTINGS_IP_SAVE_BTN_POS_Y 105
#define SETTINGS_IP_SAVE_BTN_WIDTH 270
#define SETTINGS_IP_SAVE_BTN_HEIGHT 28
#define SETTINGS_IP_SAVE_BTN_STYLE_FONT    style_font_20
#endif

//TODO 按键-菜单键，系统设置，上下按钮，飞梭选择

#if (YIHUI_VERSION || USE_PC_SIMULATOR)

enum{
    SETTINGS_PAGE_INFO=1,
    SETTINGS_PAGE_IP,
    SETTINGS_PAGE_MAX
};
int current_settings_page = SETTINGS_PAGE_INFO;

// 样式定义
static lv_style_t style_default;
static lv_style_t style_focused;
static lv_style_t style_edit;
//static lv_style_t style_checked;

void yihui_return_to_main_win(void);
void yihui_settings_win_ip_page();
static void save_network_settings();
static void ipsettings_update_highlight();

static void textArea_style_init()
{
     // 创建样式
    lv_style_reset(&style_default);  //重置样式释放内存
    lv_style_init(&style_default);
    lv_style_set_bg_color(&style_default, LV_STATE_DEFAULT, lv_color_hex(0xFFFFFF));
    lv_style_set_border_width(&style_default, LV_STATE_DEFAULT, 1);
    //lv_style_set_bg_opa(&style_default, LV_STATE_DEFAULT, LV_OPA_TRANSP);
    //lv_style_set_text_color(&style_default, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 201, 154));
    
    lv_style_reset(&style_focused);  //重置样式释放内存
    lv_style_init(&style_focused);
    lv_style_set_bg_color(&style_focused, LV_STATE_FOCUSED, lv_color_hex(0x503319));
    //lv_style_set_border_width(&style_focused, LV_STATE_FOCUSED, 0);
    //lv_style_set_bg_opa(&style_focused, LV_STATE_FOCUSED, LV_OPA_TRANSP);
    lv_style_set_text_color(&style_focused, LV_STATE_FOCUSED, LV_COLOR_MAKE(255, 201, 154));
    
    lv_style_reset(&style_edit);  //重置样式释放内存
    lv_style_init(&style_edit);
    lv_style_set_bg_color(&style_edit, LV_STATE_EDITED, lv_color_hex(0xFF0000));
    //lv_style_set_border_width(&style_edit, LV_STATE_EDITED, 0);
    //lv_style_set_bg_opa(&style_edit, LV_STATE_EDITED, LV_OPA_TRANSP);
    lv_style_set_text_color(&style_edit, LV_STATE_EDITED, LV_COLOR_MAKE(255, 201, 154));

#if 0
    lv_style_reset(&style_checked);  //重置样式释放内存
    lv_style_init(&style_checked);
    lv_style_set_bg_color(&style_checked, LV_STATE_CHECKED, lv_color_hex(0xFF0000));
    //lv_style_set_border_width(&style_edit, LV_STATE_EDITED, 0);
    //lv_style_set_bg_opa(&style_edit, LV_STATE_EDITED, LV_OPA_TRANSP);
    lv_style_set_text_color(&style_checked, LV_STATE_CHECKED, LV_COLOR_MAKE(255, 201, 154));
#endif
}


static void prNextPage_btn_event_cb(lv_obj_t * obj, lv_event_t e)
{
    if(e == LV_EVENT_RELEASED)
	{
		if(obj == settings_head_pre_btn)
		{
            if(current_settings_page == SETTINGS_PAGE_INFO)
            {
                yihui_return_to_main_win();
            }
            else if(current_settings_page == SETTINGS_PAGE_IP)
            {
                //删除ip
                lv_obj_del(settings_ip_cont);
                //进入INFO
                yihui_settings_win_info_page();
            }
		}
		else if(obj == settings_head_next_btn)
        {
            if(current_settings_page == SETTINGS_PAGE_INFO)
            {
                //删除info
                lv_obj_del(settings_info_cont);
                //进入IP设置
                yihui_settings_win_ip_page();
            }
        }
	}
}

static void info_slider_event_cb(lv_obj_t * slider, lv_event_t e)
{
    if(e == LV_EVENT_VALUE_CHANGED)
    {
        int val=lv_slider_get_value(slider);
        char strVal[10]={0};
        sprintf(strVal,"%d",val);
        if(slider == settings_brightness_slider)
        {
            lv_textarea_set_text(settings_textArea_brightness,strVal);
            backlight_adjust(val);
            g_backlight_level = val;
            save_sysconf(INI_SECTION_DEVICE,"Display_Brightness");
        }
        else if(slider == settings_micVolume_slider)
        {
            lv_textarea_set_text(settings_textArea_micVolume,strVal);
            g_micVolume = val;
            save_sysconf(INI_SECTION_BASIC,"Mic_Volume");
        }
        else if(slider == settings_lineVolume_slider)
        {
            lv_textarea_set_text(settings_textArea_LineVolume,strVal);
            g_lineVolume = val;
            save_sysconf(INI_SECTION_BASIC,"Line_Volume");
        }
        #if SUPPORT_BASS_TREBLE_CONTROL
        else if(slider == settings_bass_slider)
        {
            int16_t gain = val - 10; // 转换为-10到+10范围
            sprintf(strVal,"%d",gain);
            lv_textarea_set_text(settings_textArea_bass,strVal);
        }
        else if(slider == settings_treble_slider)
        {
            int16_t gain = val - 10; // 转换为-10到+10范围
            sprintf(strVal,"%d",gain);
            lv_textarea_set_text(settings_textArea_treble,strVal);
        }
        #endif
    }
}

static void ip_item_event_cb(lv_obj_t * obj, lv_event_t e)
{
    if(e == LV_EVENT_FOCUSED)
	{
        for(int i=0; i<4; i++) {
            if(obj == ip_items[i])
            {
                current_focus_ip = i;
            }
            else if(obj == mask_items[i])
            {
                current_focus_ip = i+4;
            }
            else if(obj == gateway_items[i])
            {
                current_focus_ip = i+8;
            }
        }
        ipsettings_update_highlight();
	}
}

static void ipSave_btn_event_cb(lv_obj_t * obj, lv_event_t e)
{
    if(e == LV_EVENT_PRESSED)
	{
        // 保存IP地址
        save_network_settings();
	}
}

void yihui_settings_win_info_page()
{
    settings_editMode = false;
    current_settings_page = SETTINGS_PAGE_INFO;

    lv_obj_set_hidden(settings_head_next_btn,false); //显示下翻页按钮

    lv_label_set_text(settings_head_title_label, "系统设置");

    //INFO
    settings_info_cont = lv_obj_create(settings_all_cont, NULL);
    lv_obj_set_size(settings_info_cont,LV_HOR_RES_MAX,LV_VER_RES_MAX-SETTINGS_HEAD_CONT_HEIGHT);
    lv_obj_set_pos(settings_info_cont,0,SETTINGS_HEAD_CONT_HEIGHT+1);
    lv_obj_set_style_local_pad_all(settings_info_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(settings_info_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_radius(settings_info_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_border_width(settings_info_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);


    //屏幕亮度
    lv_obj_t *settings_brightness_label = lv_label_create(settings_info_cont, NULL);
    #if SUPPORT_BASS_TREBLE_CONTROL
    lv_obj_set_pos(settings_brightness_label,70,60-SETTINGS_HEAD_CONT_HEIGHT);
    #else
    lv_obj_set_pos(settings_brightness_label,70,80-SETTINGS_HEAD_CONT_HEIGHT);
    #endif
    lv_label_set_text(settings_brightness_label, "屏幕亮度:");
    lv_obj_set_style_local_text_font(settings_brightness_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font24);
    settings_brightness_slider = lv_slider_create(settings_info_cont, NULL);
    lv_slider_set_value(settings_brightness_slider, g_backlight_level, LV_ANIM_OFF);
    lv_obj_set_size(settings_brightness_slider, 150, 12);
    #if SUPPORT_BASS_TREBLE_CONTROL
    lv_obj_set_pos(settings_brightness_slider,190,67-SETTINGS_HEAD_CONT_HEIGHT);
    #else
    lv_obj_set_pos(settings_brightness_slider,190,87-SETTINGS_HEAD_CONT_HEIGHT);
    #endif

    //话筒音量
    lv_obj_t *settings_mic_label = lv_label_create(settings_info_cont, NULL);
    #if SUPPORT_BASS_TREBLE_CONTROL
    lv_obj_set_pos(settings_mic_label,70,100-SETTINGS_HEAD_CONT_HEIGHT);
    #else
    lv_obj_set_pos(settings_mic_label,70,120-SETTINGS_HEAD_CONT_HEIGHT);
    #endif
    lv_label_set_text(settings_mic_label, "话筒音量:");
    lv_obj_set_style_local_text_font(settings_mic_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font24);
    settings_micVolume_slider = lv_slider_create(settings_info_cont, NULL);
    lv_slider_set_value(settings_micVolume_slider, g_micVolume, LV_ANIM_OFF);
    lv_obj_set_size(settings_micVolume_slider, 150, 12);
    #if SUPPORT_BASS_TREBLE_CONTROL
    lv_obj_set_pos(settings_micVolume_slider,190,107-SETTINGS_HEAD_CONT_HEIGHT);
    #else
    lv_obj_set_pos(settings_micVolume_slider,190,127-SETTINGS_HEAD_CONT_HEIGHT);
    #endif

    //线路音量
    lv_obj_t *settings_line_label = lv_label_create(settings_info_cont, NULL);
    #if SUPPORT_BASS_TREBLE_CONTROL
    lv_obj_set_pos(settings_line_label,70,140-SETTINGS_HEAD_CONT_HEIGHT);
    #else
    lv_obj_set_pos(settings_line_label,70,160-SETTINGS_HEAD_CONT_HEIGHT);
    #endif
    lv_label_set_text(settings_line_label, "线路音量:");
    lv_obj_set_style_local_text_font(settings_line_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font24);
    settings_lineVolume_slider = lv_slider_create(settings_info_cont, NULL);
    lv_slider_set_value(settings_lineVolume_slider, g_lineVolume, LV_ANIM_OFF);
    lv_obj_set_size(settings_lineVolume_slider, 150, 12);
    #if SUPPORT_BASS_TREBLE_CONTROL
    lv_obj_set_pos(settings_lineVolume_slider,190,147-SETTINGS_HEAD_CONT_HEIGHT);
    #else
    lv_obj_set_pos(settings_lineVolume_slider,190,167-SETTINGS_HEAD_CONT_HEIGHT);
    #endif

    #if SUPPORT_BASS_TREBLE_CONTROL
    //高音
    lv_obj_t *settings_bass_label = lv_label_create(settings_info_cont, NULL);
    lv_obj_set_pos(settings_bass_label,45,180-SETTINGS_HEAD_CONT_HEIGHT);
    lv_label_set_text(settings_bass_label, "高音电位器:");
    lv_obj_set_style_local_text_font(settings_bass_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font24);
    settings_bass_slider = lv_slider_create(settings_info_cont, NULL);
    lv_obj_set_size(settings_bass_slider, 150, 12);
    lv_obj_set_pos(settings_bass_slider,190,187-SETTINGS_HEAD_CONT_HEIGHT);
    lv_slider_set_range(settings_bass_slider, 0, 20);  // 0对应-10dB, 20对应+10dB
    lv_slider_set_value(settings_bass_slider, 10, LV_ANIM_OFF);  // 默认值0dB

    //低音
    lv_obj_t *settings_treble_label = lv_label_create(settings_info_cont, NULL);
    lv_obj_set_pos(settings_treble_label,45,220-SETTINGS_HEAD_CONT_HEIGHT);
    lv_label_set_text(settings_treble_label, "低音电位器:");
    lv_obj_set_style_local_text_font(settings_treble_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font24);
    settings_treble_slider = lv_slider_create(settings_info_cont, NULL);
    lv_slider_set_value(settings_treble_slider, g_lineVolume, LV_ANIM_OFF);
    lv_obj_set_size(settings_treble_slider, 150, 12);
    lv_obj_set_pos(settings_treble_slider,190,227-SETTINGS_HEAD_CONT_HEIGHT);
    lv_slider_set_range(settings_treble_slider, 0, 20);  // 0对应-10dB, 20对应+10dB
    lv_slider_set_value(settings_treble_slider, 10, LV_ANIM_OFF);  // 默认值0dB
    #endif


	lv_obj_set_style_local_bg_color(settings_brightness_slider, LV_SLIDER_PART_INDIC, LV_STATE_DEFAULT, get_theme_color());
	lv_obj_set_style_local_bg_color(settings_brightness_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, get_theme_color());
    lv_obj_set_style_local_bg_color(settings_micVolume_slider, LV_SLIDER_PART_INDIC, LV_STATE_DEFAULT, get_theme_color());
	lv_obj_set_style_local_bg_color(settings_micVolume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, get_theme_color());
	lv_obj_set_style_local_bg_color(settings_lineVolume_slider, LV_SLIDER_PART_INDIC, LV_STATE_DEFAULT, get_theme_color());
	lv_obj_set_style_local_bg_color(settings_lineVolume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, get_theme_color());
#if SUPPORT_BASS_TREBLE_CONTROL
    lv_obj_set_style_local_bg_color(settings_bass_slider, LV_SLIDER_PART_INDIC, LV_STATE_DEFAULT, get_theme_color());
	lv_obj_set_style_local_bg_color(settings_bass_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, get_theme_color());
    lv_obj_set_style_local_bg_color(settings_treble_slider, LV_SLIDER_PART_INDIC, LV_STATE_DEFAULT, get_theme_color());
	lv_obj_set_style_local_bg_color(settings_treble_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, get_theme_color());
#endif

#if !SUPPORT_BASS_TREBLE_CONTROL
    //版本号
    lv_obj_t *settings_version_label = lv_label_create(settings_info_cont, NULL);
    #if SUPPORT_BASS_TREBLE_CONTROL
    lv_obj_set_pos(settings_version_label,70,240-SETTINGS_HEAD_CONT_HEIGHT);
    #else
    lv_obj_set_pos(settings_version_label,70,200-SETTINGS_HEAD_CONT_HEIGHT);
    #endif
    lv_label_set_text_fmt(settings_version_label, "软件版本:  %s",LOCAL_FIRMWARE_VERSION);
    lv_obj_set_style_local_text_font(settings_version_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font24);
#endif

    settings_textArea_brightness = lv_textarea_create(settings_info_cont, NULL);
    lv_textarea_set_one_line(settings_textArea_brightness, true);
    lv_textarea_set_max_length(settings_textArea_brightness, 3);
    lv_obj_add_style(settings_textArea_brightness, LV_OBJ_PART_MAIN, &style_default);
    lv_obj_add_style(settings_textArea_brightness, LV_OBJ_PART_MAIN, &style_focused);
    lv_obj_add_style(settings_textArea_brightness, LV_OBJ_PART_MAIN, &style_edit);
    lv_textarea_set_cursor_hidden(settings_textArea_brightness, true);
    lv_textarea_set_text_align(settings_textArea_brightness, LV_LABEL_ALIGN_CENTER);
    lv_obj_align(settings_textArea_brightness, settings_brightness_slider, LV_ALIGN_OUT_RIGHT_MID,15,-2);
    lv_obj_set_size(settings_textArea_brightness, 60, 35);
    char strBrightness[10]={0};
    sprintf(strBrightness,"%d",g_backlight_level);
    lv_textarea_set_text(settings_textArea_brightness, strBrightness);


    settings_textArea_micVolume = lv_textarea_create(settings_info_cont, settings_textArea_brightness);
    lv_obj_align(settings_textArea_micVolume, settings_micVolume_slider, LV_ALIGN_OUT_RIGHT_MID,15,-2);
    lv_obj_set_size(settings_textArea_micVolume, 60, 35);
    char strMicVolume[10]={0};
    sprintf(strMicVolume,"%d",g_micVolume);
    lv_textarea_set_text(settings_textArea_micVolume, strMicVolume);

    settings_textArea_LineVolume = lv_textarea_create(settings_info_cont, settings_textArea_brightness);
    lv_obj_align(settings_textArea_LineVolume, settings_lineVolume_slider, LV_ALIGN_OUT_RIGHT_MID,15,-2);
    lv_obj_set_size(settings_textArea_LineVolume, 60, 35);
    char strLineVolume[10]={0};
    sprintf(strLineVolume,"%d",g_lineVolume);
    lv_textarea_set_text(settings_textArea_LineVolume, strLineVolume);

    #if SUPPORT_BASS_TREBLE_CONTROL
    settings_textArea_bass = lv_textarea_create(settings_info_cont, settings_textArea_brightness);
    lv_obj_align(settings_textArea_bass, settings_bass_slider, LV_ALIGN_OUT_RIGHT_MID,15,-2);
    lv_obj_set_size(settings_textArea_bass, 60, 35);
    char strBassVal[10]={0};
    sprintf(strBassVal,"%ddB",0);
    lv_textarea_set_text(settings_textArea_bass, strBassVal);

    settings_textArea_treble = lv_textarea_create(settings_info_cont, settings_textArea_brightness);
    lv_obj_align(settings_textArea_treble, settings_treble_slider, LV_ALIGN_OUT_RIGHT_MID,15,-2);
    lv_obj_set_size(settings_textArea_treble, 60, 35);
    char strTrebleVal[10]={0};
    sprintf(strTrebleVal,"%ddB",0);
    lv_textarea_set_text(settings_textArea_treble, strTrebleVal);
    #endif


    lv_obj_set_event_cb(settings_brightness_slider, info_slider_event_cb);
    lv_obj_set_event_cb(settings_micVolume_slider, info_slider_event_cb);
    lv_obj_set_event_cb(settings_lineVolume_slider, info_slider_event_cb);

    #if SUPPORT_BASS_TREBLE_CONTROL
    lv_obj_set_event_cb(settings_bass_slider, info_slider_event_cb);
    lv_obj_set_event_cb(settings_treble_slider, info_slider_event_cb);
    #endif
}


void yihui_settings_win_ip_page()
{
    settings_editMode = false;        // 编辑模式标志
    current_focus_ip = -1;

    current_settings_page = SETTINGS_PAGE_IP;

    lv_obj_set_hidden(settings_head_next_btn,true); //隐藏下翻页按钮

    lv_label_set_text(settings_head_title_label, "网络设置");

    //INFO
    settings_ip_cont = lv_obj_create(settings_all_cont, NULL);
    lv_obj_set_size(settings_ip_cont,LV_HOR_RES_MAX,LV_VER_RES_MAX-SETTINGS_HEAD_CONT_HEIGHT);
    lv_obj_set_pos(settings_ip_cont,0,SETTINGS_HEAD_CONT_HEIGHT+1);
    lv_obj_set_style_local_pad_all(settings_ip_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(settings_ip_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_radius(settings_ip_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_border_width(settings_ip_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);



    lv_obj_t* parent = settings_ip_cont;

    // IP地址部分
    lv_obj_t* ip_label = lv_label_create(parent, NULL);
    lv_label_set_text(ip_label, " IP地址:");
    lv_obj_align(ip_label, parent, LV_ALIGN_IN_TOP_LEFT, SETTINGS_IP_LABEL_POS_X, SETTINGS_IP_LABEL_POS_Y);
    lv_obj_add_style(ip_label, LV_CONT_PART_MAIN, &SETTINGS_IP_LABEL_STYLE_FONT);

    if(g_IP_Assign == IP_ASSIGN_STATIC)
    {
        split_ip(g_Static_ip_address,ip_parts);
    }
    else
    {
        split_ip(g_ipAddress,ip_parts);
    }
    split_ip(g_Subnet_Mask,mask_parts);
    split_ip(g_GateWay,gateway_parts);

    for(int i=0; i<4; i++) {
        ip_items[i] = lv_textarea_create(parent, NULL);
        lv_textarea_set_one_line(ip_items[i], true);
        lv_textarea_set_max_length(ip_items[i], 3);
        char NumBuffer[10] = {0};
        sprintf(NumBuffer, "%d", ip_parts[i]);
        lv_textarea_set_text(ip_items[i], NumBuffer);
        lv_obj_set_size(ip_items[i], SETTINGS_IP_TEXTAREA_WIDTH, SETTINGS_IP_TEXTAREA_HEIGHT);
        lv_obj_align(ip_items[i], parent, LV_ALIGN_IN_TOP_LEFT, SETTINGS_IP_TEXTAREA_POS_X + i*SETTINGS_IP_TEXTAREA_SPACING_X, SETTINGS_IP_TEXTAREA_POS_Y);
        lv_obj_add_style(ip_items[i], LV_OBJ_PART_MAIN, &style_default);
        lv_obj_add_style(ip_items[i], LV_OBJ_PART_MAIN, &style_focused);
        lv_obj_add_style(ip_items[i], LV_OBJ_PART_MAIN, &style_edit);
        lv_textarea_set_cursor_hidden(ip_items[i], true);

        lv_obj_add_style(ip_items[i], LV_CONT_PART_MAIN, &SETTINGS_IP_TEXTAREA_STYLE_FONT);

        lv_textarea_set_text_align(ip_items[i], LV_LABEL_ALIGN_CENTER);

        lv_obj_set_event_cb(ip_items[i], ip_item_event_cb);
    }

    // 子网掩码部分
    lv_obj_t* mask_label = lv_label_create(parent, NULL);
    lv_label_set_text(mask_label, "子网掩码:");
    lv_obj_align(mask_label, parent, LV_ALIGN_IN_TOP_LEFT, SETTINGS_IP_LABEL_POS_X, SETTINGS_IP_LABEL_POS_Y+SETTINGS_IP_LABEL_SPACING_Y);
    //lv_obj_set_style_local_text_color(mask_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 201, 154));
    lv_obj_add_style(mask_label, LV_CONT_PART_MAIN, &SETTINGS_IP_LABEL_STYLE_FONT);

    for(int i=0; i<4; i++) {
        mask_items[i] = lv_textarea_create(parent, NULL);
        lv_textarea_set_one_line(mask_items[i], true);
        lv_textarea_set_max_length(mask_items[i], 3);
        char NumBuffer[10] = {0};
        sprintf(NumBuffer, "%d", mask_parts[i]);
        lv_textarea_set_text(mask_items[i], NumBuffer);
        lv_obj_set_size(mask_items[i], SETTINGS_IP_TEXTAREA_WIDTH, SETTINGS_IP_TEXTAREA_HEIGHT);
        lv_obj_align(mask_items[i], parent, LV_ALIGN_IN_TOP_LEFT, SETTINGS_IP_TEXTAREA_POS_X + i*SETTINGS_IP_TEXTAREA_SPACING_X, SETTINGS_IP_TEXTAREA_POS_Y+SETTINGS_IP_TEXTAREA_SPACING_Y);
        lv_obj_add_style(mask_items[i], LV_OBJ_PART_MAIN, &style_default);
        lv_obj_add_style(mask_items[i], LV_OBJ_PART_MAIN, &style_focused);
        lv_obj_add_style(mask_items[i], LV_OBJ_PART_MAIN, &style_edit);
        lv_textarea_set_cursor_hidden(mask_items[i], true);

        lv_obj_add_style(mask_items[i], LV_CONT_PART_MAIN, &SETTINGS_IP_TEXTAREA_STYLE_FONT);

        lv_textarea_set_text_align(mask_items[i], LV_LABEL_ALIGN_CENTER);

        lv_obj_set_event_cb(mask_items[i], ip_item_event_cb);
    }

    // 网关部分
    lv_obj_t* gateway_label = lv_label_create(parent, NULL);
    lv_label_set_text(gateway_label, "默认网关:");
    lv_obj_align(gateway_label, parent, LV_ALIGN_IN_TOP_LEFT, SETTINGS_IP_LABEL_POS_X, SETTINGS_IP_LABEL_POS_Y+SETTINGS_IP_LABEL_SPACING_Y*2);
    //lv_obj_set_style_local_text_color(gateway_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 201, 154));
    lv_obj_add_style(gateway_label, LV_CONT_PART_MAIN, &SETTINGS_IP_LABEL_STYLE_FONT);

    for(int i=0; i<4; i++) {
        gateway_items[i] = lv_textarea_create(parent, NULL);
        lv_textarea_set_one_line(gateway_items[i], true);
        lv_textarea_set_max_length(gateway_items[i], 3);
        char NumBuffer[10] = {0};
        sprintf(NumBuffer, "%d", gateway_parts[i]);
        lv_textarea_set_text(gateway_items[i], NumBuffer);
        lv_obj_set_size(gateway_items[i], SETTINGS_IP_TEXTAREA_WIDTH, SETTINGS_IP_TEXTAREA_HEIGHT);
        lv_obj_align(gateway_items[i], parent, LV_ALIGN_IN_TOP_LEFT, SETTINGS_IP_TEXTAREA_POS_X + i*SETTINGS_IP_TEXTAREA_SPACING_X, SETTINGS_IP_TEXTAREA_POS_Y+SETTINGS_IP_TEXTAREA_SPACING_Y*2);
        lv_obj_add_style(gateway_items[i], LV_OBJ_PART_MAIN, &style_default);
        lv_obj_add_style(gateway_items[i], LV_OBJ_PART_MAIN, &style_focused);
        lv_obj_add_style(gateway_items[i], LV_OBJ_PART_MAIN, &style_edit);
        //lv_obj_add_style(gateway_items[i], LV_OBJ_PART_MAIN, &style_checked);
        lv_textarea_set_cursor_hidden(gateway_items[i], true);

        lv_obj_add_style(gateway_items[i], LV_CONT_PART_MAIN, &SETTINGS_IP_TEXTAREA_STYLE_FONT);

        lv_textarea_set_text_align(gateway_items[i], LV_LABEL_ALIGN_CENTER);

        lv_obj_set_event_cb(gateway_items[i], ip_item_event_cb);
    }

    //底部增加保存，退出按钮
    ipsettings_save_btn = lv_btn_create(parent, NULL);
    lv_obj_t* save_label = lv_label_create(ipsettings_save_btn, NULL);
    lv_label_set_text(save_label, "保存");

    lv_obj_set_style_local_text_color(save_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    //lv_obj_set_style_local_border_width(save_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    //lv_obj_set_style_local_bg_opa(save_label,LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_TRANSP);
    lv_obj_set_style_local_bg_color(ipsettings_save_btn, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());
    lv_obj_set_style_local_border_color(ipsettings_save_btn, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());
    
    lv_obj_set_style_local_text_color(ipsettings_save_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_COLOR_MAKE(255, 201, 154));
    lv_obj_set_style_local_bg_color(ipsettings_save_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_COLOR_MAKE(83, 53, 27));
    lv_obj_set_style_local_border_color(ipsettings_save_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_COLOR_MAKE(83, 53, 27));

    lv_obj_set_style_local_image_recolor_opa(ipsettings_save_btn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_OPA_COVER);
    lv_obj_set_style_local_image_recolor(ipsettings_save_btn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_COLOR_BLACK);


    lv_obj_add_style(ipsettings_save_btn, LV_CONT_PART_MAIN, &SETTINGS_IP_SAVE_BTN_STYLE_FONT);
    lv_obj_set_size(ipsettings_save_btn, SETTINGS_IP_SAVE_BTN_WIDTH, SETTINGS_IP_SAVE_BTN_HEIGHT);
    lv_obj_set_pos(ipsettings_save_btn, SETTINGS_IP_SAVE_BTN_POS_X, SETTINGS_IP_SAVE_BTN_POS_Y);



    lv_obj_set_event_cb(ipsettings_save_btn, ipSave_btn_event_cb);
}


void yihui_settings_win_start(void)
{
    if(control_all_cont)
    {
        lv_obj_set_hidden(control_all_cont,true);
    }
    textArea_style_init();

	settings_all_cont = lv_cont_create(lv_scr_act(), NULL);
    lv_obj_set_size(settings_all_cont,LV_HOR_RES_MAX,LV_VER_RES_MAX);
    lv_obj_set_pos(settings_all_cont,0,0);		//此处为了解决默认情况下屏幕留边的问题
    lv_obj_set_style_local_pad_all(settings_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(settings_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_radius(settings_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_border_width(settings_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

    lv_obj_t *settings_head_box = lv_obj_create(settings_all_cont, NULL);
	lv_obj_set_style_local_pad_all(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_radius(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_border_width(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	lv_obj_set_size(settings_head_box, LV_HOR_RES_MAX, SETTINGS_HEAD_CONT_HEIGHT);
	
	lv_obj_align(settings_head_box, settings_all_cont, LV_ALIGN_IN_TOP_MID,0,0);

	settings_head_title_label=lv_label_create(settings_head_box, NULL);
	lv_obj_align(settings_head_title_label, settings_head_box, LV_ALIGN_CENTER,-2,0);
	lv_label_set_align(settings_head_title_label, LV_LABEL_ALIGN_CENTER);
	
	lv_obj_set_auto_realign(settings_head_title_label,true);
	lv_obj_set_width(settings_head_title_label, lv_obj_get_width_grid(settings_head_box, 2, 1));
    
    #if(SELECTED_DISPLAY_TYPE==DISPLAY_TYPE_RGB_4P3_480X272)
    lv_obj_set_style_local_text_font(settings_head_title_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font24);
    #else
    lv_obj_set_style_local_text_font(settings_head_title_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font20);
    #endif

	lv_obj_set_style_local_bg_color(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());
	lv_obj_set_style_local_text_color(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
	lv_obj_set_style_local_radius(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	lv_obj_add_protect(settings_head_box, LV_PROTECT_CLICK_FOCUS);


    settings_head_pre_btn=lv_imgbtn_create(settings_head_box, NULL);
	lv_obj_align(settings_head_pre_btn, settings_head_box, LV_ALIGN_IN_LEFT_MID,20,6);
    lv_imgbtn_set_src(settings_head_pre_btn, LV_BTN_STATE_RELEASED, &pic_previous);
	lv_imgbtn_set_src(settings_head_pre_btn, LV_BTN_STATE_PRESSED, &pic_previous);
    lv_obj_set_style_local_image_recolor_opa(settings_head_pre_btn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_OPA_COVER);
    lv_obj_set_style_local_image_recolor(settings_head_pre_btn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_COLOR_BLACK);


    settings_head_next_btn=lv_imgbtn_create(settings_head_box, NULL);
	lv_obj_align(settings_head_next_btn, settings_head_box, LV_ALIGN_OUT_RIGHT_MID,-57,6);
    lv_imgbtn_set_src(settings_head_next_btn, LV_BTN_STATE_RELEASED, &pic_next);
	lv_imgbtn_set_src(settings_head_next_btn, LV_BTN_STATE_PRESSED, &pic_next);
    lv_obj_set_style_local_image_recolor_opa(settings_head_next_btn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_OPA_COVER);
    lv_obj_set_style_local_image_recolor(settings_head_next_btn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_COLOR_BLACK);

    lv_obj_set_event_cb(settings_head_pre_btn, prNextPage_btn_event_cb);
    lv_obj_set_event_cb(settings_head_next_btn, prNextPage_btn_event_cb);

    #if !IS_DEVICE_DECODER_TERMINAL
    lv_obj_set_hidden(settings_head_pre_btn,true);
    lv_obj_set_hidden(settings_head_next_btn,true);
    #endif
    
#if IS_DEVICE_FIRE_COLLECTOR
    yihui_settings_win_ip_page();
#else
    yihui_settings_win_info_page();
#endif
    SetCurrentWin(WIN_SETTING);

	screen_settings=lv_scr_act();
}



/* 更新所有项目的显示样式 */
static void ipsettings_update_highlight() {
    int section = current_focus_ip / 4;
    int index = current_focus_ip % 4;

    // 重置所有样式
    for(int i=0; i<4; i++) {
        lv_obj_set_state(ip_items[i],LV_STATE_DEFAULT);
        lv_obj_set_state(mask_items[i],LV_STATE_DEFAULT);
        lv_obj_set_state(gateway_items[i],LV_STATE_DEFAULT);
    }
    lv_obj_set_state(ipsettings_save_btn,LV_STATE_DEFAULT);

    printf("ipsettings_update_highlight:current_focus_ip=%d\n",current_focus_ip);
    if(current_focus_ip == -1)
        return;
    // 设置当前焦点样式
    lv_obj_t* target = NULL;
    if(current_focus_ip<12) {
        if(section == 0) target = ip_items[index];
        else if(section == 1) target = mask_items[index];
        else if(section == 2) target = gateway_items[index];
    }
    else if(current_focus_ip==12) target = ipsettings_save_btn;

    if(target) {
        if(settings_editMode) {
            //lv_obj_add_style(target, LV_OBJ_PART_MAIN, &style_edit);
            lv_obj_set_state(target,LV_STATE_EDITED);
        } else {
            //lv_obj_add_style(target, LV_OBJ_PART_MAIN, &style_focused);
            lv_obj_set_state(target,LV_STATE_FOCUSED);
        }
    }
}


static void msg_close_task(lv_task_t *param) {

	if(param->user_data)
	{
		lv_obj_del(param->user_data);
	}
	lv_task_del(param);
}

static void save_network_settings()
{
    // 保存IP地址、掩码、网关
    // 这里需要将ip_parts、mask_parts、gateway_parts转换为字符串并保存
    char ip_str[16] = {0};
    char mask_str[16] = {0};
    char gateway_str[16] = {0};
    sprintf(ip_str, "%d.%d.%d.%d", ip_parts[0], ip_parts[1], ip_parts[2], ip_parts[3]);
    sprintf(mask_str, "%d.%d.%d.%d", mask_parts[0], mask_parts[1], mask_parts[2], mask_parts[3]);
    sprintf(gateway_str, "%d.%d.%d.%d", gateway_parts[0], gateway_parts[1], gateway_parts[2], gateway_parts[3]);
    //判断是否合规
    bool isValidIP = true;
    if(!if_a_string_is_a_valid_ipv4_address(ip_str) || !isValidSubnetMask(mask_str) || !if_a_string_is_a_valid_ipv4_address(gateway_str))
    {
        isValidIP = false;
    }
    if(isGatewayByNetmask_Error(ip_str,mask_str,gateway_str))
    {
        isValidIP = false;
    }

    lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
    lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);

    if(!isValidIP)
    {
        //参数错误，弹出对话框提示错误
        lv_msgbox_set_text(msg,"参数错误,请检查网络配置");
        lv_task_create(msg_close_task, 2000, LV_TASK_PRIO_MID, msg);
        return;
    }
    else
    {
        lv_msgbox_set_text(msg,"设置成功,设备即将重启");
    }

    g_IP_Assign = IP_ASSIGN_STATIC;
    sprintf(g_Static_ip_address,"%s",ip_str);
    sprintf(g_Subnet_Mask,"%s",mask_str);
    sprintf(g_GateWay,"%s",gateway_str);

    //保存网络信息
    save_sysconf(INI_SETCION_NETWORK,NULL);	
    System_Reboot_DelayMs(500);
}

void yihui_settingsWin_rotary_event_change(int code, int value) {
    if(GetCurrentWin() != WIN_SETTING) return;

    if(code == REL_DIAL)
    {
        if(settings_editMode) {
            // 编辑模式：修改数值
            if(current_settings_page == SETTINGS_PAGE_IP)
            {
                int section = current_focus_ip / 4;
                int index = current_focus_ip % 4;
                uint8_t* parts = NULL;

                if(section == 0) parts = ip_parts;
                else if(section == 1) parts = mask_parts;
                else if(section == 2) parts = gateway_parts;

                if(parts) {
                    int new_val = (int)parts[index] + value;
                    new_val = new_val < 0 ? 0 : (new_val > 255 ? 255 : new_val);
                    parts[index] = new_val;

                    // 更新显示
                    char buf[4];
                    snprintf(buf, sizeof(buf), "%d", new_val);
                    lv_textarea_set_text(
                        (section == 0) ? ip_items[index] : 
                        (section == 1) ? mask_items[index] : gateway_items[index],
                        buf
                    );
                }
            }
            
        } else {
            if(current_settings_page == SETTINGS_PAGE_IP)
            {
                current_focus_ip += value;
                if(current_focus_ip < -1) current_focus_ip = 12;
                else if(current_focus_ip > 12) current_focus_ip = -1;
                ipsettings_update_highlight();
            }
        }
    }
    else if(code == KEY_ENTER) 
    {
        if(value == 1) 
        { // 仅处理按下事件
            if(current_settings_page == SETTINGS_PAGE_IP)
            {
                if(current_focus_ip>=0 && current_focus_ip < 12) {
                    // 保存按钮
                    // 保存IP地址、掩码、网关
                    // 这里需要将ip_parts、mask_parts、gateway_parts转换为字符串并保存
                    settings_editMode = !settings_editMode;
                    ipsettings_update_highlight();
                }
                else if(current_focus_ip == 12) {
                    // 保存按钮
                    // 这里需要将ip_parts、mask_parts、gateway_parts转换为字符串并保存
                    save_network_settings();
                }
            }
        }
    }
}


#endif