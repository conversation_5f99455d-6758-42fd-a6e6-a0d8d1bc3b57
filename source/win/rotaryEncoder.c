#include <stdbool.h>
#include <stdint.h>
#include <pthread.h>
#include <stdio.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/select.h>
#include <sys/stat.h>
#include "mi_gpio.h"
#include "sysconf.h"
#include "rotaryEncoder.h"

#include "aipu_mainWindow.h"
#include "aipu_settingsWin.h"


static void rotary_event_change(int code,int value)
{
    //printf("rotary_event_change:code=%d,value=%d\r\n",code,value);
    if(GetCurrentWin() == WIN_MAIN)
    {
        #if (AIPU_VERSION && SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_2P1_320X240)
        aipu_mainWin_rotary_event_change(code,value);
        #elif (YIHUI_VERSION)
        yihui_mainWin_rotary_event_change(code,value);
        #endif
    }
    else if(GetCurrentWin() == WIN_SETTING)
    {
        #if (AIPU_VERSION && SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_2P1_320X240)
        aipu_settings_rotary_event_change(code,value);
        #elif (YIHUI_VERSION)
        yihui_settingsWin_rotary_event_change(code,value);
        #endif
    }
}

static void* rotaryEncoder_event(void* arg) 
{
    struct input_event ev;
    sleep(1);

    int fd = -1;
    if(SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_RGB_4P3_480X272)
    {
        //触摸屏占用了event0
        fd = open("/dev/input/event1", O_RDONLY);
    }
    else
    {
        fd = open("/dev/input/event0", O_RDONLY);
    }

    if (fd == -1) {
        perror("open");
        return NULL;
    }

    fd_set readfds;
    struct timeval timeout;

    while (1) {
        FD_ZERO(&readfds); // 清空集合
        FD_SET(fd, &readfds); // 将文件描述符加入集合

        // 设置超时时间
        timeout.tv_sec = 1; // 1秒超时
        timeout.tv_usec = 0;

        int ret = select(fd + 1, &readfds, NULL, NULL, &timeout);
        if (ret == -1) {
            perror("select");
            close(fd);
            return NULL;
        } else if (ret == 0) {
            //printf("Timeout occurred! No data after 1 second.\n");
            continue;
        }

        // 检查文件描述符是否可读
        if (FD_ISSET(fd, &readfds)) {
            if (read(fd, &ev, sizeof(ev)) == sizeof(ev)) {
                bool isValidEvent = false;
                if (ev.type == EV_REL && ev.code == REL_DIAL) {
                    //printf("Rotation: %d\n", ev.value);
                    //反向输出
                    ev.value*=-1;
                    isValidEvent=true;
                } else if (ev.type == EV_KEY && ev.code == KEY_ENTER) {
                    //printf("KEY: %d\n", ev.value);
                    if(ev.value == 1)   //按下才响应事件
                    {
                        isValidEvent=true;
                    }
                }
                if(isValidEvent)
                {
                    pthread_mutex_lock(&lvglMutex);
                    rotary_event_change(ev.code,ev.value);
                    pthread_mutex_unlock(&lvglMutex);
                }
            } else {
                perror("read");
            }
        }
    }

    close(fd);

    return NULL;
}

// 创建线程
void rotaryEncoder_start(void) {

    #if USE_PC_SIMULATOR
    return;
    #endif
    
    //首先判断是否存在rotary_encoder.ko,存在则加载驱动
    if (IsFileExist("/customer/App/Modules/rotary_encoder.ko")) {
        printf("rotaryEncoder_start...\n");
        rmmod_module("rotary_encoder",0);
        #if(SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_RGB_4P3_480X272 || SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_1P9_320X170)
        system("insmod /customer/App/Modules/rotary_encoder.ko rotary_gpio_a=40 rotary_gpio_b=39 button_gpio_pin=41");
        #else
        system("insmod /customer/App/Modules/rotary_encoder.ko rotary_gpio_a=51 rotary_gpio_b=52 button_gpio_pin=45");
        #endif
    }
    else{
        printf("rotaryEncoder modules not found!\n");
        return;
    }

    pthread_t thread_id;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);

    if (pthread_create(&thread_id, &attr, rotaryEncoder_event, NULL) != 0) {
        printf("Failed to create Ec11_inputEvent_thread!\n");
    } else {
        printf("EC11 inputEvent thread created successfully.\n");
    }

    pthread_attr_destroy(&attr);
}
