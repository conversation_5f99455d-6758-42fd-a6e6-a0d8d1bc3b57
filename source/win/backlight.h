#ifndef BACKLIGHT_H_
#define BACKLIGHT_H_

/*********************************************************************
 * DEFINITION
 */
#define MODE_PWM                 1
#define MODE_COUNTER             2
#define MODE_CAPTURE             3
#define MODE_CAPTURE_WITH_DT     4

/*********************************************************************
 * LOCAL FUNCTION
 */

/*********************************************************************
 * GLOBAL FUNCTION
 */
int init_backlight_module(void);
int backlight_adjust(int level);


#define MAX_BRIGHT_LEVEL   100
#define DEFAULT_BRIGHT_LEVEL 80

extern int g_backlight_level;

#endif /*BACKLIGHT_H_*/

