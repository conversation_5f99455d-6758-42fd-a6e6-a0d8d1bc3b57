/*
 * stack.h
 *
 *  Created on: Aug 27, 2020
 *      Author: king
 */

#ifndef NETPAGER_WIN_DOUBLELINK_H_
#define NETPAGER_WIN_DOUBLELINK_H_


typedef int ElemType;
typedef struct Node {
  ElemType data;
  struct Node *prio;
  struct Node *next;
}Node,*PNode;
typedef struct DoubleLink {
  PNode first;
  PNode last;
  size_t size;
}DoubleLink;
void Win_Dlist_Init(DoubleLink *list);//初始化双链表
void Win_Dlist_push_back(DoubleLink *list, ElemType x);//在双链表的末尾插入元素
void Win_Dlist_push_front(DoubleLink *list, ElemType x);//在双链表的头部插入元素
void Win_Dlist_show_list(DoubleLink *list);//打印双链表
void Win_Dlist_pop_back(DoubleLink *list);//删除双链表的最后一个元素
void Win_Dlist_pop_front(DoubleLink *list);//删除双链表的第一个元素
void Win_Dlist_insert_val(DoubleLink *list, ElemType val);//将数据元素插入到双链表中（要求此时双链表中的数据元素顺序排列）
Node* Win_Dlist_find(DoubleLink *list, ElemType x);//查找双链表中数据值为x的结点
int Win_Dlist_length(DoubleLink *list);//求双链表的长度
void Win_Dlist_delete_val(DoubleLink *list, ElemType x);//按值删除双链表中的某个数据元素
void Win_Dlist_sort(DoubleLink *list);//对双链表进行排序
static void Win_Dlist_clear(DoubleLink *list);//清除双链表
void Win_Dlist_destroy(DoubleLink *list);//摧毁双链表

static Node* Dlist_createnode(ElemType x);//创建结点

#endif /* NETPAGER_WIN_STACK_H_ */
