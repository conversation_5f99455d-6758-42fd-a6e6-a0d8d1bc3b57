/*
 * cheme.c
 *
 *  Created on: Sep 22, 2020
 *      Author: king
 */

#include "theme.h"
#include "sysconf.h"

st_control_button_list control_button_list[MAX_CONTROL_NUM];

lv_color_t get_theme_color()
{
	#if (APP_THEME_COLOR == APP_AISP_GENERAL)
	return LV_COLOR_MAKE(234,81,25);
	#elif (APP_THEME_COLOR == APP_JUSBE_GENERAL)
	return LV_COLOR_MAKE(0xba,0x51,0xd0);
	#elif (APP_THEME_COLOR == APP_AIPU_GENERAL)
	return LV_COLOR_MAKE(255, 201, 154);
	#else
	return LV_COLOR_MAKE(76,175,80);
	#endif
}


void init_control_button()
{
	control_button_list[CONTROL_BUTTON_UDISK].pic = pic_udisk;
	control_button_list[CONTROL_BUTTON_STOP].pic = pic_stop;
	control_button_list[CONTROL_BUTTON_SETTINGS].pic = pic_settings;
	
	sprintf(control_button_list[CONTROL_BUTTON_UDISK].name,"USB");
	sprintf(control_button_list[CONTROL_BUTTON_STOP].name,"停止");
	sprintf(control_button_list[CONTROL_BUTTON_SETTINGS].name,"设置");
}