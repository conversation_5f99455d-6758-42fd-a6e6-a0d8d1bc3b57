/*
 * win.c
 *
 *  Created on: Aug 27, 2020
 *      Author: king
 */

#include <stdio.h>
#include <malloc.h>
#include "const.h"
#include "pthread.h"
#include "win.h"

static pthread_mutex_t winChangeMutex=PTHREAD_MUTEX_INITIALIZER;

DoubleLink win_stack;
static char IsWin_init=0;

void InitWinStack()
{
	Win_Dlist_Init(&win_stack);
	IsWin_init=1;
}

int GetCurrentWin()
{
	if(!IsWin_init)
		return -1;
	pthread_mutex_lock(&winChangeMutex);
	int win=win_stack.last->data;
	//printf("current_win=%d\n",win);
	pthread_mutex_unlock(&winChangeMutex);
	return win;

}

void SetCurrentWin(int win)
{
	if(!IsWin_init)
		return;
	pthread_mutex_lock(&winChangeMutex);
	Win_Dlist_push_back(&win_stack,win);
	pthread_mutex_unlock(&winChangeMutex);
}

void DeleteWin(int win)
{
	if(!IsWin_init)
		return;
	pthread_mutex_lock(&winChangeMutex);
	Win_Dlist_delete_val(&win_stack,win);
	pthread_mutex_unlock(&winChangeMutex);
}

int IsValidWin(int win)
{
	if(!IsWin_init)
		return 0;
	if(Win_Dlist_find(&win_stack,win))
		return 1;
	else
		return 0;
}
