#include "sysconf.h"
#include "rotaryEncoder.h"
#include "aipu_mainWindow.h"
#include "aipu_settingsWin.h"

#if ( AIPU_VERSION || USE_PC_SIMULATOR)

lv_obj_t *screen_main;				//控制界面screen

lv_point_t line_points[3][2];
lv_obj_t *label_deviceName,*label_deviceIP,*label_deviceSource,*label_deviceVolume;
lv_obj_t *label_remote_key;
lv_obj_t *label_timeStatus;
lv_obj_t *label_timeDisplay;
lv_obj_t *label_ac_channelStatus[4];


#if 0
// 定时器回调函数
static void timer_callback(lv_task_t * task)
{
    // 销毁当前LOGO页面
    lv_obj_clean(lv_scr_act());
    int current_win=GetCurrentWin();
    DeleteWin(current_win);
    // 启动另一个页面
    lv_demo_benchmark();

    // 删除任务，确保只执行一次
    lv_task_del(task);
}
#endif


/**
 * @brief 创建透明可点击区域
 * @param parent 父对象 (传 NULL 时使用默认屏幕)
 * @param x 水平坐标
 * @param y 垂直坐标
 * @param width 宽度
 * @param height 高度
 * @param event_cb 点击事件回调函数
 * @return 创建的透明容器对象
 */
static lv_obj_t* create_invisible_button(lv_obj_t* parent, lv_coord_t x, lv_coord_t y, 
                                 lv_coord_t width, lv_coord_t height, 
                                 lv_event_cb_t event_cb) 
{
    /* 创建容器 */
    lv_obj_t* container = lv_cont_create(parent ? parent : lv_scr_act(), NULL);
    
    /* 禁用自动布局 */
    lv_cont_set_layout(container, LV_LAYOUT_OFF);
    
    /* 启用点击检测 */
    lv_obj_set_click(container, true);

    /* 设置透明样式 */
    lv_obj_set_style_local_bg_opa(container, LV_CONT_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_TRANSP);
    lv_obj_set_style_local_border_opa(container, LV_CONT_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_TRANSP);

    /* 设置位置和尺寸 */
    lv_obj_set_pos(container, x, y);
    lv_obj_set_size(container, width, height);

    /* 绑定事件回调 */
    lv_obj_set_event_cb(container, event_cb);

    return container;
}


static lv_obj_t* create_centered_label(lv_obj_t *parent, lv_style_t * style, int x_offset, int y_offset) {
    // 创建容器对象
    lv_obj_t *container = lv_cont_create(parent, NULL);
    lv_obj_set_size(container, lv_obj_get_width(parent), 40); // 设置容器大小
    // 设置容器为透明背景，并去掉边框
    lv_obj_set_style_local_bg_color(container, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_BLACK); // 设置背景色为黑色
    lv_obj_set_style_local_bg_opa(container, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_TRANSP); // 设置背景透明
    lv_obj_set_style_local_border_width(container, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0); // 去掉边框
    
    // 创建 label 并将其放入容器
    lv_obj_t *label = lv_label_create(container, NULL);
    //lv_obj_set_style_local_text_color(label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    lv_obj_set_style_local_text_color(label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 201, 154));
    lv_label_set_align(label, LV_LABEL_ALIGN_CENTER); // 设置文本居中
    lv_obj_set_auto_realign(label,true);
    //lv_label_set_long_mode(label,LV_LABEL_LONG_SROLL_CIRC);
    lv_obj_add_style(label, LV_CONT_PART_MAIN, style);

    // 将 label 居中对齐到容器中
    lv_obj_align(label, container, LV_ALIGN_CENTER, 0, 0); 

    // 设置容器的位置
    lv_obj_align(container, parent, LV_ALIGN_IN_TOP_LEFT, x_offset, y_offset);
    return label;
}

static void create_separ_line(lv_obj_t *parent, lv_point_t *linePoints) {
    lv_obj_t *line = lv_line_create(parent, NULL);  // 创建一个线对象
    // 使用静态数组，确保它的生命周期在函数结束后仍然有效
    lv_line_set_points(line, linePoints, 2);  // 设置线的端点

    lv_obj_set_style_local_line_width(line, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 3);  // 为线对象添加样式
    lv_obj_set_style_local_line_color(line, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(55, 39, 28));  // 为线对象添加样式
}


static void aipu_main_btn_event_cb(lv_obj_t * obj, lv_event_t e)
{
    if(e == LV_EVENT_CLICKED)
    {
        aipu_settings_win_start();
    }
}

void aipu_main_win_start(void)
{
    printf("aipu_main_win_start...\n");
    lv_obj_t *backGroundImgObj = lv_img_create(lv_scr_act(), NULL);
  
    lv_img_set_src(backGroundImgObj, &background_aipu);

    //lv_obj_add_style(backGroundImgObj, LV_CONT_PART_MAIN, &style_font_32);

    line_points[0][0].x = 30;
    line_points[0][0].y = 75;
    line_points[0][1].x = LV_HOR_RES-30;
    line_points[0][1].y = 75;

    line_points[1][0].x = 30;
    line_points[1][0].y = 125;
    line_points[1][1].x = LV_HOR_RES-30;
    line_points[1][1].y = 125;

    line_points[2][0].x = 30;
    line_points[2][0].y = 175;
    line_points[2][1].x = LV_HOR_RES-30;
    line_points[2][1].y = 175;

    label_deviceName = create_centered_label(backGroundImgObj, &style_font_32, 0, 30);
    label_deviceIP = create_centered_label(backGroundImgObj, &style_font_32, 0, 80);
    #if IS_DEVICE_DECODER_TERMINAL
    label_deviceSource = create_centered_label(backGroundImgObj, &style_font_28, 0, 130);
    label_deviceVolume = create_centered_label(backGroundImgObj, &style_font_32, 0, 184);

    create_separ_line(backGroundImgObj,line_points[0]);
    create_separ_line(backGroundImgObj,line_points[1]);
    create_separ_line(backGroundImgObj,line_points[2]);
    #endif

    #if IS_DEVICE_REMOTE_CONTROLER
    label_deviceSource = create_centered_label(backGroundImgObj, &style_font_28, 0, 130);
    label_remote_key = create_centered_label(backGroundImgObj, &style_font_32, 0, 184);
    create_separ_line(backGroundImgObj,line_points[0]);
    create_separ_line(backGroundImgObj,line_points[1]);
    create_separ_line(backGroundImgObj,line_points[2]);
    #endif

    #if IS_DEVICE_GPS_SYNCHRONIZER
    label_timeStatus = create_centered_label(backGroundImgObj, &style_font_24, 0, 140);
    label_timeDisplay = create_centered_label(backGroundImgObj, &style_font_24, 0, 184);
    #endif

    #if IS_DEVICE_AUDIO_COLLECTOR || IS_DEVICE_AUDIO_MIXER

    for(int i=0;i<4;i++)
    {
        int x,y;
        if(i==0)
        {
            x=30;
            y=145;
        }
        else if(i==1)
        {
            x=185;
            y=145;
        }
        else if(i==2)
        {
            x=30;
            y=190;
        }
        else if(i==3)
        {
            x=185;
            y=190;
        }
        label_ac_channelStatus[i] = lv_label_create(backGroundImgObj, NULL);
        #if IS_DEVICE_AUDIO_COLLECTOR
        lv_label_set_text_fmt(label_ac_channelStatus[i], "CH%d:%s",i+1,(audioCollector_info.m_nSelectedStatus & g_device_collector_channel_map[i])?"开启":"关闭");
        #elif IS_DEVICE_AUDIO_MIXER
        char channelName[64]={0};
        if(i == 0)
        {
            snprintf(channelName,sizeof(channelName),"%s","SW");
            lv_label_set_text_fmt(label_ac_channelStatus[i], "%s:%s",channelName,(audioMixer_info.m_nMasterSwitch)?"开启":"关闭");
        }
        else if( i == 1)
        {
            snprintf(channelName,sizeof(channelName),"%s","NET");
            lv_label_set_text_fmt(label_ac_channelStatus[i], "%s:%s",channelName,(audioMixer_info.m_nSignalValid[0])?"开启":"关闭");
        }
        else if( i == 2)
        {
            snprintf(channelName,sizeof(channelName),"%s","MIC");
            lv_label_set_text_fmt(label_ac_channelStatus[i], "%s:%s",channelName,(audioMixer_info.m_nSignalValid[1])?"开启":"关闭");
        }
        else if( i == 3)
        {
            snprintf(channelName,sizeof(channelName),"%s","AUX");
            lv_label_set_text_fmt(label_ac_channelStatus[i], "%s:%s",channelName,(audioMixer_info.m_nSignalValid[2])?"开启":"关闭");
        }
        #endif
        lv_obj_set_pos(label_ac_channelStatus[i], x, y);
        lv_obj_set_style_local_text_color(label_ac_channelStatus[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 201, 154));
        lv_label_set_align(label_ac_channelStatus[i], LV_LABEL_ALIGN_LEFT); // 设置文本居中
        lv_obj_add_style(label_ac_channelStatus[i], LV_CONT_PART_MAIN, &style_font_28);
    }

    create_separ_line(backGroundImgObj,line_points[0]);
    create_separ_line(backGroundImgObj,line_points[1]);
    #endif

    // 创建一个透明的可点击区域,用于i386模拟进去设置界面
    lv_obj_t* invisible_enter_settings_btn = create_invisible_button(
        NULL,
        280, 200, 
        40, 40, 
        aipu_main_btn_event_cb
    );


    SetCurrentWin(WIN_MAIN);

    screen_main=lv_scr_act();

    aipu_main_win_deviceInfo_update(1);


   // 创建一个定时任务，3秒后执行回调函数
   //lv_task_t * task = lv_task_create(timer_callback, 3000, LV_TASK_PRIO_MID, NULL);
}




void aipu_main_win_deviceInfo_update(int IsSelfcall)
{
    if(!isDisplayValid())
    {
        return;
    }
	if(GetCurrentWin() != WIN_MAIN)
			return;
	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
	}
    if(strlen(g_ipAddress) == 0 && g_IP_Assign == IP_ASSIGN_DHCP)
    {
        lv_label_set_text_fmt(label_deviceName,"%s",strlen(g_device_alias) == 0?"127.0.0.1":(const char*)g_device_alias);
        if(eth_link_status == 1)
        {
            lv_label_set_text_fmt(label_deviceIP,"%s","IP地址获取中");
        }
        else
        {
            int temp_link_status=get_netlink_status("eth0");
            if(temp_link_status == 0)
            {
                lv_label_set_text_fmt(label_deviceIP,"%s","未插入网线");
            }
            else
            {
                lv_label_set_text_fmt(label_deviceIP,"%s","IP地址获取中");
            }
        }
    }
    else
    {
        if(g_IP_Assign == IP_ASSIGN_STATIC)
        {
            lv_label_set_text_fmt(label_deviceName,"%s",strlen(g_device_alias) == 0?g_Static_ip_address:(const char*)g_device_alias);
            lv_label_set_text_fmt(label_deviceIP,"%s",g_Static_ip_address);
        }
        else {
            lv_label_set_text_fmt(label_deviceName,"%s",strlen(g_device_alias) == 0?g_ipAddress:(const char*)g_device_alias);
            lv_label_set_text_fmt(label_deviceIP,"%s",g_ipAddress);
        }
    }
    
    if(label_deviceSource)
    {
        if(g_media_source == SOURCE_LOCAL_PLAY || g_media_source == SOURCE_TIMING)
        {
            lv_label_set_text_fmt(label_deviceSource,"%s:%s",Get_Source_Name_ById(g_media_source),Check_FileName(g_media_name,16));
        }
        else
        {
            if(!IS_SERVER_CONNECTED && g_media_source == SOURCE_NULL)
            {
                lv_label_set_text_fmt(label_deviceSource,"%s","离线");
            }
            else
            {
                lv_label_set_text_fmt(label_deviceSource,"%s",Get_Source_Name_ById(g_media_source));
            }
        }
    }

    #if IS_DEVICE_DECODER_TERMINAL
    if(label_deviceVolume)
    {
        lv_label_set_text_fmt(label_deviceVolume,"音量:%d",dispGetVolume());
    }
    #endif

    #if IS_DEVICE_REMOTE_CONTROLER
    if(label_remote_key)
    {
        if(g_remote_controler_keyCode)
        {
            lv_label_set_text_fmt(label_remote_key,"按键:%02d",g_remote_controler_keyCode);
        }
        else
        {
            lv_label_set_text_fmt(label_remote_key,"按键:%s","无");
        }
    }
    #endif

    #if IS_DEVICE_AUDIO_MIXER
    aipu_main_win_audioMixer_update(1);
    #endif

    if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
	}
}

#if IS_DEVICE_GPS_SYNCHRONIZER
void aipu_main_win_gps_time_update(int IsSelfcall)
{
    if(!isDisplayValid())
    {
        return;
    }
	if(GetCurrentWin() != WIN_MAIN)
			return;
	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
	}

    if(label_timeStatus)
    {
        if(g_gps_info.data_valid)
        {
            lv_label_set_text_fmt(label_timeStatus,"%s","授时状态:有效");
        }
        else
        {
            //开启重色，才能支持文本中附带颜色值
            lv_label_set_recolor(label_timeStatus, true);
            lv_label_set_text_fmt(label_timeStatus,"%s","授时状态:#ff0000 无效#");
        }
    }

    if(label_timeDisplay)
    {
        //更新时间st_CurrentTime
        lv_label_set_text_fmt(label_timeDisplay,"%04d-%02d-%02d %02d:%02d:%02d",st_CurrentTime.year,st_CurrentTime.mon,st_CurrentTime.day,st_CurrentTime.hour,st_CurrentTime.min,st_CurrentTime.sec);
    }

    if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
	}
}
#endif


#if IS_DEVICE_AUDIO_COLLECTOR
void aipu_main_win_audioCollectorStatus_update(int IsSelfcall)
{
    if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
	}
    for(int i=0;i<4;i++)
    {
        lv_label_set_text_fmt(label_ac_channelStatus[i], "CH%d:%s",i+1,(audioCollector_info.m_nSelectedStatus & g_device_collector_channel_map[i])?"开启":"关闭");
        int signal_led_pin = PAD_KEY5;
        switch(i)
        {
            case 0:
                signal_led_pin = PAD_KEY5;
            break;
            case 1:
                signal_led_pin = PAD_KEY7;
            break;
            case 2:
                signal_led_pin = PAD_KEY8;
            break;
            case 3:
                signal_led_pin = PAD_KEY9;
        }
        if(audioCollector_info.m_nSelectedStatus & g_device_collector_channel_map[i])
        {
            Set_Gpio_High(signal_led_pin);
        }
        else
        {
            Set_Gpio_Low(signal_led_pin);
        }
    }
    if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
	}
}
#endif

#if IS_DEVICE_AUDIO_MIXER
void aipu_main_win_audioMixer_update(int IsSelfcall)
{
    printf("aipu_main_win_audioMixer_update1\n");
    if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
	}
    for(int i=0;i<4;i++)
    {
        char channelName[64]={0};
        if(i == 0)
        {
            snprintf(channelName,sizeof(channelName),"%s","SW");
            lv_label_set_text_fmt(label_ac_channelStatus[i], "%s:%s",channelName,(audioMixer_info.m_nMasterSwitch)?"开启":"关闭");
        }
        else if( i == 1)
        {
            snprintf(channelName,sizeof(channelName),"%s","NET");
            lv_label_set_text_fmt(label_ac_channelStatus[i], "%s:%s",channelName,(audioMixer_info.m_nSignalValid[0])?"开启":"关闭");
        }
        else if( i == 2)
        {
            snprintf(channelName,sizeof(channelName),"%s","MIC");
            lv_label_set_text_fmt(label_ac_channelStatus[i], "%s:%s",channelName,(audioMixer_info.m_nSignalValid[1])?"开启":"关闭");
        }
        else if( i == 3)
        {
            snprintf(channelName,sizeof(channelName),"%s","AUX");
            lv_label_set_text_fmt(label_ac_channelStatus[i], "%s:%s",channelName,(audioMixer_info.m_nSignalValid[2])?"开启":"关闭");
        }
    }
    printf("aipu_main_win_audioMixer_update2\n");
    if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
	}
}
#endif

static void rotary_MainWindow_change_subVolume(int dir)
{
    if(GetCurrentWin() != WIN_MAIN)
        return;

    dispSetvolume_By_add_min(dir == ROTARY_DIR_RIGHT);
    aipu_main_win_deviceInfo_update(1);
}


void aipu_mainWin_rotary_event_change(int code, int value) {
    if(GetCurrentWin() != WIN_MAIN) return;

    if(code == REL_DIAL) 
    {
        #if IS_DEVICE_DECODER_TERMINAL
        rotary_MainWindow_change_subVolume(value);
        #endif
    }
    else if(code == KEY_ENTER) 
    {
        aipu_settings_win_start();
    }
}


#endif