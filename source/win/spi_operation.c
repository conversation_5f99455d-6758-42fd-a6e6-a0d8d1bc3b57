#include "spi_operation.h"

#define SPI_DEBUG 0 

static const char *device = "/dev/spidev1.0";
static uint8_t mode = 0; /* SPI通信使用全双工，设置CPOL＝0，CPHA＝0。 */
static uint8_t bits = 8; /* ８ｂiｔｓ读写，MSB first。*/
static uint32_t speed = 25*1000*1000;/* 设置30M传输速度 */
static uint16_t delay = 0;
static int g_SPI_Fd = 0;



int SPI_Write(uint8_t *TxBuf, int len)
{
    int ret;
    int fd = g_SPI_Fd;

    ret = write(fd, TxBuf, len);
    if (ret < 0)
        printf("SPI Write error,len=%d\n",len);
    else
    {
        #if SPI_DEBUG
                int i;
                printf("SPI Write [Len:%d]: \n", len);
                for (i = 0; i < len; i++)
                {
                    if (i % 8 == 0)
                        printf("\n\t");
                    printf("0x%02X \n", TxBuf[i]);
                }
                printf("\n");
        #endif
    }

    return ret;
}

#if 0
int SPI_Write_DMA(uint8_t *TxBuf, int len)
{
    int ret;
    int fd = g_SPI_Fd;

    /* 定义 SPI 数据传输结构体 */
    struct spi_ioc_transfer xfer = {
        .tx_buf = (unsigned long)TxBuf,  // 发送缓冲区地址
        .rx_buf = NULL,                    // 不需要接收数据
        .len = len,                     // 数据长度
        .speed_hz = 30000000,           // 传输速度，视硬件支持而定
        .delay_usecs = 0,               // 传输延迟
        .bits_per_word = 8,             // 每字的位数
        .cs_change = 0,                 // 传输后是否保持 CS 低电平
    };

    /* 使用 SPI_IOC_MESSAGE 触发传输 */
    ret = ioctl(fd, SPI_IOC_MESSAGE(1), &xfer);
    if (ret < 0) {
        printf("SPI IOC error, len=%d\n", len);
    } else {
        #if SPI_DEBUG
        int i;
        printf("SPI Write [Len:%d]: \n", len);
        for (i = 0; i < len; i++) {
            if (i % 8 == 0)
                printf("\n\t");
            printf("0x%02X ", TxBuf[i]);
        }
        printf("\n");
        #endif
    }

    return ret;
}
#endif


/**
 * 功 能：打开设备 并初始化设备
 * 入口参数 ：
 * 出口参数：
 * 返回值：0 表示已打开 0XF1 表示SPI已打开 其它出错
 * 开发人员：Lzy 2013－5－22
 */
int SPI_Open(void)
{
    int fd;
    int ret = 0;

    if (g_SPI_Fd >0) /* 设备已打开 */
        return ret;

    fd = open(device, O_WRONLY);
    if (fd < 0)
    {
        printf("can't open device\n");
        goto quit;
    }
    else
    {
        printf("SPI - Open Succeed. Start Init SPI...\n");
    }

    g_SPI_Fd = fd;
    /*
     * spi mode
     */
    ret = ioctl(fd, SPI_IOC_WR_MODE, &mode);
    if (ret == -1)
    {
        printf("can't set spi mode\n");
        goto quit;
    }
    /*
     * bits per word
     */
    ret = ioctl(fd, SPI_IOC_WR_BITS_PER_WORD, &bits);
    if (ret == -1)
    {
        printf("can't set bits per word\n");
        goto quit;
    }
    /*
     * max speed hz
     */
    ret = ioctl(fd, SPI_IOC_WR_MAX_SPEED_HZ, &speed);
    if (ret == -1)
    {
        printf("can't set max speed hz\n");
        goto quit;
    }

quit:
    return ret;
}


/**
 * 功 能：关闭SPI模块
 */
int SPI_Close(void)
{
    int fd = g_SPI_Fd;

    if (fd <=0) /* SPI是否已经打开*/
        return 0;
    close(fd);
    g_SPI_Fd = 0;

    return 0;
}


int SPI_Init()
{
    int ret=SPI_Open();
    return ret;
}
