/*
 * win.h
 *
 *  Created on: Aug 27, 2020
 *      Author: king
 */

#ifndef NETPAGER_WIN_WIN_H_
#define NETPAGER_WIN_WIN_H_
#include "lvgl/lvgl.h"
#include "doubleLink.h"
#include "pthread.h"


enum {
	WIN_MAIN   =  0x01,
	WIN_SETTING =  0x02,
	WIN_UDISK =  0x03,
};


void InitWinStack();
int GetCurrentWin();
void SetCurrentWin(int win);
void DeleteWin(int win);
int IsValidWin(int win);


#endif /* NETPAGER_WIN_WIN_H_ */
