#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include <fcntl.h>
#include <sys/ioctl.h>

#include "const.h"
#include "backlight.h"

int g_close_lcd_flag=0;

struct mcpwm_config
{
   int chan;
   unsigned long lim[3];
   unsigned long mat[3];
   unsigned long dt[3];
};


int g_backlight_level=DEFAULT_BRIGHT_LEVEL;

/*********************************************************************
 * GLOBAL VARIABLE
 */
struct mcpwm_config reg_mcpwm;
int g_pwm_fd=-1;


#define LCD_PWM_DEV_ID	0
#define LCD_PWM_PERIOD	200000		// Freq-1Khz:1000000000/1000Hz = 1000000；Freq-20Khz:1000000000/20000Hz = 50000
int control_sstar_backlight(int duty_cycle_percentage);

/*********************************************************************
 * @fn      init_backlight_module
 *
 * @brief   初始化背光调节模块
 *
 * @param   void
 *
 * @return  ERROR - 失败
 *          SUCCEED - 成功
 */
int init_backlight_module(void)
{
	printf("Init backlight module succeed,g_backlight_level=%d!\n",g_backlight_level);
	backlight_adjust(g_backlight_level);
	return 0;
}



/*********************************************************************
 * @fn      backlight_adjust
 *
 * @brief   背光调节，级数：0~11,0为关闭
 *
 * @param   level - 级数
 *
 * @return  none
 */
int backlight_adjust(int level)
{
	#if defined(USE_PC_SIMULATOR)
	return;
	#endif

#if 0
	int percentage;
	if (level <= 0) {
    	percentage = 15;    // 处理小于0的情况
	} else if (level >= 100) {
		percentage = 50;    // 处理超过100的情况
	} else {
		// 线性映射并四舍五入
		percentage = 15 + (int)((level * 35.0 / 100.0) + 0.5);
	}
#endif
	if(level <=5)
		level=5;
	
	int percentage = 15 + (int)((level * 35.0 / 100.0) + 0.5);
	
	control_sstar_backlight(percentage);

	return 0;
}




/*********************************************************************
 * @fn      Contrl_LCDBacklight
 *
 * @brief   开关LCD背光
 *
 * @param	int u_data:  1 - 高电平
 * 						 0 - 低电平
 *
 * @return  成功	- SUCCEED
 * 			失败	-	ERROR
 */
int Contrl_LCDBacklight(int val)
{
	int ret;

	if(val == 0 && g_close_lcd_flag == 0)
	{
		backlight_adjust(0);
		g_close_lcd_flag=1;
	}
	else if(val == 1 && g_close_lcd_flag == 1)
	{
		backlight_adjust(g_backlight_level);
		g_close_lcd_flag=0;
	}
	return 0;
}


#define PWM_DEV			"/sys/class/pwm/pwmchip0"
#define PWM_PERIOD		"period"
#define PWM_DUTYCYCLE	"duty_cycle"
#define PWM_POLARITY	"polarity"
#define PWM_ENABLE		"enable"
#define PWM_NORMAL		"normal"
#define PWM_INVERSED	"inversed"

// export pwm port
int ExportPwm(int nPwm)
{
	int fd = -1;
	char pwmExport[256];
	char pwmDev[256];
	
	memset(pwmDev, 0, sizeof(pwmDev));
	sprintf(pwmDev, "%s/pwm%d", PWM_DEV, nPwm);
	if (!access(pwmDev, R_OK))
	{
		//printf("pwm%d already exists\n", nPwm);
		return 0;
	}
	
	memset(pwmExport, 0, sizeof(pwmExport));
	sprintf(pwmExport, "%s/export", PWM_DEV);
	fd = open(pwmExport, O_WRONLY);
	
	if (fd < 0)
	{
		printf("failed to export pwm%d\n", nPwm);
		return -1;
	}
	else
	{
		char gpioPort[10];
		memset(gpioPort, 0, sizeof(gpioPort));
		sprintf(gpioPort, "%d", nPwm);
		write(fd, gpioPort, strlen(gpioPort));
		//printf("export gpio port: %d\n", nPwm);
		close(fd);
		return 0;
	}
}

// set pwm period
int SetPwmPeriod(int nPwm, int period)
{
	int fd = -1;
	char pwmDev[256];
	memset(pwmDev, 0, sizeof(pwmDev));
	sprintf(pwmDev, "%s/pwm%d/period", PWM_DEV, nPwm);
	
	fd = open(pwmDev, O_RDWR);
	
	if (fd < 0)
	{
		printf("failed to set pwm%d period\n", nPwm);
		return -1;
	}
	else
	{
		char szPeriod[16] = {0};
		sprintf(szPeriod, "%d", period);
		write(fd, szPeriod, sizeof(szPeriod));
		//printf("set pwm%d period: %s\n", nPwm, szPeriod);
		close(fd);
		return 0;
	}
}	

// get pwm period
int GetPwmPeriod(int nPwm, int *pPeriod)
{
	int fd = -1;
	char pwmDev[256];
	memset(pwmDev, 0, sizeof(pwmDev));
	sprintf(pwmDev, "%s/pwm%d/period", PWM_DEV, nPwm);
	
	fd = open(pwmDev, O_RDWR);
	
	if (fd < 0)
	{
		printf("failed to get pwm%d period\n", nPwm);
		return -1;
	}
	else
	{
		char szPeriod[16] = {0};
		read(fd, szPeriod, sizeof(szPeriod));
		*pPeriod = atoi(szPeriod);
		//printf("get pwm%d period: %d\n", nPwm, *pPeriod);
		close(fd);
		return 0;
	}
}

// set pwm duty_cycle
int SetPwmDutyCycle(int nPwm, int dutyCycle)
{
	int fd = -1;
	char pwmDev[256];
	memset(pwmDev, 0, sizeof(pwmDev));
	sprintf(pwmDev, "%s/pwm%d/duty_cycle", PWM_DEV, nPwm);
	
	fd = open(pwmDev, O_RDWR);
	
	if (fd < 0)
	{
		printf("failed to set pwm%d duty_cycle\n", nPwm);
		return -1;
	}
	else
	{
		char szDutyCycle[16] = {0};
		sprintf(szDutyCycle, "%d", dutyCycle);
		write(fd, szDutyCycle, sizeof(szDutyCycle));
		//printf("set pwm%d duty_cycle: %s\n", nPwm, szDutyCycle);
		close(fd);
		return 0;
	}
}

// get pwm duty_cycle
int GetPwmDutyCycle(int nPwm, int *pDutyCycle)
{
	int fd = -1;
	char pwmDev[256];
	memset(pwmDev, 0, sizeof(pwmDev));
	sprintf(pwmDev, "%s/pwm%d/duty_cycle", PWM_DEV, nPwm);
	
	fd = open(pwmDev, O_RDWR);
	
	if (fd < 0)
	{
		printf("failed to get pwm%d duty_cycle\n", nPwm);
		return -1;
	}
	else
	{
		char szDutyCycle[16] = {0};
		read(fd, szDutyCycle, sizeof(szDutyCycle));
		*pDutyCycle = atoi(szDutyCycle);
		//printf("get pwm%d duty_cycle: %d\n", nPwm, *pDutyCycle);
		close(fd);
		return 0;
	}
}

// set pwm polarity
int SetPwmPolarity(int nPwm, int polarity)
{
	int fd = -1;
	char pwmDev[256];
	memset(pwmDev, 0, sizeof(pwmDev));
	sprintf(pwmDev, "%s/pwm%d/polarity", PWM_DEV, nPwm);
	
	fd = open(pwmDev, O_RDWR);
	
	if (fd < 0)
	{
		printf("failed to set pwm%d polarity\n", nPwm);
		return -1;
	}
	else
	{
		char szPolarity[16] = {0};
		sprintf(szPolarity, "%d", polarity);
		write(fd, szPolarity, sizeof(szPolarity));
		//printf("set pwm%d polarity: %s\n", nPwm, szPolarity);
		close(fd);
		return 0;
	}
}

// get pwm polarity
int GetPwmPolarity(int nPwm, int *pPolarity)
{
	int fd = -1;
	char pwmDev[256];
	memset(pwmDev, 0, sizeof(pwmDev));
	sprintf(pwmDev, "%s/pwm%d/polarity", PWM_DEV, nPwm);
	
	fd = open(pwmDev, O_RDWR);
	
	if (fd < 0)
	{
		printf("failed to get pwm%d polarity\n", nPwm);
		return -1;
	}
	else
	{
		char szPolarity[16] = {0};
		read(fd, szPolarity, sizeof(szPolarity));
		*pPolarity = atoi(szPolarity);
		//printf("get pwm%d polarity: %d\n", nPwm, *pPolarity);
		close(fd);
		return 0;
	}
}

// set pwm enable
int SetPwmEnable(int nPwm, int enable)
{
	int fd = -1;
	char pwmDev[256];
	memset(pwmDev, 0, sizeof(pwmDev));
	sprintf(pwmDev, "%s/pwm%d/enable", PWM_DEV, nPwm);
	
	fd = open(pwmDev, O_RDWR);
	
	if (fd < 0)
	{
		printf("failed to set pwm%d enable\n", nPwm);
		return -1;
	}
	else
	{
		char szEnable[16] = {0};
		sprintf(szEnable, "%d", enable);
		write(fd, szEnable, sizeof(szEnable));
		//printf("set pwm%d enable: %s\n", nPwm, szEnable);
		close(fd);
		return 0;
	}
}

// get pwm enable
int GetPwmEnable(int nPwm, int *pEnable)
{
	int fd = -1;
	char pwmDev[256];
	memset(pwmDev, 0, sizeof(pwmDev));
	sprintf(pwmDev, "%s/pwm%d/enable", PWM_DEV, nPwm);
	
	fd = open(pwmDev, O_RDWR);
	
	if (fd < 0)
	{
		printf("failed to get pwm%d enable\n", nPwm);
		return -1;
	}
	else
	{
		char szEnable[16] = {0};
		read(fd, szEnable, sizeof(szEnable));
		*pEnable = atoi(szEnable);
		//printf("get pwm%d enable: %d\n", nPwm, *pEnable);
		close(fd);
		return 0;
	}
}


// set pwm attr
int SetPwmAttribute(int nPwm, char *pAttr, int value)
{
	int fd = -1;
	char pwmDev[256];
	memset(pwmDev, 0, sizeof(pwmDev));
	sprintf(pwmDev, "%s/pwm%d/%s", PWM_DEV, nPwm, pAttr);
	
	fd = open(pwmDev, O_RDWR);
	
	if (fd < 0)
	{
		printf("failed to set pwm%d %s\n", nPwm, pAttr);
		return -1;
	}
	else
	{
		char szValue[16] = {0};
		
		if (!strcmp(pAttr, PWM_POLARITY))
		{
			if (value == 1)
				strcpy(szValue, PWM_INVERSED);
			else if (!value)
				strcpy(szValue, PWM_NORMAL);
			else
			{
				printf("Invalid polarity parameter\n");
				return -1;
			}
		}
		else
			sprintf(szValue, "%d", value);
		
		write(fd, szValue, sizeof(szValue));
		//printf("set pwm%d %s: %s\n", nPwm, pAttr, szValue);
		close(fd);
		return 0;
	}
}

// get pwm attr
int GetPwmAttribute(int nPwm, char *pAttr, int *pValue)
{
	int fd = -1;
	char pwmDev[256];
	memset(pwmDev, 0, sizeof(pwmDev));
	sprintf(pwmDev, "%s/pwm%d/%s", PWM_DEV, nPwm, pAttr);
	
	fd = open(pwmDev, O_RDWR);
	
	if (fd < 0)
	{
		printf("failed to get pwm%d %s\n", nPwm, pAttr);
		return -1;
	}
	else
	{
		char szValue[16] = {0};
		read(fd, szValue, sizeof(szValue));
		
		if (!strcmp(pAttr, PWM_POLARITY))
		{
			if (!strncmp(szValue, PWM_INVERSED, strlen(PWM_INVERSED)))
			{
				*pValue = 1;
			}
			else if (!strncmp(szValue, PWM_NORMAL, strlen(PWM_NORMAL)))
			{
				*pValue = 0;
			}
			else
			{
				printf("Invalid polarity\n");
				return -1;
			}
		}
		else
		{
			*pValue = atoi(szValue);
			//printf("get pwm%d %s: %d\n", nPwm, pAttr, *pValue);
		}
		
		//printf("get pwm%d %s: %s\n", nPwm, pAttr, szValue);
		
		close(fd);
		return 0;
	}
}



int control_sstar_backlight(int duty_cycle_percentage)
{
	static int init_pwm=0;
	int duty_cycle = LCD_PWM_PERIOD*duty_cycle_percentage/100;
	if(1)
	{
		init_pwm=1;

		if (ExportPwm(LCD_PWM_DEV_ID))
			return -1;
		if (SetPwmAttribute(LCD_PWM_DEV_ID, PWM_PERIOD, LCD_PWM_PERIOD))
			return -1;
		if (SetPwmAttribute(LCD_PWM_DEV_ID, PWM_DUTYCYCLE, duty_cycle))
			return -1;
		if (SetPwmAttribute(LCD_PWM_DEV_ID, PWM_POLARITY, 0))	//0-normal  1-inversed
			return -1;
		if (SetPwmAttribute(LCD_PWM_DEV_ID, PWM_ENABLE, 1))   //0-disable 1-enable
			return -1;
		//printf("control_sstar_backlight:%d\n",duty_cycle_percentage);
	}
	else
	{
		SetPwmAttribute(LCD_PWM_DEV_ID, PWM_DUTYCYCLE, duty_cycle);
		SetPwmAttribute(LCD_PWM_DEV_ID, PWM_ENABLE, 1);
	}
	
	return 0;
}