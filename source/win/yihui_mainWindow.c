#include "sysconf.h"
#include "rotaryEncoder.h"
#include "theme.h"
#include "yihui_mainWindow.h"



lv_obj_t *screen_main;				//控制界面screen

lv_obj_t *control_all_cont,*control_zone_cont;

lv_obj_t *control_head_date_label;

lv_obj_t *label_deviceName,*label_deviceIP,*label_deviceSource;

lv_obj_t *control_header_serverStatus_label,*control_header_volume_label;

lv_obj_t *control_volume_slider;

lv_obj_t *control_fire_channel_label[32];


#if(SELECTED_DISPLAY_TYPE==DISPLAY_TYPE_RGB_4P3_480X272)
#define MAINWIN_HEAD_CONT_HEIGHT 45
#else
#define MAINWIN_HEAD_CONT_HEIGHT 35
#endif


#if (YIHUI_VERSION || USE_PC_SIMULATOR)


/**
 * @brief 创建透明可点击区域
 * @param parent 父对象 (传 NULL 时使用默认屏幕)
 * @param x 水平坐标
 * @param y 垂直坐标
 * @param width 宽度
 * @param height 高度
 * @param event_cb 点击事件回调函数
 * @return 创建的透明容器对象
 */
static lv_obj_t* create_invisible_button(lv_obj_t* parent, lv_coord_t x, lv_coord_t y, 
                                 lv_coord_t width, lv_coord_t height, 
                                 lv_event_cb_t event_cb) 
{
    /* 创建容器 */
    lv_obj_t* container = lv_cont_create(parent ? parent : lv_scr_act(), NULL);
    
    /* 禁用自动布局 */
    lv_cont_set_layout(container, LV_LAYOUT_OFF);
    
    /* 启用点击检测 */
    lv_obj_set_click(container, true);

    /* 设置透明样式 */
    lv_obj_set_style_local_bg_opa(container, LV_CONT_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_TRANSP);
    lv_obj_set_style_local_border_opa(container, LV_CONT_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_TRANSP);

    /* 设置位置和尺寸 */
    lv_obj_set_pos(container, x, y);
    lv_obj_set_size(container, width, height);

    /* 绑定事件回调 */
    lv_obj_set_event_cb(container, event_cb);

    return container;
}

void yihui_main_win_deviceInfo_update(int IsSelfcall)
{
    if(!isDisplayValid())
    {
        return;
    }
	if(GetCurrentWin() != WIN_MAIN)
			return;
	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
	}

    char showDeviceName[128]={0};
    char showDeviceIP[128]={0};
    char showDeviceSource[152]={0};
    if(strlen(g_ipAddress) == 0 && g_IP_Assign == IP_ASSIGN_DHCP)
    {
        sprintf(showDeviceName,"%s",strlen(g_device_alias) == 0?"127.0.0.1":(const char*)g_device_alias);
        if(label_deviceName)
        {
            if(strcmp(lv_label_get_text(label_deviceName),showDeviceName))
            {
                lv_label_set_text_fmt(label_deviceName,"%s",showDeviceName);
            }
        }
        if(eth_link_status == 1)
        {
            sprintf(showDeviceIP,"%s","IP地址获取中");
        }
        else
        {
            int temp_link_status=get_netlink_status("eth0");
            if(temp_link_status == 0)
            {
                sprintf(showDeviceIP,"%s","未插入网线");
            }
            else
            {
                sprintf(showDeviceIP,"%s","IP地址获取中");
            }
        }
        if(label_deviceIP){
            if(strcmp(lv_label_get_text(label_deviceIP),showDeviceIP))
                lv_label_set_text_fmt(label_deviceIP,"%s",showDeviceIP);
        }
    }
    else
    {
        if(g_IP_Assign == IP_ASSIGN_STATIC)
        {
            sprintf(showDeviceName,"%s",strlen(g_device_alias) == 0?g_Static_ip_address:(const char*)g_device_alias);
            sprintf(showDeviceIP,"%s",g_Static_ip_address);
        }
        else {
            sprintf(showDeviceName,"%s",strlen(g_device_alias) == 0?g_ipAddress:(const char*)g_device_alias);
            sprintf(showDeviceIP,"%s",g_ipAddress);
        }

        if(label_deviceName)
        {
            if(strcmp(lv_label_get_text(label_deviceName),showDeviceName))
            {
                lv_label_set_text_fmt(label_deviceName,"%s",showDeviceName);
            }
        }
        if(label_deviceIP){
        	if(strcmp(lv_label_get_text(label_deviceIP),showDeviceIP))
                lv_label_set_text_fmt(label_deviceIP,"%s",showDeviceIP);
        }
    }
    
    if(label_deviceSource)
    {
        if(g_media_source == SOURCE_LOCAL_PLAY || g_media_source == SOURCE_TIMING)
        {
            sprintf(showDeviceSource,"%s:%s",Get_Source_Name_ById(g_media_source),Check_FileName(g_media_name,32));
        }
        else
        {
            sprintf(showDeviceSource,"%s",Get_Source_Name_ById(g_media_source));
        }
         if(strcmp(lv_label_get_text(label_deviceSource),showDeviceSource))
            lv_label_set_text_fmt(label_deviceSource,"%s",showDeviceSource);
    }

    if(control_header_serverStatus_label)
    {
        char showControl_header_serverStatus[64]={0};
        sprintf(showControl_header_serverStatus,"%s",IS_SERVER_CONNECTED?"在线":"离线");
        if(strcmp(lv_label_get_text(control_header_serverStatus_label),showControl_header_serverStatus))
            lv_label_set_text_fmt(control_header_serverStatus_label,"%s",showControl_header_serverStatus);
    }

    #if IS_DEVICE_DECODER_TERMINAL
    if(control_header_volume_label)
    {
        int volume=dispGetVolume();
        lv_label_set_text_fmt(control_header_volume_label,"%d",volume);
        lv_slider_set_value(control_volume_slider,volume,LV_ANIM_OFF);
    }
    #endif

    #if IS_DEVICE_FIRE_COLLECTOR
    if(control_fire_channel_label[0])
    {
        for(int i=0;i<32;i++)
        {
            unsigned int bit_mask = 1 << i;
            if (!isForceStopFire && (fire_collector_info.trigger_status & bit_mask)) {
                lv_obj_set_style_local_text_color(control_fire_channel_label[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_RED);
            } else {
                lv_obj_set_style_local_text_color(control_fire_channel_label[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_BLACK);
            }
        }
    }

    #endif
    

    if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
	}
}

static lv_obj_t* create_centered_label(lv_obj_t *parent, lv_style_t * style, int x_offset, int y_offset) {
    // 创建容器对象
    lv_obj_t *container = lv_cont_create(parent, NULL);
    lv_obj_set_size(container, lv_obj_get_width(parent), 40); // 设置容器大小
    // 设置容器为透明背景，并去掉边框
    lv_obj_set_style_local_bg_color(container, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_BLACK); // 设置背景色为黑色
    lv_obj_set_style_local_bg_opa(container, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_TRANSP); // 设置背景透明
    lv_obj_set_style_local_border_width(container, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0); // 去掉边框
    
    // 创建 label 并将其放入容器
    lv_obj_t *label = lv_label_create(container, NULL);
    //lv_obj_set_style_local_text_color(label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    lv_obj_set_style_local_text_color(label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(0x30, 0x30, 0x30));
    lv_label_set_align(label, LV_LABEL_ALIGN_CENTER); // 设置文本居中
    lv_obj_set_auto_realign(label,true);
    //lv_label_set_long_mode(label,LV_LABEL_LONG_SROLL_CIRC);
    lv_obj_add_style(label, LV_CONT_PART_MAIN, style);

    // 将 label 居中对齐到容器中
    lv_obj_align(label, container, LV_ALIGN_CENTER, 0, 0); 

    // 设置容器的位置
    lv_obj_align(container, parent, LV_ALIGN_IN_TOP_LEFT, x_offset, y_offset);
    return label;
}


static void slider_volume_event_cb(lv_obj_t * slider, lv_event_t e)
{
    if(e == LV_EVENT_VALUE_CHANGED) {
        #if 0
        if(lv_slider_get_type(slider) == LV_SLIDER_TYPE_NORMAL) {
            static char buf[16];
            int vol=lv_slider_get_value(volume_slider);
            lv_snprintf(buf, sizeof(buf), "%d", vol);
            lv_obj_set_style_local_value_str(slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, buf);
        }
        #endif

        int vol=lv_slider_get_value(slider);

        dispSetVolume(vol);
        lv_label_set_text_fmt(control_header_volume_label,"%d",vol);
    }
    #if 0
    else if(e == LV_EVENT_RELEASED)	//释放后才发送音量
    {
    	int vol=lv_slider_get_value(slider);

        dispSetVolume(vol);
        lv_label_set_text_fmt(control_header_volume_label,"%d",vol);
    }
    #endif
}



static void round_btn_cb(lv_obj_t * obj, lv_event_t e)
{
	int i=0;
	if(e == LV_EVENT_PRESSED || e == LV_EVENT_RELEASED || e == LV_EVENT_PRESS_LOST)
	{
		lv_obj_t *btn=lv_obj_get_child(obj, NULL);
		if(btn)
			lv_obj_set_state(btn, lv_obj_get_state(obj, LV_OBJ_PART_MAIN));
	}

    if(e == LV_EVENT_RELEASED)
	{
		if(obj == control_button_list[CONTROL_BUTTON_STOP].obj || lv_obj_get_parent(obj) == control_button_list[CONTROL_BUTTON_STOP].obj)
		{
            Set_zone_idle_status(NULL,  __func__, __LINE__,false);
		}
		else if(obj == control_button_list[CONTROL_BUTTON_SETTINGS].obj || lv_obj_get_parent(obj) == control_button_list[CONTROL_BUTTON_SETTINGS].obj)
        {
            yihui_settings_win_start();
        }
	}
}


static void yihui_main_btn_event_cb(lv_obj_t * obj, lv_event_t e)
{
    if(e == LV_EVENT_CLICKED)
    {
        yihui_settings_win_start();
    }
}


void yihui_main_win_start(void)
{
    static char hasInitButton=0;
	if(!hasInitButton)
	{
		hasInitButton=1;
		init_control_button();	//初始化控制按钮信息
	}

	control_all_cont = lv_cont_create(lv_scr_act(), NULL);
    lv_obj_set_size(control_all_cont,LV_HOR_RES_MAX,LV_VER_RES_MAX);
    lv_obj_set_pos(control_all_cont,0,0);		//此处为了解决默认情况下屏幕留边的问题
    lv_obj_set_style_local_pad_all(control_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(control_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_radius(control_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_border_width(control_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	//lv_obj_set_event_cb(control_all_cont, control_all_cont_event_cb);

    lv_obj_t *control_head_box = lv_obj_create(control_all_cont, NULL);
	lv_obj_set_style_local_pad_all(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_radius(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_border_width(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

#if(SELECTED_DISPLAY_TYPE==DISPLAY_TYPE_RGB_4P3_480X272)
	lv_obj_set_size(control_head_box, LV_HOR_RES_MAX, MAINWIN_HEAD_CONT_HEIGHT);
#else
    lv_obj_set_size(control_head_box, LV_HOR_RES_MAX, MAINWIN_HEAD_CONT_HEIGHT);
#endif
	
	lv_obj_align(control_head_box, control_all_cont, LV_ALIGN_IN_TOP_MID,0,0);

	control_head_date_label=lv_label_create(control_head_box, NULL);
	lv_obj_align(control_head_date_label, control_head_box, LV_ALIGN_CENTER,-2,0);
	lv_label_set_align(control_head_date_label, LV_LABEL_ALIGN_CENTER);
	lv_label_set_text(control_head_date_label, "2025-00-00 00:00");
	lv_obj_set_auto_realign(control_head_date_label,true);
	lv_obj_set_width(control_head_date_label, lv_obj_get_width_grid(control_head_box, 2, 1));
    lv_obj_set_style_local_text_font(control_head_date_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &lv_font_montserrat_20);

	lv_obj_set_style_local_text_font(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &lv_font_montserrat_20);
	lv_obj_set_style_local_bg_color(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());
	//lv_obj_set_style_local_bg_grad_color(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(0x1f,0x61,0x8d));
	//lv_obj_set_style_local_bg_grad_dir(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_obj_set_style_local_text_color(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
	lv_obj_set_style_local_radius(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	lv_obj_add_protect(control_head_box, LV_PROTECT_CLICK_FOCUS);

#if IS_DEVICE_DECODER_TERMINAL
	lv_obj_t *header_img_volume=lv_img_create(control_head_box, NULL);
	lv_obj_align(header_img_volume, control_head_box, LV_ALIGN_IN_RIGHT_MID,-3,4);
	lv_img_set_src(header_img_volume, &volumeWhite);
	lv_img_set_src(header_img_volume, &volumeWhite);
    // 创建 Label 并直接对齐到图像右侧
    control_header_volume_label = lv_label_create(control_head_box, NULL);
    lv_label_set_text_fmt(control_header_volume_label, "%d", dispGetVolume());
    lv_obj_align(control_header_volume_label, header_img_volume, LV_ALIGN_OUT_RIGHT_MID, 8, 0); // 右侧偏移 5 像素
#endif
    control_header_serverStatus_label = lv_label_create(control_head_box, NULL);
    lv_label_set_text(control_header_serverStatus_label, "在线");
    #if(SELECTED_DISPLAY_TYPE==DISPLAY_TYPE_RGB_4P3_480X272)
    lv_obj_add_style(control_header_serverStatus_label, LV_CONT_PART_MAIN, &style_font_20);
    lv_obj_align(control_header_serverStatus_label, control_head_box, LV_ALIGN_IN_LEFT_MID,10,0);
    #else
    lv_obj_add_style(control_header_serverStatus_label, LV_CONT_PART_MAIN, &style_font_16);
    lv_obj_align(control_header_serverStatus_label, control_head_box, LV_ALIGN_IN_LEFT_MID,10,-1);
    #endif


    control_zone_cont = lv_obj_create(control_all_cont, NULL);

#if(SELECTED_DISPLAY_TYPE==DISPLAY_TYPE_RGB_4P3_480X272)
    lv_obj_set_y(control_zone_cont,MAINWIN_HEAD_CONT_HEIGHT);
    lv_obj_set_size(control_zone_cont, LV_HOR_RES_MAX,152);
#else
    lv_obj_set_y(control_zone_cont,MAINWIN_HEAD_CONT_HEIGHT);
	lv_obj_set_size(control_zone_cont, LV_HOR_RES_MAX,137);
#endif

    lv_obj_set_style_local_pad_all(control_zone_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_margin_all(control_zone_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_radius(control_zone_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_bg_color(control_zone_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(250,250,250));
	lv_obj_set_style_local_border_width(control_zone_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 1);
	//lv_obj_set_click(control_main_cont, false);
	lv_obj_add_protect(control_zone_cont, LV_PROTECT_CLICK_FOCUS);


#if(SELECTED_DISPLAY_TYPE==DISPLAY_TYPE_RGB_4P3_480X272)
    label_deviceName = create_centered_label(control_zone_cont, &style_font_28, 0, 8);
    label_deviceIP = create_centered_label(control_zone_cont, &style_font_28, 0, 55);
    label_deviceSource = create_centered_label(control_zone_cont, &style_font_24, 0, 102);
#else
    label_deviceIP = create_centered_label(control_zone_cont, &style_font_20, 0, -3);
#endif

#if(SELECTED_DISPLAY_TYPE==DISPLAY_TYPE_RGB_4P3_480X272)
	//创建底部控制栏
	lv_obj_t *control_bottom_box = lv_obj_create(control_all_cont, control_all_cont);
	
    lv_obj_set_y(control_bottom_box,204);
    lv_obj_set_size(control_bottom_box, LV_HOR_RES_MAX, LV_VER_RES_MAX-204);
	
	lv_obj_set_style_local_border_width(control_bottom_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	//底部控制按钮
	int button_index=0;
	for(int i=0;i<MAX_CONTROL_NUM;i++)
	{
        #if !SUPPORT_UDISK_PLAY
        if(i==CONTROL_BUTTON_UDISK)
            continue;
        #endif

		control_button_list[i].obj=lv_obj_create(control_bottom_box, NULL);
	    lv_obj_set_style_local_bg_color(control_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());
	    lv_obj_set_style_local_radius(control_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_RADIUS_CIRCLE);
		lv_obj_set_size(control_button_list[i].obj, 42, 42);
        if(i!=CONTROL_BUTTON_SETTINGS)
		    lv_obj_set_pos(control_button_list[i].obj, 20+button_index*68, 0);
        else
            lv_obj_set_pos(control_button_list[i].obj, 425, 0);

	    lv_obj_set_style_local_border_width(control_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	    lv_obj_set_style_local_pad_all(control_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	    lv_obj_set_style_local_margin_all(control_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	    lv_obj_t *imgbtn=lv_imgbtn_create(control_button_list[i].obj, NULL);
		lv_obj_align(imgbtn, control_button_list[i].obj, LV_ALIGN_CENTER,MAINWIN_HEAD_CONT_HEIGHT,5);
		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_RELEASED, &control_button_list[i].pic);
		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_PRESSED, &control_button_list[i].pic);

		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_CHECKED_RELEASED, &control_button_list[i].pic);
		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_CHECKED_PRESSED, &control_button_list[i].pic);

		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_DISABLED, &control_button_list[i].pic);

		lv_obj_set_style_local_image_recolor_opa(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_OPA_COVER);
		lv_obj_set_style_local_image_recolor(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_COLOR_BLACK);

		lv_obj_set_style_local_image_recolor_opa(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_CHECKED, LV_OPA_COVER);
		lv_obj_set_style_local_image_recolor(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_CHECKED, LV_COLOR_YELLOW);

		//创建label显示按钮标签
		control_button_list[i].label=lv_label_create(control_all_cont, NULL);
		lv_obj_align(control_button_list[i].label, control_button_list[i].obj, LV_ALIGN_OUT_BOTTOM_MID,0,1);
		lv_label_set_align(control_button_list[i].label, LV_LABEL_ALIGN_CENTER);
		lv_label_set_text(control_button_list[i].label, control_button_list[i].name);
		lv_obj_set_auto_realign(control_button_list[i].label,true);
		lv_obj_set_width(control_button_list[i].label, lv_obj_get_width_grid(control_button_list[i].obj, 1, 1));
		lv_obj_add_style(control_button_list[i].label, LV_OBJ_PART_MAIN, &style_font_16);


	    lv_obj_set_event_cb(control_button_list[i].obj, round_btn_cb);
	    lv_obj_set_event_cb(imgbtn, round_btn_cb);

		button_index++;
	}

    lv_obj_t *imgVolume=lv_img_create(control_bottom_box, NULL);
    #if !SUPPORT_UDISK_PLAY
    lv_obj_set_pos(imgVolume, 120,15);
    #else
    lv_obj_set_pos(imgVolume, 152,15);
    #endif
    lv_img_set_src(imgVolume, &volumeBlack);
    //lv_obj_set_style_local_bg_color(imgbtn, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());
    //lv_obj_set_style_local_image_recolor_opa(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_COVER);
	//lv_obj_set_style_local_image_recolor(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_GRAY);



    control_volume_slider= lv_slider_create(control_bottom_box, NULL);
	lv_slider_set_value(control_volume_slider, 50, LV_ANIM_OFF);
    #if !SUPPORT_UDISK_PLAY
    lv_obj_set_pos(control_volume_slider, 162,22);
    #else
    lv_obj_set_pos(control_volume_slider, 195,22);
    #endif
	lv_obj_set_event_cb(control_volume_slider, slider_volume_event_cb);

    lv_obj_set_width(control_volume_slider, 198);
    lv_obj_set_height(control_volume_slider, 12);
	
	/*Use the knobs style value the display the current value in focused state*/
	lv_obj_set_style_local_margin_top(control_volume_slider, LV_SLIDER_PART_BG, LV_STATE_DEFAULT, LV_DPX(25));

	lv_obj_set_style_local_value_font(control_volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, lv_theme_get_font_small());
	//lv_obj_set_style_local_value_ofs_y(control_volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_FOCUSED, - LV_DPX(25));
	lv_obj_set_style_local_value_opa(control_volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, LV_OPA_TRANSP);
	lv_obj_set_style_local_value_opa(control_volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_FOCUSED, LV_OPA_COVER);

	lv_obj_set_style_local_transition_time(control_volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, 300);
	lv_obj_set_style_local_transition_prop_5(control_volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, LV_STYLE_VALUE_OFS_Y);
	lv_obj_set_style_local_transition_prop_6(control_volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, LV_STYLE_VALUE_OPA);

	lv_obj_set_style_local_bg_color(control_volume_slider, LV_SLIDER_PART_INDIC, LV_STATE_DEFAULT, get_theme_color());
	lv_obj_set_style_local_bg_color(control_volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, get_theme_color());

#endif


#if(SELECTED_DISPLAY_TYPE==DISPLAY_TYPE_SPI_1P9_320X170)
    #if IS_DEVICE_FIRE_COLLECTOR
    for(int i=0;i<32;i++)
    {
        //分成4行显示，每行8个通道
        int row=i/8;
        int col=i%8;
        control_fire_channel_label[i]=lv_label_create(control_zone_cont, NULL);
        lv_obj_set_size(control_fire_channel_label[i], 20, 20);
        lv_obj_set_pos(control_fire_channel_label[i], 30+col*MAINWIN_HEAD_CONT_HEIGHT, 34+row*24);
        
        lv_label_set_text_fmt(control_fire_channel_label[i],"%02d",i+1);
        lv_obj_add_style(control_fire_channel_label[i], LV_CONT_PART_MAIN, &style_font_16);
    }
    #endif

    //右下角加入透明的可点击区域，用于i386模拟进去设置界面
    lv_obj_t* invisible_enter_settings_btn = create_invisible_button(
        NULL,
        280, 132, 
        40, 40, 
        yihui_main_btn_event_cb
    );
#endif


    SetCurrentWin(WIN_MAIN);

	screen_main=lv_scr_act();

    yihui_main_win_deviceInfo_update(1);

	UI_UpdateSystemTime(1);
}


static void rotary_MainWindow_change_subVolume(int dir)
{
    if(GetCurrentWin() != WIN_MAIN)
        return;

    dispSetvolume_By_add_min(dir == ROTARY_DIR_RIGHT);
    yihui_main_win_deviceInfo_update(1);
}





void yihui_mainWin_rotary_event_change(int code, int value) {
    if(GetCurrentWin() != WIN_MAIN) return;

    if(code == REL_DIAL) 
    {
        #if IS_DEVICE_DECODER_TERMINAL
        rotary_MainWindow_change_subVolume(value);
        #endif
    }
    else if(code == KEY_ENTER) 
    {
        yihui_settings_win_start();
    }
}

#endif