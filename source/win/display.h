#ifndef DISPLAY_H
#define DISPLAY_H

#include "lvgl/lvgl.h"
#include "win.h"
#include <pthread.h>
#include "aipu_mainWindow.h"
#include "aipu_settingsWin.h"
#include "yihui_mainWindow.h"
#include "yihui_settingsWindow.h"

#ifdef USE_PC_SIMULATOR
#define FONT_PATH_WOFF2 "/home/<USER>/app/Display/font/HarmonyOSSansSC.woff2"
#define FONT_PATH_TTF "/home/<USER>/app/Display/font/font.ttf"
#else
#define FONT_PATH_WOFF2 "/customer/App/Display/font/HarmonyOSSansSC.woff2"
#define FONT_PATH_TTF "/customer/App/Display/font/font.ttf"
#endif


extern pthread_mutex_t lvglMutex;	//LVGL界面锁
extern lv_font_t font16,font20,font24,font28,font32;
extern lv_style_t style_font_16,style_font_20,style_font_24,style_font_28,style_font_32;

uint32_t _GetTime0();
void display_init();
bool isDisplayValid();
int getDisplayType();


void display_update_mainWin_external();
void UI_UpdateSystemTime(int IsSelfcall);

LV_IMG_DECLARE(logo_aipu);
LV_IMG_DECLARE(background_aipu);



int dispGetVolume();
int dispSetVolume(int volume);
void dispSetvolume_By_add_min(bool isAdd);

#endif
