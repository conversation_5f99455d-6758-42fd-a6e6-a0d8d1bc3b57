#include "sysconf.h"
#include "rotaryEncoder.h"
#include "aipu_settingsWin.h"

#if ( AIPU_VERSION || USE_PC_SIMULATOR)

enum{
    SETTINGS_PAGE_MAIN = 1,
    SETTINGS_PAGE_IP   = 2,
    SETTINGS_PAGE_REMOTE_ADDRCODE = 3,
    SETTINGS_PAGE_LOCAL_VOLUME = 4
};

lv_obj_t *screen_settings;				//控制界面screen
extern lv_obj_t *screen_main;		//控制界面screen

lv_obj_t *backGroundImgObj;

lv_obj_t *page_settings_main;		        //设置页面主菜单
lv_obj_t *page_settings_ip;	                //设置页面IP菜单
lv_obj_t *page_settings_remoteAddr;	        //设置页面遥控码菜单
lv_obj_t *page_settings_localVolume;	        //设置页面本地音量菜单

static int current_page = 0;

// 全局变量定义
static int current_focus_main = -1;         // 当前焦点项索引(0-2,2为保存)
static int current_focus_ip = -1;           // 当前焦点项索引（0-13，12为保存，13为退出）
static int current_focus_remoteAddr = -1;   // 当前焦点项索引（0-2，1为保存，2为退出）
static int current_focus_localVolume = -1;  // 当前焦点项索引（0-3，0为话筒音量，1为线路音量，2为保存，3为退出）
static bool edit_mode = false;              // 编辑模式标志

// IP地址、掩码、网关的数值存储
static uint8_t ip_parts[4] = {0};
static uint8_t mask_parts[4] = {0};
static uint8_t gateway_parts[4] = {0};

// LVGL对象指针
static lv_obj_t* ip_items[4];
static lv_obj_t* mask_items[4];
static lv_obj_t* gateway_items[4];

// 样式定义
static lv_style_t style_default;
static lv_style_t style_focused;
static lv_style_t style_edit;

static lv_obj_t *ipsettings_save_btn,*ipsettings_quit_btn;
static lv_obj_t *main_settings_ipsettings_btn, *main_settings_remote_addrCode_btn, *main_settings_volume_btn, *main_settings_quit_btn;
static lv_obj_t *remoteAddrCode_TextArea;
static lv_obj_t *remoteAddrCode_save_btn,*remoteAddrCode_quit_btn;

static lv_obj_t *micVolume_TextArea,*lineVolume_TextArea;
static lv_obj_t *localVolume_save_btn,*localVolume_quit_btn;

uint8_t remoteAddrCodeVal=0;
uint8_t localMicVolume_CodeVal=0;
uint8_t localLineVolume_CodeVal=0;

// 设置透明样式
lv_style_t style_trans;


static void save_network_settings();
static void save_localVolume_settings();
static void saveBtn_handle();
static void quitBtn_handle();


static void textArea_style_init()
{
     // 创建样式
    lv_style_reset(&style_default);  //重置样式释放内存
    lv_style_init(&style_default);
    lv_style_set_bg_color(&style_default, LV_STATE_DEFAULT, lv_color_hex(0xFFFFFF));
    lv_style_set_border_width(&style_default, LV_STATE_DEFAULT, 1);
    //lv_style_set_bg_opa(&style_default, LV_STATE_DEFAULT, LV_OPA_TRANSP);
    //lv_style_set_text_color(&style_default, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 201, 154));
    
    lv_style_reset(&style_focused);  //重置样式释放内存
    lv_style_init(&style_focused);
    lv_style_set_bg_color(&style_focused, LV_STATE_FOCUSED, lv_color_hex(0x503319));
    //lv_style_set_border_width(&style_focused, LV_STATE_FOCUSED, 0);
    //lv_style_set_bg_opa(&style_focused, LV_STATE_FOCUSED, LV_OPA_TRANSP);
    lv_style_set_text_color(&style_focused, LV_STATE_FOCUSED, LV_COLOR_MAKE(255, 201, 154));
    
    lv_style_reset(&style_edit);  //重置样式释放内存
    lv_style_init(&style_edit);
    lv_style_set_bg_color(&style_edit, LV_STATE_EDITED, lv_color_hex(0xFF0000));
    //lv_style_set_border_width(&style_edit, LV_STATE_EDITED, 0);
    //lv_style_set_bg_opa(&style_edit, LV_STATE_EDITED, LV_OPA_TRANSP);
    lv_style_set_text_color(&style_edit, LV_STATE_EDITED, LV_COLOR_MAKE(255, 201, 154));
}


/* 更新所有项目的显示样式 */
static void ipsettings_update_highlight() {
    int section = current_focus_ip / 4;
    int index = current_focus_ip % 4;

    // 重置所有样式
    for(int i=0; i<4; i++) {
        #if 0
        lv_obj_remove_style(ip_items[i], LV_OBJ_PART_MAIN, &style_focused);
        lv_obj_remove_style(ip_items[i], LV_OBJ_PART_MAIN,&style_edit);
        lv_obj_add_style(ip_items[i], LV_OBJ_PART_MAIN, &style_default);
        
        lv_obj_remove_style(mask_items[i], LV_OBJ_PART_MAIN, &style_focused);
        lv_obj_remove_style(mask_items[i], LV_OBJ_PART_MAIN, &style_edit);
        lv_obj_add_style(mask_items[i], LV_OBJ_PART_MAIN, &style_default);
        
        lv_obj_remove_style(gateway_items[i], LV_OBJ_PART_MAIN, &style_focused);
        lv_obj_remove_style(gateway_items[i], LV_OBJ_PART_MAIN, &style_edit);
        lv_obj_add_style(gateway_items[i], LV_OBJ_PART_MAIN, &style_default);
        #endif
        lv_obj_set_state(ip_items[i],LV_STATE_DEFAULT);
        lv_obj_set_state(mask_items[i],LV_STATE_DEFAULT);
        lv_obj_set_state(gateway_items[i],LV_STATE_DEFAULT);
    }
    lv_obj_set_state(ipsettings_save_btn,LV_STATE_DEFAULT);
    lv_obj_set_state(ipsettings_quit_btn,LV_STATE_DEFAULT);

    // 设置当前焦点样式
    lv_obj_t* target = NULL;
    if(current_focus_ip<12) {
        if(section == 0) target = ip_items[index];
        else if(section == 1) target = mask_items[index];
        else if(section == 2) target = gateway_items[index];
    }
    else if(current_focus_ip==12) target = ipsettings_save_btn;
    else if(current_focus_ip==13) target = ipsettings_quit_btn;

    if(target) {
        if(edit_mode) {
            //lv_obj_add_style(target, LV_OBJ_PART_MAIN, &style_edit);
            lv_obj_set_state(target,LV_STATE_EDITED);
        } else {
            //lv_obj_add_style(target, LV_OBJ_PART_MAIN, &style_focused);
            lv_obj_set_state(target,LV_STATE_FOCUSED);
        }
    }
}


void remoteAddr_update_highlight() {

    lv_obj_set_state(remoteAddrCode_TextArea,LV_STATE_DEFAULT);
    lv_obj_set_state(remoteAddrCode_save_btn,LV_STATE_DEFAULT);
    lv_obj_set_state(remoteAddrCode_quit_btn,LV_STATE_DEFAULT);

    // 设置当前焦点样式
    lv_obj_t* target = NULL;
    if(current_focus_remoteAddr == 0)
    {
        target = remoteAddrCode_TextArea;
    }
    else if(current_focus_remoteAddr == 1)
    {
        target = remoteAddrCode_save_btn;
    }
    else if(current_focus_remoteAddr == 2)
    {
        target = remoteAddrCode_quit_btn;
    }

    if(target) {
        if(edit_mode) {
            lv_obj_set_state(target,LV_STATE_EDITED);
        } else {
            lv_obj_set_state(target,LV_STATE_FOCUSED);
        }
    }
}


void localVolume_update_highlight() {

    lv_obj_set_state(micVolume_TextArea,LV_STATE_DEFAULT);
    lv_obj_set_state(lineVolume_TextArea,LV_STATE_DEFAULT);
    lv_obj_set_state(localVolume_save_btn,LV_STATE_DEFAULT);
    lv_obj_set_state(localVolume_quit_btn,LV_STATE_DEFAULT);

    // 设置当前焦点样式
    lv_obj_t* target = NULL;
    if(current_focus_localVolume == 0)
    {
        target = micVolume_TextArea;
    }
    else if(current_focus_localVolume == 1)
    {
        target = lineVolume_TextArea;
    }
    else if(current_focus_localVolume == 2)
    {
        target = localVolume_save_btn;
    }
    else if(current_focus_localVolume == 3)
    {
        target = localVolume_quit_btn;
    }

    if(target) {
        if(edit_mode) {
            lv_obj_set_state(target,LV_STATE_EDITED);
        } else {
            lv_obj_set_state(target,LV_STATE_FOCUSED);
        }
    }
}

void mainSettings_update_highlight() {
    printf("mainSettings_update_highlight\n");
    lv_obj_set_state(main_settings_ipsettings_btn,LV_STATE_DEFAULT);
    #if IS_DEVICE_REMOTE_CONTROLER
    lv_obj_set_state(main_settings_remote_addrCode_btn,LV_STATE_DEFAULT);
    #elif IS_DEVICE_DECODER_TERMINAL
    lv_obj_set_state(main_settings_volume_btn,LV_STATE_DEFAULT);
    #endif
    lv_obj_set_state(main_settings_quit_btn,LV_STATE_DEFAULT);

    // 设置当前焦点样式
    lv_obj_t* target = NULL;
    if(current_focus_main == 0)
    {
        target = main_settings_ipsettings_btn;
    }
    else if(current_focus_main == 1)
    {
        #if IS_DEVICE_REMOTE_CONTROLER
        target = main_settings_remote_addrCode_btn;
        #elif IS_DEVICE_DECODER_TERMINAL
        target = main_settings_volume_btn;
        #endif
    }
    else if(current_focus_main == 2)
    {
        target = main_settings_quit_btn;
    }
    if(target) {
        lv_obj_set_state(target,LV_STATE_FOCUSED);
    }
}


// 创建透明页面容器（后续对象会覆盖在背景之上）
lv_obj_t* create_page() {
    lv_obj_t *page = lv_obj_create(lv_scr_act(), NULL);
    
    //重要，需要设置控件大小，除了lv_obj_create(NULL,NULL)外，都需要设置大小
    //lv_disp_get_hor_res(NULL), lv_disp_get_ver_res(NULL));  // 设置背景图为屏幕大小
    lv_obj_set_size(page, lv_disp_get_hor_res(NULL), lv_disp_get_ver_res(NULL));  // 设置背景图为屏幕大小

#if 1
    lv_style_reset(&style_trans);  //重置样式释放内存
    lv_style_init(&style_trans);
    
    lv_style_set_bg_opa(&style_trans, LV_STATE_DEFAULT, LV_OPA_TRANSP);
    lv_style_set_border_opa(&style_trans, LV_STATE_DEFAULT, LV_OPA_TRANSP);
    
    lv_obj_add_style(page, LV_OBJ_PART_MAIN, &style_trans);
#endif
    //lv_obj_set_hidden(page, true);  // LVGL 7.x 的隐藏方式
    return page;
}



static void saveBtn_handle()
{
    if(current_page == SETTINGS_PAGE_IP)
    {
        save_network_settings();
    }
    else if(current_page == SETTINGS_PAGE_REMOTE_ADDRCODE)
    {
        save_remoteAddrCode_settings();
    }
    else if(current_page == SETTINGS_PAGE_LOCAL_VOLUME)
    {
        //保存话筒音量和线路音量
        save_localVolume_settings();
        //保存了也要退出到上一级菜单
        quitBtn_handle();
    }
}
static void quitBtn_handle()
{
    if(current_page == SETTINGS_PAGE_MAIN)
    {
        printf("click:quit btn...\n");
        return_to_mainWindow(1);
    }
    else if(current_page == SETTINGS_PAGE_IP)
    {
        #if IS_DEVICE_AUDIO_COLLECTOR || IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_AUDIO_MIXER || IS_DEVICE_GPS_SYNCHRONIZER
        return_to_mainWindow(1);
        #else
        lv_obj_del(page_settings_ip);
        lv_obj_set_hidden(page_settings_main,false);
        current_page = SETTINGS_PAGE_MAIN;
        #endif
    }
    else if(current_page == SETTINGS_PAGE_REMOTE_ADDRCODE)
    {
        lv_obj_del(page_settings_remoteAddr);
        lv_obj_set_hidden(page_settings_main,false);
        current_page = SETTINGS_PAGE_MAIN;
    }
    else if(current_page == SETTINGS_PAGE_LOCAL_VOLUME)
    {
        lv_obj_del(page_settings_localVolume);
        lv_obj_set_hidden(page_settings_main,false);
        current_page = SETTINGS_PAGE_MAIN;
    }
}

static void aipu_settings_btn_event_cb(lv_obj_t * obj, lv_event_t e)
{
    if(e == LV_EVENT_CLICKED)
    {
        if(obj == ipsettings_save_btn || obj == localVolume_save_btn || obj == remoteAddrCode_save_btn)
        {
            saveBtn_handle();
        }
        else if(obj == main_settings_quit_btn || obj == remoteAddrCode_quit_btn || obj == ipsettings_quit_btn\
                || obj == localVolume_quit_btn)
        {
            quitBtn_handle();
        }
        else if(obj == main_settings_ipsettings_btn)
        {
            lv_obj_set_hidden(page_settings_main,true);
            create_IPSettingsPage_ui();
        }
        else if(obj == main_settings_remote_addrCode_btn)
        {
            lv_obj_set_hidden(page_settings_main,true);
            create_remote_controler_addrCode_ui();
        }
        else if(obj == main_settings_volume_btn)
        {
            lv_obj_set_hidden(page_settings_main,true);
            create_local_volume_ui();
        }
    }
}


void create_MainPage_ui() {

    current_focus_ip = -1;
    edit_mode = false;

    current_page = SETTINGS_PAGE_MAIN;
    page_settings_main = create_page();
    lv_obj_t* parent = page_settings_main;

    main_settings_ipsettings_btn = lv_btn_create(parent, NULL);
    lv_obj_t* save_label = lv_label_create(main_settings_ipsettings_btn, NULL);
    lv_label_set_text(save_label, "IP地址设置");
 
  
    lv_obj_set_style_local_text_color(main_settings_ipsettings_btn, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 201, 154));
    lv_obj_set_style_local_bg_opa(main_settings_ipsettings_btn, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_TRANSP);
    lv_obj_set_style_local_border_opa(main_settings_ipsettings_btn, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_TRANSP);
  
    lv_obj_set_style_local_text_color(main_settings_ipsettings_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_COLOR_MAKE(255, 201, 154));
    lv_obj_set_style_local_bg_color(main_settings_ipsettings_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_COLOR_MAKE(255, 0, 0));
    lv_obj_set_style_local_bg_opa(main_settings_ipsettings_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_OPA_COVER);
    lv_obj_set_style_local_border_color(main_settings_ipsettings_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_COLOR_MAKE(255, 0, 0));

    lv_obj_add_style(main_settings_ipsettings_btn, LV_CONT_PART_MAIN, &style_font_28);
    lv_obj_set_size(main_settings_ipsettings_btn, 220, 38);
    lv_obj_set_pos(main_settings_ipsettings_btn, 50, 50);

    lv_obj_set_event_cb(main_settings_ipsettings_btn, aipu_settings_btn_event_cb);

#if IS_DEVICE_REMOTE_CONTROLER
    main_settings_remote_addrCode_btn = lv_btn_create(parent, main_settings_ipsettings_btn);
    lv_obj_t* remoteAddrCode_label = lv_label_create(main_settings_remote_addrCode_btn, NULL);
    lv_label_set_text(remoteAddrCode_label, "遥控地址码设置");
    lv_obj_set_size(main_settings_remote_addrCode_btn, 220, 38);
    lv_obj_set_pos(main_settings_remote_addrCode_btn, 50, 105);

    lv_obj_set_event_cb(main_settings_remote_addrCode_btn, aipu_settings_btn_event_cb);
#elif IS_DEVICE_DECODER_TERMINAL
    main_settings_volume_btn = lv_btn_create(parent, main_settings_ipsettings_btn);
    lv_obj_t* volumeSettings_label = lv_label_create(main_settings_volume_btn, NULL);
    lv_label_set_text(volumeSettings_label, "本地音量设置");
    lv_obj_set_size(main_settings_volume_btn, 220, 38);
    lv_obj_set_pos(main_settings_volume_btn, 50, 105);

    lv_obj_set_event_cb(main_settings_volume_btn, aipu_settings_btn_event_cb);
#endif

    main_settings_quit_btn = lv_btn_create(parent, main_settings_ipsettings_btn);
    lv_obj_t* quit_label = lv_label_create(main_settings_quit_btn, NULL);
    lv_label_set_text(quit_label, "退出");
    lv_obj_set_size(main_settings_quit_btn, 120, 36);
    lv_obj_set_pos(main_settings_quit_btn, 100, 175);

    lv_obj_set_event_cb(main_settings_quit_btn, aipu_settings_btn_event_cb);
}


void create_remote_controler_addrCode_ui() {

    current_focus_remoteAddr = -1;
    edit_mode = false;

    current_page = SETTINGS_PAGE_REMOTE_ADDRCODE;
    page_settings_remoteAddr = create_page();
    lv_obj_t* parent = page_settings_remoteAddr;

    remoteAddrCodeVal=g_remote_controler_addressCode;

     // IP地址部分
    lv_obj_t* remoteAddrCode_label = lv_label_create(parent, NULL);
    lv_label_set_text(remoteAddrCode_label, "遥控地址码:");
    lv_obj_align(remoteAddrCode_label, parent, LV_ALIGN_IN_TOP_LEFT, 45, 85);
    lv_obj_set_style_local_text_color(remoteAddrCode_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 201, 154));
    lv_obj_add_style(remoteAddrCode_label, LV_CONT_PART_MAIN, &style_font_28);

    remoteAddrCode_TextArea = lv_textarea_create(parent, NULL);
    lv_textarea_set_one_line(remoteAddrCode_TextArea, true);
    lv_textarea_set_max_length(remoteAddrCode_TextArea, 3);
    char strKeyCode[10]={0};
    sprintf(strKeyCode,"%d",remoteAddrCodeVal);
    lv_textarea_set_text(remoteAddrCode_TextArea, strKeyCode);
    lv_obj_set_size(remoteAddrCode_TextArea, 60, 35);
    lv_obj_align(remoteAddrCode_TextArea, parent, LV_ALIGN_IN_TOP_LEFT, 205 , 85);
    lv_obj_add_style(remoteAddrCode_TextArea, LV_OBJ_PART_MAIN, &style_default);
    lv_obj_add_style(remoteAddrCode_TextArea, LV_OBJ_PART_MAIN, &style_focused);
    lv_obj_add_style(remoteAddrCode_TextArea, LV_OBJ_PART_MAIN, &style_edit);
    lv_textarea_set_cursor_hidden(remoteAddrCode_TextArea, true);

    lv_textarea_set_text_align(remoteAddrCode_TextArea, LV_LABEL_ALIGN_CENTER);

    
    //底部增加保存，退出按钮
    remoteAddrCode_save_btn = lv_btn_create(parent, NULL);
    lv_obj_t* save_label = lv_label_create(remoteAddrCode_save_btn, NULL);
    lv_label_set_text(save_label, "保存");
    //lv_obj_set_style_local_text_color(save_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_BLACK);
    //lv_obj_set_style_local_border_width(save_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    //lv_obj_set_style_local_bg_opa(save_label,LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_TRANSP);
    lv_obj_set_style_local_bg_color(remoteAddrCode_save_btn, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 222, 192));
    lv_obj_set_style_local_border_color(remoteAddrCode_save_btn, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 222, 192));
    
    lv_obj_set_style_local_text_color(remoteAddrCode_save_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_COLOR_MAKE(255, 201, 154));
    lv_obj_set_style_local_bg_color(remoteAddrCode_save_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_COLOR_MAKE(83, 53, 27));
    lv_obj_set_style_local_border_color(remoteAddrCode_save_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_COLOR_MAKE(83, 53, 27));

    lv_obj_add_style(remoteAddrCode_save_btn, LV_CONT_PART_MAIN, &style_font_24);
    lv_obj_set_size(remoteAddrCode_save_btn, 100, 36);
    lv_obj_set_pos(remoteAddrCode_save_btn, 50, 180);


    remoteAddrCode_quit_btn = lv_btn_create(parent, remoteAddrCode_save_btn);
    lv_obj_t* quit_label = lv_label_create(remoteAddrCode_quit_btn, NULL);
    lv_label_set_text(quit_label, "退出");
    lv_obj_set_size(remoteAddrCode_quit_btn, 100, 36);
    lv_obj_set_pos(remoteAddrCode_quit_btn, 175, 180);


    lv_obj_set_event_cb(remoteAddrCode_save_btn, aipu_settings_btn_event_cb);
    lv_obj_set_event_cb(remoteAddrCode_quit_btn, aipu_settings_btn_event_cb);
}


void create_local_volume_ui() {

    current_focus_localVolume = -1;
    edit_mode = false;

    current_page = SETTINGS_PAGE_LOCAL_VOLUME;
    page_settings_localVolume = create_page();
    lv_obj_t* parent = page_settings_localVolume;

    localMicVolume_CodeVal=g_micVolume;
    localLineVolume_CodeVal=g_lineVolume;


    lv_obj_t* micVolume_label = lv_label_create(parent, NULL);
    lv_label_set_text(micVolume_label, "话筒音量:");
    lv_obj_align(micVolume_label, parent, LV_ALIGN_IN_TOP_LEFT, 55, 45);
    lv_obj_set_style_local_text_color(micVolume_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 201, 154));
    lv_obj_add_style(micVolume_label, LV_CONT_PART_MAIN, &style_font_28);

    micVolume_TextArea = lv_textarea_create(parent, NULL);
    lv_textarea_set_one_line(micVolume_TextArea, true);
    lv_textarea_set_max_length(micVolume_TextArea, 3);
    char strVolume[10]={0};
    sprintf(strVolume,"%d",localMicVolume_CodeVal);
    lv_textarea_set_text(micVolume_TextArea, strVolume);
    lv_obj_set_size(micVolume_TextArea, 60, 35);
    lv_obj_align(micVolume_TextArea, parent, LV_ALIGN_IN_TOP_LEFT, 205 , 43);
    lv_obj_add_style(micVolume_TextArea, LV_OBJ_PART_MAIN, &style_default);
    lv_obj_add_style(micVolume_TextArea, LV_OBJ_PART_MAIN, &style_focused);
    lv_obj_add_style(micVolume_TextArea, LV_OBJ_PART_MAIN, &style_edit);
    lv_textarea_set_cursor_hidden(micVolume_TextArea, true);

    lv_textarea_set_text_align(micVolume_TextArea, LV_LABEL_ALIGN_CENTER);


    lv_obj_t* lineVolume_label = lv_label_create(parent, micVolume_label);
    lv_label_set_text(lineVolume_label, "线路音量:");
    lv_obj_align(lineVolume_label, parent, LV_ALIGN_IN_TOP_LEFT, 55, 98);

    lineVolume_TextArea = lv_textarea_create(parent, micVolume_TextArea);
    sprintf(strVolume,"%d",localLineVolume_CodeVal);
    lv_textarea_set_text(lineVolume_TextArea, strVolume);
    lv_obj_align(lineVolume_TextArea, parent, LV_ALIGN_IN_TOP_LEFT, 205 , 98);


    
    //底部增加保存，退出按钮
    localVolume_save_btn = lv_btn_create(parent, NULL);
    lv_obj_t* save_label = lv_label_create(localVolume_save_btn, NULL);
    lv_label_set_text(save_label, "保存");
    //lv_obj_set_style_local_text_color(save_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_BLACK);
    //lv_obj_set_style_local_border_width(save_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    //lv_obj_set_style_local_bg_opa(save_label,LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_TRANSP);
    lv_obj_set_style_local_bg_color(localVolume_save_btn, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 222, 192));
    lv_obj_set_style_local_border_color(localVolume_save_btn, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 222, 192));
    
    lv_obj_set_style_local_text_color(localVolume_save_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_COLOR_MAKE(255, 201, 154));
    lv_obj_set_style_local_bg_color(localVolume_save_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_COLOR_MAKE(83, 53, 27));
    lv_obj_set_style_local_border_color(localVolume_save_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_COLOR_MAKE(83, 53, 27));

    lv_obj_add_style(localVolume_save_btn, LV_CONT_PART_MAIN, &style_font_24);
    lv_obj_set_size(localVolume_save_btn, 100, 36);
    lv_obj_set_pos(localVolume_save_btn, 50, 180);


    localVolume_quit_btn = lv_btn_create(parent, localVolume_save_btn);
    lv_obj_t* quit_label = lv_label_create(localVolume_quit_btn, NULL);
    lv_label_set_text(quit_label, "退出");
    lv_obj_set_size(localVolume_quit_btn, 100, 36);
    lv_obj_set_pos(localVolume_quit_btn, 175, 180);


    lv_obj_set_event_cb(localVolume_save_btn, aipu_settings_btn_event_cb);
    lv_obj_set_event_cb(localVolume_quit_btn, aipu_settings_btn_event_cb);
}

/* 创建设置界面 */
void create_IPSettingsPage_ui() {

    current_focus_ip = -1;         // 当前焦点项索引（0-11，12为保存，13为退出）
    edit_mode = false;        // 编辑模式标志

    current_page = SETTINGS_PAGE_IP;
    
    page_settings_ip = create_page();
    lv_obj_t* parent = page_settings_ip;

    // IP地址部分
    lv_obj_t* ip_label = lv_label_create(parent, NULL);
    lv_label_set_text(ip_label, "地址:");
    lv_obj_align(ip_label, parent, LV_ALIGN_IN_TOP_LEFT, 15, 25);
    lv_obj_set_style_local_text_color(ip_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 201, 154));
    lv_obj_add_style(ip_label, LV_CONT_PART_MAIN, &style_font_24);
#if 1

    if(g_IP_Assign == IP_ASSIGN_STATIC)
    {
        split_ip(g_Static_ip_address,ip_parts);
    }
    else
    {
        split_ip(g_ipAddress,ip_parts);
    }
    split_ip(g_Subnet_Mask,mask_parts);
    split_ip(g_GateWay,gateway_parts);

    for(int i=0; i<4; i++) {
        ip_items[i] = lv_textarea_create(parent, NULL);
        lv_textarea_set_one_line(ip_items[i], true);
        lv_textarea_set_max_length(ip_items[i], 3);
        char NumBuffer[10] = {0};
        sprintf(NumBuffer, "%d", ip_parts[i]);
        lv_textarea_set_text(ip_items[i], NumBuffer);
        lv_obj_set_size(ip_items[i], 50, 30);
        lv_obj_align(ip_items[i], parent, LV_ALIGN_IN_TOP_LEFT, 72 + i*60, 20);
        lv_obj_add_style(ip_items[i], LV_OBJ_PART_MAIN, &style_default);
        lv_obj_add_style(ip_items[i], LV_OBJ_PART_MAIN, &style_focused);
        lv_obj_add_style(ip_items[i], LV_OBJ_PART_MAIN, &style_edit);
        lv_textarea_set_cursor_hidden(ip_items[i], true);

        lv_obj_add_style(ip_items[i], LV_CONT_PART_MAIN, &style_font_20);

        lv_textarea_set_text_align(ip_items[i], LV_LABEL_ALIGN_CENTER);
    }
#endif

    // 子网掩码部分
    lv_obj_t* mask_label = lv_label_create(parent, NULL);
    lv_label_set_text(mask_label, "掩码:");
    lv_obj_align(mask_label, parent, LV_ALIGN_IN_TOP_LEFT, 15, 75);
    lv_obj_set_style_local_text_color(mask_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 201, 154));
    lv_obj_add_style(mask_label, LV_CONT_PART_MAIN, &style_font_24);
#if 1
    for(int i=0; i<4; i++) {
        mask_items[i] = lv_textarea_create(parent, NULL);
        lv_textarea_set_one_line(mask_items[i], true);
        lv_textarea_set_max_length(mask_items[i], 3);
        char NumBuffer[10] = {0};
        sprintf(NumBuffer, "%d", mask_parts[i]);
        lv_textarea_set_text(mask_items[i], NumBuffer);
        lv_obj_set_size(mask_items[i], 50, 30);
        lv_obj_align(mask_items[i], parent, LV_ALIGN_IN_TOP_LEFT, 72 + i*60, 70);
        lv_obj_add_style(mask_items[i], LV_OBJ_PART_MAIN, &style_default);
        lv_obj_add_style(mask_items[i], LV_OBJ_PART_MAIN, &style_focused);
        lv_obj_add_style(mask_items[i], LV_OBJ_PART_MAIN, &style_edit);
        lv_textarea_set_cursor_hidden(mask_items[i], true);

        lv_obj_add_style(mask_items[i], LV_CONT_PART_MAIN, &style_font_20);

        lv_textarea_set_text_align(mask_items[i], LV_LABEL_ALIGN_CENTER);
    }
#endif
    // 网关部分
    lv_obj_t* gateway_label = lv_label_create(parent, NULL);
    lv_label_set_text(gateway_label, "网关:");
    lv_obj_align(gateway_label, parent, LV_ALIGN_IN_TOP_LEFT, 15, 125);
    lv_obj_set_style_local_text_color(gateway_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 201, 154));
    lv_obj_add_style(gateway_label, LV_CONT_PART_MAIN, &style_font_24);

#if 1
    for(int i=0; i<4; i++) {
        gateway_items[i] = lv_textarea_create(parent, NULL);
        lv_textarea_set_one_line(gateway_items[i], true);
        lv_textarea_set_max_length(gateway_items[i], 3);
        char NumBuffer[10] = {0};
        sprintf(NumBuffer, "%d", gateway_parts[i]);
        lv_textarea_set_text(gateway_items[i], NumBuffer);
        lv_obj_set_size(gateway_items[i], 50, 40);
        lv_obj_align(gateway_items[i], parent, LV_ALIGN_IN_TOP_LEFT, 72 + i*60, 120);
        lv_obj_add_style(gateway_items[i], LV_OBJ_PART_MAIN, &style_default);
        lv_obj_add_style(gateway_items[i], LV_OBJ_PART_MAIN, &style_focused);
        lv_obj_add_style(gateway_items[i], LV_OBJ_PART_MAIN, &style_edit);
        lv_textarea_set_cursor_hidden(gateway_items[i], true);

        lv_obj_add_style(gateway_items[i], LV_CONT_PART_MAIN, &style_font_20);

        lv_textarea_set_text_align(gateway_items[i], LV_LABEL_ALIGN_CENTER);
    }
#endif

    //底部增加保存，退出按钮
    ipsettings_save_btn = lv_btn_create(parent, NULL);
    lv_obj_t* save_label = lv_label_create(ipsettings_save_btn, NULL);
    lv_label_set_text(save_label, "保存");
    //lv_obj_set_style_local_text_color(save_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_BLACK);
    //lv_obj_set_style_local_border_width(save_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    //lv_obj_set_style_local_bg_opa(save_label,LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_TRANSP);
    lv_obj_set_style_local_bg_color(ipsettings_save_btn, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 222, 192));
    lv_obj_set_style_local_border_color(ipsettings_save_btn, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 222, 192));
    
    lv_obj_set_style_local_text_color(ipsettings_save_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_COLOR_MAKE(255, 201, 154));
    lv_obj_set_style_local_bg_color(ipsettings_save_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_COLOR_MAKE(83, 53, 27));
    lv_obj_set_style_local_border_color(ipsettings_save_btn, LV_OBJ_PART_MAIN, LV_STATE_FOCUSED, LV_COLOR_MAKE(83, 53, 27));

    lv_obj_add_style(ipsettings_save_btn, LV_CONT_PART_MAIN, &style_font_24);
    lv_obj_set_size(ipsettings_save_btn, 100, 36);
    lv_obj_set_pos(ipsettings_save_btn, 65, 180);



    ipsettings_quit_btn = lv_btn_create(parent, ipsettings_save_btn);
    lv_obj_t* quit_label = lv_label_create(ipsettings_quit_btn, NULL);
    lv_label_set_text(quit_label, "退出");
    lv_obj_set_size(ipsettings_quit_btn, 100, 36);
    lv_obj_set_pos(ipsettings_quit_btn, 190, 180);

    lv_obj_set_event_cb(ipsettings_save_btn, aipu_settings_btn_event_cb);
    lv_obj_set_event_cb(ipsettings_quit_btn, aipu_settings_btn_event_cb);
}


static void msg_close_task(lv_task_t *param) {

	if(param->user_data)
	{
		lv_obj_del(param->user_data);
	}
	lv_task_del(param);
}

static void save_network_settings()
{
    // 保存IP地址、掩码、网关
    // 这里需要将ip_parts、mask_parts、gateway_parts转换为字符串并保存
    char ip_str[16] = {0};
    char mask_str[16] = {0};
    char gateway_str[16] = {0};
    sprintf(ip_str, "%d.%d.%d.%d", ip_parts[0], ip_parts[1], ip_parts[2], ip_parts[3]);
    sprintf(mask_str, "%d.%d.%d.%d", mask_parts[0], mask_parts[1], mask_parts[2], mask_parts[3]);
    sprintf(gateway_str, "%d.%d.%d.%d", gateway_parts[0], gateway_parts[1], gateway_parts[2], gateway_parts[3]);
    //判断是否合规
    bool isValidIP = true;
    if(!if_a_string_is_a_valid_ipv4_address(ip_str) || !isValidSubnetMask(mask_str) || !if_a_string_is_a_valid_ipv4_address(gateway_str))
    {
        isValidIP = false;
    }
    if(isGatewayByNetmask_Error(ip_str,mask_str,gateway_str))
    {
        isValidIP = false;
    }

#if 0
    static const char * btns[] = {"OK", ""};
    lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
    lv_msgbox_add_btns(msg, btns);
    lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
    lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
    lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
    lv_obj_set_size(240,120);
#endif

    lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
    lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);

    if(!isValidIP)
    {
        //参数错误，弹出对话框提示错误
        lv_msgbox_set_text(msg,"参数错误,请检查网络配置");
        lv_task_create(msg_close_task, 2000, LV_TASK_PRIO_MID, msg);
        return;
    }
    else
    {
        lv_msgbox_set_text(msg,"设置成功,设备即将重启");
    }

    g_IP_Assign = IP_ASSIGN_STATIC;
    sprintf(g_Static_ip_address,"%s",ip_str);
    sprintf(g_Subnet_Mask,"%s",mask_str);
    sprintf(g_GateWay,"%s",gateway_str);

    //保存网络信息
    save_sysconf(INI_SETCION_NETWORK,NULL);	
    System_Reboot_DelayMs(500);
}

static void save_localVolume_settings()
{
    //保存MIC音量和线路音量
    if(g_micVolume != localMicVolume_CodeVal)
    {
        g_micVolume = localMicVolume_CodeVal;
        save_sysconf(INI_SECTION_BASIC,"Mic_Volume");
    }
    if(g_lineVolume!= localLineVolume_CodeVal)
    {
        g_lineVolume = localLineVolume_CodeVal;
        save_sysconf(INI_SECTION_BASIC,"Line_Volume");
    }
}


void save_remoteAddrCode_settings()
{
    lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
    lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);

    lv_msgbox_set_text(msg,"遥控地址码设置成功");
    lv_task_create(msg_close_task, 1500, LV_TASK_PRIO_MID, msg);

    g_remote_controler_addressCode = remoteAddrCodeVal;
    //保存按键信息
    save_sysconf(INI_SECTION_REMOTE,NULL);
}


void return_to_mainWindow(int IsSelfcall)
{
    if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
	}
    DeleteWin(WIN_SETTING);
    lv_obj_del(screen_settings);
    lv_scr_load(screen_main);
    SetCurrentWin(WIN_MAIN);
    aipu_main_win_deviceInfo_update(IsSelfcall);
    if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
	}
}

/* 旋转编码器事件处理 */
void aipu_settings_rotary_event_change(int code, int value) {
    if(GetCurrentWin() != WIN_SETTING) return;

    if(code == REL_DIAL) {
        if(edit_mode) {
            // 编辑模式：修改数值
            if(current_page == SETTINGS_PAGE_IP)
            {
                int section = current_focus_ip / 4;
                int index = current_focus_ip % 4;
                uint8_t* parts = NULL;

                if(section == 0) parts = ip_parts;
                else if(section == 1) parts = mask_parts;
                else if(section == 2) parts = gateway_parts;

                if(parts) {
                    int new_val = (int)parts[index] + value;
                    new_val = new_val < 0 ? 0 : (new_val > 255 ? 255 : new_val);
                    parts[index] = new_val;

                    // 更新显示
                    char buf[4];
                    snprintf(buf, sizeof(buf), "%d", new_val);
                    lv_textarea_set_text(
                        (section == 0) ? ip_items[index] : 
                        (section == 1) ? mask_items[index] : gateway_items[index],
                        buf
                    );
                }
            }
            else if(current_page == SETTINGS_PAGE_REMOTE_ADDRCODE)
            {
                int new_val = remoteAddrCodeVal + value;
                new_val = new_val < 1 ? 1 : (new_val > 255 ? 255 : new_val);
                remoteAddrCodeVal = new_val;

                // 更新显示
                char buf[4];
                snprintf(buf, sizeof(buf), "%d", new_val);
                lv_textarea_set_text(remoteAddrCode_TextArea,buf);
            }
            else if(current_page == SETTINGS_PAGE_LOCAL_VOLUME)
            {
                // 更新显示
                char buf[4];
                if(current_focus_localVolume == 0)
                {
                    int new_val = localMicVolume_CodeVal + value;
                    new_val = new_val < 0? 0 : (new_val > 100? 100 : new_val);
                    localMicVolume_CodeVal = new_val;

                    snprintf(buf, sizeof(buf), "%d", localMicVolume_CodeVal);
                    lv_textarea_set_text(micVolume_TextArea,buf);
                }
                else if(current_focus_localVolume == 1)
                {
                    int new_val = localLineVolume_CodeVal + value;
                    new_val = new_val < 0? 0 : (new_val > 100? 100 : new_val);
                    localLineVolume_CodeVal = new_val;

                    snprintf(buf, sizeof(buf), "%d", localLineVolume_CodeVal);
                    lv_textarea_set_text(lineVolume_TextArea,buf);
                }
            }
        } else {
            // 导航模式：切换焦点
            if(current_page == SETTINGS_PAGE_MAIN)
            {
                current_focus_main += value;
                if(current_focus_main < -1) current_focus_main = 2;
                else if(current_focus_main > 2) current_focus_main = -1;
                mainSettings_update_highlight();
            }
            else if(current_page == SETTINGS_PAGE_IP)
            {
                current_focus_ip += value;
                if(current_focus_ip < -1) current_focus_ip = 13;
                else if(current_focus_ip > 13) current_focus_ip = -1;
                ipsettings_update_highlight();
            }
            else if(current_page == SETTINGS_PAGE_REMOTE_ADDRCODE)
            {
                current_focus_remoteAddr += value;
                if(current_focus_remoteAddr < -1) current_focus_remoteAddr = 2;
                else if(current_focus_remoteAddr > 2) current_focus_remoteAddr = -1;
                remoteAddr_update_highlight();
            }
            else if(current_page == SETTINGS_PAGE_LOCAL_VOLUME)
            {
                current_focus_localVolume += value;
                if(current_focus_localVolume < -1) current_focus_localVolume = 3;
                else if(current_focus_localVolume > 3) current_focus_localVolume = -1;
                localVolume_update_highlight();
            }


        }
    }
    else if(code == KEY_ENTER) {
        if(value == 1) 
        { // 仅处理按下事件
            if(current_page == SETTINGS_PAGE_MAIN)
            {
                if(current_focus_main == 0)
                {
                    lv_obj_set_hidden(page_settings_main,true);
                    create_IPSettingsPage_ui();
                }
                else if(current_focus_main == 1)
                {
                    lv_obj_set_hidden(page_settings_main,true);
                    #if IS_DEVICE_REMOTE_CONTROLER
                    create_remote_controler_addrCode_ui();
                    #elif IS_DEVICE_DECODER_TERMINAL
                    create_local_volume_ui();
                    #endif
                }
                else if(current_focus_main == 2)
                {
                    // 退出按钮
                    printf("click:quit btn...\n");
                    return_to_mainWindow(1);
                }
            }
            else if(current_page == SETTINGS_PAGE_IP)
            {
                if(current_focus_ip>=0 && current_focus_ip < 12) {
                    // 保存按钮
                    // 保存IP地址、掩码、网关
                    // 这里需要将ip_parts、mask_parts、gateway_parts转换为字符串并保存
                    edit_mode = !edit_mode;
                    ipsettings_update_highlight();
                }
                else if(current_focus_ip == 12) {
                    // 保存按钮
                    saveBtn_handle();
                }
                else if(current_focus_ip == 13) {
                    // 退出按钮
                    quitBtn_handle();
                }
            }
            else if(current_page == SETTINGS_PAGE_REMOTE_ADDRCODE)
            {
                if(current_focus_remoteAddr==0) {
                    edit_mode = !edit_mode;
                    remoteAddr_update_highlight();
                }
                else if(current_focus_remoteAddr == 1) {
                    // 保存按钮
                    saveBtn_handle();
                }
                else if(current_focus_remoteAddr == 2) {
                    // 退出按钮
                    quitBtn_handle();
                }
            }
            else if(current_page == SETTINGS_PAGE_LOCAL_VOLUME)
            {
                if(current_focus_localVolume==0 || current_focus_localVolume == 1) {
                    edit_mode =!edit_mode;
                    localVolume_update_highlight();
                }
                else if(current_focus_localVolume == 2) {
                    // 保存按钮
                    saveBtn_handle();
                }
                else if(current_focus_localVolume == 3) {
                    // 退出按钮
                    quitBtn_handle();
                }
            }
        }
    }
}

// 创建页面控件
void aipu_settings_win_start()
{
    //新建一个screen
    screen_settings = lv_obj_create(NULL, NULL);
    lv_scr_load(screen_settings);

    backGroundImgObj = lv_img_create(screen_settings, NULL);

    lv_img_set_src(backGroundImgObj, &background_aipu);

    current_focus_main = -1;
    current_focus_ip = -1;
    edit_mode = false;

    textArea_style_init();

    #if IS_DEVICE_AUDIO_COLLECTOR || IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_AUDIO_MIXER || IS_DEVICE_GPS_SYNCHRONIZER
    create_IPSettingsPage_ui(page_settings_ip);
    #else
    create_MainPage_ui(page_settings_main);
    #endif

    SetCurrentWin(WIN_SETTING);
}


#endif