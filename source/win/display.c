#include <time.h>
#include <stdint.h>
#include <string.h>
#include "sysconf.h"
#include "../lv_freetype/lv_freetype.h"
#include "display.h"
#include "st7789.h"

#include <signal.h>
#include <sys/time.h>
#include "../thirdParty/yhBoard.h"

#ifdef USE_PC_SIMULATOR
#define SDL_MAIN_HANDLED /*To fix SDL's "undefined reference to WinMain" \
                            issue*/
#include <SDL2/SDL.h>
#include "lv_drivers/display/monitor.h"
#include "lv_drivers/indev/mouse.h"
#endif

#if SUPPORT_SIP
#include "../pjsip/appConfig.h"
#endif

lv_font_t font16,font20,font24,font28,font32;
lv_style_t style_font_16,style_font_20,style_font_24,style_font_28,style_font_32;

pthread_mutex_t lvglMutex=PTHREAD_MUTEX_INITIALIZER;	//LVGL界面锁


#define DISP_BUF_SIZE LV_HOR_RES_MAX*LV_VER_RES_MAX


static void disp_init(void);
static void disp_flush(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p);

char* getFontPath();

uint32_t _GetTime0()
{
    struct timespec ts;
    uint32_t ms;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    ms = (ts.tv_sec * 1000) + (ts.tv_nsec / 1000000);
    if(ms == 0)
    {
        ms = 1;
    }
    return ms;
}

#if LV_TICK_CUSTOM

uint32_t custom_tick_get(void)
{
    static uint32_t last_time_ms = 0;
    static uint32_t start_ms = 0;

    if (start_ms == 0) {
        start_ms = _GetTime0();
    }

    uint32_t now_ms = _GetTime0();
    uint32_t time_ms = now_ms - start_ms;
#if 0
    // 检查时间是否平滑递增
    if (last_time_ms != 0) {  // 确保不是首次调用
        if (now_ms < last_time_ms) {
            printf("Warning: Time went backwards! now_ms=%u, last_time_ms=%u\n", now_ms, last_time_ms);
        } else if ((now_ms - last_time_ms) > 20) {  // 自定义跳变阈值，如20ms
            printf("Warning: Time jump detected! now_ms=%u, last_time_ms=%u, diff=%u\n",
                   now_ms, last_time_ms, now_ms - last_time_ms);
        }
        else if ((now_ms - last_time_ms) < 10) {  // 自定义跳变阈值，如10ms
            printf("Warning: Time speed detected! now_ms=%u, last_time_ms=%u, diff=%u\n",
                   now_ms, last_time_ms, now_ms - last_time_ms);
        }
    }
#endif
    last_time_ms = now_ms;

    return time_ms;
}

#endif



void font_loading()
{
	  //lv_ft_init(32); /*Cache max 64 glyphs*/
      lv_freetype_init(3,8,0);
	  /*Create a font*/

	  lv_ft_info_t fontInfo;
      
      fontInfo.name=getFontPath();

      fontInfo.weight=16;
      fontInfo.style=0;
	  lv_ft_font_init(&fontInfo);
      memcpy(&font16,fontInfo.font,sizeof(lv_font_t));
	  /*Create style with the new font*/
	  lv_style_init(&style_font_16);
	  lv_style_set_text_font(&style_font_16, LV_STATE_DEFAULT, &font16);

      fontInfo.weight=20;
      fontInfo.style=0;
	  lv_ft_font_init(&fontInfo);
      memcpy(&font20,fontInfo.font,sizeof(lv_font_t));
	  /*Create style with the new font*/
	  lv_style_init(&style_font_20);
	  lv_style_set_text_font(&style_font_20, LV_STATE_DEFAULT, &font20);
 
      fontInfo.weight=24;
	  lv_ft_font_init(&fontInfo);
      memcpy(&font24,fontInfo.font,sizeof(lv_font_t));
	  /*Create style with the new font*/
	  lv_style_init(&style_font_24);
	  lv_style_set_text_font(&style_font_24, LV_STATE_DEFAULT, &font24);

      fontInfo.weight=28;
	  lv_ft_font_init(&fontInfo);
      memcpy(&font28,fontInfo.font,sizeof(lv_font_t));
	  /*Create style with the new font*/
	  lv_style_init(&style_font_28);
	  lv_style_set_text_font(&style_font_28, LV_STATE_DEFAULT, &font28);

      fontInfo.weight=32;
	  lv_ft_font_init(&fontInfo);
      memcpy(&font32,fontInfo.font,sizeof(lv_font_t));
	  /*Create style with the new font*/
	  lv_style_init(&style_font_32);
	  lv_style_set_text_font(&style_font_32, LV_STATE_DEFAULT, &font32);
}


/**********************
 *  STATIC VARIABLES
 **********************/

/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
 **********************/

void lv_port_disp_init_spi(void)
{
    /*-------------------------
     * Initialize your display
     * -----------------------*/
    disp_init();
    /*-----------------------------
     * Create a buffer for drawing
     *----------------------------*/
    
    /*A small buffer for LittlevGL to draw the screen's content*/
    static lv_color_t buf1[DISP_BUF_SIZE]; // 第一个缓冲区
    //static lv_color_t buf2[DISP_BUF_SIZE]; // 第二个缓冲区

    /* Initialize a descriptor for the buffers */
    static lv_disp_buf_t disp_buf;
    //lv_disp_buf_init(&disp_buf, buf1, buf2, DISP_BUF_SIZE); // 初始化为双缓冲
    lv_disp_buf_init(&disp_buf, buf1, NULL, DISP_BUF_SIZE); // 初始化为单缓冲
    
    /*-----------------------------------
     * Register the display in LVGL
     *----------------------------------*/
    static lv_disp_drv_t disp_drv;                  /*Descriptor of a display driver*/
    lv_disp_drv_init(&disp_drv);                    /*Basic initialization*/

    /*Set up the functions to access to your display*/

    /*Set the resolution of the display*/
    disp_drv.hor_res = LV_HOR_RES_MAX;
    disp_drv.ver_res = LV_VER_RES_MAX;

    /*Used to copy the buffer's content to the display*/
    disp_drv.flush_cb = disp_flush;
    disp_drv.buffer   = &disp_buf;

    /*Required for Example 3)*/
    //disp_drv.full_refresh = 1;

    /* Fill a memory array with a color if you have GPU.
     * Note that, in lv_conf.h you can enable GPUs that has built-in support in LVGL.
     * But if you have a different GPU you can use with this callback.*/
    //disp_drv.gpu_fill_cb = gpu_fill;

    /*Finally register the driver*/
    lv_disp_drv_register(&disp_drv);
}

/**********************
 *   STATIC FUNCTIONS
 **********************/

/*Initialize your display and the required peripherals.*/
static void disp_init(void)
{
    /*You code here*/
    ST7789_Init();
}

volatile bool disp_flush_enabled = true;

/* Enable updating the screen (the flushing process) when disp_flush() is called by LVGL
 */
void disp_enable_update(void)
{
    disp_flush_enabled = true;
}

/* Disable updating the screen (the flushing process) when disp_flush() is called by LVGL
 */
void disp_disable_update(void)
{
    disp_flush_enabled = false;
}

/*Flush the content of the internal buffer the specific area on the display
 *You can use DMA or any hardware acceleration to do this operation in the background but
 *'lv_disp_flush_ready()' has to be called when finished.*/
static void disp_flush(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    if(disp_flush_enabled) {
        uint32_t w = ( area->x2 - area->x1 + 1 );
        uint32_t h = ( area->y2 - area->y1 + 1 );
        ST7789_DrawImage(area->x1, area->y1, w, h, (uint16_t*)&color_p->full);
    }

    /*IMPORTANT!!!
     *Inform the graphics library that you are ready with the flushing*/
    lv_disp_flush_ready(disp_drv);
}


#ifdef USE_PC_SIMULATOR


/**
 * Print the memory usage periodically
 * @param param
 */
static void memory_monitor(lv_task_t *param) {
  (void)param; /*Unused*/

  lv_mem_monitor_t mon;
  lv_mem_monitor(&mon);
  printf("used: %6d (%3d %%), frag: %3d %%, biggest free: %6d\n",
         (int)mon.total_size - mon.free_size, mon.used_pct, mon.frag_pct,
         (int)mon.free_biggest_size);
}

/**
 * A task to measure the elapsed time for LVGL
 * @param data unused
 * @return never return
 */
static int tick_thread(void *data) {
  (void)data;

  while (1) {
    SDL_Delay(5);   /*Sleep for 5 millisecond*/
    lv_tick_inc(5); /*Tell LittelvGL that 5 milliseconds were elapsed*/
  }

  return 0;
}

static void hal_init_sdl(void) {
  /* Use the 'monitor' driver which creates window on PC's monitor to simulate a display*/
  monitor_init();

  /*Create a display buffer*/
  static lv_disp_buf_t disp_buf1;
  static lv_color_t buf1_1[LV_HOR_RES_MAX * LV_VER_RES_MAX];
  lv_disp_buf_init(&disp_buf1, buf1_1, NULL, LV_HOR_RES * LV_VER_RES);

  /*Create a display*/
  lv_disp_drv_t disp_drv;
  lv_disp_drv_init(&disp_drv); /*Basic initialization*/
  disp_drv.buffer = &disp_buf1;
  disp_drv.flush_cb = monitor_flush;
  lv_disp_drv_register(&disp_drv);

  /* Add the mouse as input device
   * Use the 'mouse' driver which reads the PC's mouse*/
  mouse_init();
  lv_indev_drv_t indev_drv;
  lv_indev_drv_init(&indev_drv); /*Basic initialization*/
  indev_drv.type = LV_INDEV_TYPE_POINTER;

  /*This function will be called periodically (by the library) to get the mouse position and state*/
  indev_drv.read_cb = mouse_read;
  lv_indev_t *mouse_indev = lv_indev_drv_register(&indev_drv);

#if 0
  /*Set a cursor for the mouse*/
  LV_IMG_DECLARE(mouse_cursor_icon); /*Declare the image file.*/
  lv_obj_t * cursor_obj = lv_img_create(lv_scr_act(), NULL); /*Create an image object for the cursor */
  lv_img_set_src(cursor_obj, &mouse_cursor_icon);           /*Set the image source*/
  lv_indev_set_cursor(mouse_indev, cursor_obj);             /*Connect the image  object to the driver*/
#endif
  /* Tick init.
   * You have to call 'lv_tick_inc()' in periodically to inform LittelvGL about
   * how much time were elapsed Create an SDL thread to do this*/
  SDL_CreateThread(tick_thread, "tick", NULL);

  /* Optional:
   * Create a memory monitor task which prints the memory usage in
   * periodically.*/
  lv_task_create(memory_monitor, 5000, LV_TASK_PRIO_MID, NULL);
}
#endif

void display_init()
{
    //初始化窗口栈
    InitWinStack();
    //初始化lvgl
    lv_init();

    //加载字体需要时间（尤其是woff2，如果是ttf，则影响不大），所以这里先加载字体再初始化显示，以免黑屏切换时间长）
    font_loading();

#ifdef USE_PC_SIMULATOR
    hal_init_sdl();
#elif SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_2P1_320X240 || SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_1P9_320X170
    lv_port_disp_init_spi();
#elif SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_RGB_4P3_480X272
#if 1
      if (0 != sstar_lv_init()) {
        printf("ERR: sstar_lv_init failed.\n");
        return;
    }
#endif
    //设置背光(放到此处是因为ssd初始化disp后会重新加载config.ini的PWM设置)
	init_backlight_module();
#endif
}


bool isSpiDevExist(int spiId)
{
   if(spiId==0)
   {
        return IsFileExist("/dev/spidev0.0");
   }
   else
   {
        return IsFileExist("/dev/spidev1.0");
   }
}

char* getFontPath()
{
    static char fontPath[256]={0};
    memset(fontPath,0,sizeof(fontPath));
    if(IsFileExist(FONT_PATH_TTF))
    {
        if(IsFileExist(FONT_PATH_WOFF2))
        {
            remove_file(FONT_PATH_WOFF2);
        }
        sprintf(fontPath,"%s",FONT_PATH_TTF);
    }
    else if(IsFileExist(FONT_PATH_WOFF2))
    {
        sprintf(fontPath,"%s",FONT_PATH_WOFF2);
    }
    return fontPath;
}

/*
1、检测到权限存在（包括设备类型）
2、检测到SPI设备节点存在
3、检测到字体文件存在
*/
bool checkDisplayRight()
{   
    #ifdef USE_PC_SIMULATOR
    return true;
    #endif

    #if !SUPPORT_DISPLAY
    return false;
    #endif

    //消防采集器C，不使用屏幕
    #if((CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_C) || (CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_C))
        return false;
    #endif

    int isValid=0;
    if(IS_EXTENSION_HAS_DISPLAY)
    {
        if(strlen(getFontPath()) > 0)
        {
            if(SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_RGB_4P3_480X272)
            {
                isValid=1;
            }
            else if(SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_1P9_320X170)
            {
                if(isSpiDevExist(1))
                {
                    isValid=1;
                }
            }
            if(SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_2P1_320X240)
            {
                if(isSpiDevExist(1))
                {
                    isValid=1;
                }
            }
        }
    }
    return isValid;
}


int getDisplayType()
{
    static int displayType=-1;
    if(displayType!=-1)
    {
        return displayType;
    }
    if(!checkDisplayRight())
    {
        displayType = DISPLAY_TYPE_NULL;
        return displayType;
    }

    displayType = SELECTED_DISPLAY_TYPE;
    
    return displayType;
}


bool isDisplayValid()
{
    int displayType=getDisplayType();
    return (displayType!=DISPLAY_TYPE_NULL);
}





void display_update_mainWin_external()
{
    if(!isDisplayValid())
        return;
    #if (AIPU_VERSION && SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_2P1_320X240)
    aipu_main_win_deviceInfo_update(0);
    #elif (YIHUI_VERSION)
    yihui_main_win_deviceInfo_update(0);
    #endif
}


void UI_UpdateSystemTime(int IsSelfcall)
{
    if(!isDisplayValid())
        return;

    char date[32]={0};
    int year=st_CurrentTime.year;
    int mon=st_CurrentTime.mon;
    int day=st_CurrentTime.day;
    int hour=st_CurrentTime.hour;
    int min=st_CurrentTime.min;
    if(year<2025)
        year=2025;
    if(mon<1)
        mon=1;
    if(mon>12)
        mon=12;
    if(day<1)
        day=1;
    if(day>31)
        day=31;
    if(hour<0)
        hour=0;
    if(hour>23)
        hour=23;
    if(min<0)
        min=0;
    if(min>59)
        min=59;
	sprintf(date,"%04d-%02d-%02d  %02d:%02d",year,mon,day,hour,min);

	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
	}
	int currentWin = GetCurrentWin();
	if(currentWin == WIN_MAIN || currentWin == WIN_UDISK)
	{
        if(control_head_date_label)
        {
            if(strcmp(lv_label_get_text(control_head_date_label),date))
            {
                lv_label_set_text(control_head_date_label, date);
            }
        }
	}
	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
	}
}







int dispGetVolume()
{
	if(g_media_source == SOURCE_NET_PAGING)
	{
		return pager_property.volume;
	}
	#if SUPPORT_SIP
	else if(g_media_source == SOURCE_SIP_CALLING)
	{
		return appData.voiceInfoSettings.callVolumeTemp;
	}
	#endif
	else
	{
		return g_system_volume;
	}
}


int dispSetVolume(int volume)
{
	int temp_volume=0;
	if(g_paging_status == PAGING_START)
	{   
		pager_property.volume = volume;
		pkg_query_current_status(NULL);

		printf("set g_paging_vol:%d\n", pager_property.volume);
	}
	#if SUPPORT_SIP
	else if(g_media_source == SOURCE_SIP_CALLING)	
	{
		appData.voiceInfoSettings.callVolumeTemp =volume;
		sem_sip_conf_send();
		pkg_query_current_status(NULL);
	}
	#endif
	else
	{
		temp_volume = g_system_volume;
		g_system_volume = volume;
		pkg_query_current_status(NULL);
		//if(g_system_volume != 0)
		g_pre_system_volume = g_system_volume;

		//如果是定时音源，那么记录此音量
		if( get_system_source() == SOURCE_TIMING)
		{
			g_timing_volume = g_system_volume;
		}
						
		if(temp_volume != g_system_volume)
		{
			save_sysconf(INI_SECTION_BASIC,"System_Volume");//主机设置音量保存
		}
	}
}


void dispSetvolume_By_add_min(bool isAdd)
{
	int temp_volume = 0;

	int step = 1;
	if(!isAdd)
	{
		step *=-1;
	}

	if(g_paging_status == PAGING_START)
	{
		if( step<0 && (pager_property.volume < abs(step)) )
		{
			pager_property.volume=0;
		}
		else{
			pager_property.volume += step;
		}
		if(pager_property.volume>100)
			pager_property.volume=100;
		pkg_query_current_status(NULL);
		//printf("set g_paging_vol:%d\n", pager_property.volume);
	}
	#if SUPPORT_SIP
	else if(g_media_source == SOURCE_SIP_CALLING)	
	{
		if( step<0 && (appData.voiceInfoSettings.callVolumeTemp < abs(step)) )
		{
			appData.voiceInfoSettings.callVolumeTemp=0;
		}
		else{
			appData.voiceInfoSettings.callVolumeTemp += step;
		}
		if(appData.voiceInfoSettings.callVolumeTemp>100)
			appData.voiceInfoSettings.callVolumeTemp=100;

		sem_sip_conf_send();
		pkg_query_current_status(NULL);
	}
	#endif
	else
	{
		temp_volume = g_system_volume;
		//如果是音量减，且当前系统音量已经比步进小了,那么音量为0
		if( step<0 && (g_system_volume < abs(step)) )
		{
			g_system_volume=0;
		}
		else{
			g_system_volume+=step;
		}
		if(g_system_volume>100)
			g_system_volume=100;
		pkg_query_current_status(NULL);
		g_pre_system_volume = g_system_volume;

		//如果是定时音源，那么记录此音量
		if( get_system_source() == SOURCE_TIMING)
		{
			g_timing_volume = g_system_volume;
		}
		
		if(temp_volume == g_system_volume)
		{
			return;
		}
		save_sysconf(INI_SECTION_BASIC,"System_Volume");//主机设置音量保存
	}
}

#if YIHUI_VERSION || USE_PC_SIMULATOR
void yihui_return_to_main_win(void)
{
    int currentWin=GetCurrentWin();
    if(currentWin == WIN_MAIN)
        return;
    if(currentWin==WIN_SETTING)
    {
        lv_obj_del(settings_all_cont);
        lv_obj_set_hidden(control_all_cont,false);
    }
    SetCurrentWin(WIN_MAIN);
    yihui_main_win_deviceInfo_update(1);
	UI_UpdateSystemTime(1);
}

void YH_Sar_Key_Press(int keyId)
{
    pthread_mutex_lock(&lvglMutex);
    int currentWin=GetCurrentWin();
    if(keyId==KEY_ID_MENU)
    {
        if(currentWin == WIN_MAIN)
        {
            yihui_settings_win_start();
        }
        else if(currentWin == WIN_SETTING)
        {
            yihui_return_to_main_win();
        }
    }
    else if(keyId == KEY_ID_PLAY)
    {
        #if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_F)
        if(fire_collector_info.trigger_status)
        {
            isForceStopFire = true;
            //发送给服务器
            Fire_Collector_Send_Trig_Status(0);
            g_media_source = SOURCE_NULL;
            //设置GPIO低电平
            Set_Gpio_Low(PAD_KEY9);
            //刷新界面
            pthread_mutex_unlock(&lvglMutex);
            display_update_mainWin_external();
            return;
        }
        #endif
    }
    pthread_mutex_unlock(&lvglMutex);
}
#endif