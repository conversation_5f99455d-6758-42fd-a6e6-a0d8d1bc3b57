#ifndef NETPAGER_WIN_THEME_H_
#define NETPAGER_WIN_THEME_H_

#include "lvgl/lvgl.h"

enum
{
	CONTROL_BUTTON_UDISK,
	CONTROL_BUTTON_STOP,
	CONTROL_BUTTON_SETTINGS,
	MAX_CONTROL_NUM
};
typedef struct
{
	lv_obj_t *obj;
	lv_img_dsc_t pic;
	lv_obj_t *label;
	char name[32];
}st_control_button_list;

extern st_control_button_list control_button_list[MAX_CONTROL_NUM];

LV_IMG_DECLARE(pic_zoneGroup);
LV_IMG_DECLARE(pic_selectAll);
LV_IMG_DECLARE(pic_cancel);
LV_IMG_DECLARE(pic_settings);
LV_IMG_DECLARE(pic_paging);
LV_IMG_DECLARE(pic_call);
LV_IMG_DECLARE(pic_headphones);
LV_IMG_DECLARE(pic_music);
LV_IMG_DECLARE(pic_udisk);
LV_IMG_DECLARE(pic_control_play);
LV_IMG_DECLARE(pic_control_pause);
LV_IMG_DECLARE(pic_stop);
LV_IMG_DECLARE(pic_volume);

//MUSICLIST
LV_IMG_DECLARE(pic_play);
LV_IMG_DECLARE(pic_preplay);
LV_IMG_DECLARE(pic_nextplay);
LV_IMG_DECLARE(pic_singlePlay);
LV_IMG_DECLARE(pic_singleRepeat);
LV_IMG_DECLARE(pic_sequencyPlay);
LV_IMG_DECLARE(pic_listLoop);
LV_IMG_DECLARE(pic_shufflePlay);

//LZY serverMusicList、udiskMusicList
LV_IMG_DECLARE(pic_upload);
LV_IMG_DECLARE(pic_delete);


LV_IMG_DECLARE(audioh_25);
LV_IMG_DECLARE(audiol_25);
LV_IMG_DECLARE(pic_back);
LV_IMG_DECLARE(pic_micMute);
LV_IMG_DECLARE(pic_up);
LV_IMG_DECLARE(pic_down);
LV_IMG_DECLARE(mic_on);
LV_IMG_DECLARE(mic_off);

LV_IMG_DECLARE(pic_enter);
LV_IMG_DECLARE(pic_quit);

LV_IMG_DECLARE(volumeWhite);
LV_IMG_DECLARE(volumeBlack);

LV_IMG_DECLARE(pic_previous);
LV_IMG_DECLARE(pic_next);

lv_color_t get_theme_color();
void init_control_button();

#endif