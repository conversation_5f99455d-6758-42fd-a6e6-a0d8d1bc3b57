/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> 
 * @Date: 2022-02-16 20:44:11 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-02-17 10:55:43
 */


#ifndef _MI_GPIO_H_
#define _MI_GPIO_H_

#define BASE_RIU_PA                 0x1F000000
#define PMSLEEP_BANK                0x000E00
#define PM_SAR_BANK                 0x001400
#define ALBANY1_BANK                0x151500
#define ALBANY2_BANK                0x151600
#define CHIPTOP_BANK                0x101E00
#define PADTOP_BANK                 0x103C00
#define PM_PADTOP_BANK              0x003F00
#define UTMI0_BANK                  0x142100
#define ETH_BANK                    0x003300
#define PM_PADTOP_BANK              0x003F00
#define CHIPTOP_BANK                0x101E00
#define PADTOP_BANK                 0x103C00
#define PADGPIO_BANK                0x103E00
#define PM_SAR_BANK                 0x001400
#define PMSLEEP_BANK                0x000E00
#define PM_GPIO_BANK                0x000F00

#define PAD_SR_GPIO07               0x07

#define PAD_SD_GPIO1                0x3C

#define PAD_GPIO0                   0x3D
#define PAD_GPIO1                   0x3E
#define PAD_GPIO2                   0x3F    //P19,Led_Ctrl1 Out
#define PAD_GPIO3                   0x40    //P19,Key1  In
#define PAD_GPIO4                   0x41    //P19,Led_Ctrl2 Out
#define PAD_GPIO5                   0x42    //P19,Key2  In
#define PAD_GPIO6                   0x43
#define PAD_GPIO7                   0x44
#define PAD_KEY1                    0x28    //P19,Ext1 In
#define PAD_KEY2                    0x29    //P19,Ext2 In
#define PAD_KEY3                    0x2A    //P19,Relay Ctrl1 Out
#define PAD_KEY4                    0x2B    //P19,Relay Ctrl2 Out,Other Audio Relay Switch
#define PAD_KEY5                    0x2C    //AMP shutdown
#define PAD_KEY6                    0x2D    //Emc relay
#define PAD_KEY7                    0x2E    //Ext Trigger Signal
#define PAD_KEY8                    0x2F    //UART2 TX
#define PAD_KEY9                    0x30    //UART2 RX
#define PAD_KEY10                   0x31    //Ext Trigger Mode ctrl，not used
#define PAD_KEY11                   0x32    //4G Reset ctrl
#define PAD_KEY12                   0x33    //4G Pwrkey ctrl
#define PAD_KEY13                   0x34    //USB SELECT


#define PAD_SD_D2                   0x3A
#define PAD_SD_D3                   0x39
#define PAD_SD_CMD                  0x38
#define PAD_SD_CLK                  0x37
#define PAD_SD_D0                   0x36
#define PAD_SD_D1                   0x35

#define GPIO_HIGH_VAL               0x7B
#define GPIO_LOW_VAL                0x78
#define GPIO_INPUT_VAL              0x7C

#define GET_BANK_ADDR(base,bank,offset)   ((base) + ((bank) << 1) + ((offset) << 2))

#define REG_W_WORD(addr,val)              {(*(unsigned short *)(addr)) = (unsigned short)(val);}
#define REG_W_WORD_MASK(addr,val,mask)    {(*(unsigned short *)(addr)) = ((*(unsigned short *)(addr)) & ~(mask)) | ((unsigned short)(val) & (mask));}
#define REG_R_WORD(addr)                  (*(unsigned short *)(addr))
#define REG_R_WORD_MASK(addr,mask)        (*(unsigned short *)(addr)) & (mask))

#define BIT8        0x100

#define BANK_TO_ADDR32(b) (b<<9)
#define REG_ADDR(riu_base,bank,reg_offset) ((riu_base)+BANK_TO_ADDR32(bank)+(reg_offset*4))












void Set_Gpio_Input(int offset);
int Get_Gpio_Value(int offset);

void Set_Gpio_High(int offset);
void Set_Gpio_Low(int offset);

void Enable_Amp_Output(int IsEnable);
void Enable_Signal_Output(int IsEnable);
void Ctrl_Relay_100V(int IsEnable);

void GPIO_OutPut_Server_Connection(int outPut);
void GPIO_OutPut_NetAudio_Signal(int outPut);
void GPIO_OutPut_NetAudio_Led(int outPut);
void Ctrl_Relay_EMC(int outPut);

void GPIO_Set_Call_Input();
int GPIO_Get_Call_Key_Value(int keyId);
int GPIO_Get_Call_Ext_Value(int ExtId);
void GPIO_OutPut_Call_Led(int ledId,int outPut);
void GPIO_OutPut_Call_Relay(int relayId,int outPut);
void Create_Poll_Test_Thread(void);

void GPIO_OutPut_Module4G_Main_Power(int isOn);
void GPIO_OutPut_Module4G_POWER(int isOn);
void GPIO_OutPut_Module4G_Reset(int isReset);
//4G模块LED控制（未连接上服务器灯灭，连接上服务器常亮，有音乐信号闪烁）
void GPIO_OUTPUT_Module4G_Led(int outPut);
void GPIO_set_usb_mutex(int enable);
//gpio设置触发模式（1:电平输入; 0:短路输入）
void GPIO_set_trigger_mode(int mode);
#if 0
int GPIO_Get_Module4G_Status();
void GPIO_Set_Module4G_Input();
#endif
void Set_Trigger_Led(int outPut);

//GPIO输出网络连接状态(音乐信号闪烁，用于P26、P28)
void GPIO_OutPut_LedStatusFlicker(int outPut);

#if YIHUI_VERSION
void YH_control_lineout(int enable);
#endif

#endif


