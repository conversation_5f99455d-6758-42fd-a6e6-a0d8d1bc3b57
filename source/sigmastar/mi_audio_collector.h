/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> 
 * @Date: 2022-06-24 16:18:56
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-06-24 16:18:56
 */

#ifndef _MI_AUDIO_COLLECTOR_H_
#define _MI_AUDIO_COLLECTOR_H_

#ifndef USE_PC_SIMULATOR

#include "mi_sys.h"
#include "mi_common_datatype.h"
#include "mi_ao.h"
#include "mi_ai.h"
#include "audio_par_common.h"
#include "AudioAedProcess.h"
#include "AudioProcess.h"

#if(IS_DEVICE_AUDIO_COLLECTOR || IS_DEVICE_AUDIO_MIXER || IS_DEVICE_PHONE_GATEWAY)

#if(IS_DEVICE_AUDIO_COLLECTOR)
#define AUDIO_COLLECTOR_CH1_VALID_THRESHOLD_DB  -36 //dB
#define AUDIO_COLLECTOR_CH2_VALID_THRESHOLD_DB  -45 //dB
#define AUDIO_COLLECTOR_CH3_VALID_THRESHOLD_DB  -45 //dB
#define AUDIO_COLLECTOR_CH4_VALID_THRESHOLD_DB  -45 //dB
#define COLLECTOR_SIGNAL_INVLID_TIMEOUT  15      //秒
#elif (IS_DEVICE_AUDIO_MIXER)
#define AUDIO_COLLECTOR_CH1_VALID_THRESHOLD_DB  -48 //dB
#define AUDIO_COLLECTOR_CH2_VALID_THRESHOLD_DB  -48 //dB
#define AUDIO_COLLECTOR_CH3_VALID_THRESHOLD_DB  -48 //dB
#define AUDIO_COLLECTOR_CH4_VALID_THRESHOLD_DB  -40 //dB
#define COLLECTOR_SIGNAL_INVLID_TIMEOUT  30      //秒
#elif (IS_DEVICE_PHONE_GATEWAY)
#define AUDIO_COLLECTOR_CH1_VALID_THRESHOLD_DB  -50 //dB
#define AUDIO_COLLECTOR_CH2_VALID_THRESHOLD_DB  -50 //dB
#define AUDIO_COLLECTOR_CH3_VALID_THRESHOLD_DB  -50 //dB
#define AUDIO_COLLECTOR_CH4_VALID_THRESHOLD_DB  -50 //dB
#define COLLECTOR_SIGNAL_INVLID_TIMEOUT  15      //秒
#endif

void mi_audio_write_mixer(const unsigned char *buffer, int count);
void mi_audio_out_collector_init(unsigned int sample_rate, unsigned char fmt, unsigned char channels);
void mi_audio_out_collector_deinit();
void mi_audio_in_collector_init(unsigned int sample_rate, unsigned char fmt, unsigned char channels);
void mi_audio_in_collector_deinit();

#endif
#endif
#endif
