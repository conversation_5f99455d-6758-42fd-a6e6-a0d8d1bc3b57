/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-03 17:40:51 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-09-04 16:48:03
 */

#ifndef USE_PC_SIMULATOR

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include "pthread.h"

#include "mi_functions.h"

static pthread_mutex_t mutex_sys_init=PTHREAD_MUTEX_INITIALIZER;

static int mi_sys_init_flag=0;

#define GET_REBOOT_TYPE_CMD "cat /sys/class/mstar/msys/REBOOT_TYPE"

void mi_sys_init()
{
    pthread_mutex_lock(&mutex_sys_init);
    if(mi_sys_init_flag)
    {
        pthread_mutex_unlock(&mutex_sys_init);
        return;
    }
    MI_S32 ret=MI_SYS_Init();
    if(MI_SUCCESS != ret)
    {
        printf("MI_SYS_Init err:0x%x\n", ret);
    }
    else
    {
        mi_sys_init_flag=1;
    }
    pthread_mutex_unlock(&mutex_sys_init);
}

void mi_sys_exit()
{
    pthread_mutex_lock(&mutex_sys_init);
    if(!mi_sys_init_flag)
    {
        pthread_mutex_unlock(&mutex_sys_init);
        return;
    }
    MI_S32 ret=MI_SYS_Exit();
    if(MI_SUCCESS != ret)
    {
        printf("MI_SYS_Exit err:0x%x\n", ret);
    }
    pthread_mutex_unlock(&mutex_sys_init);
}

MI_U64 sstar_get_chip_uuid()
{
    MI_U64 u64Uuid=0;
    MI_S32 s32Ret = MI_ERR_SYS_FAILED;
    s32Ret = MI_SYS_ReadUuid (&u64Uuid);
    if(!s32Ret)
    {
        printf("uuid: %llx\n",u64Uuid);
    }
    return u64Uuid;
}

/*获取ssd212启动类型
Reboot_Type: 1 (WDT)
Reboot_Type: 2 (SW)
Reboot_Type: 3 (HW)
*/
int sstar_get_reboot_type()
{
    int reboot_type = 3;

    char buffer[32];
    FILE *fp = popen(GET_REBOOT_TYPE_CMD, "r");
    if (!fp) {
        return reboot_type;
    }

    if (!fgets(buffer, sizeof(buffer), fp)) {
        pclose(fp);
        return reboot_type;
    }

    pclose(fp);

    if (strstr(buffer, "WDT")) {
        reboot_type = 1;
    } else if (strstr(buffer, "SW")) {
        reboot_type = 2;
    } else if (strstr(buffer, "HW")) {
        reboot_type = 3;
    }

    printf("sstar_get_reboot_type=%d\n",reboot_type);
    return reboot_type;
}

#endif