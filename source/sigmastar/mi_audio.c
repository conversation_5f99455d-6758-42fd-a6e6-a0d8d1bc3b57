/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-03 17:40:51 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2025-01-11 10:17:42
 */

#ifndef USE_PC_SIMULATOR

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <stdint.h>
#include <limits.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <semaphore.h>

#include "sysconf.h"
#include "mi_audio.h"

#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
#include "call_ring.h"

    #if SUPPORT_CODEC_G722
    #include "g722_encoder.h"
    #include "g722_decoder.h"
    #define G722_CALL_BUFFER_SIZE 128
    #endif
#endif

#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)

#if ENABLE_SPEEX_DENOISE
#include "speex/speex_preprocess.h"
#endif

#if ENABLE_WEBRTC_DENOISE
#include "webrtc_ns.h"
#endif

#define MI_AO_SAMPLE_PER_FRAME_NET_PLAY     1152    //1152/44100=26ms
#define MI_AO_SAMPLE_PER_FRAME_NET_PAGING   1024    //1024/44100=23ms
#define MI_AO_SAMPLE_PER_FRAME_NET_CALL     768     //512/16000=32ms
#define MI_AO_SAMPLE_PER_FRAME_LOCAL   960      
#define MI_AI_SAMPLE_PER_FRAME_LOCAL  480     //320/48000=6.7ms,MI_AO_SAMPLE_PER_FRAME_LOCAL=960 实测24ms；384/48000=8ms,MI_AO_SAMPLE_PER_FRAME_LOCAL=1152实测29ms
#define MI_AI_SAMPLE_PER_FRAME_CALL  256     //256/16000=16ms

#define USER_BUF_DEPTH      (4)
#define TOTAL_BUF_DEPTH     (8)


#define MAX_ADC_DATA_PKG_NUM    6
typedef struct
{
    int16_t adc0_data[MAX_ADC_DATA_PKG_NUM][MI_AI_SAMPLE_PER_FRAME_LOCAL];
    int16_t adc1_data[MAX_ADC_DATA_PKG_NUM][MI_AI_SAMPLE_PER_FRAME_LOCAL];
    int16_t adc2_data[MAX_ADC_DATA_PKG_NUM][MI_AI_SAMPLE_PER_FRAME_LOCAL];
    int16_t adc_data_len[MAX_ADC_DATA_PKG_NUM];
    int8_t adc_data_valid[MAX_ADC_DATA_PKG_NUM];
    int8_t write_pos;
    int8_t read_pos;
}_st_AdcData_Info;

_st_AdcData_Info st_AdcData_Info;


typedef struct AiChnPriv_s
{
    MI_AUDIO_DEV AiDevId;
    MI_AI_CHN AiChn;
    MI_S32 s32Fd;
    MI_U32 u32TotalSize;
    MI_U32 u32ChnCnt;
    pthread_t tid;
} AiChnPriv_t;

static AiChnPriv_t stAiChnPriv[MI_AUDIO_MAX_CHN_NUM];
pthread_t tid_local_write;
static MI_S32   AiChnFd[MI_AUDIO_MAX_CHN_NUM] = {[0 ... MI_AUDIO_MAX_CHN_NUM-1] = -1};


#define MI_DEFAULT_AO_DEV_ID    0
#define MI_DEFAULT_AI_DEV_ID    5       //AI_DEV_ID_ADC_0_1_2 (5)

#define MI_DEFAULT_AI_CHANNEL_CNT   4


enum
{
    AI_MODE_NULL,
    AI_MODE_MUISC_48K,
    AI_MODE_CALL_16K
};

int mi_ao_init_flag=0;
static int mi_ai_init_flag=0;   //0-AI未启动  1-音乐模式48K  2-对讲模式16K
static int mi_ao_channel_num=0;
static int mi_ai_channel_num=0;

unsigned int mi_ao_sampleRate=0;

static unsigned int mi_ao_totalByteNum=0;


//均衡器
#define EQ_FRAME_SIZE           128         //EQ每次处理的采样点个数
static char *mi_eq_workingBuffer = NULL;
EQ_HANDLE mi_eq_handle=NULL;
static int  mi_eq_init_flag=0;

static short mi_eq_table[129];

static int16_t mi_eq_remain_data[512];     //EQ处理剩下的数据
static int mi_eq_remain_samples=0;             //EQ处理剩下的长度(以short为单位)

//互斥锁
pthread_mutex_t mutex_audio_out=PTHREAD_MUTEX_INITIALIZER;
pthread_mutex_t mutex_audio_in=PTHREAD_MUTEX_INITIALIZER;
pthread_mutex_t mutex_audio_eq=PTHREAD_MUTEX_INITIALIZER;

extern int concentrated_write_need_wait;

int dac_sampers_cnt=0;


sem_t sem_aiReady;  //信号量-ai数据已准备好


void pkg_query_current_status(unsigned char *rxbuf);

void* mi_audio_local_write_thread();
void* mi_audio_call_write_thread();



void mi_audio_out_init(unsigned int sample_rate, unsigned char fmt, unsigned char channels)
{
    int sysSource = get_system_source();
    //如果开启了EQ，才强制采样率转换
    if(mi_eq_init_flag)     //dsp_eq_info.eq_mode
    {
        //当处于寻呼、采集音源时不转采样率(这两种音源不支持EQ)
        if( !( (sysSource >= SOURCE_AUDIO_COLLECTOR_BASE && sysSource <= SOURCE_AUDIO_COLLECTOR_MAX) || sysSource == SOURCE_NET_PAGING || sysSource == SOURCE_CALL ) )
        {
            #if ENABLE_LIBSAMPLERATE_SRC
            sample_rate=SRC_LIBSAMPLE_RATE;
            #elif ENABLE_SOXR_SRC
            sample_rate=SRC_SOXR_RATE;
            #endif
        }
    }

    mi_sys_init();
    //退出之前的audio
    mi_audio_out_deinit();

    dac_sampers_cnt=0;

    pthread_mutex_lock(&mutex_audio_out);
    MI_S32 ret = MI_SUCCESS;
    MI_AUDIO_Attr_t stAoSetAttr, stAoGetAttr;
    MI_S32 s32AoGetVolume;
    MI_AO_ChnParam_t stAoChnParam;
    MI_U32 u32DmaBufSize;
    MI_BOOL bInitAdec = FALSE, bInitRes = FALSE;

    MI_AUDIO_DEV AoDevId = MI_DEFAULT_AO_DEV_ID;
    MI_AO_CHN AoChn = 0;


    memset(&stAoSetAttr, 0x0, sizeof(MI_AUDIO_Attr_t));
    stAoSetAttr.eBitwidth = E_MI_AUDIO_BIT_WIDTH_16;
    stAoSetAttr.eWorkmode = E_MI_AUDIO_MODE_I2S_MASTER;
    stAoSetAttr.WorkModeSetting.stI2sConfig.bSyncClock = FALSE;
    stAoSetAttr.WorkModeSetting.stI2sConfig.eFmt = E_MI_AUDIO_I2S_FMT_I2S_MSB;
    stAoSetAttr.WorkModeSetting.stI2sConfig.eMclk = E_MI_AUDIO_I2S_MCLK_0;
    
    if( sysSource == SOURCE_NULL || sysSource == SOURCE_AUX )
    {
        stAoSetAttr.u32PtNumPerFrm = MI_AO_SAMPLE_PER_FRAME_LOCAL;
    }
    else if(sysSource == SOURCE_CALL)
    {
        stAoSetAttr.u32PtNumPerFrm = MI_AO_SAMPLE_PER_FRAME_NET_CALL;
    }
    else if(sysSource == SOURCE_NET_PAGING || (sysSource >= SOURCE_AUDIO_COLLECTOR_BASE && sysSource <= SOURCE_AUDIO_COLLECTOR_MAX))
    {
        stAoSetAttr.u32PtNumPerFrm = MI_AO_SAMPLE_PER_FRAME_NET_PAGING;
    }
    else
    {
        stAoSetAttr.u32PtNumPerFrm = MI_AO_SAMPLE_PER_FRAME_NET_PLAY;
    }
    stAoSetAttr.u32ChnCnt = 1;

    mi_ao_channel_num=channels;
    mi_ao_sampleRate=sample_rate;

#if 0
    if (channels == 2)
    {
        stAoSetAttr.eSoundmode = E_MI_AUDIO_SOUND_MODE_STEREO;
    }
    else if (channels == 1)
    {
        stAoSetAttr.eSoundmode = E_MI_AUDIO_SOUND_MODE_MONO;
    }
#endif
    stAoSetAttr.eSoundmode = E_MI_AUDIO_SOUND_MODE_STEREO;  //永远保持双通道输出

    stAoSetAttr.eSamplerate = sample_rate;

    

    ret = MI_AO_SetPubAttr(AoDevId, &stAoSetAttr);

    if(MI_SUCCESS != ret)
    {
        printf("set ao %d attr err:0x%x\n", AoDevId, ret);
        pthread_mutex_unlock(&mutex_audio_out);
        return;
    }

    ret = MI_AO_GetPubAttr(AoDevId, &stAoGetAttr);
    if(MI_SUCCESS != ret)
    {
        printf("get ao %d attr err:0x%x\n", AoDevId, ret);
        pthread_mutex_unlock(&mutex_audio_out);
        return;
    }
    /* enable ao device */
    ret = MI_AO_Enable(AoDevId);
    if(MI_SUCCESS != ret)
    {
        printf("enable ao dev %d err:0x%x\n", AoDevId, ret);
        pthread_mutex_unlock(&mutex_audio_out);
        return;
    }
    ret = MI_AO_EnableChn(AoDevId, AoChn);
    if (MI_SUCCESS != ret)
    {
        printf("enable ao dev %d chn %d err:0x%x\n", AoDevId, AoChn, ret);
        pthread_mutex_unlock(&mutex_audio_out);
        return;
    }
    printf("mi_audio_out_init succeed:%d...\n",sample_rate);

    MI_AO_SetVolume(AoDevId, AoChn, 0, E_MI_AO_GAIN_FADING_OFF);

    mi_ao_init_flag=1;

    pthread_mutex_unlock(&mutex_audio_out);
}


void mi_audio_out_deinit()
{
    pthread_mutex_lock(&mutex_audio_out);
    MI_S32 ret;
    MI_AUDIO_DEV AoDevId = MI_DEFAULT_AO_DEV_ID;
    MI_AO_CHN AoChn = 0;
    if(mi_ao_init_flag)
    {
        //ExecFuncNoExit(MI_AO_DisableChn(0, 0), MI_SUCCESS,ret);
        //ExecFuncNoExit(MI_AO_Disable(0), MI_SUCCESS,ret);
#if 0
        /* get chn stat */  
        MI_AO_ChnState_t stStatus;
        ret = MI_AO_QueryChnStat(AoDevId, AoChn, &stStatus);  
        if (MI_SUCCESS != ret)  
        {  
            printf("query chn status ao dev %d chn %d err:0x%x\n", AoDevId, AoChn, ret);   
        }  
        
    /* clear chn buf */  
        ret = MI_AO_ClearChnBuf(AoDevId, AoChn);  
        if (MI_SUCCESS != ret)  
        {
            printf("clear chn buf ao dev %d chn %d err:0x%x\n", AoDevId, AoChn, ret);  
        }
#endif

        /* clear chn buf */  
        ret = MI_AO_ClearChnBuf(AoDevId, AoChn);  
        if (MI_SUCCESS != ret)  
        {  
            printf("clear chn buf ao dev %d chn %d err:0x%x\n", AoDevId, AoChn, ret);  
            return;  
        }  

        ret = MI_AO_DisableChn(AoDevId, AoChn);
        if(MI_SUCCESS != ret)
        {
            printf("disable ao dev %d chn %d err:0x%x\n", 0, 0, ret);
            pthread_mutex_unlock(&mutex_audio_out);
            return;
        }
        ret = MI_AO_Disable(AoDevId);
        if (MI_SUCCESS != ret)
        {
            printf("disable ao dev %d err:0x%x\n", 0, ret);
            pthread_mutex_unlock(&mutex_audio_out);
            return;
        }
        mi_ao_init_flag=0;
        mi_ao_totalByteNum=0;

        //重新初始化EQ（重要），否则切换到本地会有冲击，或者使用IaaEq_Reset
        if(mi_eq_init_flag)
        {
            mi_audio_eq_init(1);
        }
        printf("mi_audio_out_deinit ok...\n");
    }
    pthread_mutex_unlock(&mutex_audio_out);
}










extern pthread_mutex_t mutex_system_source;


static void* aiGetChnPortBuf(void* data)
{
    AiChnPriv_t* priv = (AiChnPriv_t*)data;
    MI_AUDIO_Frame_t stAiChFrame;
    MI_AUDIO_AecFrame_t stAecFrame;
    MI_S32 s32Ret;
    struct timeval tv_before, tv_after;
    MI_S64 before_us, after_us;
    MI_U32 u32ChnIdx, u32ChnIdxStart,u32ChnIdxEnd;

    memset(&stAiChFrame, 0, sizeof(MI_AUDIO_Frame_t));
    memset(&stAecFrame, 0, sizeof(MI_AUDIO_AecFrame_t));

    int can_play=0;
    while(mi_ai_init_flag)
    {
        //gettimeofday(&tv_before, NULL);
        u32ChnIdxStart = 0;
        u32ChnIdxEnd = priv->u32ChnCnt;
        int u32_len=0;
        for (u32ChnIdx = u32ChnIdxStart; u32ChnIdx < u32ChnIdxEnd; u32ChnIdx++)
        {
            s32Ret = MI_AI_GetFrame(priv->AiDevId, u32ChnIdx, &stAiChFrame, NULL, -1);
            if(u32ChnIdx == 0)
                memcpy(st_AdcData_Info.adc0_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            else if(u32ChnIdx == 1)
                memcpy(st_AdcData_Info.adc1_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            else if(u32ChnIdx == 2)
                memcpy(st_AdcData_Info.adc2_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            u32_len = stAiChFrame.u32Len[0];
            MI_AI_ReleaseFrame(priv->AiDevId, u32ChnIdx,  &stAiChFrame,  NULL);
        }

        if (MI_SUCCESS == s32Ret)
        {
            //printf("u32=%d\n",u32_len);
            #if 1
            st_AdcData_Info.adc_data_len[st_AdcData_Info.write_pos] = u32_len;
            st_AdcData_Info.adc_data_valid[st_AdcData_Info.write_pos] = 1;
            st_AdcData_Info.write_pos++;
            if(st_AdcData_Info.write_pos>=MAX_ADC_DATA_PKG_NUM)
            {
                st_AdcData_Info.write_pos=0;
            }
            #endif
            #if 0
            //第一个包延迟发送，避免卡顿
            if(can_play<1) //first can_play=0,enter this=1
            {
               can_play++;
            }
            else
            #endif
            {
                sem_post(&sem_aiReady);
            }
            //pthread_mutex_unlock(&mutex_system_source);
            
            //printf("write: %x,len: %d\n",stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            //write(priv->s32Fd, stAiChFrame.apVirAddr[0], stAiChFrame.u32Len[0]);
            #if 0
            gettimeofday(&tv_after, NULL);
            before_us = tv_before.tv_sec * 1000000 + tv_before.tv_usec;
            after_us = tv_after.tv_sec * 1000000 + tv_after.tv_usec;
            //if (after_us - before_us > 10 * 1000)
            {
                //printf("Chn:%d,len=%d,cost time:%lldus = %lldms.\n", priv->AiChn, stAiChFrame.u32Len[0], after_us - before_us, (after_us - before_us) / 1000);
            }
            priv->u32TotalSize += stAiChFrame.u32Len[0];
            {
                //printf("Chn:%d,len=%d,cost time:%lldus = %lldms.\n");
            }
            #endif
        }
        else
        {
            printf("Dev%dChn%d get frame failed!!!error:0x%x\n", priv->AiDevId, priv->AiChn, s32Ret);
        }
    }
    return NULL;
}

#if 1
void mi_audio_in_init(unsigned int sample_rate, unsigned char fmt, unsigned char channels)
{
    pthread_mutex_lock(&mutex_audio_in);
    //判断是否需要再次初始化
    if(mi_ai_init_flag == AI_MODE_MUISC_48K && sample_rate == 48000)
    {
        printf("already AI_MODE_MUISC_48K,return!\n");
        pthread_mutex_unlock(&mutex_audio_in);
        return;
    }
    else if(mi_ai_init_flag == AI_MODE_CALL_16K && sample_rate == 16000)
    {
        printf("already AI_MODE_CALL_16K,return!\n");
        pthread_mutex_unlock(&mutex_audio_in);
        return;
    }
    mi_sys_init();
    //退出之前的audio
    mi_audio_in_deinit();

    sem_init(&sem_aiReady,0,0);

    MI_S32 ret = MI_SUCCESS;
    MI_AUDIO_Attr_t stAiSetAttr, stAiGetAttr;
    MI_SYS_ChnPort_t    stAiChnOutputPort0[MI_AUDIO_MAX_CHN_NUM];
    MI_AI_ChnParam_t stAiChnParam[MI_AUDIO_MAX_CHN_NUM];
    MI_BOOL bInitAdec = FALSE, bInitRes = FALSE;

    MI_AUDIO_DEV AiDevId = MI_DEFAULT_AI_DEV_ID;
    MI_AO_CHN AiChn = 0;

    MI_U32  u32ChnIdx;

    memset(&stAiSetAttr, 0x0, sizeof(MI_AUDIO_Attr_t));
    stAiSetAttr.eBitwidth = E_MI_AUDIO_BIT_WIDTH_16;
    if(sample_rate == 48000)
        stAiSetAttr.u32PtNumPerFrm = MI_AI_SAMPLE_PER_FRAME_LOCAL;
    else if(sample_rate == 16000)
        stAiSetAttr.u32PtNumPerFrm = MI_AI_SAMPLE_PER_FRAME_CALL;
    stAiSetAttr.u32ChnCnt = MI_DEFAULT_AI_CHANNEL_CNT;
    stAiSetAttr.eSoundmode = E_MI_AUDIO_SOUND_MODE_MONO;
    stAiSetAttr.eSamplerate = sample_rate;


    ret = MI_AI_SetPubAttr(AiDevId, &stAiSetAttr);

    if(MI_SUCCESS != ret)
    {
        printf("set ao %d attr err:0x%x\n", AiDevId, ret);
        pthread_mutex_unlock(&mutex_audio_in);
        return;
    }

    ret = MI_AI_GetPubAttr(AiDevId, &stAiGetAttr);
    if(MI_SUCCESS != ret)
    {
        printf("get ai %d attr err:0x%x\n", AiDevId, ret);
        pthread_mutex_unlock(&mutex_audio_in);
        return;
    }
    /* enable ai device */
    ret = MI_AI_Enable(AiDevId);
    if(MI_SUCCESS != ret)
    {
        printf("enable ai dev %d err:0x%x\n", AiDevId, ret);
        pthread_mutex_unlock(&mutex_audio_in);
        return;
    }

    mi_ai_init_flag=(sample_rate == 48000)?AI_MODE_MUISC_48K:AI_MODE_CALL_16K;

    memset(&stAiChnParam, 0x0, sizeof(MI_AI_ChnParam_t));

    for(u32ChnIdx=0;u32ChnIdx<stAiSetAttr.u32ChnCnt;u32ChnIdx++)
    {
        stAiChnParam[u32ChnIdx].stChnGain.bEnableGainSet = TRUE;
        if(u32ChnIdx == 0)      //LINE
        {
            stAiChnParam[u32ChnIdx].stChnGain.s16FrontGain = 2;    //0dB
            stAiChnParam[u32ChnIdx].stChnGain.s16RearGain = 0;
        }
        else if(u32ChnIdx == 1) //NetSpeakerD/P28:LOCAL MIC    NetSpeakerE:REFER ECHO
        {
            #if YIHUI_VERSION
            stAiChnParam[u32ChnIdx].stChnGain.s16FrontGain = 2;    //0dB

            #else

                #if (IS_DEVICE_DECODER_TERMINAL)
                stAiChnParam[u32ChnIdx].stChnGain.s16FrontGain = 5;    //9dB,20mv-60mv
                #elif(IS_DEVICE_INTERCOM_TERMINAL)
                stAiChnParam[u32ChnIdx].stChnGain.s16FrontGain = 2;    //0dB为250mv,250*1.5=375 此处增益需要调整，否则回声太大AEC会消掉原声
                #endif

            #endif

            stAiChnParam[u32ChnIdx].stChnGain.s16RearGain = 0;
        }
        else if(u32ChnIdx == 2 || u32ChnIdx == 3)   //NetSpeakerD:Remote MIC or environment monitor NetSpeakerE:LOCAL MIC     P28:REFER ECHO
        {
            #if (IS_DEVICE_DECODER_TERMINAL)
            stAiChnParam[u32ChnIdx].stChnGain.s16FrontGain = 2;    //0dB
            #elif(IS_DEVICE_INTERCOM_TERMINAL)
            if(mi_ai_init_flag == AI_MODE_MUISC_48K)
                stAiChnParam[u32ChnIdx].stChnGain.s16FrontGain = 5;    //18dB,15mv-120mv，后级13dB，4.4倍 528mV
            else if(mi_ai_init_flag == AI_MODE_CALL_16K)
                stAiChnParam[u32ChnIdx].stChnGain.s16FrontGain = 5;    //18dB,15mv-120mv，后级13dB，4.4倍 528mV
            #endif
            stAiChnParam[u32ChnIdx].stChnGain.s16RearGain = 0;
        }
    }

    for(u32ChnIdx=0;u32ChnIdx<stAiSetAttr.u32ChnCnt;u32ChnIdx++)
    {
        stAiChnPriv[u32ChnIdx].AiChn = u32ChnIdx;
        stAiChnPriv[u32ChnIdx].AiDevId = AiDevId;
        stAiChnPriv[u32ChnIdx].s32Fd = AiChnFd[u32ChnIdx];
        stAiChnPriv[u32ChnIdx].u32ChnCnt = MI_DEFAULT_AI_CHANNEL_CNT == 4?3:MI_DEFAULT_AI_CHANNEL_CNT; 
        stAiChnPriv[u32ChnIdx].u32TotalSize = 0;

        MI_AI_SetChnParam( AiDevId,  u32ChnIdx, &stAiChnParam[u32ChnIdx]);

        if(u32ChnIdx<stAiChnPriv[u32ChnIdx].u32ChnCnt)
        {
            stAiChnOutputPort0[u32ChnIdx].eModId = E_MI_MODULE_ID_AI;
            stAiChnOutputPort0[u32ChnIdx].u32DevId = AiDevId;
            stAiChnOutputPort0[u32ChnIdx].u32ChnId = u32ChnIdx;
            stAiChnOutputPort0[u32ChnIdx].u32PortId = 0;
            
            //ExecFunc(MI_AI_SetVqeVolume(AiDevId, u32ChnIdx, s32AiVolume), MI_SUCCESS);
            MI_SYS_SetChnOutputPortDepth(&stAiChnOutputPort0[u32ChnIdx], USER_BUF_DEPTH, TOTAL_BUF_DEPTH);
        }

        MI_AI_EnableChn(AiDevId, u32ChnIdx);
        if(u32ChnIdx==0)
        {
            struct sched_param sched3;
            sched3.sched_priority = 99;
            if(mi_ai_init_flag == AI_MODE_MUISC_48K)
            {
                pthread_create(&tid_local_write, NULL, mi_audio_local_write_thread, NULL);
            }
            #if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
            else if(mi_ai_init_flag == AI_MODE_CALL_16K)
            {
                pthread_create(&tid_local_write, NULL, mi_audio_call_write_thread, NULL);
            }
            #endif
            
            pthread_create(&stAiChnPriv[u32ChnIdx].tid, NULL, aiGetChnPortBuf, &stAiChnPriv[u32ChnIdx]);
            //pthread_setname_np(stAiChnPriv[u32ChnIdx].tid,"AiThread");
            pthread_setschedparam(tid_local_write, SCHED_RR,  &sched3);
            pthread_setschedparam(stAiChnPriv[u32ChnIdx].tid, SCHED_RR,  &sched3);
        }
        //pthread_create(&stAiChnPriv[u32ChnIdx].tid, NULL, aiGetChnPortBuf, &stAiChnPriv[u32ChnIdx]);
    }


    printf("mi_audio_in_init succeed...\n");

    pthread_mutex_unlock(&mutex_audio_in);
}


void mi_audio_in_deinit()
{
    MI_S32 ret;
    if(mi_ai_init_flag)
    {
        MI_U32 u32ChnIdx, u32ChnIdxStart,u32ChnIdxEnd;
        u32ChnIdxStart = 0;
        u32ChnIdxEnd = MI_DEFAULT_AI_CHANNEL_CNT;

        mi_ai_init_flag=0;
        sem_post(&sem_aiReady);

        pthread_join(tid_local_write, NULL);
        pthread_join(stAiChnPriv[0].tid, NULL);

        memset(&st_AdcData_Info,0,sizeof(st_AdcData_Info));

        for (u32ChnIdx = u32ChnIdxStart; u32ChnIdx < u32ChnIdxEnd; u32ChnIdx++)
        {
            ret = MI_AI_DisableChn(MI_DEFAULT_AI_DEV_ID, u32ChnIdx);
            if(MI_SUCCESS != ret)
            {
                printf("disable ai dev %d chn %d err:0x%x\n", 0, 0, ret);
                return;
            }
        }
        ret = MI_AI_Disable(MI_DEFAULT_AI_DEV_ID);
        if (MI_SUCCESS != ret)
        {
            printf("disable ai dev %d err:0x%x\n", 0, ret);
            return;
        }

        sem_destroy(&sem_aiReady);
        printf("mi_audio_in_deinit ok...\n");
    }
}
#endif



int Calculate_amplitude(int16_t* pcmData, int sampleCnt)
{
    if (sampleCnt > 0){
        int sum = 0;
        for (int i = 0; i < sampleCnt; i++){
            sum += abs(pcmData[i]);
        }
        sum/=sampleCnt;
        return sum;
   }
   else
        return 0;
}



//音量表中数据表示的是音频通路数字部分的Gain值
//4095表示0dB,为0时表示Mute。音量可调整增益表中只做负增益
//需要正增益设置每个source源的预增益
//两级音量之间的计算公式为 "20*log(Vol1/Vol2)"，单位dB
const uint16_t mSysVol[100 + 1] =
{
	/* 0-100级音量控制, 0.25dB等级 */
	0,
	5, 10, 18, 27, 38, 50, 63, 79, 94, 112, /*-31.25dB*/
	126, 141, 135, 178, 199, 217, 237, 258, 282, 307, /*-22.5dB*/
	325, 345, 365, 387, 410, 434, 460, 487, 516, 546, /*-17.5dB*/
	562, 579, 595, 613, 631, 649, 668, 688, 708, 728, /*-15dB*/
	750, 772, 794, 817, 841, 866, 891, 917, 944, 971, /*-12.5db*/
	1000, 1029, 1059, 1090, 1122, 1154, 1188, 1223, 1259, 1295, /*-10db*/
	1333, 1372, 1412, 1453, 1496, 1539, 1584, 1631, 1678, 1727, /*-7.5dB*/
	1778, 1830, 1883, 1938, 1995, 2053, 2113, 2175, 2238, 2303, /*-5dB*/
	2371, 2440, 2511, 2584, 2660, 2738, 2817, 2900, 2984, 3072, /*-2.5dB*/
	3161, 3254, 3349, 3446, 3547, 3651, 3757, 3867, 3980, 4095/*0db*/
//	/*32级音量控制*/
//	0/*-72db*/,
//	3/*-56db*/,		6/*-56db*/,		15/*-49db*/,	26/*-44db*/,	41/*-40db*/,	65/*-36db*/,	103/*-32db*/,	145/*-29db*/,
//	205/*-26db*/,	258/*-24db*/,	325/*-22db*/,	410/*-20db*/,	460/*-19db*/,	516/*-18db*/,	576/*-17db*/,	649/*-16db*/,
//	728/*-15db*/,	817/*-14db*/,	917/*-13db*/,	1029/*-12db*/,	1154/*-11db*/,	1295/*-10db*/,	1453/*-9db*/,	1631/*-8db*/,
//	1830/*-7db*/,	2053/*-6db*/,	2303/*-5db*/,	2584/*-4db*/,	2900/*-3db*/,	3254/*-2db*/,	3651/*-1db*/,	4095/*0db*/
};


int16_t  buffer_stereo[16384]={0};
int16_t  eq_stereo[16384]={0};

extern int amp_init_ok;
extern int adc_signal_can_detect;

void mi_audio_write(const unsigned char *buffer, int count)
{
    if(!mi_ao_init_flag)
    {
        return;
    }
    if(count == 0)
        return;

    int curMediaSource = g_media_source;

    //printf("mi_audio_write:%d...\n",count);
    MI_AUDIO_Frame_t stAoSendFrame;
    memset(&stAoSendFrame, 0x0, sizeof(MI_AUDIO_Frame_t));
    MI_AUDIO_DEV AoDevId = MI_DEFAULT_AO_DEV_ID;
    int i=0;
    //char *bufferLC=(char *)malloc(count);
    //char *bufferRC=(char *)malloc(count);
    
    int16_t *buffer_pcm_16;
    int buffer_len;
    int SamplesPreFrame=0;

    if(g_media_source == SOURCE_API_TTS_MUSIC)
    {
        if(g_ApiPlayType == API_PLAY_TTS)
        {
            mi_ao_channel_num=1;
        }
        if(g_ApiPlayType == API_PLAY_URL)
        {
            mi_ao_channel_num=2;
        }
    }
    if(g_isPlayIPTone)
    {
        mi_ao_channel_num=1;
    }

    if(mi_ao_channel_num == 2)//立体声
    {
        buffer_len=count;
        SamplesPreFrame=buffer_len/2/2;
        memcpy(buffer_stereo,buffer,buffer_len);
        buffer_pcm_16=buffer_stereo;
    }
    else
    {
        buffer_len=count*2;
        SamplesPreFrame=buffer_len/2/2;
        buffer_pcm_16=(int16_t *)buffer;
        //单声道转双声道
        for(i=0;i<SamplesPreFrame;i++)
        {
            buffer_stereo[2 * i + 0] =  buffer_pcm_16[i];
            buffer_stereo[2 * i + 1] =  buffer_pcm_16[i];
        }
        buffer_pcm_16=buffer_stereo;
    }

    int sysSource = get_system_source();
    //printf("dac0_db=%d\n",Calculate_amplitude(buffer_pcm_16,SamplesPreFrame*2));
    int t_sysvol=mSysVol[(unsigned int)(g_system_volume*g_sub_volume*0.01)];
    if(g_paging_status == PAGING_START)
    {
        t_sysvol=mSysVol[(unsigned int)(pager_property.volume*g_sub_volume*0.01)];
    }

    if(g_exist_volumeControl_device)
    {
        if(sysSource !=SOURCE_NET_PAGING && sysSource!=SOURCE_FIRE_ALARM)
        {
            t_sysvol=mSysVol[(unsigned int)(g_system_volume*g_volumeCD_net_volume*0.01)];
        }
    }

    if(g_ApiPlayType)
    {
        if(g_ApiPlayType == API_PLAY_TTS)
        {
            t_sysvol=mSysVol[(unsigned int)(curTTSParm.m_nVolume*g_sub_volume*0.01)];
        }
        else if(g_ApiPlayType == API_PLAY_URL)
        {
            t_sysvol=mSysVol[(unsigned int)(curUrlPlayParm.m_nVolume*g_sub_volume*0.01)];
        }
    }

    if(g_isPlayIPTone)
    {
        t_sysvol=mSysVol[(unsigned int)(50)];
    }

    //如果是网络音源（除寻呼、采集外），需要先衰减10dB，与之前的控制设备兼容
    if( sysSource != SOURCE_NET_PAGING && !(sysSource >= SOURCE_AUDIO_COLLECTOR_BASE && sysSource <= SOURCE_AUDIO_COLLECTOR_MAX)\
        &&  sysSource != SOURCE_AUDIO_MIXED && sysSource != SOURCE_PHONE_GATEWAY)
    {
        for(i=0; i<SamplesPreFrame; i++)
        {
            //1295为-10dB
            buffer_pcm_16[2 * i + 0] = limit_value_16bit( ((((int32_t)buffer_pcm_16[2 * i + 0]) * 1295 + 2048) >> 12) );
            buffer_pcm_16[2 * i + 1] = limit_value_16bit( ((((int32_t)buffer_pcm_16[2 * i + 1]) * 1295 + 2048) >> 12) );
        }
    }

    #if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
    //如果此时处于对讲音源，代表是在响铃状态（因为对讲中不会进到此函数）
    if(sysSource == SOURCE_CALL)
    {
        t_sysvol = mSysVol[50];  //default ring volume
        if(g_system_language == ENGLISH)
            t_sysvol = mSysVol[60];  //default ring volume
    }
    #endif

    for(i=0; i<SamplesPreFrame; i++)
    {
        buffer_pcm_16[2 * i + 0] = limit_value_16bit( (((((int32_t)buffer_pcm_16[2 * i + 0]) * t_sysvol + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L] + 2048) >> 12 );
        buffer_pcm_16[2 * i + 1] = limit_value_16bit( (((((int32_t)buffer_pcm_16[2 * i + 1]) * t_sysvol + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R] + 2048) >> 12 );
    }


    pthread_mutex_lock(&mutex_audio_eq);
    if(mi_eq_init_flag && mi_ao_sampleRate == 48000)
    {
        //判断需要处理多少次EQ，一次只能处理128个采样点

        //printf("Pre:mi_eq_remain_samples=%d\n",mi_eq_remain_samples);

        memcpy(eq_stereo,mi_eq_remain_data,mi_eq_remain_samples*2*2);                 //先将之前剩下的数据放到前面
        memcpy(eq_stereo+mi_eq_remain_samples*2,buffer_pcm_16,SamplesPreFrame*2*2);     //将新的数据放到后面
        int eq_count=(SamplesPreFrame+mi_eq_remain_samples)/EQ_FRAME_SIZE;
        mi_eq_remain_samples =(SamplesPreFrame+mi_eq_remain_samples)%EQ_FRAME_SIZE;
        int process_samples = eq_count*EQ_FRAME_SIZE;       //实际需要处理的长度(字节)
        memcpy(mi_eq_remain_data,(eq_stereo+process_samples*2),mi_eq_remain_samples*2*2);  //将剩下未处理的数据放入data，以便下次使用

        //printf("buffer_count=%d,eq_count=%d\n",count,eq_count);

        for(i=0;i<eq_count;i++)
        {
            int ret = IaaEq_Run(mi_eq_handle, eq_stereo+i*EQ_FRAME_SIZE*2);
            if(ret)
            {
                printf("IaaEq_Run failed !\n");
            }
        }

        #if AUDIO_PHASE_INVERT
        PhaseInvert(eq_stereo,process_samples*2,IS_AMP_WITH_AUDIO_DRIVER?PHASE_INVERT_STERO:PHASE_INVERT_RIGHT);
        #endif

        stAoSendFrame.u32Len[0] = process_samples*2*2;
        stAoSendFrame.apVirAddr[0] = eq_stereo;
        stAoSendFrame.apVirAddr[1] = NULL;

        //printf("Cur:eq_count=%d,process_samples=%d,mi_eq_remain_samples=%d\n",eq_count,process_samples,mi_eq_remain_samples);
    }
    else
    {
        #if AUDIO_PHASE_INVERT
        PhaseInvert(buffer_pcm_16,buffer_len/2,IS_AMP_WITH_AUDIO_DRIVER?PHASE_INVERT_STERO:PHASE_INVERT_RIGHT);
        #endif

        stAoSendFrame.u32Len[0] = buffer_len;
        stAoSendFrame.apVirAddr[0] = buffer_pcm_16;
        stAoSendFrame.apVirAddr[1] = NULL;
    }
    pthread_mutex_unlock(&mutex_audio_eq);


    #if 0
    else
    {
        //分离左右声道数据
        int lc_index=0,rc_index=0;
        int lc=1;
        for(i=0;i<count;i+=2)
        {
            if(lc)
            {
                bufferLC[lc_index++] = buffer[i];
                bufferLC[lc_index++] = buffer[i+1];
                lc=0;
            }
            else
            {
                bufferRC[rc_index++] = buffer[i];
                bufferRC[rc_index++] = buffer[i+1];
                lc=1;
            }
        }
        printf("lc_index=%d,rc_index=%d\n",lc_index,rc_index);
        stAoSendFrame.u32Len[0] = lc_index;
        stAoSendFrame.u32Len[1] = rc_index;
        stAoSendFrame.apVirAddr[0] = bufferLC;
        stAoSendFrame.apVirAddr[1] = bufferRC;
    }
    #endif

#if 0
    if(dac_sampers_cnt == 6)
    {
        dac_sampers_cnt=7;
        printf("Send Real PCM...\n");
    }
    else if(dac_sampers_cnt < 6)
    {
        dac_sampers_cnt++;
        printf("Clear Buffer...\n");
        memset(buffer_pcm_16,0,buffer_len);
    }
#endif

    int s32Ret = MI_SUCCESS;
    int k=0;
    do
    {
        s32Ret = MI_AO_SendFrame(AoDevId, 0, &stAoSendFrame, -1);
        //k++;
        //if(k>=2)
            //printf("KKKKKKKKKKFJDKSJFKDJ...\n");
    }
    while (s32Ret == MI_AO_ERR_NOBUF && curMediaSource == g_media_source);

    mi_ao_totalByteNum+=buffer_len;
#if 1
    MI_AO_ChnState_t stChnState;
    s32Ret = MI_AO_QueryChnStat(MI_DEFAULT_AO_DEV_ID, 0, &stChnState);
    //printf("ChnBusyNum=%d\n",stChnState.u32ChnBusyNum);
    if(stChnState.u32ChnBusyNum <= buffer_len && mi_ao_totalByteNum>=65536)
    {
        concentrated_write_need_wait=1;
        printf("concentrated_write_need_wait=1...\n");
    }
    else
    {
        concentrated_write_need_wait=0;
    }
#endif

    
#if 0
    MI_AO_ChnState_t stChnState;
    do
    {
        s32Ret = MI_AO_QueryChnStat(0, 0, &stChnState);
        // if (Mp3Playing == FALSE)
        // {
        //     printf("stop mp3 \n\r");
        //     return MAD_FLOW_STOP;
        // }
    }
    while (stChnState.u32ChnBusyNum > 500);

    if (s32Ret != MI_SUCCESS)
    {
        printf("[Warning]: MI_AO_SendFrame fail, error is 0x%x: \n", s32Ret);
    }
#endif
    //printf("mi_audio_write ok...\n");
}




void* mi_audio_local_write_thread()
{

    AedHandle AED_HANDLE; 
    unsigned int aedBuffSize=0;  
    char *working_buf_ptr = NULL;
    AedProcessStruct aed_params;

    AedSampleRate sample_rate = AED_SRATE_48K;  
    aed_params.channel = 1; 
    aed_params.point_number = MI_AI_SAMPLE_PER_FRAME_LOCAL;

    ALGO_AED_RET ret = ALGO_AED_RET_SUCCESS;

    aedBuffSize = IaaAed_GetBufferSize();  
    working_buf_ptr = (char *)malloc(aedBuffSize);  
    if(NULL == working_buf_ptr)  
    {  
        printf("malloc workingBuffer failed !\n");   
    }  
    printf("malloc workingBuffer succeed !\n");  
    AED_HANDLE = IaaAed_Init(working_buf_ptr, &aed_params);  
    if(NULL == AED_HANDLE)  
    {  
        printf("IaaAed_Init faild !\n");  
    }  
    printf("IaaAed_Init succeed !\n");  
    ret = IaaAed_Config(AED_HANDLE);  
    if(ALGO_AED_RET_SUCCESS != ret)  
    {  
        printf("IaaAed_Config failed !, ret = %d\n", ret);  
    }

    ret = IaaAed_SetSampleRate(AED_HANDLE, sample_rate);  
    if(ALGO_AED_RET_SUCCESS != ret)  
    {  
        printf("IaaAed_SetSampleRate failed !, ret = %d\n", ret);  
    }  
    #if 0
    ret = IaaAed_SetLsdThreshold(AED_HANDLE, lsd_threshold_db);  
    if(ALGO_AED_RET_SUCCESS != ret)  
    {  
        printf("IaaAed_SetLsdThreshold failed !, ret = %d\n", ret);  
        return -1;  
    }  
    #endif

    printf("IaaAed OK...\n");

#if ENABLE_SPEEX_DENOISE
    SpeexPreprocessState *speexPreState_LocalMic = speex_preprocess_state_init(MI_AI_SAMPLE_PER_FRAME_LOCAL, AED_SRATE_48K);
    int IsEnableDeNoise=1;//1表示开启，0表示关闭
    //SPEEX_PREPROCESS_SET_DENOISE表示降噪
    speex_preprocess_ctl(speexPreState_LocalMic, SPEEX_PREPROCESS_SET_DENOISE, &IsEnableDeNoise);//降噪
    int noiseSuppress = MAIC_REAR_VLID_THRESHOLD_DB;//噪音分贝数，是一个负值
    //Speex的降噪是通过简单的设置音频数据的阀值，过滤掉低分贝的声音来实现的
    //优点是简单，使用Speex编解码库时可以直接使用
    //缺点是会把声音细节抹掉
    speex_preprocess_ctl(speexPreState_LocalMic, SPEEX_PREPROCESS_SET_NOISE_SUPPRESS, &noiseSuppress);


    SpeexPreprocessState *speexPreState_RemoteMic = speex_preprocess_state_init(MI_AI_SAMPLE_PER_FRAME_LOCAL, AED_SRATE_48K);
    //SPEEX_PREPROCESS_SET_DENOISE表示降噪
    speex_preprocess_ctl(speexPreState_RemoteMic, SPEEX_PREPROCESS_SET_DENOISE, &IsEnableDeNoise);//降噪
    noiseSuppress = MAIC_REAR_VLID_THRESHOLD_DB;//噪音分贝数，是一个负值
    //Speex的降噪是通过简单的设置音频数据的阀值，过滤掉低分贝的声音来实现的
    //优点是简单，使用Speex编解码库时可以直接使用
    //缺点是会把声音细节抹掉
    speex_preprocess_ctl(speexPreState_RemoteMic, SPEEX_PREPROCESS_SET_NOISE_SUPPRESS, &noiseSuppress);
    printf("init speex ok.\n");
#endif

#if ENABLE_WEBRTC_DENOISE
    webrtc_ns_init(0,48000,1);
    #if 1
    webrtc_ns_init(1,48000,1);
    printf("init webrtc ok.\n");
    #endif
#endif

    //printf("mi_audio_write:%d...\n",count);
    MI_AUDIO_Frame_t stAoSendFrame;
    memset(&stAoSendFrame, 0x0, sizeof(MI_AUDIO_Frame_t));
    MI_AUDIO_DEV AoDevId = MI_DEFAULT_AO_DEV_ID;
    int i=0;
    
    int16_t *adc0_ori_pcm_16,*adc1_ori_pcm_16,*adc2_ori_pcm_16;

    int16_t  adc0_progcess_mono[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  adc1_progcess_mono[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  adc2_progcess_mono[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t mono_mix[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  mix_progcess_stero[MI_AI_SAMPLE_PER_FRAME_LOCAL*2]={0};
    
    int buffer_len;
    int SamplesPreFrame=0;

    buffer_len=MI_AI_SAMPLE_PER_FRAME_LOCAL*2*2;
    SamplesPreFrame=buffer_len/2/2;

    int is_local_source=0;
    int local_source_cnt=0;

    int thread_delay_us=2000;    //2ms
    double perSamples_delay_ms = (double)MI_AI_SAMPLE_PER_FRAME_LOCAL / 48000 *1000;
    int enter_local_source_threshold=1000/perSamples_delay_ms;
    int signal_db_print_threshold=500/perSamples_delay_ms;
    int signal_timeout_threshold=AUX_INVLID_TIMEOUT*1000/perSamples_delay_ms;

    printf("perSamples_delay_ms=%f,signal_db_print_threshold=%d,signal_timeout_threshold=%d\n",perSamples_delay_ms,signal_db_print_threshold,signal_timeout_threshold);

    //需要独立检测判断阈值
    #if YIHUI_VERSION
    int adc_threshold_array[3]={MIC_FRONT_VALID_THRESHOLD_DB,MIC_FRONT_VALID_THRESHOLD_DB,MIC_FRONT_VALID_THRESHOLD_DB};
    #else
    int adc_threshold_array[3]={AUX_VALID_THRESHOLD_DB,MIC_FRONT_VALID_THRESHOLD_DB,MIC_FRONT_VALID_THRESHOLD_DB};
    #endif
    int adc_valid_array[3]={0};
    int adc_timeoutCnt_array[3]={0};

    //4G控制器，需要降低本地信号触发阈值
    if(IS_MODEL_WITH_4G)
    {
        adc_threshold_array[0] = AUX_VALID_THRESHOLD_LOW_DB;
    }

    while(mi_ai_init_flag)
    {
        sem_wait(&sem_aiReady);
        if(!mi_ai_init_flag)
            break;

        //当蓝牙模块存在的时候,ADC2当做音乐处理
        if(IS_MODEL_WOODEN && g_exist_bluetooth_module)
        {
            adc_threshold_array[2] = AUX_VALID_THRESHOLD_DB;
        }

        pthread_mutex_lock(&mutex_system_source);
        int sysSource = get_system_source();
        if( sysSource == SOURCE_NULL || sysSource == SOURCE_AUX )
        {
            if(is_local_source == 0)
            {
                local_source_cnt++;
                if( local_source_cnt >= enter_local_source_threshold)
                {
                    is_local_source=1;
                    local_source_cnt=0;
                }
            }
        }
        else
        {
            is_local_source=0;
            local_source_cnt=0;
        }
        pthread_mutex_unlock(&mutex_system_source);

        if(!g_allow_localSource)
        {
            is_local_source=0;
        }

        #if LOCAL_SOURCE_PRIORITY_HIGHEST
        is_local_source=1;
        local_source_cnt=0;
        #endif

            //pthread_mutex_lock(&mutex_audio_out);
            if(st_AdcData_Info.adc_data_valid[st_AdcData_Info.read_pos])
            {
                adc0_ori_pcm_16=(int16_t *)st_AdcData_Info.adc0_data[st_AdcData_Info.read_pos];
                adc1_ori_pcm_16=(int16_t *)st_AdcData_Info.adc1_data[st_AdcData_Info.read_pos];
                adc2_ori_pcm_16=(int16_t *)st_AdcData_Info.adc2_data[st_AdcData_Info.read_pos];

                int16_t *adc_ori_buf_array[3]={adc0_ori_pcm_16,adc1_ori_pcm_16,adc2_ori_pcm_16};
                int16_t *adc_pro_buf_array[3]={adc0_progcess_mono,adc1_progcess_mono,adc2_progcess_mono};

                int db_val=-96;
                int adc_valid=0;
                if(adc_signal_can_detect)
                {
                    for(int i=0;i<3;i++)
                    {
                        switch(i)
                        {
                            case 0:
                                if(!dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_L])
                                {
                                    continue;
                                }
                                #if YIHUI_VERSION
                                if(!bMicInsert)
                                {
                                    continue;
                                }
                                #endif
                            break;
                            case 1:
                                //对讲终端，本地播放时不混合回声参考通道
                                //对于P28/P26，如果支持对讲，那么本地播放时不混合MIC通道
                                if(IS_DEVICE_SUPPORT_INTERCOM)
                                {
                                    adc_valid_array[i]=0;
                                    continue;
                                }
                                if(!dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_R])
                                {
                                    continue;
                                }
                            break;
                            case 2:
                                //对讲终端，本地播放时不混合MIC通道
                                //对于P28/P26，本地播放时不混合回声参考通道
                                if(IS_DEVICE_INTERCOM_TERMINAL || IS_MODEL_SIMPLE_DECODER || IS_MODEL_SIMPLE_AMP)
                                {
                                    adc_valid_array[i]=0;
                                    continue;
                                }
                                if(!dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC1_L])
                                {
                                    continue;
                                }
                            break;
                        }
                        IaaAed_RunLsd(AED_HANDLE,*(adc_ori_buf_array+i), &db_val);
                        if(db_val>adc_threshold_array[i])
                        {
                            if(!adc_valid_array[i])
                            {
                                printf("ADC%d is valid:%d dB\n",i,db_val);
                                adc_valid_array[i]=1;
                            }
                            adc_timeoutCnt_array[i]=0;
                        }
                        else
                        {
                            if(adc_valid_array[i])
                            {
                                adc_timeoutCnt_array[i]++;
                                if(adc_timeoutCnt_array[i] == signal_timeout_threshold)
                                {
                                    printf("ADC%d is invaild:%d dB\n",i,db_val);
                                    adc_valid_array[i]=0;
                                }
                            }
                        }
                        if(adc_valid_array[i])
                        {
                            adc_valid=1;
                        }
                    }
                }

                #if 0
                pcmAudioMix3(mono_mix,adc0_ori_pcm_16,adc1_ori_pcm_16,adc2_ori_pcm_16,SamplesPreFrame);
                static int readSamples=0;
                readSamples++;
                int lsd_db=-96;
                if(adc_signal_can_detect)
                {
                    ret = ALGO_AED_RET_SUCCESS;
                    ret = IaaAed_RunLsd(AED_HANDLE,mono_mix, &lsd_db);
                    //lsd_db=get_signal_db(mono_mix,SamplesPreFrame);
                    //开机前三秒，不检测信号
                    if (ALGO_AED_RET_SUCCESS != ret)
                    {  
                        printf("MI_AED_RunLsd failed !, ret = %d\n", ret);  
                    }
                    else
                    {
                        if( readSamples%signal_db_print_threshold == 0)
                        {
                            readSamples=0;
                            printf("lsd:%d dB\n",lsd_db);
                        }
                    }
                }
                #endif
                //量化各路ADC音量
                for(i=0;i<3;i++)
                {
                    if(!adc_valid_array[i])
                    {
                        memset(*(adc_pro_buf_array+i),0,MI_AI_SAMPLE_PER_FRAME_LOCAL*2);
                    }
                    else
                    {
                        int module_gain = 0;
                        if(i == 0)
                        {
                            module_gain=dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_L];
                        }
                        else if(i == 1)
                        {
                            module_gain=dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_R];
                        }
                        else if(i == 2)
                        {
                            module_gain=dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC1_L];
                        }

                        int t_adcVol=mSysVol[100];

                        if(isDisplayValid())
                        {
                            if(IS_DEVICE_YIHUI_SERIES_BOARD)
                            {
                                if(i == 0)
                                    t_adcVol=mSysVol[g_micVolume];
                                else if(i == 1)
                                    t_adcVol=mSysVol[g_lineVolume];
                            }
                            else
                            {
                            	if(i == 0)
                                    t_adcVol=mSysVol[g_lineVolume];
                                else if(i == 1)
                                    t_adcVol=mSysVol[g_micVolume];
                            }
                        }
                        else
                        {
                            if(i == 0)
                                t_adcVol=mSysVol[adcCheckVolumeVal[0]];
                            else if(i == 1)
                                t_adcVol=mSysVol[adcCheckVolumeVal[2]];
                        }

                        for(int k=0; k<SamplesPreFrame; k++)
                        {
                            *(*(adc_pro_buf_array+i)+k) = limit_value_16bit( (((int32_t)(*(*(adc_ori_buf_array+i)+k))) * module_gain + 2048) >> 12 );
                            if(isDisplayValid() || g_exist_adcCheck_module)
                            {
                                if(t_adcVol!=mSysVol[100])
                                {
                                    *(*(adc_pro_buf_array+i)+k) = limit_value_16bit( (((int32_t)(*(*(adc_pro_buf_array+i)+k))) * t_adcVol + 2048) >> 12 );
                                }
                            }
                        }
                    }
                }

                #if ENABLE_WEBRTC_DENOISE
                if(is_local_source)
                {
                    //此处即使没有信号，也进行音频处理，否则可能会影响webrtc的处理流程（待验证）
                    //20220411 没有信号不进行音频算法处理，节省CPU资源(初步测试没有影响)
                    if(adc_valid_array[0])
                    {
                        if(IS_NEED_WEBRTC_LINE_DEVICE)
                        {
                            webrtc_ns_proc(0,adc0_progcess_mono,MI_AI_SAMPLE_PER_FRAME_LOCAL);
                        }
                    }
                    //如果adc2有效且存在蓝牙模块
                    if(adc_valid_array[2] && (IS_MODEL_WOODEN && g_exist_bluetooth_module))
                    {
                        //if(IS_NEED_WEBRTC_LINE_DEVICE)
                        {
                            webrtc_ns_proc(1,adc2_progcess_mono,MI_AI_SAMPLE_PER_FRAME_LOCAL);
                        }
                    }
                }
                #endif

                #if ENABLE_SPEEX_DENOISE
                if(is_local_source)
                {
                    #if YIHUI_VERSION
                    if(adc_valid_array[0])
                        speex_preprocess_run(speexPreState_LocalMic, adc0_progcess_mono);
                    #else
                    if(adc_valid_array[1])
                        speex_preprocess_run(speexPreState_LocalMic, adc1_progcess_mono);
                    #endif
                    //如果adc2有效且存在不蓝牙模块
                    if(adc_valid_array[2] && !(IS_MODEL_WOODEN && g_exist_bluetooth_module))
                        speex_preprocess_run(speexPreState_RemoteMic, adc2_progcess_mono);
                }
                #endif

                if(adc_valid)
                {
                    pcmAudioMix3(mono_mix,adc0_progcess_mono,adc1_progcess_mono,adc2_progcess_mono,SamplesPreFrame);
                }
                else
                {
                    memset(mono_mix,0,sizeof(mono_mix));
                }

                #if DECODER_LZY_VERESION_FM_C4A1
                if(!g_lzy_fm_signal_detect)
                {
                    is_local_source=0;
                    adc_valid=0;
                }
                #endif

                if(adc_valid)
                {
                    if(!g_signal_aux)
                    {
                        g_signal_aux=1;
                        printf("g_signal_aux:%d\r\n",g_signal_aux);
                        #if LOCAL_SOURCE_PRIORITY_HIGHEST
                        if(get_system_source() != SOURCE_NULL)
                        {
                            Set_zone_idle_status(NULL,  __func__, __LINE__,true);
                        }
                        #endif
                        if(get_system_source() == SOURCE_NULL)
                        {
                            //清空A0缓存区，避免上一次错误数据，20231221 改为有数据的时候才传输本地音频，所以当再次有数据时，需要清除缓存区
                            MI_AO_ClearChnBuf(MI_DEFAULT_AO_DEV_ID,0);
                            printf("MI_AO_ClearChnBuf...\n");
                            set_system_source(SOURCE_AUX);
                            pkg_query_current_status(NULL);	//发送当前状态
                        }
                    }
                }
                else
                {
                    if(g_signal_aux)
                    {
                        g_signal_aux=0;
                        // ClearGpio(17,0);    //P001 关闭继电器，关闭电源
                        printf("g_signal_aux:%d\r\n",g_signal_aux);
                        if(get_system_source() == SOURCE_AUX)
                        {
                            set_system_source(SOURCE_NULL);
                            pkg_query_current_status(NULL);	//发送当前状态
                        }
                    }
                }
                if(amp_init_ok && mi_ao_init_flag && is_local_source && g_signal_aux)
                {
                    //将单声道数据转换为立体声
                    MonoToStereo(mono_mix,SamplesPreFrame,mix_progcess_stero);

                    //默认本地音量固定100
                    int t_sysvol=mSysVol[(unsigned int)(DEFAULT_SYSTEM_VOLUME*g_sub_volume*0.01)];
                    
                    #if 0 //20241116 增加设备本地音量设置，以它为准
                    //判断是否存在串口显示模块,存在则以系统音量为准。
                    if(g_PreExistDispModule)
                    {
                        t_sysvol=mSysVol[(unsigned int)(g_system_volume*g_sub_volume*0.01)];
                    }
                    #endif
                    
                    if(g_exist_volumeControl_device)
                    {
                        t_sysvol=mSysVol[(unsigned int)(g_volumeCD_aux_volume*g_sub_volume*0.01)];
                    }
                    else
                    {
                        t_sysvol=mSysVol[(unsigned int)(g_aux_volume*g_sub_volume*0.01)];
                    }

                    //量化系统音量
                    for(i=0; i<SamplesPreFrame; i++)
                    {
                        mix_progcess_stero[2 * i + 0] = limit_value_16bit( (((((int32_t)mix_progcess_stero[2 * i + 0]) * t_sysvol + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L] + 2048) >> 12 );
                        mix_progcess_stero[2 * i + 1] = limit_value_16bit( (((((int32_t)mix_progcess_stero[2 * i + 1]) * t_sysvol + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R] + 2048) >> 12 );
                    }
                
                    pthread_mutex_lock(&mutex_audio_eq);
                    if(mi_eq_init_flag)
                    {
                        //判断需要处理多少次EQ，一次只能处理128个采样点

                        //printf("Pre:mi_eq_remain_samples=%d\n",mi_eq_remain_samples);

                        memcpy(eq_stereo,mi_eq_remain_data,mi_eq_remain_samples*2*2);                 //先将之前剩下的数据放到前面
                        memcpy(eq_stereo+mi_eq_remain_samples*2,mix_progcess_stero,SamplesPreFrame*2*2);     //将新的数据放到后面
                        int eq_count=(SamplesPreFrame+mi_eq_remain_samples)/EQ_FRAME_SIZE;
                        mi_eq_remain_samples =(SamplesPreFrame+mi_eq_remain_samples)%EQ_FRAME_SIZE;
                        int process_samples = eq_count*EQ_FRAME_SIZE;       //实际需要处理的长度(字节)
                        memcpy(mi_eq_remain_data,(eq_stereo+process_samples*2),mi_eq_remain_samples*2*2);  //将剩下未处理的数据放入data，以便下次使用

                        //printf("buffer_count=%d,eq_count=%d\n",count,eq_count);

                        for(i=0;i<eq_count;i++)
                        {
                            int ret = IaaEq_Run(mi_eq_handle, eq_stereo+i*EQ_FRAME_SIZE*2);
                            if(ret)
                            {
                                printf("IaaEq_Run failed !\n");
                            }
                        }

                        #if AUDIO_PHASE_INVERT
                        PhaseInvert(eq_stereo,process_samples*2,IS_AMP_WITH_AUDIO_DRIVER?PHASE_INVERT_STERO:PHASE_INVERT_RIGHT);
                        #endif

                        stAoSendFrame.u32Len[0] = process_samples*2*2;
                        stAoSendFrame.apVirAddr[0] = eq_stereo;
                        stAoSendFrame.apVirAddr[1] = NULL;

                    // printf("Cur:eq_count=%d,process_samples=%d,mi_eq_remain_samples=%d\n",eq_count,process_samples,mi_eq_remain_samples);
                    }
                    else
                    {
                        #if AUDIO_PHASE_INVERT
                        PhaseInvert(mix_progcess_stero,buffer_len/2,IS_AMP_WITH_AUDIO_DRIVER?PHASE_INVERT_STERO:PHASE_INVERT_RIGHT);
                        #endif

                        stAoSendFrame.u32Len[0] = buffer_len;
                        stAoSendFrame.apVirAddr[0] = mix_progcess_stero;
                        stAoSendFrame.apVirAddr[1] = NULL;
                    }
                    pthread_mutex_unlock(&mutex_audio_eq);

                    //printf("send\n");
                    #if 0
                    if(dac_sampers_cnt == 6)
                    {
                        dac_sampers_cnt=7;
                        printf("Send Real PCM...\n");
                    }
                    else if(dac_sampers_cnt < 6)
                    {
                        dac_sampers_cnt++;
                        printf("Clear Loal Buffer...\n");
                        memset(mix_progcess_stero,0,buffer_len);
                    }
                    #endif
                    int s32Ret = MI_SUCCESS;
                    int k=0;
                    do
                    {
                        s32Ret = MI_AO_SendFrame(AoDevId, 0, &stAoSendFrame, -1);
                    }
                    while (s32Ret == MI_AO_ERR_NOBUF);
                }
                #if 1
                st_AdcData_Info.adc_data_valid[st_AdcData_Info.read_pos] = 0;
                st_AdcData_Info.read_pos++;
                if(st_AdcData_Info.read_pos>=MAX_ADC_DATA_PKG_NUM)
                {
                    st_AdcData_Info.read_pos=0;
                }
                #endif
            }
            //pthread_mutex_unlock(&mutex_audio_out);
            //usleep(thread_delay_us);
    }

    printf("AED end !\n");  
    ret = IaaAed_Release(AED_HANDLE);
    printf("SPEEX Denoise end !\n");
    #if ENABLE_SPEEX_DENOISE
    speex_preprocess_state_destroy(speexPreState_LocalMic);
    speex_preprocess_state_destroy(speexPreState_RemoteMic);
    #endif
    #if ENABLE_WEBRTC_DENOISE
    webrtc_ns_deinit(0);
    webrtc_ns_deinit(1);
    #endif
    if(ALGO_AED_RET_SUCCESS != ret)  
    {  
        printf("IaaAed_Release failed !, ret = %d\n", ret);  
    }
}


#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)

const uint16_t mIntercomInVol[10] =
{
	/* 0-100级音量控制, 0.25dB等级 */
	0,
	1300,2050,2580,3250, /*-10dB,-6dB,-4dB,-2dB*/
    4096,                //*0dB,
    5150,6500,8192,10300 /*+2dB,+4dB,+6dB,+8dB*/
};

const uint16_t mIntercomOutVol[10] =
{
	/* 0-100级音量控制, 0.25dB等级 */
	0,
	1300,1640,2050,2580, /*+-10dB,-8dB,-6dB,-4dB*/
    3250,               //*-2dB
    4096,5150,6500,8192 /*+0dB,+2dB,+4dB,+6dB*/
};


void* mi_audio_call_write_thread()
{
    MI_S32 ret;
	unsigned int aecWorkingBufferSize;
    char *aecWorkingBuffer=NULL;
    AEC_HANDLE pAECHandle;

    AudioAecInit aec_init;
    AudioAecConfig aec_config;
    
    /*********************User change section start*******************/
    unsigned int supMode_band[6] = {20,40,60,80,100,120};   //todo校正
    unsigned int supMode[7] = {16,16,16,16,16,16,16};              //强度
    aec_init.point_number = 128; //AEC算法每次处理的采样点个数
    aec_init.nearend_channel = 1;
    aec_init.farend_channel = 1;
    aec_init.sample_rate = IAA_AEC_SAMPLE_RATE_16000;
    aec_config.delay_sample = 0;
    aec_config.comfort_noise_enable = IAA_AEC_FALSE;
    /*********************User change section end*******************/
    memcpy(&(aec_config.suppression_mode_freq[0]), supMode_band, sizeof(supMode_band));
    memcpy(&(aec_config.suppression_mode_intensity[0]), supMode, sizeof(supMode));

    //(1)IaaAec_GetBufferSize
    aecWorkingBufferSize = IaaAec_GetBufferSize();
    aecWorkingBuffer = (char*)malloc(aecWorkingBufferSize);
    if(NULL == aecWorkingBuffer)
    {
        printf("workingBuffer malloc failed !\n");
        return NULL;
    }
    printf("workingBuffer malloc success !\n");
    //(2)IaaAec_Init
    pAECHandle = IaaAec_Init(aecWorkingBuffer, &aec_init);
    if (NULL == pAECHandle)
    {
        printf("AEC init failed !\r\n");
        return NULL;
    }
    printf("AEC init success !\r\n");
    //(3)IaaAec_Config
    ret = IaaAec_Config(pAECHandle, &aec_config);
    if(ret)
    {
        printf("IaaAec_Config failed !\n");
        return NULL;
    }
    printf("IaaAec_Config succeed !\n");


    ANR_HANDLE anr_handle;
    AudioProcessInit anr_init;
    AudioAnrConfig anr_config;

    unsigned int anr_workingBufferSize;  
    char *anr_workingBuffer = NULL;

    int intensity_band[6] = {3,24,40,64,80,128};  
    int intensity[7] = {10,10,10,10,10,10,10};

    anr_init.point_number = 128;
    anr_init.channel = 1;
    anr_init.sample_rate = IAA_APC_SAMPLE_RATE_16000;

        /******ANR Config*******/  
    anr_config.anr_enable = 1;  
    anr_config.user_mode = 2;  
    memcpy(anr_config.anr_intensity_band, intensity_band, sizeof(intensity_band));  
    memcpy(anr_config.anr_intensity, intensity, sizeof(intensity));  
    anr_config.anr_smooth_level = 10;  
    anr_config.anr_converge_speed = 0;

    anr_workingBufferSize = IaaAnr_GetBufferSize();
    anr_workingBuffer = (char *)malloc(anr_workingBufferSize);

    //(2)IaaAnr_Init  
    anr_handle = IaaAnr_Init(anr_workingBuffer, &anr_init);  
    if(NULL == anr_handle)  
    {
        printf("IaaAnr_Init failed !\n");  
        return NULL;  
    }  
    printf("IaaAnr_Init succeed !\n");  
    //(3)IaaAnr_Config  
    ret = IaaAnr_Config(anr_handle, &anr_config);  
    if(ret)  
    {  
        printf("IaaAnr_Config failed !\n");  
        return NULL;  
    }  
    printf("IaaAnr_Config succeed !\n"); 

    //(2)IaaAgc_Init


    AGC_HANDLE agc_handle;
    AudioProcessInit agc_init;
    AudioAgcConfig agc_config;

    unsigned int agc_workingBufferSize;  
    char *agc_workingBuffer = NULL;



    agc_init.point_number = 128;  
    agc_init.channel = 1;  
    agc_init.sample_rate = IAA_APC_SAMPLE_RATE_16000;

    #if 0
    short compression_ratio_input[7] = {-60,-50,-40,0,0,0,0};  
    short compression_ratio_output[7] = {-50,-40,-30,0,0,0,0};  
    memcpy(agc_config.compression_ratio_input, compression_ratio_input, sizeof(compression_ratio_input));  
    memcpy(agc_config.compression_ratio_output, compression_ratio_output, sizeof(compression_ratio_output));  
    agc_config.agc_enable = 1;  
    agc_config.user_mode = 1;  
    agc_config.gain_info.gain_max  = 20;  
    agc_config.gain_info.gain_min  = -10;  
    agc_config.gain_info.gain_init = -10;  
    agc_config.drop_gain_max = 10; 
    agc_config.gain_step = 1;
    agc_config.attack_time = 1;  
    agc_config.release_time = 1;  
    agc_config.noise_gate_db = -75;  
    agc_config.noise_gate_attenuation_db = 0;  
    agc_config.drop_gain_threshold = -35;
    #else

    short compression_ratio_input[7] = {-55,-50,-40,-10,0,0,0};
    short compression_ratio_output[7] = {-50,-40,-30,-15,-8,0,0};

    memcpy(agc_config.compression_ratio_input, compression_ratio_input, sizeof(compression_ratio_input));  
    memcpy(agc_config.compression_ratio_output, compression_ratio_output, sizeof(compression_ratio_output));  
    agc_config.agc_enable = 1;  
    agc_config.user_mode = 1;
    agc_config.gain_info.gain_max  = 15;  
    agc_config.gain_info.gain_min  = -10;  
    agc_config.gain_info.gain_init = -10;  
    agc_config.drop_gain_max = 10; 
    agc_config.gain_step = 1;
    agc_config.attack_time = 1;
    agc_config.release_time = 1;
    agc_config.noise_gate_db = -60;
    agc_config.noise_gate_attenuation_db = 0;
    agc_config.drop_gain_threshold = -20;
    #endif

    agc_workingBufferSize = IaaAgc_GetBufferSize();
    agc_workingBuffer = (char *)malloc(agc_workingBufferSize);

    //(2)IaaAgc_config  
    agc_handle = IaaAgc_Init(agc_workingBuffer, &agc_init);  
    if(NULL == agc_handle)  
    {
        printf("IaaAgc_Init failed !\n");  
        return NULL;  
    }  
    printf("IaaAgc_Init succeed !\n");  
    //(3)IaaAgc_Config  
    ret = IaaAgc_Config(agc_handle, &agc_config);  
    if(ret)  
    {  
        printf("IaaAgc_Config failed !\n");  
        return NULL;  
    }  
    printf("IaaAgc_Config succeed !\n"); 



    #if SUPPORT_CODEC_G722
	uint8_t ibuf[G722_CALL_BUFFER_SIZE];
	int16_t obuf[G722_CALL_BUFFER_SIZE * 2];	//此处可确保还原成原始数据G722_CALL_BUFFER_SIZE*2*sizeof(int16_t)
	G722_ENC_CTX *g722_ectx =NULL;
	G722_DEC_CTX *g722_dctx =NULL;
	int srate = ~ G722_SAMPLE_RATE_8000;
	size_t oblen = sizeof(obuf);	//512bytes

	if(m_stCallInfo.audioCoding == DECODE_G722)
	{
		g722_ectx = g722_encoder_new(64000, srate);
		if (g722_ectx == NULL) {
			fprintf(stderr, "g722_encoder_new() failed\n");
			exit (1);
		}
		g722_dctx = g722_decoder_new(64000, srate);
		if (g722_dctx == NULL) {
			fprintf(stderr, "g722_decoder_new() failed\n");
			exit (1);
		}
	}
	#endif


    #if ENABLE_SPEEX_DENOISE
    SpeexPreprocessState *speexPreState_LocalMic = speex_preprocess_state_init(MI_AI_SAMPLE_PER_FRAME_LOCAL, AED_SRATE_48K);
    int IsEnableDeNoise=1;//1表示开启，0表示关闭
    //SPEEX_PREPROCESS_SET_DENOISE表示降噪
    speex_preprocess_ctl(speexPreState_LocalMic, SPEEX_PREPROCESS_SET_DENOISE, &IsEnableDeNoise);//降噪
    int noiseSuppress = -45;//噪音分贝数，是一个负值
    //Speex的降噪是通过简单的设置音频数据的阀值，过滤掉低分贝的声音来实现的
    //优点是简单，使用Speex编解码库时可以直接使用
    //缺点是会把声音细节抹掉
    speex_preprocess_ctl(speexPreState_LocalMic, SPEEX_PREPROCESS_SET_NOISE_SUPPRESS, &noiseSuppress);
    #endif




    MI_AUDIO_Frame_t stAoSendFrame;
    memset(&stAoSendFrame, 0x0, sizeof(MI_AUDIO_Frame_t));
    MI_AUDIO_DEV AoDevId = MI_DEFAULT_AO_DEV_ID;
    int i=0;
    
    int16_t *adc0_ori_pcm_16,*adc1_ori_pcm_16,*adc2_ori_pcm_16;

    int16_t  adc0_progcess_mono[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};  //LINE IN
    int16_t  adc1_progcess_mono[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};  //refer echo
    int16_t  adc2_progcess_mono[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};  //MIC
    int16_t  mono_mix_near[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  mono_far[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  mono_ring[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  mono_far_mix_ring[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  mix_progcess_stero[MI_AI_SAMPLE_PER_FRAME_LOCAL*2]={0};
    
    int buffer_len=MI_AI_SAMPLE_PER_FRAME_CALL*2*2;
    int SamplesPreFrame=buffer_len/2/2; //MI_AI_SAMPLE_PER_FRAME_CALL



    int called_connected_ring_bytes = sizeof(_acCalled_connected);
    int called_connected_ring_ReadTotal=0;


    while(mi_ai_init_flag)
    {
        sem_wait(&sem_aiReady);
        if(!mi_ai_init_flag)
            break;

        pthread_mutex_lock(&mutex_system_source);
        int sysSource = get_system_source();
        if( sysSource != SOURCE_CALL)
        {
            pthread_mutex_unlock(&mutex_system_source);
            break;
        }
        pthread_mutex_unlock(&mutex_system_source);

        //对于解码终端E，adc1为ECHO通道,adc2为MIC通道
        //对接P26/P28，adc1为MIC通道，adc2为ECHO通道
        if(st_AdcData_Info.adc_data_valid[st_AdcData_Info.read_pos])
        {
            adc0_ori_pcm_16=(int16_t *)st_AdcData_Info.adc0_data[st_AdcData_Info.read_pos];
            adc1_ori_pcm_16=(int16_t *)st_AdcData_Info.adc1_data[st_AdcData_Info.read_pos];
            adc2_ori_pcm_16=(int16_t *)st_AdcData_Info.adc2_data[st_AdcData_Info.read_pos];
            
            //int16_t *adc_ori_buf_array[3]={adc0_ori_pcm_16,adc1_ori_pcm_16,adc2_ori_pcm_16};
            int16_t *adc_ori_buf_array[3];
            #if(IS_DEVICE_INTERCOM_TERMINAL)
            adc_ori_buf_array[0] = adc0_ori_pcm_16;
            adc_ori_buf_array[1] = adc1_ori_pcm_16;
            adc_ori_buf_array[2] = adc2_ori_pcm_16;
            #else
            // 根据条件调整顺序
            adc_ori_buf_array[0] = adc0_ori_pcm_16;
            adc_ori_buf_array[1] = adc2_ori_pcm_16;
            adc_ori_buf_array[2] = adc1_ori_pcm_16;
            #endif

            int16_t *adc_pro_buf_array[3]={adc0_progcess_mono,adc1_progcess_mono,adc2_progcess_mono};

            //量化各路ADC音量
            for(i=0;i<3;i++)
            {
                int module_gain = 0;
                if(i == 0)
                {
                    module_gain=dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_L];
                }
                else if(i == 1)
                {
                    module_gain=dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_R];
                }
                else if(i == 2)
                {
                    module_gain=dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC1_L];
                }
                for(int k=0; k<SamplesPreFrame; k++)
                {
                    *(*(adc_pro_buf_array+i)+k) = limit_value_16bit( (((int32_t)(*(*(adc_ori_buf_array+i)+k))) * module_gain + 2048) >> 12 );
                }
            }

            #if 0
            //20230227，MIC灵敏度20mv，*8=160mv，需要升到250mv再发出去（暂时，正常要升到300mv），增大1.6倍（4dB):6500
            for(int i=0; i<SamplesPreFrame; i++)
            {
                adc2_progcess_mono[i] = limit_value_16bit( ((((int32_t)adc2_progcess_mono[i]) * 5150 + 2048) >> 12) );
            }
            #endif

            #if 1
            int t_micvol=mIntercomInVol[m_stCallDeviceConfig.micVol];
            for(int i=0; i<SamplesPreFrame; i++)
            {
                adc2_progcess_mono[i] = limit_value_16bit( ((((int32_t)adc2_progcess_mono[i]) * t_micvol + 2048) >> 12) );
            }

            //20230505换了mic，整体再增加6dB
            for(int i=0; i<SamplesPreFrame; i++)
            {
                adc2_progcess_mono[i] = limit_value_16bit( ((((int32_t)adc2_progcess_mono[i]) * 8200 + 2048) >> 12) );
            }
            #endif

            //(5)IaaAgc_Run  
            IaaAgc_Run(agc_handle, adc2_progcess_mono);
            IaaAgc_Run(agc_handle, adc2_progcess_mono+128);
            
            
        
            #if 0
                        //将line1与mic2的数据合成作为近端音频
                        pcmAudioMix2(mono_mix_near,adc0_progcess_mono,adc2_progcess_mono,SamplesPreFrame);
            #else       //对讲时不叠加line1,本地才叠加
                        memcpy(mono_mix_near,adc2_progcess_mono,sizeof(mono_mix_near));
            #endif

            #if 1

                        //将mic1远端音频做aec处理
                        //由于aec算法一次只能处理128采样点，但SamplesPreFrame是256，所以需要分两次完成
                        ret = IaaAec_Run(pAECHandle, mono_mix_near, adc1_progcess_mono);
                        if(ret < 0)
                        {
                            printf("IaaAec_Run1 failed !\n");
                        }
                        ret = IaaAec_Run(pAECHandle, mono_mix_near+128, adc1_progcess_mono+128);
                        if(ret < 0)
                        {
                            printf("IaaAec_Run2 failed !\n");
                        }
                        //现在的mono_mix_near已经是回声消除后的数据,将其发送给服务器
                        //memset(mono_mix_near,0,sizeof(mono_mix_near));
            #endif

            #if ENABLE_SPEEX_DENOISE
            //speex_preprocess_run(speexPreState_LocalMic, mono_mix_near);
            #endif

            IaaAnr_Run(anr_handle, mono_mix_near);
            IaaAnr_Run(anr_handle, mono_mix_near+128);

            if(m_stCallInfo.audioCoding == DECODE_STANDARD_PCM)
            {
                Send_Call_Audio_Stream((unsigned char*)mono_mix_near,SamplesPreFrame*2);
            }
            else if(m_stCallInfo.audioCoding == DECODE_G722) 
            {
                int encode_buf_len=g722_encode(g722_ectx, (short*)mono_mix_near, (oblen / sizeof(obuf[0])), ibuf);
			    memcpy(mono_mix_near,ibuf,encode_buf_len);	//128bytes
                Send_Call_Audio_Stream((unsigned char*)mono_mix_near,encode_buf_len);
            }

            //todo 获取远端数据，将单声道数据转换为立体声后输出
            if(amp_init_ok && mi_ao_init_flag)
            {
                int decode_buf_len = 0;
                int cnt=0;

                while(cnt<4 && m_stCall_recv_stream.rx_call_valid[m_stCall_recv_stream.rx_call_write_pos] )
                {
                    if(m_stCallInfo.audioCoding == DECODE_STANDARD_PCM)
                    {
                        decode_buf_len = m_stCall_recv_stream.rx_call_len[m_stCall_recv_stream.rx_call_write_pos];
                        memcpy(mono_far,m_stCall_recv_stream.rx_call_data[m_stCall_recv_stream.rx_call_write_pos],decode_buf_len);
                    }
                    else if(m_stCallInfo.audioCoding == DECODE_G722)
                    {
                        int outlen=g722_decode(g722_dctx, m_stCall_recv_stream.rx_call_data[m_stCall_recv_stream.rx_call_write_pos], m_stCall_recv_stream.rx_call_len[m_stCall_recv_stream.rx_call_write_pos], obuf);
                        decode_buf_len = outlen*sizeof(obuf[0]);
                        memcpy(mono_far,obuf,decode_buf_len);	//512bytes
                    }

                    //printf("rx_call_stream.rx_call_write_pos=%d,len=%d\n",m_stCall_recv_stream.rx_call_write_pos,decode_buf_len);


                    m_stCall_recv_stream.rx_call_valid[m_stCall_recv_stream.rx_call_write_pos]=0;
                    m_stCall_recv_stream.rx_call_write_pos++;
                    if(m_stCall_recv_stream.rx_call_write_pos >= RX_CALL_BUF_PKG_MAX)
                    {
                        m_stCall_recv_stream.rx_call_write_pos=0;
                    }
                    cnt++;

                    //如果是被叫，接通后需要播报铃声
                    if(m_stCallInfo.isCallingParty == 0)
                    {
                        if(called_connected_ring_ReadTotal<called_connected_ring_bytes)
                        {
                            int readBytes=(called_connected_ring_bytes-called_connected_ring_ReadTotal>=decode_buf_len)?decode_buf_len:called_connected_ring_bytes-called_connected_ring_ReadTotal;
                            memset(mono_ring,0,sizeof(mono_ring));
                            memcpy(mono_ring,_acCalled_connected+called_connected_ring_ReadTotal,readBytes);
                            called_connected_ring_ReadTotal+=readBytes;

                            //pcmAudioMix2(mono_far_mix_ring,mono_far,mono_ring,decode_buf_len);

                            //将单声道数据转换为立体声
                            MonoToStereo(mono_ring,decode_buf_len/2,mix_progcess_stero);
#if 1   //将铃声降低10dB
                            for(i=0; i<SamplesPreFrame; i++)
                            {
                                //1295为-10dB,2900为-3dB，2050为-6dB
                                mix_progcess_stero[2 * i + 0] = limit_value_16bit( ((((int32_t)mix_progcess_stero[2 * i + 0]) * 800 + 2048) >> 12) );
                                mix_progcess_stero[2 * i + 1] = limit_value_16bit( ((((int32_t)mix_progcess_stero[2 * i + 1]) * 800 + 2048) >> 12) );
                            }
#endif
                        }
                        else
                        {
                            //将单声道数据转换为立体声
                            MonoToStereo(mono_far,decode_buf_len/2,mix_progcess_stero);
                        }
                    }
                    else
                    {
                        //将单声道数据转换为立体声
                        MonoToStereo(mono_far,decode_buf_len/2,mix_progcess_stero);
                    }

                    int t_farOutvol=mIntercomOutVol[m_stCallDeviceConfig.farOutVol];
                    //调整DAC音量
                    for(i=0; i<SamplesPreFrame; i++)
                    {
                        mix_progcess_stero[2 * i + 0] = limit_value_16bit( (((((int32_t)mix_progcess_stero[2 * i + 0]) * t_farOutvol + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L] + 2048) >> 12 );
                        mix_progcess_stero[2 * i + 1] = limit_value_16bit( (((((int32_t)mix_progcess_stero[2 * i + 1]) * 5800 + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R] + 2048) >> 12 );
                    }

                    #if 1
                    #if AUDIO_PHASE_INVERT
                    PhaseInvert(mix_progcess_stero,buffer_len/2,IS_AMP_WITH_AUDIO_DRIVER?PHASE_INVERT_STERO:PHASE_INVERT_RIGHT);
                    #endif
                    #endif
                    stAoSendFrame.u32Len[0] = buffer_len;
                    stAoSendFrame.apVirAddr[0] = mix_progcess_stero;
                    stAoSendFrame.apVirAddr[1] = NULL;

                    int s32Ret = MI_SUCCESS;
                    int k=0;
                    do
                    {
                        s32Ret = MI_AO_SendFrame(AoDevId, 0, &stAoSendFrame, -1);
                    }
                    while (s32Ret == MI_AO_ERR_NOBUF);
                }
            }

            #if 1
            st_AdcData_Info.adc_data_valid[st_AdcData_Info.read_pos] = 0;
            st_AdcData_Info.read_pos++;
            if(st_AdcData_Info.read_pos>=MAX_ADC_DATA_PKG_NUM)
            {
                st_AdcData_Info.read_pos=0;
            }
            #endif
        }
    }

    IaaAec_Free(pAECHandle);
	free(aecWorkingBuffer);

    IaaAnr_Free(anr_handle);  
    free(anr_workingBuffer);

    IaaAgc_Free(agc_handle);
    free(agc_workingBuffer);

    #if ENABLE_SPEEX_DENOISE
    speex_preprocess_state_destroy(speexPreState_LocalMic);
    #endif

    #if SUPPORT_CODEC_G722
	if(g722_ectx)
	{
		g722_encoder_destroy(g722_ectx);
	}
	if(g722_dctx)
	{
		g722_decoder_destroy(g722_dctx);
	}
	#endif
}
#endif






int mi_audio_eq_init(int isEnable)
{
    int ret=-1;
    mi_auido_eq_deinit();

    pthread_mutex_lock(&mutex_audio_eq);
    
    //采样率为48000的时候开启EQ
    if(isEnable)
    {
        AudioProcessInit eq_init;
        AudioEqConfig  eq_config;
        AudioHpfConfig hpf_config;

        eq_init.point_number = EQ_FRAME_SIZE;
        eq_init.channel = 2;    //永远保持双声道
        eq_init.sample_rate = IAA_APC_SAMPLE_RATE_48000;
        eq_config.eq_enable = 1;
        eq_config.user_mode = 1;

        hpf_config.hpf_enable=0;

        memcpy(eq_config.eq_gain_db,mi_eq_table,sizeof(eq_config.eq_gain_db));
        unsigned int workingBufferSize = IaaEq_GetBufferSize();
        mi_eq_workingBuffer = (char *)malloc(workingBufferSize);
        if(NULL == mi_eq_workingBuffer)
        {
            printf("malloc workingBuffer failed !\n");
            return -1;
        }
        printf("malloc workingBuffer succeed !\n");
        //(2)IaaEq_Init
        mi_eq_handle = IaaEq_Init(mi_eq_workingBuffer, &eq_init);
        if(NULL == mi_eq_handle)
        {
            printf("IaaEq_Init failed !\n");
            return -1;
        }
        printf("IaaEq_Init succeed !\n");
        //(3)IaaEq_Config
        ret = IaaEq_Config(mi_eq_handle, &hpf_config, &eq_config);
        if(ret)
        {
            printf("IaaEq_Config failed !\n");
            return -1;
        }
        mi_eq_init_flag=1;
        printf("mi_auido_eq_init ok\n");
    }
    else
    {
        printf("EQ disable...\n");
    }

    pthread_mutex_unlock(&mutex_audio_eq);
}


void mi_auido_eq_deinit()
{
    pthread_mutex_lock(&mutex_audio_eq);
    if(mi_eq_init_flag)
    {
        IaaEq_Free(mi_eq_handle);
        free(mi_eq_workingBuffer);
        mi_eq_workingBuffer=NULL;
        mi_eq_remain_samples = 0;
        memset(mi_eq_remain_data,0,sizeof(mi_eq_remain_data));
        mi_eq_init_flag=0;
        printf("mi_auido_eq_deinit ok\n");
    }
    pthread_mutex_unlock(&mutex_audio_eq);
}

//数量，起始点
short dsp_freq_info[10][2]={  
                           {0,0},       //31hz
                           {0,0},       //62hz
                           {1,0},       //125hz        0~186
                           {1,1},       //250hz        186~372
                           {2,2},       //500hz        372~744
                           {4,4},       //1000hz       744~1488
                           {9,8},       //2000hz       1488~3162
                           {15,17},     //4000hz       3162~5952    
                           {33,32},     //8000hz       5952~12090
                           {25,65}      //16000hz       12090~16740
                        };
void mi_audio_set_eq_mode(int mode)
{
    memset(mi_eq_table,0,sizeof(mi_eq_table));
   	if(mode != 0)
	{
        //int once_freq=48000/2/129;    //186hz
        int i=0,j=0,k=0;
        if( mode !=1 )
        {
            for(i=0;i<10;i++)
            {
                short freqGain=0;
                if( EqGainArray[mode][i] >128 )
                {
                    freqGain = EqGainArray[mode][i] -256;
                }
                else
                {
                    freqGain = EqGainArray[mode][i];
                }
                for(j=0;j<dsp_freq_info[i][0];j++)
                {
                    *(mi_eq_table+dsp_freq_info[i][1]+j) = freqGain;
                }
            }
        }
        else
        {
            for(i=0;i<10;i++)
            {
                short freqGain=0;
                if( dsp_eq_info.gain[i] >128 )
                {
                    freqGain = dsp_eq_info.gain[i] -256;
                }
                else
                {
                    freqGain = dsp_eq_info.gain[i];
                }
                for(j=0;j<dsp_freq_info[i][0];j++)
                {
                    *(mi_eq_table+dsp_freq_info[i][1]+j) = freqGain;
                }
            }
        }

        mi_audio_eq_init(1);
    }
    else
    {
        mi_audio_eq_init(0);
    }
}




void Handle_Signal_100V(int Is100V)
{
  printf("Handle_Signal_100V:%d\r\n",Is100V);
  if(Is100V)
  {
    if(PriorityIsValid(PRIORITY_100V))
    {
        set_system_source(SOURCE_100V_INPUT);
        pkg_query_current_status(NULL);
    }
  }
  else
  {
    if(get_system_source() == SOURCE_100V_INPUT)
    {
        Set_zone_idle_status(NULL,  __func__, __LINE__,true);
    }
  }
}

#endif

#endif