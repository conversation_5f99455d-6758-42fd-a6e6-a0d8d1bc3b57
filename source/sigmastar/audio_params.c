/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-21 15:59:34 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-09-21 16:03:11
 */

#ifndef USE_PC_SIMULATOR

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>

#include "sysconf.h"
#include "audio_params.h"


st_dsp_firmware_feature_info dsp_firmware_feature;
 
#if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
	#if (DEFAULT_MODULE_ID == POWER_P13_2x20W_OLD)	//旧的AMP经过TPF605A
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x1,0x01,0x00,0x01,0x01,0x00}, \
  																			 {2933,16306,3693,0,4757,7118,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P13_2x20W)	//新的AMP直连CPU LINEOUT L
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x1,0x01,0x00,0x01,0x01,0x00}, \
  																			 {2933,16306,3693,0,17000,7118,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P13_2x20W_V20_E || DEFAULT_MODULE_ID == POWER_P13_2x20W_V20)	//V20
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x1,0x01,0x00,0x01,0x01,0x00}, \
  																			 {2350,18500,2850,0,12950,8300,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P13_2x20W_V22)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x1,0x01,0x00,0x01,0x01,0x00}, \
  																			 {1780,18500,2250,0,12950,8300,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P13_2x20W_LED)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x1,0x01,0x00,0x01,0x01,0x00}, \
  																			 {1780,18500,2250,0,12950,8300,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P13_2x20W_V25)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x1,0x01,0x00,0x01,0x01,0x00}, \
  																			 {1780,18500,2250,0,9500,7300,0}  }; 
	#elif (DEFAULT_MODULE_ID == POWER_P17_2x30W_OLD)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x01,0x00,0x01,0x01,0x00}, \
  																			 {2933,0,3693,0,5853,7118,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P17_2x20W_V11)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x01,0x00,0x01,0x01,0x00}, \
  																			 {2933,0,3693,0,17000,7118,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P17_2x30W_V11)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x01,0x00,0x01,0x01,0x00}, \
  																			 {2933,0,3693,0,20766,7118,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P17_2x20W_V12)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x01,0x00,0x01,0x01,0x00}, \
  																			 {1750,0,1750,0,11300,8500,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P17_2x30W_V12)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x01,0x00,0x01,0x01,0x00}, \
  																			 {1750,0,1750,0,14200,8500,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P17_2x15W_V12)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x01,0x00,0x01,0x01,0x00}, \
  																			 {1750,0,1750,0,10000,8500,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P15_2x33W_V10)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x01,0x00,0x01,0x01,0x00}, \
  																			 {2000,0,2000,0,11200,7500,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P21_2x30W_V10)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x00,0x00,0x01,0x01,0x00}, \
  																			 {2000,0,0,0,10800,7200,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P21_2x15W_V10)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x00,0x00,0x01,0x01,0x00}, \
  																			 {2000,0,0,0,7800,7200,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P21_1x30W_V10)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x00,0x00,0x01,0x01,0x00}, \
  																			 {2000,0,0,0,10800,7200,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P21_2x20W_V10)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x00,0x00,0x01,0x01,0x00}, \
  																			 {2000,0,0,0,9100,7200,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P21_2x18W_V20)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x00,0x00,0x01,0x01,0x00}, \
																			 {1780,18500,2250,0,11700,7300,0} }; 
	#elif (DEFAULT_MODULE_ID == POWER_P16_NORMAL || DEFAULT_MODULE_ID == POWER_P16_LZY)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x01,0x00,0x01,0x01,0x00}, \
  																			 {1750,0,1750,0,11900,9600,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P19_INTERCOM_V10)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x01,0x00,0x01,0x01,0x00}, \
  																			 {1750,4096,18500,0,7500,9400,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P20_2x30W)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x00,0x00,0x01,0x01,0x00}, \
																			 {2000,0,0,0,10800,7200,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P20_2x30W_V12)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x00,0x00,0x01,0x01,0x00}, \
																			 {2000,0,0,0,12100,7500,0}  };
	#elif (DEFAULT_MODULE_ID == POWER_P20_2x30W_780E_V20)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x00,0x00,0x01,0x01,0x00}, \
																			 {2000,0,0,0,10800,7500,0}  };

	#elif (DEFAULT_MODULE_ID == POWER_P26_NORMAL || DEFAULT_MODULE_ID == POWER_P26_LZY || DEFAULT_MODULE_ID == POWER_P26_V12)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x00,0x00,0x01,0x01,0x00}, \
  																			 {2000,0,0,0,6000,6000,0}  };

	#elif (DEFAULT_MODULE_ID == POWER_P28_18W)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x00,0x00,0x01,0x01,0x00}, \
																			 {1780,18500,2250,0,11700,7300,0} }; 
	#elif (DEFAULT_MODULE_ID == POWER_P29_5W)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x00,0x00,0x01,0x01,0x00}, \
																			 {1780,18500,2250,0,6300,7300,0} }; 
	
	#elif (DEFAULT_MODULE_ID == POWER_P31_RGB_4P3 || DEFAULT_MODULE_ID == POWER_P31_SPI_1P9)
	const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x01,0x00,0x00,0x01,0x01,0x00}, \
  																			 {4096,4096,0,0,4096,4096,0}  };
	#endif

#else
const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x0,0x00,0x00,0x00,0x00,0x00}, \
  																			 {0,0,0,0,0,0,0}  };
#endif


st_dsp_eq_info dsp_eq_info;


/************************EQ模式***************************************
0: 关闭
1：自定义
2：流行
3：舞曲
4：摇滚
5：古典
6：人声
7：柔和
以下字段选择（自定义）有效
*/
unsigned short EqFreqArray[10]={ 31,62,125,250,500,1000,2000,4000,8000,16000 };
unsigned char EqGainArray[DSP_MAX_EQ_MODE_NUM][10]={
  										{0,0,0,0,0,0,0,0,0,0},	//关闭
  										{0,0,0,0,0,0,0,0,0,0},	//自定义
										{6,5,253,254,5,4,252,253,6,4},
										{4,3,252,250,0,0,3,4,4,5},
										{7,6,2,1,253,252,2,1,4,5},
										{6,7,1,12,255,1,252,250,249,248},
										{251,250,252,253,3,4,5,4,253,253},
										{251,251,252,252,3,2,4,4,0,0},
									 };


#endif