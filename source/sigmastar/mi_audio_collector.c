/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2022-06-24 16:18:56
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-06-24 16:18:56
 */

#ifndef USE_PC_SIMULATOR

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <stdint.h>
#include <limits.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <semaphore.h>

#include "sysconf.h"
#include "audioCollector/AudioCollectorProcess.h"
#include "audioMixer/AudioMixerProcess.h"
#include "mi_audio_collector.h"

#if(IS_DEVICE_AUDIO_COLLECTOR || IS_DEVICE_AUDIO_MIXER || IS_DEVICE_PHONE_GATEWAY)

#if(IS_DEVICE_AUDIO_COLLECTOR)
#define MI_AO_SAMPLE_PER_FRAME_LOCAL   768
#define MI_AI_SAMPLE_PER_FRAME  256     //256/32000=8ms
#elif(IS_DEVICE_AUDIO_MIXER)
#define MI_AO_SAMPLE_PER_FRAME_NET_PLAY     1152    //1152/44100=26ms
#define MI_AO_SAMPLE_PER_FRAME_NET_PAGING   1024    //1024/44100=23ms
#define MI_AI_SAMPLE_PER_FRAME  256                 //256/32000=8ms
#elif(IS_DEVICE_PHONE_GATEWAY)
#define MI_AI_SAMPLE_PER_FRAME  256                 //256/32000=8ms
#endif

#define USER_BUF_DEPTH      (4)
#define TOTAL_BUF_DEPTH     (8)


#define MAX_ADC_DATA_PKG_NUM    4
typedef struct
{
    int16_t adc0_data[MAX_ADC_DATA_PKG_NUM][MI_AI_SAMPLE_PER_FRAME];
    int16_t adc1_data[MAX_ADC_DATA_PKG_NUM][MI_AI_SAMPLE_PER_FRAME];
    int16_t adc2_data[MAX_ADC_DATA_PKG_NUM][MI_AI_SAMPLE_PER_FRAME];
    int16_t adc3_data[MAX_ADC_DATA_PKG_NUM][MI_AI_SAMPLE_PER_FRAME];
    int16_t adc_data_len[MAX_ADC_DATA_PKG_NUM];
    int8_t adc_data_valid[MAX_ADC_DATA_PKG_NUM];
    int8_t write_pos;
    int8_t read_pos;
}_st_AdcData_Info;

static _st_AdcData_Info st_AdcData_Info;


typedef struct AiChnPriv_s
{
    MI_AUDIO_DEV AiDevId;
    MI_AI_CHN AiChn;
    MI_S32 s32Fd;
    MI_U32 u32TotalSize;
    MI_U32 u32ChnCnt;
    pthread_t tid;
} AiChnPriv_t;

static AiChnPriv_t stAiChnPriv[MI_AUDIO_MAX_CHN_NUM];
static pthread_t tid_local_write;
static MI_S32   AiChnFd[MI_AUDIO_MAX_CHN_NUM] = {[0 ... MI_AUDIO_MAX_CHN_NUM-1] = -1};


#define MI_DEFAULT_AO_DEV_ID    0

#if(IS_DEVICE_AUDIO_COLLECTOR || IS_DEVICE_AUDIO_MIXER)
#define MI_DEFAULT_AI_DEV_ID    2       //I2S RX
#elif (IS_DEVICE_PHONE_GATEWAY)
#define MI_DEFAULT_AI_DEV_ID    5       //AI_DEV_ID_ADC_0_1_2 (5)
#endif

#define MI_DEFAULT_AI_CHANNEL_CNT   4

static int mi_ao_init_flag=0;
static int mi_ai_init_flag=0;
static int mi_ao_channel_num=0;
static int mi_ai_channel_num=0;

static unsigned int mi_ao_sampleRate=0;

static unsigned int mi_ao_totalByteNum=0;
//互斥锁
static pthread_mutex_t mutex_audio_out=PTHREAD_MUTEX_INITIALIZER;
static pthread_mutex_t mutex_audio_in=PTHREAD_MUTEX_INITIALIZER;
static pthread_mutex_t mutex_audio_eq=PTHREAD_MUTEX_INITIALIZER;


static sem_t sem_aiReady;  //信号量-ai数据已准备好

extern int adc_signal_can_detect;

int adc_signal_valid=0;         //ADC信号是否有效（四路通道至少存在一路）

static void* mi_audio_local_write_thread();

#if(IS_DEVICE_AUDIO_MIXER)
int16_t  buffer_stereo[16384]={0};
//音量表中数据表示的是音频通路数字部分的Gain值
//4095表示0dB,为0时表示Mute。音量可调整增益表中只做负增益
//需要正增益设置每个source源的预增益
//两级音量之间的计算公式为 "20*log(Vol1/Vol2)"，单位dB
const uint16_t mSysVol[100 + 1] =
{
	/* 0-100级音量控制, 0.25dB等级 */
	0,
	5, 10, 18, 27, 38, 50, 63, 79, 94, 112, /*-31.25dB*/
	126, 141, 135, 178, 199, 217, 237, 258, 282, 307, /*-22.5dB*/
	325, 345, 365, 387, 410, 434, 460, 487, 516, 546, /*-17.5dB*/
	562, 579, 595, 613, 631, 649, 668, 688, 708, 728, /*-15dB*/
	750, 772, 794, 817, 841, 866, 891, 917, 944, 971, /*-12.5db*/
	1000, 1029, 1059, 1090, 1122, 1154, 1188, 1223, 1259, 1295, /*-10db*/
	1333, 1372, 1412, 1453, 1496, 1539, 1584, 1631, 1678, 1727, /*-7.5dB*/
	1778, 1830, 1883, 1938, 1995, 2053, 2113, 2175, 2238, 2303, /*-5dB*/
	2371, 2440, 2511, 2584, 2660, 2738, 2817, 2900, 2984, 3072, /*-2.5dB*/
	3161, 3254, 3349, 3446, 3547, 3651, 3757, 3867, 3980, 4095/*0db*/
//	/*32级音量控制*/
//	0/*-72db*/,
//	3/*-56db*/,		6/*-56db*/,		15/*-49db*/,	26/*-44db*/,	41/*-40db*/,	65/*-36db*/,	103/*-32db*/,	145/*-29db*/,
//	205/*-26db*/,	258/*-24db*/,	325/*-22db*/,	410/*-20db*/,	460/*-19db*/,	516/*-18db*/,	576/*-17db*/,	649/*-16db*/,
//	728/*-15db*/,	817/*-14db*/,	917/*-13db*/,	1029/*-12db*/,	1154/*-11db*/,	1295/*-10db*/,	1453/*-9db*/,	1631/*-8db*/,
//	1830/*-7db*/,	2053/*-6db*/,	2303/*-5db*/,	2584/*-4db*/,	2900/*-3db*/,	3254/*-2db*/,	3651/*-1db*/,	4095/*0db*/
};
//淡化级别，0不淡化，1:-3dB，2：-6dB，3：-9dB，4：-12dB，5:-16dB，6：-19dB，7：-21dB，8：-24dB，9：-27dB
const uint16_t mixerVolumeFadeLevelInVol[10] =
{
	4096,
	2900,2050,1450,1030, /*-3dB,-6dB,-9dB,-12dB*/
    730,                //*-15dB,
    515,365,258,184 /*-18dB,-21dB,-24dB,-27dB*/
};

void mi_audio_write_mixer(const unsigned char *buffer, int count)
{
    #if !IS_DEVICE_AUDIO_MIXER
    return;
    #endif
    if(!mi_ao_init_flag)
    {
        return;
    }
    if(count == 0)
        return;
    //printf("mi_audio_write:%d...\n",count);
    MI_AUDIO_Frame_t stAoSendFrame;
    memset(&stAoSendFrame, 0x0, sizeof(MI_AUDIO_Frame_t));
    MI_AUDIO_DEV AoDevId = MI_DEFAULT_AO_DEV_ID;
    int i=0;
    //char *bufferLC=(char *)malloc(count);
    //char *bufferRC=(char *)malloc(count);
    
    int16_t *buffer_pcm_16;
    int buffer_len;
    int SamplesPreFrame=0;

    if(mi_ao_channel_num == 2)//立体声
    {
        buffer_len=count;
        SamplesPreFrame=buffer_len/2/2;
        memcpy(buffer_stereo,buffer,buffer_len);
        buffer_pcm_16=buffer_stereo;
    }
    else
    {
        buffer_len=count*2;
        SamplesPreFrame=buffer_len/2/2;
        buffer_pcm_16=(int16_t *)buffer;
        //单声道转双声道
        for(i=0;i<SamplesPreFrame;i++)
        {
            buffer_stereo[2 * i + 0] =  buffer_pcm_16[i];
            buffer_stereo[2 * i + 1] =  buffer_pcm_16[i];
        }
        buffer_pcm_16=buffer_stereo;
    }

    //printf("dac0_db=%d\n",Calculate_amplitude(buffer_pcm_16,SamplesPreFrame*2));
    int t_sysvol=mSysVol[(unsigned int)(g_system_volume*g_sub_volume*0.01)];
    if(g_paging_status == PAGING_START)
    {
        t_sysvol=mSysVol[(unsigned int)(pager_property.volume*g_sub_volume*0.01)];
    }

    //如果是网络音源（除寻呼、采集、混音外），需要先衰减10dB，与之前的控制设备兼容
    //20230524 混音发出去的数据跟寻呼类似，需要先处理好增益再发送，终端接收到音频后无需再次处理
    int sysSource = get_system_source();
    if( sysSource != SOURCE_NET_PAGING && !(sysSource >= SOURCE_AUDIO_COLLECTOR_BASE && sysSource <= SOURCE_AUDIO_COLLECTOR_MAX) )
    {
        for(i=0; i<SamplesPreFrame; i++)
        {
            //1295为-10dB
            buffer_pcm_16[2 * i + 0] = limit_value_16bit( ((((int32_t)buffer_pcm_16[2 * i + 0]) * 1295 + 2048) >> 12) );
            buffer_pcm_16[2 * i + 1] = limit_value_16bit( ((((int32_t)buffer_pcm_16[2 * i + 1]) * 1295 + 2048) >> 12) );
        }
    }

    //经过SGM8905后，输出衰减了3.7dB（6300），需要先放大
    for(i=0; i<SamplesPreFrame; i++)
    {
        buffer_pcm_16[2 * i + 0] = limit_value_16bit( ((((int32_t)buffer_pcm_16[2 * i + 0]) * 6300 + 2048) >> 12) );
        buffer_pcm_16[2 * i + 1] = limit_value_16bit( ((((int32_t)buffer_pcm_16[2 * i + 1]) * 6300 + 2048) >> 12) );
    }

    //混音器网络音源跟随混音器的音量大小
    for(i=0; i<SamplesPreFrame; i++)
    {
        buffer_pcm_16[2 * i + 0] = limit_value_16bit( ((((int32_t)buffer_pcm_16[2 * i + 0]) * t_sysvol + 2048) >> 12) );
        buffer_pcm_16[2 * i + 1] = limit_value_16bit( ((((int32_t)buffer_pcm_16[2 * i + 1]) * t_sysvol + 2048) >> 12) );
    }



    stAoSendFrame.u32Len[0] = buffer_len;
    stAoSendFrame.apVirAddr[0] = buffer_pcm_16;
    stAoSendFrame.apVirAddr[1] = NULL;

    int s32Ret = MI_SUCCESS;
    int k=0;
    do
    {
        s32Ret = MI_AO_SendFrame(AoDevId, 0, &stAoSendFrame, -1);
    }
    while (s32Ret == MI_AO_ERR_NOBUF);

    mi_ao_totalByteNum+=buffer_len;
#if 1
    MI_AO_ChnState_t stChnState;
    s32Ret = MI_AO_QueryChnStat(MI_DEFAULT_AO_DEV_ID, 0, &stChnState);
    //printf("ChnBusyNum=%d\n",stChnState.u32ChnBusyNum);
    if(stChnState.u32ChnBusyNum <= buffer_len && mi_ao_totalByteNum>=65536)
    {
        concentrated_write_need_wait=1;
        printf("concentrated_write_need_wait=1...\n");
    }
    else
    {
        concentrated_write_need_wait=0;
    }
#endif

}
#endif


void mi_audio_out_collector_init(unsigned int sample_rate, unsigned char fmt, unsigned char channels)
{
    mi_sys_init();
    //退出之前的audio
    mi_audio_out_collector_deinit();

    pthread_mutex_lock(&mutex_audio_out);
    MI_S32 ret = MI_SUCCESS;
    MI_AUDIO_Attr_t stAoSetAttr, stAoGetAttr;
    MI_S32 s32AoGetVolume;
    MI_AO_ChnParam_t stAoChnParam;
    MI_U32 u32DmaBufSize;
    MI_BOOL bInitAdec = FALSE, bInitRes = FALSE;

    MI_AUDIO_DEV AoDevId = MI_DEFAULT_AO_DEV_ID;
    MI_AO_CHN AoChn = 0;


    memset(&stAoSetAttr, 0x0, sizeof(MI_AUDIO_Attr_t));
    stAoSetAttr.eBitwidth = E_MI_AUDIO_BIT_WIDTH_16;
    stAoSetAttr.eWorkmode = E_MI_AUDIO_MODE_I2S_MASTER;
    stAoSetAttr.WorkModeSetting.stI2sConfig.bSyncClock = FALSE;
    stAoSetAttr.WorkModeSetting.stI2sConfig.eFmt = E_MI_AUDIO_I2S_FMT_I2S_MSB;
    stAoSetAttr.WorkModeSetting.stI2sConfig.eMclk = E_MI_AUDIO_I2S_MCLK_0;
    
#if IS_DEVICE_AUDIO_COLLECTOR
    stAoSetAttr.u32PtNumPerFrm = MI_AO_SAMPLE_PER_FRAME_LOCAL;
#elif IS_DEVICE_AUDIO_MIXER
    int sysSource = get_system_source();
    if(sysSource == SOURCE_NET_PAGING || (sysSource >= SOURCE_AUDIO_COLLECTOR_BASE && sysSource <= SOURCE_AUDIO_COLLECTOR_MAX))
    {
        stAoSetAttr.u32PtNumPerFrm = MI_AO_SAMPLE_PER_FRAME_NET_PAGING;
    }
    else
    {
        stAoSetAttr.u32PtNumPerFrm = MI_AO_SAMPLE_PER_FRAME_NET_PLAY;
    }
#endif

    stAoSetAttr.u32ChnCnt = 1;

    mi_ao_channel_num=channels;
    mi_ao_sampleRate=sample_rate;

    stAoSetAttr.eSoundmode = E_MI_AUDIO_SOUND_MODE_STEREO;  //永远保持双通道输出

    stAoSetAttr.eSamplerate = sample_rate;

    

    ret = MI_AO_SetPubAttr(AoDevId, &stAoSetAttr);

    if(MI_SUCCESS != ret)
    {
        printf("set ao %d attr err:0x%x\n", AoDevId, ret);
        pthread_mutex_unlock(&mutex_audio_out);
        return;
    }

    ret = MI_AO_GetPubAttr(AoDevId, &stAoGetAttr);
    if(MI_SUCCESS != ret)
    {
        printf("get ao %d attr err:0x%x\n", AoDevId, ret);
        pthread_mutex_unlock(&mutex_audio_out);
        return;
    }
    /* enable ao device */
    ret = MI_AO_Enable(AoDevId);
    if(MI_SUCCESS != ret)
    {
        printf("enable ao dev %d err:0x%x\n", AoDevId, ret);
        pthread_mutex_unlock(&mutex_audio_out);
        return;
    }
    ret = MI_AO_EnableChn(AoDevId, AoChn);
    if (MI_SUCCESS != ret)
    {
        printf("enable ao dev %d chn %d err:0x%x\n", AoDevId, AoChn, ret);
        pthread_mutex_unlock(&mutex_audio_out);
        return;
    }
    printf("mi_audio_out_init succeed:%d...\n",sample_rate);

    MI_AO_SetVolume(AoDevId, AoChn, 0, E_MI_AO_GAIN_FADING_OFF);

    mi_ao_init_flag=1;

    pthread_mutex_unlock(&mutex_audio_out);
}


void mi_audio_out_collector_deinit()
{
    pthread_mutex_lock(&mutex_audio_out);
    MI_S32 ret;
    MI_AUDIO_DEV AoDevId = MI_DEFAULT_AO_DEV_ID;
    MI_AO_CHN AoChn = 0;
    if(mi_ao_init_flag)
    {
        /* clear chn buf */  
        ret = MI_AO_ClearChnBuf(AoDevId, AoChn);  
        if (MI_SUCCESS != ret)  
        {  
            printf("clear chn buf ao dev %d chn %d err:0x%x\n", AoDevId, AoChn, ret);  
            return;  
        }  

        ret = MI_AO_DisableChn(AoDevId, AoChn);
        if(MI_SUCCESS != ret)
        {
            printf("disable ao dev %d chn %d err:0x%x\n", 0, 0, ret);
            pthread_mutex_unlock(&mutex_audio_out);
            return;
        }
        ret = MI_AO_Disable(AoDevId);
        if (MI_SUCCESS != ret)
        {
            printf("disable ao dev %d err:0x%x\n", 0, ret);
            pthread_mutex_unlock(&mutex_audio_out);
            return;
        }
        mi_ao_init_flag=0;
        mi_ao_totalByteNum=0;

        printf("mi_audio_out_deinit ok...\n");
    }
    pthread_mutex_unlock(&mutex_audio_out);
}



static void* aiGetChnPortBuf(void* data)
{
    AiChnPriv_t* priv = (AiChnPriv_t*)data;
    MI_AUDIO_Frame_t stAiChFrame;
    MI_AUDIO_AecFrame_t stAecFrame;
    MI_S32 s32Ret;
    struct timeval tv_before, tv_after;
    MI_S64 before_us, after_us;
    MI_U32 u32ChnIdx, u32ChnIdxStart,u32ChnIdxEnd;

    memset(&stAiChFrame, 0, sizeof(MI_AUDIO_Frame_t));
    memset(&stAecFrame, 0, sizeof(MI_AUDIO_AecFrame_t));

    int can_play=0;
    while(mi_ai_init_flag)
    {
        //gettimeofday(&tv_before, NULL);
        u32ChnIdxStart = 0;
        u32ChnIdxEnd = priv->u32ChnCnt;
        int u32_len=0;
        for (u32ChnIdx = u32ChnIdxStart; u32ChnIdx < u32ChnIdxEnd; u32ChnIdx++)
        {
            s32Ret = MI_AI_GetFrame(priv->AiDevId, u32ChnIdx, &stAiChFrame, NULL, -1);
            #if(CURRENT_DEVICE_MODEL == MODEL_AUDIO_COLLECTOR_C)
            {
                if(u32ChnIdx == 0)  //对应MIC1
                    memcpy(st_AdcData_Info.adc2_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
                else if(u32ChnIdx == 1) //对应MIC3
                    memcpy(st_AdcData_Info.adc0_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
                else if(u32ChnIdx == 2) //对应MIC2
                    memcpy(st_AdcData_Info.adc3_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
                else if(u32ChnIdx == 3) //对应MIC4
                    memcpy(st_AdcData_Info.adc1_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            }
            #elif(CURRENT_DEVICE_MODEL == MODEL_AUDIO_MIXER_ENCODER_C)
            {
                if(u32ChnIdx == 0)  //对应MIC1，AIN3+AIN4混音（也就是线路混音）
                    memcpy(st_AdcData_Info.adc1_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
                else if(u32ChnIdx == 1) //对应MIC3（麦克风）
                    memcpy(st_AdcData_Info.adc3_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
                else if(u32ChnIdx == 2) //对应MIC2（网络）
                    memcpy(st_AdcData_Info.adc0_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
                else if(u32ChnIdx == 3) //对应MIC4（另外一个线路，需要和MIC1的线路混音）
                    memcpy(st_AdcData_Info.adc2_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            }
            #elif(IS_DEVICE_AUDIO_COLLECTOR || IS_DEVICE_AUDIO_MIXER)
            if(u32ChnIdx == 0)  //网络,对应MIC1
                memcpy(st_AdcData_Info.adc0_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            else if(u32ChnIdx == 1) //channel2、channel3数据对调,原因不明 //无效，对应MIC3
                memcpy(st_AdcData_Info.adc2_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            else if(u32ChnIdx == 2) //AUX，对应MIC2
                memcpy(st_AdcData_Info.adc1_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            else if(u32ChnIdx == 3) //MIC，对应MIC4
                memcpy(st_AdcData_Info.adc3_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            #elif IS_DEVICE_PHONE_GATEWAY
            if(u32ChnIdx == 0)
                memcpy(st_AdcData_Info.adc0_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            else if(u32ChnIdx == 1)
                memcpy(st_AdcData_Info.adc1_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            else if(u32ChnIdx == 2)
                memcpy(st_AdcData_Info.adc2_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            else if(u32ChnIdx == 3)
                memcpy(st_AdcData_Info.adc3_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            #endif
            
            u32_len = stAiChFrame.u32Len[0];
            MI_AI_ReleaseFrame(priv->AiDevId, u32ChnIdx,  &stAiChFrame,  NULL);
        }

        if (MI_SUCCESS == s32Ret)
        {
            //printf("u32=%d\n",u32_len);
            #if 1
            st_AdcData_Info.adc_data_len[st_AdcData_Info.write_pos] = u32_len;
            st_AdcData_Info.adc_data_valid[st_AdcData_Info.write_pos] = 1;
            st_AdcData_Info.write_pos++;
            if(st_AdcData_Info.write_pos>=MAX_ADC_DATA_PKG_NUM)
            {
                st_AdcData_Info.write_pos=0;
            }
            #endif
            #if 0
            //第一个包延迟发送，避免卡顿
            if(can_play<1) //first can_play=0,enter this=1
            {
               can_play++;
            }
            else
            #endif
            {
                sem_post(&sem_aiReady);
            }
            //pthread_mutex_unlock(&mutex_system_source);
            
            //printf("write: %x,len: %d\n",stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            //write(priv->s32Fd, stAiChFrame.apVirAddr[0], stAiChFrame.u32Len[0]);
            #if 0
            gettimeofday(&tv_after, NULL);
            before_us = tv_before.tv_sec * 1000000 + tv_before.tv_usec;
            after_us = tv_after.tv_sec * 1000000 + tv_after.tv_usec;
            //if (after_us - before_us > 10 * 1000)
            {
                //printf("Chn:%d,len=%d,cost time:%lldus = %lldms.\n", priv->AiChn, stAiChFrame.u32Len[0], after_us - before_us, (after_us - before_us) / 1000);
            }
            priv->u32TotalSize += stAiChFrame.u32Len[0];
            {
                //printf("Chn:%d,len=%d,cost time:%lldus = %lldms.\n");
            }
            #endif
        }
        else
        {
            printf("Dev%dChn%d get frame failed!!!error:0x%x\n", priv->AiDevId, priv->AiChn, s32Ret);
        }
    }
    return NULL;
}

#if 1
void mi_audio_in_collector_init(unsigned int sample_rate, unsigned char fmt, unsigned char channels)
{
    mi_sys_init();
    //退出之前的audio
    mi_audio_in_collector_deinit();

    sem_init(&sem_aiReady,0,0);

    pthread_mutex_lock(&mutex_audio_in);
    MI_S32 ret = MI_SUCCESS;
    MI_AUDIO_Attr_t stAiSetAttr, stAiGetAttr;
    MI_SYS_ChnPort_t    stAiChnOutputPort0[MI_AUDIO_MAX_CHN_NUM];
    MI_AI_ChnParam_t stAiChnParam[MI_AUDIO_MAX_CHN_NUM];
    MI_BOOL bInitAdec = FALSE, bInitRes = FALSE;

    MI_AUDIO_DEV AiDevId = MI_DEFAULT_AI_DEV_ID;
    MI_AO_CHN AiChn = 0;

    MI_U32  u32ChnIdx;

    memset(&stAiSetAttr, 0x0, sizeof(MI_AUDIO_Attr_t));
    stAiSetAttr.eBitwidth = E_MI_AUDIO_BIT_WIDTH_16;
    stAiSetAttr.u32PtNumPerFrm = MI_AI_SAMPLE_PER_FRAME;
    stAiSetAttr.u32ChnCnt = MI_DEFAULT_AI_CHANNEL_CNT;
    stAiSetAttr.eSoundmode = E_MI_AUDIO_SOUND_MODE_MONO;
    stAiSetAttr.eSamplerate = sample_rate;

    stAiSetAttr.eWorkmode = E_MI_AUDIO_MODE_TDM_MASTER;
    stAiSetAttr.WorkModeSetting.stI2sConfig.bSyncClock = TRUE;
    stAiSetAttr.WorkModeSetting.stI2sConfig.eFmt = E_MI_AUDIO_I2S_FMT_I2S_MSB;
    stAiSetAttr.WorkModeSetting.stI2sConfig.eMclk = E_MI_AUDIO_I2S_MCLK_12_288M;
    stAiSetAttr.WorkModeSetting.stI2sConfig.u32TdmSlots = MI_DEFAULT_AI_CHANNEL_CNT;
    stAiSetAttr.WorkModeSetting.stI2sConfig.eI2sBitWidth = E_MI_AUDIO_BIT_WIDTH_16;


    ret = MI_AI_SetPubAttr(AiDevId, &stAiSetAttr);

    if(MI_SUCCESS != ret)
    {
        printf("set ao %d attr err:0x%x\n", AiDevId, ret);
        pthread_mutex_unlock(&mutex_audio_in);
        return;
    }

    ret = MI_AI_GetPubAttr(AiDevId, &stAiGetAttr);
    if(MI_SUCCESS != ret)
    {
        printf("get ai %d attr err:0x%x\n", AiDevId, ret);
        pthread_mutex_unlock(&mutex_audio_in);
        return;
    }
    /* enable ai device */
    ret = MI_AI_Enable(AiDevId);
    if(MI_SUCCESS != ret)
    {
        printf("enable ai dev %d err:0x%x\n", AiDevId, ret);
        pthread_mutex_unlock(&mutex_audio_in);
        return;
    }

    memset(&stAiChnParam, 0x0, sizeof(MI_AI_ChnParam_t));

    for(u32ChnIdx=0;u32ChnIdx<stAiSetAttr.u32ChnCnt;u32ChnIdx++)
    {
        stAiChnParam[u32ChnIdx].stChnGain.bEnableGainSet = TRUE;
        if(u32ChnIdx == 0)      //LINE
        {
            stAiChnParam[u32ChnIdx].stChnGain.s16FrontGain = 2;    //0dB
            stAiChnParam[u32ChnIdx].stChnGain.s16RearGain = 0;
        }
        else if(u32ChnIdx == 1) //LOCAL MIC
        {
            stAiChnParam[u32ChnIdx].stChnGain.s16FrontGain = 2;    //0dB
            stAiChnParam[u32ChnIdx].stChnGain.s16RearGain = 0;
        }
        else if(u32ChnIdx == 2 || u32ChnIdx == 3)   //Remote MIC or environment monitor
        {
            stAiChnParam[u32ChnIdx].stChnGain.s16FrontGain = 2;    //0dB
            stAiChnParam[u32ChnIdx].stChnGain.s16RearGain = 0;
        }
    }

    mi_ai_init_flag=1;

    for(u32ChnIdx=0;u32ChnIdx<stAiSetAttr.u32ChnCnt;u32ChnIdx++)
    {
        stAiChnPriv[u32ChnIdx].AiChn = u32ChnIdx;
        stAiChnPriv[u32ChnIdx].AiDevId = AiDevId;
        stAiChnPriv[u32ChnIdx].s32Fd = AiChnFd[u32ChnIdx];
        stAiChnPriv[u32ChnIdx].u32ChnCnt = MI_DEFAULT_AI_CHANNEL_CNT; 
        stAiChnPriv[u32ChnIdx].u32TotalSize = 0;

        MI_AI_SetChnParam( AiDevId,  u32ChnIdx, &stAiChnParam[u32ChnIdx]);

        if(u32ChnIdx<stAiChnPriv[u32ChnIdx].u32ChnCnt)
        {
            stAiChnOutputPort0[u32ChnIdx].eModId = E_MI_MODULE_ID_AI;
            stAiChnOutputPort0[u32ChnIdx].u32DevId = AiDevId;
            stAiChnOutputPort0[u32ChnIdx].u32ChnId = u32ChnIdx;
            stAiChnOutputPort0[u32ChnIdx].u32PortId = 0;
            
            //ExecFunc(MI_AI_SetVqeVolume(AiDevId, u32ChnIdx, s32AiVolume), MI_SUCCESS);
            MI_SYS_SetChnOutputPortDepth(&stAiChnOutputPort0[u32ChnIdx], USER_BUF_DEPTH, TOTAL_BUF_DEPTH);
        }

        MI_AI_EnableChn(AiDevId, u32ChnIdx);
        if(u32ChnIdx==0)
        {
            struct sched_param sched3;
            sched3.sched_priority = 99;
            pthread_create(&tid_local_write, NULL, mi_audio_local_write_thread, NULL);
            pthread_create(&stAiChnPriv[u32ChnIdx].tid, NULL, aiGetChnPortBuf, &stAiChnPriv[u32ChnIdx]);
            //pthread_setname_np(stAiChnPriv[u32ChnIdx].tid,"AiThread");
            pthread_setschedparam(tid_local_write, SCHED_RR,  &sched3);
            pthread_setschedparam(stAiChnPriv[u32ChnIdx].tid, SCHED_RR,  &sched3);
        }
        //pthread_create(&stAiChnPriv[u32ChnIdx].tid, NULL, aiGetChnPortBuf, &stAiChnPriv[u32ChnIdx]);
    }


    printf("mi_audio_in_init succeed...\n");

    pthread_mutex_unlock(&mutex_audio_in);
}


void mi_audio_in_collector_deinit()
{
    pthread_mutex_lock(&mutex_audio_in);
    MI_S32 ret;
    if(mi_ai_init_flag)
    {
        MI_U32 u32ChnIdx, u32ChnIdxStart,u32ChnIdxEnd;
        u32ChnIdxStart = 0;
        u32ChnIdxEnd = MI_DEFAULT_AI_CHANNEL_CNT;

        mi_ai_init_flag=0;
        sem_post(&sem_aiReady);

        pthread_join(stAiChnPriv[0].tid, NULL);

        for (u32ChnIdx = u32ChnIdxStart; u32ChnIdx < u32ChnIdxEnd; u32ChnIdx++)
        {
            ret = MI_AI_DisableChn(MI_DEFAULT_AI_DEV_ID, u32ChnIdx);
            if(MI_SUCCESS != ret)
            {
                printf("disable ai dev %d chn %d err:0x%x\n", 0, 0, ret);
                pthread_mutex_unlock(&mutex_audio_in);
                return;
            }
        }
        ret = MI_AI_Disable(MI_DEFAULT_AI_DEV_ID);
        if (MI_SUCCESS != ret)
        {
            printf("disable ai dev %d err:0x%x\n", 0, ret);
            pthread_mutex_unlock(&mutex_audio_in);
            return;
        }

        sem_destroy(&sem_aiReady);
        printf("mi_audio_in_deinit ok...\n");
    }
    pthread_mutex_unlock(&mutex_audio_in);
}
#endif







void* mi_audio_local_write_thread()
{

    AedHandle AED_HANDLE; 
    unsigned int aedBuffSize=0;  
    char *working_buf_ptr = NULL;
    AedProcessStruct aed_params;

    AedSampleRate sample_rate = AED_SRATE_32K;  
    aed_params.channel = 1; 
    aed_params.point_number = 128;      //AED算法的采样点只支持128

    ALGO_AED_RET ret = ALGO_AED_RET_SUCCESS;

    aedBuffSize = IaaAed_GetBufferSize();  
    working_buf_ptr = (char *)malloc(aedBuffSize);  
    if(NULL == working_buf_ptr)  
    {  
        printf("malloc workingBuffer failed !\n");   
    }  
    printf("malloc workingBuffer succeed !\n");  
    AED_HANDLE = IaaAed_Init(working_buf_ptr, &aed_params);  
    if(NULL == AED_HANDLE)  
    {  
        printf("IaaAed_Init faild !\n");  
    }  
    printf("IaaAed_Init succeed !\n");  
    ret = IaaAed_Config(AED_HANDLE);  
    if(ALGO_AED_RET_SUCCESS != ret)  
    {  
        printf("IaaAed_Config failed !, ret = %d\n", ret);  
    }

    ret = IaaAed_SetSampleRate(AED_HANDLE, sample_rate);  
    if(ALGO_AED_RET_SUCCESS != ret)  
    {  
        printf("IaaAed_SetSampleRate failed !, ret = %d\n", ret);  
    }  
    #if 0
    ret = IaaAed_SetLsdThreshold(AED_HANDLE, lsd_threshold_db);  
    if(ALGO_AED_RET_SUCCESS != ret)  
    {  
        printf("IaaAed_SetLsdThreshold failed !, ret = %d\n", ret);  
        return -1;  
    }  
    #endif

    printf("IaaAed OK...\n");

    //printf("mi_audio_write:%d...\n",count);
    MI_AUDIO_Frame_t stAoSendFrame;
    memset(&stAoSendFrame, 0x0, sizeof(MI_AUDIO_Frame_t));
    MI_AUDIO_DEV AoDevId = MI_DEFAULT_AO_DEV_ID;
    int i=0;
    
    int16_t *adc0_ori_pcm_16,*adc1_ori_pcm_16,*adc2_ori_pcm_16,*adc3_ori_pcm_16;

    int16_t  send_adc0_progcess_mono[MI_AI_SAMPLE_PER_FRAME]={0};
    int16_t  send_adc1_progcess_mono[MI_AI_SAMPLE_PER_FRAME]={0};
    int16_t  send_adc2_progcess_mono[MI_AI_SAMPLE_PER_FRAME]={0};
    int16_t  send_adc3_progcess_mono[MI_AI_SAMPLE_PER_FRAME]={0};

    int16_t  adc0_progcess_mono[MI_AI_SAMPLE_PER_FRAME]={0};
    int16_t  adc1_progcess_mono[MI_AI_SAMPLE_PER_FRAME]={0};
    int16_t  adc2_progcess_mono[MI_AI_SAMPLE_PER_FRAME]={0};
    int16_t  adc3_progcess_mono[MI_AI_SAMPLE_PER_FRAME]={0};
    int16_t mono_mix[MI_AI_SAMPLE_PER_FRAME]={0};
    int16_t  mix_progcess_stero[MI_AI_SAMPLE_PER_FRAME*2]={0};
    
    int buffer_len;
    int SamplesPreFrame=0;

    buffer_len=MI_AI_SAMPLE_PER_FRAME*2*2;
    SamplesPreFrame=buffer_len/2/2;

    int is_local_source=0;
    int local_source_cnt=0;

    double perSamples_delay_ms = (double)MI_AI_SAMPLE_PER_FRAME / AED_SRATE_32K *1000;
    int enter_local_source_threshold=1000/perSamples_delay_ms;
    int signal_db_print_threshold=500/perSamples_delay_ms;
    int signal_timeout_threshold=COLLECTOR_SIGNAL_INVLID_TIMEOUT*1000/perSamples_delay_ms;

    int signal_timeOutSamplesCnt=0;
    int signal_check_threshold=30*60*1000/perSamples_delay_ms;    //每隔30分钟开始检测一次信号,无信号时强制休息1秒钟
    int signal_sleep_threshold=1000/perSamples_delay_ms;          //时间到了，休息1秒
    int signal_sleep_cnt=0;

    printf("perSamples_delay_ms=%f,signal_db_print_threshold=%d,signal_timeout_threshold=%d\n",perSamples_delay_ms,signal_db_print_threshold,signal_timeout_threshold);

    //需要独立检测判断阈值
    int adc_threshold_array[4]={AUDIO_COLLECTOR_CH1_VALID_THRESHOLD_DB,AUDIO_COLLECTOR_CH2_VALID_THRESHOLD_DB,AUDIO_COLLECTOR_CH3_VALID_THRESHOLD_DB,AUDIO_COLLECTOR_CH4_VALID_THRESHOLD_DB};
    int adc_valid_array[4]={0};
    int adc_timeoutCnt_array[4]={0};

    while(mi_ai_init_flag)
    {
        sem_wait(&sem_aiReady);
        if(!mi_ai_init_flag)
            break;

        //pthread_mutex_lock(&mutex_audio_out);
        if(st_AdcData_Info.adc_data_valid[st_AdcData_Info.read_pos])
        {
            adc0_ori_pcm_16=(int16_t *)st_AdcData_Info.adc0_data[st_AdcData_Info.read_pos];
            adc1_ori_pcm_16=(int16_t *)st_AdcData_Info.adc1_data[st_AdcData_Info.read_pos];
            adc2_ori_pcm_16=(int16_t *)st_AdcData_Info.adc2_data[st_AdcData_Info.read_pos];
            adc3_ori_pcm_16=(int16_t *)st_AdcData_Info.adc3_data[st_AdcData_Info.read_pos];

            int16_t *adc_ori_buf_array[4]={adc0_ori_pcm_16,adc1_ori_pcm_16,adc2_ori_pcm_16,adc3_ori_pcm_16};
            int16_t *adc_pro_buf_array[4]={adc0_progcess_mono,adc1_progcess_mono,adc2_progcess_mono,adc3_progcess_mono};
            int16_t *send_adc_pro_buf_array[4]={send_adc0_progcess_mono,send_adc1_progcess_mono,send_adc2_progcess_mono,send_adc3_progcess_mono};

            int db_val=-96;
            if(adc_signal_can_detect)
            //if(1)
            {
                for(int i=0;i<4;i++)
                {
                    #if 0   //采集器和混音器开机不读取dsp设置
                    switch(i)
                    {
                        case 0:
                            if(!dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_L])
                            {
                                continue;
                            }
                        break;
                        case 1:
                            if(!dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_R])
                            {
                                continue;
                            }
                        break;
                        case 2:
                            if(!dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC1_L])
                            {
                                continue;
                            }
                        break;
                        #if 0   //ADC1_R出厂switch为0
                        case 3:
                            if(!dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC1_R])
                            {
                                continue;
                            }
                        break;
                        #endif
                    }
                    #endif
                    IaaAed_RunLsd(AED_HANDLE,*(adc_ori_buf_array+i), &db_val);
                    if(i==1)
                    {
                        //printf("ADC%d:%d dB\n",i,db_val);
                    }
                    if(db_val>adc_threshold_array[i])
                    {
                        if(!adc_valid_array[i])
                        {
                            printf("ADC%d is valid:%d dB\n",i,db_val);
                            adc_valid_array[i]=1;
                        }
                        adc_timeoutCnt_array[i]=0;
                    }
                    else
                    {
                        if(adc_valid_array[i])
                        {
                            adc_timeoutCnt_array[i]++;
                            if(adc_timeoutCnt_array[i] == signal_timeout_threshold)
                            {
                                printf("ADC%d is invaild:%d dB\n",i,db_val);
                                adc_valid_array[i]=0;
                            }
                        }
                    }
                }
            }

            #if IS_DEVICE_AUDIO_MIXER
            //混音器，网络音源强制认为有效，不判断具体的增益是否满足要求，因为可能有小信号
            if(get_system_source() != SOURCE_NULL && get_system_source() != SOURCE_AUX)
            {
                adc_valid_array[0]=1;
            }
            else
            {
                adc_valid_array[0]=0;
            }
            #endif


            //四路通道信号均超时，认为ADC无信号
            if(!adc_valid_array[0] && !adc_valid_array[1] && !adc_valid_array[2] && !adc_valid_array[3])
            {
                adc_signal_valid=0;
            }
            else
            {
                adc_signal_valid=1;
            }

            //量化各路ADC音量
            for(i=0;i<4;i++)
            {
                if(!adc_valid_array[i])
                {
                    memset(*(adc_pro_buf_array+i),0,MI_AI_SAMPLE_PER_FRAME*2);
                    memset(*(send_adc_pro_buf_array+i),0,MI_AI_SAMPLE_PER_FRAME*2);
                }
                else
                {
                    int module_gain_out = 9200;     //7db
                    for(int k=0; k<SamplesPreFrame; k++)
                    {
                        *(*(adc_pro_buf_array+i)+k) = limit_value_16bit( (((int32_t)(*(*(adc_ori_buf_array+i)+k))) * module_gain_out + 2048) >> 12 );
                    }
                    #if IS_DEVICE_AUDIO_MIXER
                    if(i==0)    //ADC0(NET)音源不处理增益
                    {
                        memcpy(*(send_adc_pro_buf_array+i),*(adc_ori_buf_array+i),MI_AI_SAMPLE_PER_FRAME*2);
                    }
                    else
                    #endif
                    {
                        int module_gain_network = 2750; //-3.5dB
                        for(int k=0; k<SamplesPreFrame; k++)
                        {
                            *(*(send_adc_pro_buf_array+i)+k) = limit_value_16bit( (((int32_t)(*(*(adc_ori_buf_array+i)+k))) * module_gain_network + 2048) >> 12 );
                        }
                    }
                }
            }

            #if 1       //检测是否需要休息，每隔30分钟，检测到没信号时休息1秒钟，避免网络累积延迟，本地无需考虑累积延迟
            int adc_netCanSend=1;   //网路是否可以发送标志（用于每隔一段时间自动休眠，避免网络累积延时）
            if(signal_timeOutSamplesCnt == signal_check_threshold)
            {
                if(signal_sleep_cnt<signal_sleep_threshold)   //如果还没休息够
                {
                    if(!adc_signal_valid)   //没有信号，开始休息
                    {
                        signal_sleep_cnt++;
                        adc_netCanSend=0;
                    }
                }
                else    //已经休息够了，让其下次重新开始计时
                {
                    signal_sleep_cnt=0;
                    signal_timeOutSamplesCnt=0;
                }
            }
            else
            {
                signal_timeOutSamplesCnt++;
            }
            #endif

            #if IS_DEVICE_AUDIO_COLLECTOR

            for(i=0;i<4;i++)
            {
                if(adc_valid_array[i])
                {
                    audioCollector_info.m_nChannelStatus |= g_device_collector_channel_map[i];
                }
                else
                {
                    audioCollector_info.m_nChannelStatus &= ~g_device_collector_channel_map[i];
                }
            }

            int triggerChannelValid=audioCollector_info.m_nChannelStatus & g_device_collector_channel_map[audioCollector_info.m_nTriggerChannelId-1];
            if(audioCollector_info.m_nTriggerSwitch)
            {
                if(triggerChannelValid)
                {
                    if(!audioCollector_info.m_nSignalValid)
                    {
                        if(IS_SERVER_CONNECTED)
                        {
                            audioCollector_info.m_nSignalValid=1;
                            send_online_info();
                        }
                    }
                }
                else
                {
                    if(audioCollector_info.m_nSignalValid)
                    {
                        audioCollector_info.m_nSignalValid=0;
                        send_online_info();
                    }
                }
            }

            if(adc_netCanSend)
            {
                audioCollector_Data_Add(send_adc_pro_buf_array,st_AdcData_Info.adc_data_len[st_AdcData_Info.read_pos]);
                pcmAudioMix4(mono_mix,adc0_progcess_mono,adc1_progcess_mono,adc2_progcess_mono,adc3_progcess_mono,SamplesPreFrame);
            }
            #elif IS_DEVICE_AUDIO_MIXER
            //A1+A2=MIC=MIC4P=ADC3
            //A3+A4=AUX=MIC2P=ADC1
            //NET=MIC1P=ADC0

            audioMixer_info.m_nSignalValid[0] = adc_valid_array[0];
            audioMixer_info.m_nSignalValid[1] = adc_valid_array[3];
            #if (CURRENT_DEVICE_MODEL == MODEL_AUDIO_MIXER_ENCODER_C)
            audioMixer_info.m_nSignalValid[2] = adc_valid_array[1] || adc_valid_array[2];
            #elif (CURRENT_DEVICE_MODEL == MODEL_AUDIO_MIXER_ENCODER) 
            audioMixer_info.m_nSignalValid[2] = adc_valid_array[1];
            #endif

            if(adc_signal_valid)
            {
                //MIXER_C额外的ADC2需要与ADC1混合
                #if (CURRENT_DEVICE_MODEL == MODEL_AUDIO_MIXER_ENCODER_C)
                if(adc_valid_array[2])
                {
                    int16_t  mix_adc1_adc2[MI_AI_SAMPLE_PER_FRAME]={0};
                    pcmAudioMix2(mix_adc1_adc2,adc1_progcess_mono,adc2_progcess_mono,SamplesPreFrame);
                    memcpy(send_adc1_progcess_mono,mix_adc1_adc2,MI_AI_SAMPLE_PER_FRAME*2);
                }
                #endif

                //printf("g_audio_mixer_signal_valid=%d,m_nMasterSwitch=%d\n",g_audio_mixer_signal_valid,audioMixer_info.m_nMasterSwitch);
                //MIC存在时，AUX、网络音源需要根据淡化级别处理
                if(adc_valid_array[3])
                {   
                    int fadeVolume=mixerVolumeFadeLevelInVol[audioMixer_info.m_nVolumeFadeLevel];
                    for(int k=0; k<SamplesPreFrame; k++)
                    {
                        send_adc1_progcess_mono[k] = limit_value_16bit( ((((int32_t)send_adc1_progcess_mono[k]) * fadeVolume + 2048) >> 12) );
                        send_adc0_progcess_mono[k] = limit_value_16bit( ((((int32_t)send_adc0_progcess_mono[k]) * fadeVolume + 2048) >> 12) );
                    }
                }
                if(!g_audio_mixer_signal_valid)
                {
                    if(audioMixer_info.m_nMasterSwitch)
                    {
                        if(audioMixer_info.m_nTriggerType == 1 ||\
                           (audioMixer_info.m_nTriggerType == 2 && (adc_valid_array[3] || adc_valid_array[0])) ||\
                           (audioMixer_info.m_nTriggerType == 3 && (adc_valid_array[1] || adc_valid_array[0]))
                          )
                        {
                            if(IS_SERVER_CONNECTED)
                            {
                                printf("g_audio_mixer_signal_valid=1,m_nTriggerType=%d=%d\n",audioMixer_info.m_nTriggerType);
                                audioMixer_clear();
                                g_audio_mixer_signal_valid=1;   //音频混音器信号有效
                                send_online_info();
                            }
                        }
                    }
                }
                if(get_system_source() == SOURCE_NULL)
                {
                    g_signal_aux=1;
                    set_system_source(SOURCE_AUX);
                    pkg_query_current_status(NULL);	//发送当前状态
                }
            }
            else
            {
                if(g_audio_mixer_signal_valid)
                {
                    printf("g_audio_mixer_signal_valid=0\n");
                    g_audio_mixer_signal_valid=0;   //音频混音器信号无效
                    g_device_audio_mixer_ready=0;   //混音器准备标志重置
                    send_online_info();
                }
                if(get_system_source() == SOURCE_AUX)
                {
                    g_signal_aux=0;
                    set_system_source(SOURCE_NULL);
                    pkg_query_current_status(NULL);	//发送当前状态
                }
            }
            if(g_audio_mixer_signal_valid && g_device_audio_mixer_ready && adc_netCanSend)        //无信号时不发送，超过5秒不发送，可能引起终端退出混音音源。
            {
                if(audioMixer_info.m_nTriggerType == 1)         //混合触发
                {
                    pcmAudioMix4(mono_mix,send_adc0_progcess_mono,send_adc1_progcess_mono,send_adc2_progcess_mono,send_adc3_progcess_mono,SamplesPreFrame);
                    //如果麦克风存在，那么信号类型为MIC，否则为AUX
                    if(adc_valid_array[3])
                    {
                       g_audio_mixer_signalType = MIXER_SIGNAL_TYPE_MIC;    //信号类型:MIC
                    }
                    else
                    {
                        g_audio_mixer_signalType = MIXER_SIGNAL_TYPE_AUX;   //信号类型:AUX
                    }
                }
                else if(audioMixer_info.m_nTriggerType == 2)    //MIC触发，NET+MIC
                {
                    pcmAudioMix2(mono_mix,send_adc0_progcess_mono,send_adc3_progcess_mono,SamplesPreFrame);
                    //如果麦克风存在，那么信号类型为MIC，否则为AUX
                    if(adc_valid_array[3])
                    {
                       g_audio_mixer_signalType = MIXER_SIGNAL_TYPE_MIC;    //信号类型:MIC
                    }
                    else
                    {
                        g_audio_mixer_signalType = MIXER_SIGNAL_TYPE_AUX;   //信号类型:AUX
                    }
                }
                else if(audioMixer_info.m_nTriggerType == 3)   //AUX触发，NET+AUX
                {
                    pcmAudioMix2(mono_mix,send_adc0_progcess_mono,send_adc1_progcess_mono,SamplesPreFrame);
                    g_audio_mixer_signalType = MIXER_SIGNAL_TYPE_AUX;       //信号类型:AUX
                }

                audioMixer_Data_Add(mono_mix,st_AdcData_Info.adc_data_len[st_AdcData_Info.read_pos]);
            }
            #elif IS_DEVICE_PHONE_GATEWAY
            if(stModule4GInfo.callStatus!=MODULE_4G_CALL_STATUS_IDLE)
            {
                if(!g_phone_gateway_signal_valid)
                {
                    g_phone_gateway_signal_valid=1;   //电话网关信号有效
                    send_online_info();
                }
                if(g_device_phone_gateway_ready)
                {
                    if(stModule4GInfo.callStatus == MODULE_4G_CALL_STATUS_MESSAGE_READY)
                    {
                        stModule4GInfo.callStatus = MODULE_4G_CALL_STATUS_MESSAGE_TTS;
                        //使用TTS播报
                        module_4g_play_message();

                        //TTS播报时需要增大增益12dB
                        for(int i=0; i<SamplesPreFrame; i++)
                        {
                            send_adc1_progcess_mono[i] = limit_value_16bit( ((((int32_t)send_adc1_progcess_mono[i]) * 16400 + 2048) >> 12) );
                        }
                    }
                    phoneGateway_Data_Add(send_adc1_progcess_mono,st_AdcData_Info.adc_data_len[st_AdcData_Info.read_pos]);
                }
            }
            else
            {
                if(g_phone_gateway_signal_valid)
                {
                    g_phone_gateway_signal_valid=0;   //电话网关信号无效
                    send_online_info();
                }
            }
            #endif

            #if IS_DEVICE_AUDIO_COLLECTOR   //混音器不进行本地音源输出，只接收网络并输出
            if(mi_ao_init_flag && adc_signal_valid)     //有信号才输出，避免累积延时
            {
                //将单声道数据转换为立体声
                MonoToStereo(mono_mix,SamplesPreFrame,mix_progcess_stero);
             
                #if 0
                PhaseInvert(mix_progcess_stero,buffer_len/2,IS_AMP_WITH_AUDIO_DRIVER?PHASE_INVERT_STERO:PHASE_INVERT_RIGHT);
                #endif

                stAoSendFrame.u32Len[0] = buffer_len;
                stAoSendFrame.apVirAddr[0] = mix_progcess_stero;
                stAoSendFrame.apVirAddr[1] = NULL;
                
                int s32Ret = MI_SUCCESS;
                int k=0;
                do
                {
                    s32Ret = MI_AO_SendFrame(AoDevId, 0, &stAoSendFrame, -1);
                }
                while (s32Ret == MI_AO_ERR_NOBUF);
            }
            #endif

            st_AdcData_Info.adc_data_valid[st_AdcData_Info.read_pos] = 0;
            st_AdcData_Info.read_pos++;
            if(st_AdcData_Info.read_pos>=MAX_ADC_DATA_PKG_NUM)
            {
                st_AdcData_Info.read_pos=0;
            }
        }
        //pthread_mutex_unlock(&mutex_audio_out);
        //usleep(thread_delay_us);
    }

    printf("AED end !\n");  
    ret = IaaAed_Release(AED_HANDLE);
    if(ALGO_AED_RET_SUCCESS != ret)  
    {  
        printf("IaaAed_Release failed !, ret = %d\n", ret);  
    }
}

#endif

#endif