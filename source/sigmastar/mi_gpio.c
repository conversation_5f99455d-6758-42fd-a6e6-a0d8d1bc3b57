/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2022-02-16 20:44:05 
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2022-02-17 11:04:59
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/mman.h>
#include "mi_gpio.h"
#include "const.h"


typedef struct
{
    unsigned char *virt_addr;
    unsigned char *mmap_base;
    unsigned int mmap_length;
}MmapHandle;

static unsigned int const page_size_mask = 0xFFF;

MmapHandle* devMemMMap(unsigned int phys_addr, unsigned int length)
{
    int fd;
    unsigned int phys_offset = 0;

    fd = open("/dev/mem", O_RDWR|O_SYNC);
    if (fd == -1)
    {
        printf("open /dev/mem fail\n");
        return NULL;
    }

    MmapHandle *handle = malloc(sizeof(MmapHandle));
    //phys_offset =(phys_addr & (page_size_mask));
    //phys_addr &= ~(page_size_mask);
    handle->mmap_length = length + phys_offset;
    handle->mmap_base = mmap(NULL, handle->mmap_length, PROT_READ|PROT_WRITE, MAP_SHARED, fd, phys_addr);
    handle->virt_addr = handle->mmap_base + phys_offset;
    //printf("phys_addr: %#x\n", phys_addr);
    //printf("virt_addr: %p\n", handle->virt_addr);
    //printf("phys_offset: %#x\n", phys_offset);

    if (handle->mmap_base == MAP_FAILED)
    {
        printf("mmap fail\n");
        close(fd);
        free(handle);
        return NULL;
    }

    close(fd);
    return handle;
}

int devMemUmap(MmapHandle* handle)
{
    int ret = 0;

    ret = munmap(handle->mmap_base, handle->mmap_length);
    if(ret != 0)
    {
        printf("munmap fail\n");
        return ret;
    }
    free(handle);
    return ret;
}

//设置GPIO为输入
void Set_Gpio_Input(int offset)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, offset), GPIO_INPUT_VAL);
    devMemUmap(riu_base);
}

// 获取GPIO电平状态（GPIO输入时有效） 1为高电平,0为低电平
int Get_Gpio_Value(int offset)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    unsigned short content=REG_R_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, offset));
    int value = (content & 0x0001) ? 1 : 0;
    devMemUmap(riu_base);
    return value;
}



void Set_Gpio_High(int offset)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, offset), GPIO_HIGH_VAL);
    devMemUmap(riu_base);
}

void Set_Gpio_Low(int offset)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, offset), GPIO_LOW_VAL);
    devMemUmap(riu_base);
}


//功放信号脚 低电平有效
void Enable_Amp_Output(int IsEnable)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    int controlGpio = PAD_KEY5;
    #if YIHUI_VERSION
    controlGpio = PAD_GPIO0;
    #endif

    //龙之音调频版本(C4A1),关闭功放
    #if DECODER_LZY_VERESION_FM_C4A1
    IsEnable=0;
    #endif

    //如果是简单版本解码器，不处理
    if(IS_MODEL_SIMPLE_DECODER)
    {
        return;
    }

    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    printf("Enable_Amp_Output:%d\n",IsEnable);
    if(IsEnable)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, controlGpio), GPIO_LOW_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, controlGpio), GPIO_HIGH_VAL);
    }

    devMemUmap(riu_base);
}

//运放信号脚 高电平有效
void Enable_Signal_Output(int IsEnable)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if YIHUI_VERSION
    return;
    #endif

    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    printf("Enable_Signal_Output:%d\n",IsEnable);

    int controlGpio = PAD_GPIO6;
    if(IS_MODEL_SIMPLE_DECODER || IS_MODEL_SIMPLE_AMP)
    {
        controlGpio = PAD_SR_GPIO07;
    }
    if(IsEnable)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, controlGpio), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, controlGpio), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);
}


//音频继电器切换 低电平为100V通道
void Ctrl_Relay_100V(int IsEnable)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if YIHUI_VERSION
    return;
    #endif

    if(IS_MODEL_SIMPLE_DECODER || IS_MODEL_SIMPLE_AMP)
    {
        return;
    }

    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    printf("Ctrl_Relay_100V:%d\n",IsEnable);
    if(IsEnable)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY4), GPIO_LOW_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY4), GPIO_HIGH_VAL);
    }

    devMemUmap(riu_base);
}

//GPIO输出网络连接状态
void GPIO_OutPut_Server_Connection(int outPut)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if YIHUI_VERSION
    return;
    #endif

    //户外板，PAD_SD_D3作为网络指示灯，其他版本作为UART1_TX使用
    //龙之音版本,KEY_GPIO8作为网络指示灯，其他版本作为UART2_TX使用
    //电源时序器,PAD_SD_CLK作为网络指示灯
    if(!IS_MODEL_OUTDOOR && !IS_MODEL_DECODER_LZY && !IS_DEVICE_POWER_SEQUENCE && !IS_MODEL_WITH_4G)
        return;

    if(IS_MODEL_SIMPLE_DECODER || IS_MODEL_SIMPLE_AMP)
        return;
    if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_C || CURRENT_DEVICE_MODEL == MODEL_AUDIO_COLLECTOR_C\
		|| CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_C || CURRENT_DEVICE_MODEL == MODEL_REMOTE_CONTROLER_C)
    {
        return;
    }

    #if (IS_DEVICE_POWER_SEQUENCE && (CUR_POWER_SEQUENCE_TYPE == MODEL_SEQUENCE_POWER_WEISHENG))
    Disp_Send_SequencePower_NetStatus(outPut);
    #endif

    printf("GPIO_OutPut_Server_Connection:%d\n",outPut);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(outPut)
    {
        if(IS_MODEL_OUTDOOR)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_D3), GPIO_HIGH_VAL);
        }
        else if(IS_MODEL_DECODER_LZY)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY8), GPIO_HIGH_VAL);
        }
        else if(IS_DEVICE_POWER_SEQUENCE)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_CLK), GPIO_HIGH_VAL);
        }
        else if(IS_MODEL_WITH_4G)
        {
            #if SUPPORT_INFORMATION_PUBLISH
            if(!IS_EXTENSION_HAS_INFORMATION_PUB)
            #endif
            {
                REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_D3), GPIO_HIGH_VAL);
            }
        }
    }
    else
    {
        if(IS_MODEL_OUTDOOR)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_D3), GPIO_LOW_VAL);
        }
        else if(IS_MODEL_DECODER_LZY)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY8), GPIO_LOW_VAL);
        }
        else if(IS_DEVICE_POWER_SEQUENCE)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_CLK), GPIO_LOW_VAL);
        }
        else if(IS_MODEL_WITH_4G)
        {
            #if !DECODER_LZY_VERESION_FM_C4A1
            #if SUPPORT_INFORMATION_PUBLISH
            if(!IS_EXTENSION_HAS_INFORMATION_PUB)
            #endif
            {
                REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_D3), GPIO_LOW_VAL);
            }
            #endif
        }
    }

    devMemUmap(riu_base);
}

// GPIO输出网络音频信号状态（定时需要提前打开）,暂不区分是否网络音源，有信号就可以
void GPIO_OutPut_NetAudio_Signal(int outPut)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif
    
    #if YIHUI_VERSION
    return;
    #endif

    #if IS_DEVICE_POWER_SEQUENCE
        return;
    #endif

    #if DECODER_LZY_VERESION_FM_C4A2    //C4A2翻转控制
    outPut=!outPut;
    #endif

    printf("GPIO_OutPut_NetAudio_Signal:%d\n",outPut);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(outPut)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_CMD), GPIO_HIGH_VAL);
        if(IS_MODEL_DECODER)    //DECODER NORMAL为SIGNAL_IO,DECODER LZY为RelayCtrl
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_CLK), GPIO_HIGH_VAL);
        }
        else if(IS_MODEL_SIMPLE_DECODER)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY5), GPIO_HIGH_VAL);
        }
        else if(IS_MODEL_SIMPLE_AMP)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY10), GPIO_HIGH_VAL);
        }
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_CMD), GPIO_LOW_VAL);
        if(IS_MODEL_DECODER)    //DECODER NORMAL为SIGNAL_IO,DECODER LZY为RelayCtrl
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_CLK), GPIO_LOW_VAL);
        }
        else if(IS_MODEL_SIMPLE_DECODER)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY5), GPIO_LOW_VAL);
        }
        else if(IS_MODEL_SIMPLE_AMP)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY10), GPIO_LOW_VAL);
        }
    }

    devMemUmap(riu_base);
}


// GPIO输出网络音频信号状态灯(实际有信号才点亮),暂不区分是否网络音源，有信号就可以
void GPIO_OutPut_NetAudio_Led(int outPut)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if YIHUI_VERSION
    return;
    #endif

    if(!IS_MODEL_DECODER && !IS_MODEL_WITH_4G && !IS_DEVICE_AUDIO_COLLECTOR && !IS_DEVICE_FIRE_COLLECTOR)
        return;

    if(IS_MODEL_SIMPLE_DECODER || IS_MODEL_SIMPLE_AMP)
        return;
    if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_C || CURRENT_DEVICE_MODEL == MODEL_AUDIO_COLLECTOR_C\
		|| CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_C || CURRENT_DEVICE_MODEL == MODEL_REMOTE_CONTROLER_C)
    {
        return;
    }
    
    printf("GPIO_OutPut_NetAudio_Led:%d\n",outPut);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(outPut)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_D0), GPIO_HIGH_VAL);
        if(IS_MODEL_DECODER_LZY)    //龙之音版本,KEY_GPIO9作为音频信号指示灯，其他版本作为UART2_RX使用
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY9), GPIO_HIGH_VAL);
        }
        else if(IS_MODEL_WITH_4G)
        {
            #if SUPPORT_INFORMATION_PUBLISH
            if(!IS_EXTENSION_HAS_INFORMATION_PUB)
            #endif
            {
                REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_D2), GPIO_HIGH_VAL);
            }
        }
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_D0), GPIO_LOW_VAL);
        if(IS_MODEL_DECODER_LZY)    //龙之音版本,KEY_GPIO9作为音频信号指示灯，其他版本作为UART2_RX使用
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY9), GPIO_LOW_VAL);
        }
        else if(IS_MODEL_WITH_4G)
        {
            #if SUPPORT_INFORMATION_PUBLISH
            if(!IS_EXTENSION_HAS_INFORMATION_PUB)
            #endif
            {
                REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_D2), GPIO_LOW_VAL);
            }
        }
    }

    devMemUmap(riu_base);
}


//消防强切继电器 高电平有效
void Ctrl_Relay_EMC(int outPut)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if !IS_DEVICE_DECODER_TERMINAL
    return;
    #endif
    
    //P28，不处理
    if(IS_MODEL_SIMPLE_AMP)
    {
        return;
    }

    int controlGpio = PAD_KEY6;
    #if YIHUI_VERSION
    controlGpio = PAD_GPIO4;
    #endif
    if(IS_MODEL_P26_V12)
    {
        controlGpio = PAD_KEY7;
    }
    else if(IS_MODEL_P26_V10)
    {
        controlGpio = PAD_KEY3;
    }

    printf("Ctrl_Relay_EMC:%d\n",outPut);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(outPut)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, controlGpio), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, controlGpio), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);
}




#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)

void GPIO_OutPut_Call_Led(int ledId,int outPut)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if YIHUI_VERSION
    return;
    #endif

    if(!IS_DEVICE_SUPPORT_INTERCOM)
        return;

    #if(!IS_DEVICE_INTERCOM_TERMINAL)
    if(ledId == 2)
        return;
    #endif

    if(ledId!=1 && ledId!=2)
    {
        return;
    }
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    //printf("GPIO_OutPut_Call_Led:ledId=%d,output=%d\n",ledId,outPut);
    if(outPut)
    {
        if(ledId == 1)
        {
            #if IS_DEVICE_INTERCOM_TERMINAL
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO2), GPIO_HIGH_VAL);
            #else
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY8), GPIO_HIGH_VAL);
            #endif
        }
        else if(ledId == 2)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO4), GPIO_HIGH_VAL);
        }
    }
    else
    {
        if(ledId == 1)
        {
            #if IS_DEVICE_INTERCOM_TERMINAL
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO2), GPIO_LOW_VAL);
            #else
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY8), GPIO_LOW_VAL);
            #endif
        }
        else if(ledId == 2)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO4), GPIO_LOW_VAL);
        }
    }

    devMemUmap(riu_base);
}

void GPIO_OutPut_Call_Relay(int relayId,int outPut)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if YIHUI_VERSION
    return;
    #endif

    #if !IS_DEVICE_INTERCOM_TERMINAL
    return;
    #endif

    if(relayId!=1 && relayId!=2)
    {
        return;
    }
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    //printf("GPIO_OutPut_Call_Relay1:%d\n",outPut);
    if(outPut)
    {
        if(relayId == 1)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY3), GPIO_HIGH_VAL);
        }
        else if(relayId == 2)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY4), GPIO_HIGH_VAL);
        }
    }
    else
    {
        if(relayId == 1)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY3), GPIO_LOW_VAL);
        }
        else if(relayId == 2)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY4), GPIO_LOW_VAL);
        }
    }

    devMemUmap(riu_base);
}



void GPIO_Set_Call_Input()
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if YIHUI_VERSION
    return;
    #endif

    //启动后将相关引脚设置为输入
    #if IS_DEVICE_INTERCOM_TERMINAL
    Set_Gpio_Input(PAD_GPIO3);  //Key1
    Set_Gpio_Input(PAD_GPIO5);  //Key2
    Set_Gpio_Input(PAD_KEY1);   //ExtIn1
    Set_Gpio_Input(PAD_KEY2);   //ExtIn2
    #else
    Set_Gpio_Input(PAD_KEY9);   //KEY
    #endif
}

int GPIO_Get_Call_Key_Value(int keyId)
{
    #ifdef USE_PC_SIMULATOR
    return -1;
    #endif

    #if YIHUI_VERSION
    return -1;
    #endif

    if(!IS_DEVICE_SUPPORT_INTERCOM)
        return -1;

    if(keyId!=1 && keyId!=2)
    {
        return -1;
    }

    #if IS_DEVICE_INTERCOM_TERMINAL
    if(keyId == 1)
        return Get_Gpio_Value(PAD_GPIO3);
    else if(keyId == 2)
        return Get_Gpio_Value(PAD_GPIO5);
    #else
    if(keyId == 1)
        return Get_Gpio_Value(PAD_KEY9);
    else if(keyId == 2)
        return 1;
    #endif
    return 1;
}


int GPIO_Get_Call_Ext_Value(int ExtId)
{
    #ifdef USE_PC_SIMULATOR
    return -1;
    #endif

    #if YIHUI_VERSION
    return -1;
    #endif

    #if !IS_DEVICE_INTERCOM_TERMINAL
    return -1;
    #endif

    if(ExtId!=1 && ExtId!=2)
    {
        return -1;
    }
    if(ExtId == 1)
        return Get_Gpio_Value(PAD_KEY1);
    else if(ExtId == 2)
        return Get_Gpio_Value(PAD_KEY2);
}

#endif


//GPIO控制4G模块主电源供电(V12以上支持)
void GPIO_OutPut_Module4G_Main_Power(int isOn)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if YIHUI_VERSION
    return;
    #endif

    if(!IS_MODEL_WITH_4G)
        return;
    printf("GPIO_OutPut_Module4G_Main_Power:%d\n",isOn);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(isOn)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO1), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO1), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);
}


//GPIO控制4G模块开机键，低电平保持1.5秒+开机
void GPIO_OutPut_Module4G_POWER(int isOn)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if YIHUI_VERSION
    return;
    #endif

    if(!IS_MODEL_WITH_4G)
        return;
    printf("GPIO_OutPut_Module4G_PWRKEY:%d\n",isOn);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(isOn)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY12), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY12), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);
}

//GPIO控制4G模块复位键，低电平复位
void GPIO_OutPut_Module4G_Reset(int isReset)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if YIHUI_VERSION
    return;
    #endif

    if(!IS_MODEL_WITH_4G)
        return;
    printf("GPIO_OutPut_Module4G_Reset:%d\n",isReset);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(isReset)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY11), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY11), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);
}

//4G模块LED控制（未连接上服务器灯灭，连接上服务器常亮，有音乐信号闪烁）
void GPIO_OUTPUT_Module4G_Led(int outPut)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if YIHUI_VERSION
    return;
    #endif

    if(!IS_MODEL_WITH_4G)
        return;
    //printf("GPIO_OUTPUT_Module4G_Led:%d\n",outPut);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(outPut)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_D1), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_D1), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);

}


#if 0     //********新的P20板载4G模块，无需检测
int GPIO_Get_Module4G_Status()
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    if(!IS_MODEL_WITH_4G)
        return;
    
    int io=Get_Gpio_Value(PAD_KEY13);
    if(io)
        return 0;
    else
        return 1;
}

void GPIO_Set_Module4G_Input()
{
    //启动后将相关引脚设置为输入
    Set_Gpio_Input(PAD_KEY13);  //Key13,4G check
}
#endif

//gpio设置USB通路
void GPIO_set_usb_mutex(int enable)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if YIHUI_VERSION
    return;
    #endif

    if(!IS_MODEL_WITH_4G)
        return;
    printf("GPIO_Set_usb_mutex:%d\n",enable);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(enable)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY13), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY13), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);
}


//gpio设置触发模式（1:电平输入; 0:短路输入）
void GPIO_set_trigger_mode(int mode)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if YIHUI_VERSION
    return;
    #endif

    if(!IS_MODEL_WITH_4G)
        return;
    printf("GPIO_set_trigger_mode:%d\n",mode);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(mode)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY10), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_KEY10), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);
}


//触发控制灯，有触发时输出高电平，目前使用GPIO5，注意：P19对讲板使用了此引脚，如果也需要启用触发功能，需要改变此引脚
void Set_Trigger_Led(int outPut)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if YIHUI_VERSION
    return;
    #endif

    if(!IS_MODEL_WITH_4G)
        return;
    printf("Set_Trigger_Led:%d\n",outPut);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(outPut)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO5), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO5), GPIO_LOW_VAL);
    }
    devMemUmap(riu_base);
}



//GPIO输出网络连接状态(音乐信号闪烁，用于P26、P28)
void GPIO_OutPut_LedStatusFlicker(int outPut)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    bool isValid=false;

    int controlGpio=PAD_KEY4;

    #if YIHUI_VERSION     //易会系列是服务灯
    isValid =true;
    if(SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_RGB_4P3_480X272)
    {
        controlGpio = PAD_KEY4;
    }
    else if(SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_1P9_320X170)
    {
        controlGpio = PAD_KEY8;
    }
    #endif

    if(IS_MODEL_SIMPLE_DECODER || IS_MODEL_SIMPLE_AMP)
    {
        isValid=true;
    }
    if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_C || CURRENT_DEVICE_MODEL == MODEL_AUDIO_COLLECTOR_C\
		|| CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_C || CURRENT_DEVICE_MODEL == MODEL_REMOTE_CONTROLER_C\
        || CURRENT_DEVICE_MODEL == MODEL_GPS_SYNCHRONIZER)
    {
        isValid=true;
    }

    if(!isValid)
        return;

    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(outPut)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, controlGpio), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, controlGpio), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);
}


#if YIHUI_VERSION
void YH_control_lineout(int enable)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif
    int controlGpio=PAD_GPIO3;
    if(SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_RGB_4P3_480X272)
	{
		controlGpio = PAD_GPIO3;
	}

    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(enable)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, controlGpio), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, controlGpio), GPIO_LOW_VAL);
    }
}

void YH_control_amp_fan(int enable)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    #if (CURRENT_DEVICE_MODEL!=MODEL_IP_SPEAKER_F || SELECTED_DISPLAY_TYPE!=DISPLAY_TYPE_RGB_4P3_480X272)
    return;
    #endif
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(enable)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO7), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO7), GPIO_LOW_VAL);
    }
}
#endif