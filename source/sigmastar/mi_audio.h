/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-04 16:18:56 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-02-24 18:03:12
 */

#ifndef _MI_AUDIO_H_
#define _MI_AUDIO_H_

#ifndef USE_PC_SIMULATOR

#include "mi_sys.h"
#include "mi_common_datatype.h"
#include "mi_ao.h"
#include "mi_ai.h"
#include "audio_par_common.h"
#include "AudioAedProcess.h"
#include "AudioProcess.h"
#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
#include "AudioAecProcess.h"
#endif


#define AUX_VALID_THRESHOLD_DB  -60 //dB
#define AUX_VALID_THRESHOLD_LOW_DB  -45 //-45dB     //4G控制器，需要降低本地信号触发阈值
#define MIC_FRONT_VALID_THRESHOLD_DB  -52 //dB
#define MAIC_REAR_VLID_THRESHOLD_DB   -42 //dB
#define AUX_INVLID_TIMEOUT  8      //秒

void mi_audio_out_init(unsigned int sample_rate, unsigned char fmt, unsigned char channels);
void mi_audio_out_deinit();
void mi_audio_in_init(unsigned int sample_rate, unsigned char fmt, unsigned char channels);
void mi_audio_in_deinit();
void mi_audio_write(const unsigned char *buffer, int count);
int mi_audio_eq_init(int isEnable);
void mi_auido_eq_deinit();
void mi_audio_set_eq_mode(int mode);
#endif
#endif
