#ifndef _INFORMATION_PUBLISH_H_
#define _INFORMATION_PUBLISH_H_


#define MAX_INFORMATION_PUBLISH_EFFECTS     10
#define MAX_INFORMATION_PUBLISH_TEXT_BYTES 240  //信息发布文本最大长度，不超过300个字节，最大150个汉字   


#define INFORMATION_PUB_UART_DEVICE     "/dev/ttyS1" //uart device name

#define INFORMATION_PUB_UART_HEADER     0x0000AA55
#define INFORMATION_PUB_UART_ENDER      0x0A0D0000

#define INFORMATION_PUB_UART_CMD_PROGRAM     0xDA00
#define INFORMATION_PUB_UART_CMD_REFRESH     0xD900

#define INFORMATION_PUB_UART_BUFFER_MAX 512

// 寻呼方式
typedef enum
{
    EF_AUTO = 0x30,                    // 自动（当区域够显示时静止，不够显示时连续左移）;
    EF_PAGE_TURNING = 0x31,            // 翻页（当区域够显示时静止，不够显示时翻页显示） 
    EF_ROTATE_LEFT = 0x32,             // 连续左移
    EF_LEFT = 0x33,                    // 左移（带停留）
    EF_ROTATE_DOWN = 0x34,             // 连续下移
    EF_DOWN = 0x35,                    // 下移（带停留）
    EF_FLICKER = 0x36,                 // 闪烁
    EF_ROTATE_UP = 0x37,               // 连续上移
    EF_UP = 0x38,                      // 上移（带停留）
    EF_SNOW = 0x39                     // 飘雪
}EffectsType;

/*******音频混音器相关变量************/
typedef struct {
    unsigned char m_bEnableDisplay;                          //是否启用显示（0/1，默认为0关闭）
    char        m_szText[MAX_INFORMATION_PUBLISH_TEXT_BYTES+1]; //显示文字
    unsigned char m_nEffects;                           //特效
    unsigned char m_nMoveSpeed;                          //移动速度，值域1-64(单位5ms)
    unsigned char m_nStayTime;                          //停留时间，值域1-255（单位1s）
}st_informationPub;
extern st_informationPub informationPub_info;


extern unsigned char g_information_publish_effectsArry[MAX_INFORMATION_PUBLISH_EFFECTS];


void InformationPub_Control_Program();
void InformationPub_Control_Refresh();
void InformationPub_Control_ProgramAndRefresh();
bool InformationPub_Uart_Init(void);
#endif