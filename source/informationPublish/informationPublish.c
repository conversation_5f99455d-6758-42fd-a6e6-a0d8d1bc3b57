#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <semaphore.h>
#include "sysconf.h"
#include <termios.h>
#include "../uart/uartCommon.h"
#include "../network/udp_client.h"
#include "../network/network_process.h"
#include "informationPublish.h"

#if(SUPPORT_INFORMATION_PUBLISH)

/*******信息发布相关变量************/
unsigned char g_information_publish_effectsArry[MAX_INFORMATION_PUBLISH_EFFECTS]={\
    EF_AUTO,EF_PAGE_TURNING,EF_ROTATE_LEFT,\
    EF_LEFT,EF_ROTATE_DOWN,EF_DOWN,EF_FLICKER,\
    EF_ROTATE_UP,EF_UP,EF_SNOW\
};

st_informationPub informationPub_info;


static int informationPub_Uartfd=-1;

void InformationPub_Uart_Command_Proc( unsigned char cmd, unsigned short datalen,unsigned char *data )
{
	
}


void InformationPub_uart_send(unsigned short cmd, unsigned int datalen,unsigned char *data) {
	unsigned char *send_buf=NULL;
	send_buf= (unsigned char*)malloc(INFORMATION_PUB_UART_BUFFER_MAX);
    memset(send_buf,0,INFORMATION_PUB_UART_BUFFER_MAX);

	int pos = 0, i;
    unsigned int header=INFORMATION_PUB_UART_HEADER;
    memcpy(send_buf+pos,&header,4); //帧头 4字节
    pos+=4;
    send_buf[pos++] = 0x01; //地址（固定0x01,1字节）
    send_buf[pos++] = 0x01; //标志（固定0x01,1字节）
    
    memcpy(send_buf+pos,&cmd,2); //操作码（2字节）
    pos+=2;
    pos+=2;                      //保留（2字节)
    pos+=4;                      //帧序号（固定0x00000000,4字节)
    
    memcpy(send_buf+pos,&datalen,4);  //总长度（4字节）
    pos+=4;
    memcpy(send_buf+pos,&datalen,2);   //帧长度（2字节），目前等于总长度
    pos+=2;

    //将数据拷贝到buf中
    memcpy(send_buf+pos,data,datalen);  //总长度（4字节）
    pos+=datalen;

    unsigned int footer=INFORMATION_PUB_UART_ENDER;
    memcpy(send_buf+pos,&footer,4); //帧尾 4字节
    pos+=4;

	write(informationPub_Uartfd, send_buf, pos); //发送数据

#if 1
	printf("InformationPub_uart_send:cmd=0x%x,datalen=%d\r\n",cmd,datalen);

#else
    printf("InformationPub_uart_send:cmd=0x%x,datalen=%d\r\n",cmd,datalen);
	printf("InformationPub_uart_send:\r\n");
	for(i=0;i<pos;i++)
	{
		printf("%02x ",send_buf[i]);
	}
	printf("\r\n");
#endif

	free(send_buf);
}



void InformationPub_Control_Program()
{
  	unsigned short cmd = INFORMATION_PUB_UART_CMD_PROGRAM;
	unsigned char send_buf[INFORMATION_PUB_UART_BUFFER_MAX] = { 0 };
	int pos = 0;

    int text_len = strlen(informationPub_info.m_szText);
    if(!informationPub_info.m_bEnableDisplay)
    {
        text_len=0;
    }
    int zone_data_size_pos = 26+text_len;   //区域数据大小，4个字节
    int program_data_size_pos = 25+zone_data_size_pos;   //节目数据大小，4个字节

    pos+=16; //目标控制器型号（设全0x00指代任意型号） 16字节
    send_buf[pos++] = 0x80; //宽度
    send_buf[pos++] = 0x00;
    send_buf[pos++] = 0x20; //高度
    send_buf[pos++] = 0x00;
    send_buf[pos++] = 0x01; //单色
    send_buf[pos++] = 0x01; //节目个数
    pos+=8; //8字节预留

    send_buf[pos++] = 0x00; //节目号
    memcpy(send_buf+pos,&program_data_size_pos,4); //节目数据大小，4个字节
    pos+=4;


    send_buf[pos++] = 0x01; //节目中区域个数

    send_buf[pos++] = 0x00; // 单节目循环播放
    send_buf[pos++] = 0x00; // 单节目循环播放
    send_buf[pos++] = 0x01; // D6D7=0 时，D8为循环播放次数 
    pos+=16;                //节目定时属性，不开放，全设为0,16字节


    send_buf[pos++] = 0x01; // 区域类型D0[0] = 1 为前景（默认），D0[0] = 0为背景
    memcpy(send_buf+pos,&zone_data_size_pos,4); //区域数据大小，4个字节
    pos+=4;

    send_buf[pos++] = 0x0E; //区域类型：内码区域，D5 = 0x0E
    pos+=4;                 //X、Y起始坐标，都为0，共4个字节
    send_buf[pos++] = 0x7F; //结束坐标X
    send_buf[pos++] = 0x00; //结束坐标X
    send_buf[pos++] = 0x1F; //结束坐标Y
    send_buf[pos++] = 0x00; //结束坐标Y

    send_buf[pos++] = 0x01; //使用红色
    send_buf[pos++] = 0x00; //未使用
    send_buf[pos++] = 0x00; //未使用

    //send_buf[pos++] = g_information_publish_effectsArry[informationPub_info.m_nEffects]; //进场特效
    send_buf[pos++] = 1;    //此处其实无效，所以固定为1（立即显示）

    send_buf[pos++] = informationPub_info.m_nMoveSpeed; //移动速度
    send_buf[pos++] = informationPub_info.m_nStayTime; //停留时间低位
    send_buf[pos++] = 0; //停留时间高位

    send_buf[pos++] = 0x20;         //文字大小(固定32）

    memcpy(send_buf+pos,&text_len,4);
    pos+=4;

    //文本数据
    memcpy(send_buf+pos,informationPub_info.m_szText,text_len);
    pos+=text_len;

	InformationPub_uart_send(cmd,pos,send_buf);
}

void InformationPub_Control_Refresh()
{
  	unsigned short cmd = INFORMATION_PUB_UART_CMD_REFRESH;
	unsigned char send_buf[INFORMATION_PUB_UART_BUFFER_MAX] = { 0 };
	int pos = 0;

    int text_len = strlen(informationPub_info.m_szText);
    if(!informationPub_info.m_bEnableDisplay)
    {
        text_len=0;
    }

    //‘%disp' 协议头部 5字节
    send_buf[pos++] = 0x25;
    send_buf[pos++] = 0x64;
    send_buf[pos++] = 0x69;
    send_buf[pos++] = 0x73;
    send_buf[pos++] = 0x70; 

    send_buf[pos++] = 0x30; //区域编号'0';
    send_buf[pos++] = 0x3A; //红色;
    send_buf[pos++] = g_information_publish_effectsArry[informationPub_info.m_nEffects]; //进场特效

    //文本数据
    memcpy(send_buf+pos,informationPub_info.m_szText,text_len);
    pos+=text_len;

	InformationPub_uart_send(cmd,pos,send_buf);
}


void InformationPub_Control_ProgramAndRefresh()
{
    InformationPub_Uart_Init();
    InformationPub_Control_Program();
    usleep(800000);
    InformationPub_Control_Refresh();
}

/*********************************************************************
 * @fn      InformationPub_Uart_Init
 *
 * @brief   初始化信息发布串口通讯
 *
 * @param   void
 *
 * @return  ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
bool InformationPub_Uart_Init(void)
{
    if(informationPub_Uartfd >=0)
    {
        return false;
    }
    system("/customer/riu_w 0x103e 0x3a 0x0075");
    informationPub_Uartfd = Init_Serial_Port(INFORMATION_PUB_UART_DEVICE, B115200);
	return true;
}




#endif
