#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <semaphore.h>
#include <fcntl.h>
#include <sys/socket.h>
#include <linux/netlink.h>
#include "sysconf.h"
#include "uartCommon.h"
#include "module4G.h"

int g_module_4G_status=MODULE_4G_NOTFOUND;    //4G模块状态
static int module_4g_tty_at_fd=-1;               //4G模块usb tty节点
static pthread_t t_at_tty_Pthread;

static pthread_mutex_t mutex_4gInit=PTHREAD_MUTEX_INITIALIZER;	//4g初始化锁

static int module_4g_product=MODULE_4G_PRODUCT_UNKNOWN;    //4G模块的型号

int module_4g_event=MODULE_4G_EVENT_NONE;

void Create_Module4G_Event_Task(void);
void Create_Module4G_UsbCheck_task(void);
bool Module4G_AT_Init(void);
void Module4G_AT_unInit();
void Module_4G_reset();
void Send_AT_Command(char *cmd);

// AT指令定义
#define AT_CMD_ECHO_OFF "ATE0"
#define AT_CMD_ECHO_ON "ATE1"
#define AT_CMD_CFUN "AT+CFUN"
#define AT_CMD_COPS "AT+COPS?"
#define AT_CMD_CPIN "AT+CPIN?"
#define AT_CMD_CSQ "AT+CSQ"
#define AT_CMD_CCID "AT+ICCID"
#define AT_CMD_CREG "AT+CREG?"
#define AT_CMD_CGSN "AT+CGSN"
#define AT_CMD_CIMI "AT+CIMI"
#define AT_CMD_CGMM "AT+CGMM"   //查询模块型号
#define AT_CMD_CDNSCFG "AT+CDNSCFG?"    //查询DNS配置

#define AT_CMD_SIMAUTO "AT*SIMAUTO=1"   //SIM卡自动切换（AIR780用)

#define AT_CMD_CLIP_ON "AT+CLIP=1"   //启用来电显示
#define AT_CMD_ATA  "ATA"       //接听来电
#define AT_CMD_ATH  "ATH"       //挂断所有通话
#define AT_CMD_AUDCH "AT+AUDCH=1,0" //设置语音输出通道为1耳机（headphone），默认为2喇叭(speaker)
#define AT_CMD_CLVL "AT+CLVL=85"  //设置通话音量为95
#define AT_CMD_CMGF_TEXT "AT+CMGF=1"  //设置短信格式为TEXT模式
#define AT_CMD_TTS_MODE "AT+CTTSPARAM=100,0,50,50,0"

//以下为GPS相关指令（AIR780EG专用)
#define AT_CMD_ENABLE_CGNSPWR_AIR780EG  "AT+CGNSPWR=1"
#define AT_CMD_DISABLE_CGNSPWR_AIR780EG "AT+CGNSPWR=0"
#define AT_CMD_CGNSINF_AIR780EG "AT+CGNSINF"

_stModule4GInfo stModule4GInfo;

//获取4G模块的型号(AIR724UG or Air AIR780E)
int Get_4G_Card_Model() {
    FILE *pipe;
    char buffer[128];
    char cmd[] = "lsusb";
    char* vendorID;
    char* productID;
    int product = MODULE_4G_PRODUCT_UNKNOWN;

    // 执行lsusb命令并获取输出
    pipe = popen(cmd, "r");
    if (!pipe) {
        return product;
    }

    // 解析lsusb输出
    while (fgets(buffer, sizeof(buffer), pipe) != NULL) {
        char vendorID[5];   // 储存vendor ID，长度为4个字符 (+1个字节存放终止符'\0')
        char productID[5];  // 储存product ID，长度为4个字符 (+1个字节存放终止符'\0')

        // 使用sscanf函数提取vendor ID和product ID
        if (sscanf(buffer, "Bus %*3s Device %*3s: ID %4s:%4s", vendorID, productID) == 2) {
            // 检查是否是4G网卡的vendor ID和product ID
            if (strncasecmp(vendorID, "1782",4) == 0 && strncasecmp(productID, "4e00",4) == 0) {
                product = MODULE_4G_PRODUCT_AIR724UG;
            }
            else if (strncasecmp(vendorID, "19d1",4) == 0 && strncasecmp(productID, "0001",4) == 0) {
                product = MODULE_4G_PRODUCT_AIR780E;
            }
            // 添加更多的型号匹配条件...
        }
    }

    // 关闭管道
    pclose(pipe);

    printf("Get_4G_Card_Model:product=%d\n",product);
    return product;
}

void InitModule4G()
{
    #if 0   //20230201新的P20板载4G模块，无需检测
    //首先将相关引脚设置为输入
    GPIO_Set_Module4G_Input();
    usleep(10000);
    //判断4G模块是否插入？
    int hasModule4G=1;//GPIO_Get_Module4G_Status();
    //如果已经插入，那么初始化4G模块
    if(hasModule4G)
    {
        printf("4G Module is Exist,Create Event task!\n");
        g_module_4G_status = MODULE_4G_OFF; //设置为关机状态
        Create_Module4G_Event_Task();
    }
    else
    {
        printf("4G Module is not Exist!\n");
    }
    #else
    GPIO_OUTPUT_Module4G_Led(0);
    g_module_4G_status = MODULE_4G_OFF; //设置为关机状态
    Create_Module4G_Event_Task();
    #endif

}



/*********************************************************************
 * @fn      Create_Module4G_Event
 *
 * @brief  	4G模块事件线程
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_Module4G_Event(void *p_arg)
{
    //先创建4G模块USB检测线程
    Create_Module4G_UsbCheck_task();

    //判断是否已经插入，通过ttyusb0判断
    int cnt=20;
    int found_4g=0;
    while( cnt-- )
    {
        if(Module4G_AT_Init())
        {
            found_4g=1;
            break;
        }
        usleep(100000);
    }
    
    
    //如果没有4G模块，则需要控制开机
    //把PWRKEY管脚拉低1.2秒以上之后模块会进入开机流程，理论上是1.2秒，但是实际需要1.5秒，否则也会停止开机
    //软件会检测VBAT管脚电压， 若VBAT管脚电压大于软件设置的开机电压（3.1V），会继续开机动作直至系统开
    //机完成；否则，会停止执行开机动作，系统会关机，开机成功后PWRKEY管脚可以释放。
    //if(g_module_4G_status <= MODULE_4G_OFF)
    if(!found_4g)
    {
        printf("Not Found 4G,power on!\n");
        GPIO_OutPut_Module4G_POWER(1);
        usleep(1600*1000);
        GPIO_OutPut_Module4G_POWER(0);
    }
    
    int insertCnt=0;
    unsigned int runtime_no_signal=0;
    unsigned int runtime_1s=0;
    unsigned int runtime_100ms=0;

    int basic_status=0;         //系统基础状态
    int ledGpio_val=0;          //ledGpio输出电平
    while(1)
    {
        switch(module_4g_event)
        {
            case MODULE_4G_EVENT_INSERT:
                if(insertCnt<10)
                {
                    insertCnt++;
                    if(Module4G_AT_Init())
                    {
                        module_4g_event = MODULE_4G_EVENT_NONE;
                        insertCnt=0;
                    }
                }
                else
                {
                    System_Reboot();
                }
            break;
            case MODULE_4G_EVENT_REMOVE:
                Module4G_AT_unInit();
                module_4g_event = MODULE_4G_EVENT_NONE;
                insertCnt=0;
            break;
            default:
                insertCnt=0;
            break;
        }

        if( (++runtime_100ms)%10 == 0 )
        {
            runtime_1s++;
            if(runtime_1s == 15 && g_module_4G_status <= MODULE_4G_OFF)   //15秒还没有找到4G模块，重新开机
            {
                printf("Not Found 4G 222,power on!\n");
                GPIO_OutPut_Module4G_POWER(1);
                usleep(1600*1000);
                GPIO_OutPut_Module4G_POWER(0);
            }

            //连续1小时未连接到服务器，对4G模块进行复位
            //连续1小时10分未连接到服务器，对网络板进行重启(暂仅限4G)
            runtime_no_signal = IS_SERVER_CONNECTED?0:runtime_no_signal+1;
            int readyToRebootOrReset=0;
            if( runtime_no_signal )
            {
                if( (runtime_no_signal % 4200) == 0 )
                {
                    if(!eth_link_status && g_network_mode == NETWORK_MODE_WAN)
                    {
                        readyToRebootOrReset=1;
                        //关闭4G供电主电源,让其断电，然后再重启
                        GPIO_OutPut_Module4G_Main_Power(0);
                        System_Reboot();
                    }
                }
                else if( (runtime_no_signal % 3600) == 0 )
                {
                    if(!eth_link_status && g_network_mode == NETWORK_MODE_WAN)
                    {
                        readyToRebootOrReset=1;
                        Module_4G_reset();
                    }
                }
            }

            if((runtime_100ms%30) == 0)
            {
                if(!readyToRebootOrReset)
                {
                    static int canGetCCIDCnt=0;
                    static int canGetCSQCnt=0;
                    #if IS_DEVICE_PHONE_GATEWAY
                    if(g_module_4G_status == MODULE_4G_PREPARING || g_module_4G_status == MODULE_4G_UNUSED)
                    #else
                    if(g_module_4G_status == MODULE_4G_PREPARING)
                    #endif
                    {
                        #if 0   //20230916 AIR780E不支持
                        if(stModule4GInfo.at_send_cnt>stModule4GInfo.at_receive_cnt+8)
                        {
                            printf("error2,ready reset 4g!\n");
                            Module_4G_reset();
                        }
                        else
                        #endif
                        {
                            //printf("stModule4GInfo.at_send_cnt=%d,at_receive_cnt=%d\n",stModule4GInfo.at_send_cnt,stModule4GInfo.at_receive_cnt);
                            if(stModule4GInfo.creg_status == 1)
                            {
                                //如果还未获取过号码，那么获取号码
                                if(strlen(stModule4GInfo.iccid) == 0 && ++canGetCCIDCnt>=2)     //creg_status变成1注册成功后至少间隔5秒获取号码，否则此指令会延时返回，但影响不大
                                {
                                    canGetCCIDCnt=0;
                                    Send_AT_Command(AT_CMD_CCID);
                                }
                            }
                            if(!IS_SERVER_CONNECTED || strlen(stModule4GInfo.iccid) == 0)
                            {
                                Send_AT_Command(AT_CMD_CREG);
                            }
                            if(IS_SERVER_CONNECTED && stModule4GInfo.creg_status!=1)
                            {
                                Send_AT_Command(AT_CMD_CREG);
                            }

                            #if 1
                            if(++canGetCSQCnt>=5 || stModule4GInfo.csq_rssi == 99 || stModule4GInfo.csq_rssi == 0)    //99代表未知或不可测
                            {
                                //获取信号强度
                                canGetCSQCnt=0;
                                Send_AT_Command(AT_CMD_CSQ);
                            }
                            #endif

                            if(!stModule4GInfo.hasReady && stModule4GInfo.creg_status && strlen(stModule4GInfo.iccid)>0)
                            {
                                #if IS_DEVICE_PHONE_GATEWAY
                                printf("Ready to enable clip!\n");
                                //启用来电显示
                                Send_AT_Command(AT_CMD_CLIP_ON);
                                //设置语音输出通道
                                Send_AT_Command(AT_CMD_AUDCH);
                                //设置通话音量
                                Send_AT_Command(AT_CMD_CLVL);

                                //设置短信为TEXT模式
                                Send_AT_Command(AT_CMD_CMGF_TEXT);
                                //设置TTS模式
                                Send_AT_Command(AT_CMD_TTS_MODE);
                                #endif

                                stModule4GInfo.hasReady=1;
                            }
                        }
                    }
                    else
                    {
                        canGetCCIDCnt=0;
                        canGetCSQCnt=0;
                    }

                    //获取DNS配置
                    if(!stModule4GInfo.dns_configured && stModule4GInfo.creg_status == 1)
                    {
                        Send_AT_Command(AT_CMD_CDNSCFG);   //获取DNS配置
                    }

                    //获取GPS信息
                    if(module_4g_product == MODULE_4G_PRODUCT_AIR780E)
                    {
                        if(strlen(stModule4GInfo.cgmm) == 0)
                        {
                            Send_AT_Command(AT_CMD_CGMM);   //获取模块型号
                            Send_AT_Command(AT_CMD_SIMAUTO);   //自动切换SIM卡
                        }
                        if(strstr(stModule4GInfo.cgmm,"Air780EG"))
                        {
                            if(stModule4GInfo.gpsInfo.latitude == 0 || stModule4GInfo.gpsInfo.longitude == 0)
                                Send_AT_Command(AT_CMD_CGNSINF_AIR780EG);
                        }
                    }
                }
            }
            //printf("runtime_no_signal=%d\n",runtime_no_signal);
        }

        //4G模块LED控制（未连接上服务器灯灭，连接上服务器常亮，有音乐信号闪烁）
        //0-未连接上服务器 1-连接上服务器 2-有音乐信号

        static int count = 0;
        static int prev_state=-1,state=0;
        static int state_has_changed = 0; // 变态是否已经改变
        static int led_status=0;   //默认灭
        int sysSource = get_system_source();
        if(!IS_SERVER_CONNECTED)
        {
            state = 0;
        }
        else if(sysSource!=SOURCE_NULL)
        {
            state = 2;
        }
        else
        {
            state = 1;
        }
    
        if (prev_state != state) {
            prev_state = state;
            count = 0;
            state_has_changed=1;
        }
        else
        {
            state_has_changed=0;
        }
    
        if (state == 0) {
            if(g_module_4G_status == MODULE_4G_PREPARING)
            {
                // 搜网状态，LED亮0.2秒，灭1.8秒
                if (stModule4GInfo.creg_status == 1) {
                    if (count < 18) {
                        if(!led_status)
                        {
                            //printf("LED亮1.8秒\n");
                            GPIO_OUTPUT_Module4G_Led(1); // 亮1.8秒
                            led_status=1;
                        }
                    } else {
                        if(led_status)
                        {
                            //printf("LED灭0.2秒\n");
                            GPIO_OUTPUT_Module4G_Led(0); // 灭0.2秒
                            led_status=0;
                        }
                    }
                    count = (count + 1) % 20; // 20个时间单位为一次循环，每个时间单位100ms，所以20个单位为2秒
                }
                // 待机状态，LED亮1.8秒，灭0.2秒
                else {
                    if (count < 2) {
                        if(!led_status)
                        {
                            //printf("LED亮0.2秒\n");
                            GPIO_OUTPUT_Module4G_Led(1); // 亮0.2秒
                            led_status=1;
                        }
                    } else {
                        if(led_status)
                        {
                            //printf("LED灭1.8秒\n");
                            GPIO_OUTPUT_Module4G_Led(0); // 灭1.8秒
                            led_status=0;
                        }
                    }
                    count = (count + 1) % 20; // 20个时间单位为一次循环，每个时间单位100ms，所以20个单位为2秒
                }
            }
            else
            {
                //处于有线模式，或者网卡没有准备好，led常灭
                if(led_status)
                {
                    //printf("LED常灭\n");
                    GPIO_OUTPUT_Module4G_Led(0); // LED常灭
                    led_status=0;
                }
            }
        }
        // 如果状态为已连接上服务器，则LED常量亮
        else if (state == 1 && state_has_changed) {
            //printf("LED常亮\n");
            GPIO_OUTPUT_Module4G_Led(1); // LED常亮
            led_status=1;
        }
        // 如果状态为正在播放，则LED亮0.3秒，灭0.3秒
        else if (state == 2) {
            if (count < 3) {
                if(!led_status)
                {
                    //printf("LED亮0.3秒\n");
                    GPIO_OUTPUT_Module4G_Led(1); // 亮0.3秒
                    led_status=1;
                }
            } else {
                if(led_status)
                {
                    //printf("LED灭0.3秒\n");
                    GPIO_OUTPUT_Module4G_Led(0); // 灭0.3秒
                    led_status=0;
                }
            }
            count = (count + 1) % 6; // 6个时间单位为一次循环，每个时间单位100ms，所以6个单位为0.6秒
        }

        usleep(100000);
    }
}

/*********************************************************************
 * @fn      Create_Module4G_Event_Task
 *
 * @brief  	创建4G模块事件线程
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_Module4G_Event_Task(void)
{
    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Create_Module4G_Event, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}





/*********************************************************************
 * @fn      Create_Module4G_Event
 *
 * @brief  	4G模块事件线程
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_Module4G_UsbCheck(void *p_arg)
{
    struct sockaddr_nl client;
    struct timeval tv;
    int CppLive, rcvlen, ret;
    fd_set fds;
    int buffersize = 1024;
    CppLive = socket(AF_NETLINK, SOCK_RAW, NETLINK_KOBJECT_UEVENT);
    memset(&client, 0, sizeof(client));
    client.nl_family = AF_NETLINK;
    client.nl_pid = getpid();
    client.nl_groups = 1; /* receive broadcast message*/
    setsockopt(CppLive, SOL_SOCKET, SO_RCVBUF, &buffersize, sizeof(buffersize));
    bind(CppLive, (struct sockaddr*)&client, sizeof(client));
    char buf[1024] = { 0 };
    while (1) {
        FD_ZERO(&fds);
        FD_SET(CppLive, &fds);
        memset(buf,0,sizeof(buf));
        //tv.tv_sec = 0;
        //tv.tv_usec = 100 * 1000;
        //ret = select(CppLive + 1, &fds, NULL, NULL, &tv);
        ret = select(CppLive + 1, &fds, NULL, NULL, NULL);
        if(ret < 0)
            continue;
        if(!(ret > 0 && FD_ISSET(CppLive, &fds)))
            continue;
        /* receive data */
        rcvlen = recv(CppLive, &buf, sizeof(buf), 0);
        if (rcvlen > 0) 
        {
            //printf("%s\n", buf);
            if(strstr(buf, "remove@")) // 拔出时内核会发送消息为:remove@/.../ttyUSBx
			{
                //printf("remove...\n");
                if(strstr(buf, "ttyUSB2") || strstr(buf, "ttyUSB3"))
                {
                    if(module_4g_event!=MODULE_4G_EVENT_REMOVE)
                    {
                        printf("Air724UG Remove...\n");
                        g_module_4G_status = MODULE_4G_OFF;
                        module_4g_event = MODULE_4G_EVENT_REMOVE;
                    }
                }
            }
            if(strstr(buf, "add@"))
            {
                //printf("Add...\n");
                if(strstr(buf, "tty/ttyUSB2") || strstr(buf, "tty/ttyUSB3"))
                {
                    if(module_4g_event != MODULE_4G_EVENT_INSERT)
                    {
                        printf("Air724UG Insert...\n");
                        module_4g_event = MODULE_4G_EVENT_INSERT;
                    }
                }
            }
            /*You can do something here to make the program more perfect!!!*/
        }
    }
    close(CppLive);
}

void Create_Module4G_UsbCheck_task(void)
{
    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Create_Module4G_UsbCheck, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}






// 发送AT指令
void Send_AT_Command(char *cmd)
{
    if(g_module_4G_status <= MODULE_4G_OFF || cmd == NULL)
        return;
    
    // 按照 AT 指令格式发送数据，并添加 \r\n 结尾
    char buf[256];
    snprintf(buf, sizeof(buf), "%s\r\n", cmd);
    //tcflush(module_4g_tty_at_fd, TCIOFLUSH); // 清空串口缓存
    stModule4GInfo.at_send_cnt++;
    int len = write(module_4g_tty_at_fd, buf, strlen(buf));
    if (len < 0) {
        printf("Failed to Send_AT_Command\n");
        return;
    }
    else
    {
        printf("Send_AT_Command:%s\n",cmd);
    }
}

void send_AT_play_tts(char *tts_text)
{
	char at_cmd[512]={0};
	sprintf(at_cmd,"AT+CTTS=2,\"%s\"",tts_text);
    Send_AT_Command(at_cmd);
}

void module_4g_play_message()
{
    send_AT_play_tts(stModule4GInfo.message_text);
}


/*********************************************************************
 * @fn      Recv_Module4G_AT
 *
 * @brief   AT接收线程(回调函数)
 *
 * @param   void
 *
 * @return  none
 */
void *Recv_Module4G_AT(void)
{
	printf("Enter Recv_Module4G_AT...\n");
	int Rxlen;
	int ret, i;
	int Index = 0;
	fd_set readfd;
	struct timeval timeout;
	int max_fd;
	int Pkg_Length=0;
	int Read_Size = 1024;

	char Rxbuf[1024]={0};

	FD_ZERO(&readfd);               //清空读文件描述集合
	FD_SET(module_4g_tty_at_fd, &readfd);  //注册套接字文件描述符

    //首先关闭回显
    Send_AT_Command(AT_CMD_ECHO_OFF);

	while (g_module_4G_status != MODULE_4G_OFF)
	{
		timeout.tv_sec = 0;      // 获取数据超时时间设置
		timeout.tv_usec = 100000;

        memset(Rxbuf,0, MAX_UART_BUF_SIZE);

		FD_ZERO(&readfd);               //清空读文件描述集合
		FD_SET(module_4g_tty_at_fd, &readfd);  //注册套接字文件描述符
		ret = select(module_4g_tty_at_fd+1, &readfd, NULL, NULL, &timeout);
		switch(ret)
		{
			case -1 : //调用出错
				perror("select");
				break;

			case 0 : //超时
				//printf("timeout!\n");
				break;

			default : //判断是否有数据可读

				/*接收对应串口的数据*/
                memset(Rxbuf,0,sizeof(Rxbuf));
				Rxlen = read(module_4g_tty_at_fd, &Rxbuf[Index], Read_Size-1);
                if(Rxlen > 2)
                {
                    printf("4G AT Recive:%s\n",Rxbuf);
                    stModule4GInfo.at_receive_cnt++;
                    /*20230916 此处不能这样处理，因为AIR780E连续发送两条指令，只会收到一条带有多个应答的指令。
                    Send_AT_Command:AT+CREG?
                    Send_AT_Command:AT+CSQ
                    4G AT Recive:
                    +CREG: 0,1

                    OK

                    +CSQ: 25,0

                    OK
                    */
                    char *p = Rxbuf;
                    while (p < Rxbuf + Rxlen) {
                        char *q = strchr(p, '\r');
                        if (q != NULL) {
                            *q = '\0';
                            if (*(q + 1) == '\n') {
                                *q = '\0';
                                
                                int creg_urc_val=0,creg_stat_val=0;
                                int csq_rssi_val=0,csq_ber_val=0;
                                char iccid[32]={0};
                                float latitude=0,longitude=0;   //纬度,经度
                                char cgmm[64]={0};  //模块型号
                                char callTelnumber[16]={0};
                                char messageTelnumber[16]={0};
                                char strDEACT[64]={0};
                                char strConnect[64]={0};
                                int match_num=0;
                                if ((match_num=sscanf(p,"+CREG:%d,%d", &creg_urc_val, &creg_stat_val)) == 2) {
                                    printf("value1=%u value2=%u\n", creg_urc_val, creg_stat_val);
                                    if(creg_stat_val!=stModule4GInfo.creg_status)
                                    {
                                        stModule4GInfo.creg_status=creg_stat_val;
                                        printf("stModule4GInfo.creg_status=%d\n",stModule4GInfo.creg_status);
                                    }
                                }else if ((match_num=sscanf(p,"+ICCID:%s", &iccid)) == 1) {
                                    if(strcmp(iccid,stModule4GInfo.iccid))
                                    {
                                        sprintf(stModule4GInfo.iccid,iccid);
                                        printf("stModule4GInfo.iccid=%s\n",stModule4GInfo.iccid);
                                    }
                                }else if ((match_num=sscanf(p,"+CSQ:%d,%d", &csq_rssi_val, &csq_ber_val)) == 2) {
                                    if(csq_rssi_val!=stModule4GInfo.csq_rssi)
                                    {
                                        stModule4GInfo.csq_rssi=csq_rssi_val;
                                        printf("stModule4GInfo.csq_rssi=%d\n",stModule4GInfo.csq_rssi);
                                    }
                                }
                                else if ((match_num=sscanf(p, "+CGNSINF: %*d,%*d,%*[^,],%f,%f", &latitude, &longitude)) == 2) {
                                    if(latitude!=stModule4GInfo.gpsInfo.latitude || longitude!=stModule4GInfo.gpsInfo.longitude)
                                    {
                                        stModule4GInfo.gpsInfo.latitude = latitude;
                                        stModule4GInfo.gpsInfo.longitude = longitude;
                                        printf("stModule4GInfo.latitude=%f,longitude=%f\n",stModule4GInfo.gpsInfo.latitude,stModule4GInfo.gpsInfo.longitude);
                                    }
                                }
                                else if ((match_num=sscanf(p, "+CGMM: %s", &cgmm)) == 1) {
                                    printf("Module 4G:CGMM=%s\n",cgmm);
                                    sprintf(stModule4GInfo.cgmm,cgmm);
                                    printf("stModule4GInfo.cgmm=%s,len=%d\n",stModule4GInfo.cgmm,strlen(stModule4GInfo.cgmm));
                                    if(strstr(stModule4GInfo.cgmm,"Air780EG"))
                                    {
                                        Send_AT_Command(AT_CMD_ENABLE_CGNSPWR_AIR780EG);    //打开GPS电源
                                    }
                                }
                                else if (strncmp(p, "+CDNSCFG:", 9) == 0) {
                                    char primary_dns[16] = {0};
                                    char secondary_dns[16] = {0};
                                    // 解析DNS配置响应，格式: +CDNSCFG: "8.8.8.8","8.8.4.4"
                                    if ((match_num = sscanf(p, "+CDNSCFG: \"%15[^\"]\",\"%15[^\"]\"", primary_dns, secondary_dns)) == 2) {
                                        printf("Module 4G DNS: Primary=%s, Secondary=%s\n", primary_dns, secondary_dns);
                                        // 更新DNS信息到结构体
                                        if (strcmp(primary_dns, stModule4GInfo.primary_dns) != 0 ||
                                            strcmp(secondary_dns, stModule4GInfo.secondary_dns) != 0) {
                                            strcpy(stModule4GInfo.primary_dns, primary_dns);
                                            strcpy(stModule4GInfo.secondary_dns, secondary_dns);
                                            // 更新全局DNS变量
                                            strcpy(module4g_primary_dns, primary_dns);
                                            strcpy(module4g_secondary_dns, secondary_dns);
                                            stModule4GInfo.dns_configured = 1;
                                            printf("DNS updated: Primary=%s, Secondary=%s\n", module4g_primary_dns, module4g_secondary_dns);
                                            // 配置DNS
                                            config_module_4g_dns();
                                        }
                                    }
                                }
                                #if IS_DEVICE_PHONE_GATEWAY
                                else if ((match_num=sscanf(p, "+CLIP: \"%49[^\"]", &callTelnumber)) == 1) {
                                    printf("Module CLIP:CLIP=%s\n",callTelnumber);
                                    //判断该号码是否属于白名单内，是的话接听
                                    if(isMatchTelPhoneNumber(callTelnumber))
                                    {
                                        Send_AT_Command(AT_CMD_ATA);
                                    }
                                    else
                                    {
                                        //挂断陌生电话
                                        Send_AT_Command(AT_CMD_ATH);
                                    }
                                }
                                else if ((match_num=sscanf(p, "*CGEV: %5s", &strDEACT)) == 1) {
                                    if(strcmp(strDEACT,"DEACT") == 0)
                                    {
                                        //挂断
                                        if(stModule4GInfo.callStatus!=MODULE_4G_CALL_STATUS_IDLE)
                                        {
                                            printf("Call Deact!\n");
                                            stModule4GInfo.callStatus=MODULE_4G_CALL_STATUS_IDLE;
                                        }
                                    }
                                }
                                else if (strncmp(p,"CONNECT",strlen("CONNECT")) == 0) {
                                    if(stModule4GInfo.callStatus==MODULE_4G_CALL_STATUS_IDLE)
                                    {
                                        printf("Call Connect!\n");
                                        stModule4GInfo.callStatus=MODULE_4G_CALL_STATUS_CONNECT;
                                    }
                                }
                                else if ((match_num=sscanf(p, "+CMT: \"%49[^\"]", &messageTelnumber)) == 1) {
                                    printf("Module CMT:TelPhone=%s\n",messageTelnumber);
                                    //TODO 判断该号码是否属于白名单内
                                    if(isMatchTelPhoneNumber(messageTelnumber))
                                    {
                                        char messageData[256]={0};    // 存储短信数据

                                        #if 0
                                         // 根据格式提取第二行数据
                                         printf("Data:\n");
                                        for(int i=0;i<Rxlen;i++)
                                        {
                                            printf("%d ",Rxbuf[i]);
                                        }
                                        printf("\n");
                                        #endif

                                        //char array[] = {0, 10, 'X', 0, 10, 'Y', 13, 10}; // 示例 ASCII 码数组
                                        //一个空字符加一个换行符，号码信息，一个空字符加换行符，短信正文，回车符，换行符
                                        int start = 0;
                                        int count = 0;
                                        for (int i = 0; i < Rxlen; i++) {
                                            if (Rxbuf[i] == 0 && Rxbuf[i + 1] == 10) {
                                                count++;
                                                if (count == 2) {
                                                start = i + 2;
                                                break;
                                                }
                                            }
                                        }

                                        if (start != 0) {
                                            sscanf((char *)&Rxbuf[start], "%[^\r]", messageData);
                                            printf("message: %s\n", messageData);
                                            sprintf(stModule4GInfo.message_text,messageData);
                                            if(stModule4GInfo.callStatus==MODULE_4G_CALL_STATUS_IDLE)
                                            {
                                                stModule4GInfo.callStatus = MODULE_4G_CALL_STATUS_MESSAGE_READY;
                                            }
                                        } else {
                                            printf("message error\n");
                                        }
                                    }
                                }
                                else if (strncmp(p,"+CTTS:0",strlen("+CTTS:0")) == 0) {
                                    //TTS播放完毕
                                    printf("TTS END!\n");
                                    if(stModule4GInfo.callStatus!=MODULE_4G_CALL_STATUS_CONNECT)
                                    {
                                        stModule4GInfo.callStatus=MODULE_4G_CALL_STATUS_IDLE;
                                    }
                                }
                                #endif


                                p = q + 2;
                                continue;
                            }
                        }
                        break;
                    }
                }

				break;
		}
	}
    printf("Exit Recv_Module4G_AT...\n");
	pthread_exit(NULL);
}




void Module4G_AT_unInit()
{
    pthread_mutex_lock(&mutex_4gInit);
    if(module_4g_tty_at_fd>=0)
    {
        //20230627 重要，此处一定要再次OFF,否则在特定情况下会异常，造成死锁
        g_module_4G_status = MODULE_4G_OFF;
        pthread_join(t_at_tty_Pthread, NULL);
        close(module_4g_tty_at_fd);
        module_4g_tty_at_fd=-1;

        //如果没有连接有线网卡，且TCP已经处于连接的状态下，需要重连
        if(!eth_link_status)
        {
            if(g_network_mode == NETWORK_MODE_WAN && tcp_get_master_connect_status() == 1)
            {
                tcp_client_reconnect();
            }
        }
    }
    pthread_mutex_unlock(&mutex_4gInit);
}


void config_module_4g_dns()
{
    if(g_module_4G_status <= MODULE_4G_OFF )
    {
        return;
    }
    char cmd[128]={0};
    //设置DNS
    sprintf(cmd,"echo \"nameserver %s\" > /etc/resolv.conf",module4g_primary_dns);
    pox_system(cmd);

    sprintf(cmd,"echo \"nameserver %s\" >> /etc/resolv.conf",module4g_secondary_dns);
    pox_system(cmd);

    sprintf(mqtt_dns_url,"udp://%s:53",module4g_primary_dns);
    printf("config_module_4g_dns:%s,%s\n",module4g_primary_dns,module4g_secondary_dns);
}

void config_module_4g_network()
{
    if(g_module_4G_status <= MODULE_4G_OFF )
    {
        return;
    }

    //如果有线网卡已经连接，关闭4G网卡
    if(eth_link_status)
    {
        g_module_4G_status = MODULE_4G_UNUSED;
        pox_system("ifconfig eth1 down");

        //如果是静态模式，需要重新设置一遍DNS
        if(g_IP_Assign == IP_ASSIGN_STATIC)
        {
            if(strlen(g_Primary_DNS) >=7)
            {
                char cmd[128]={0};
                sprintf(cmd,"echo \"nameserver %s\" > /etc/resolv.conf",g_Primary_DNS);
                pox_system(cmd);
                if(strlen(g_Alternative_DNS) >=7)
                {
                    sprintf(cmd,"echo \"nameserver %s\" >> /etc/resolv.conf",g_Alternative_DNS);
                    pox_system(cmd);
                }
            }
        }
    }
    else
    {
        //配置4G网卡地址
        pox_system("ifconfig eth1 down");
        pox_system("ifconfig eth1 up");

        char cmd[128]={0};
        //设置静态IP、子网掩码
        if(module_4g_product == MODULE_4G_PRODUCT_AIR780E)
        {
            sprintf(cmd,"ifconfig %s %s netmask %s","eth1","************","*************");
        }
        else
        {
            sprintf(cmd,"ifconfig %s %s netmask %s","eth1","***********","*************");
        }
        pox_system(cmd);

        //设置网关
        if(module_4g_product == MODULE_4G_PRODUCT_AIR780E)
        {
            sprintf(cmd,"route add default gw %s","************");
        }
        else
        {
            sprintf(cmd,"route add default gw %s","***********");
        }
        pox_system(cmd);

        //设置DNS
        sprintf(cmd,"echo \"nameserver %s\" > /etc/resolv.conf",module4g_primary_dns);
        pox_system(cmd);

        sprintf(cmd,"echo \"nameserver %s\" >> /etc/resolv.conf",module4g_secondary_dns);
        pox_system(cmd);

        if(g_module_4G_status == MODULE_4G_UNUSED)
        {
            g_module_4G_status = MODULE_4G_PREPARING;
        }
    }
}

/*********************************************************************
 * @fn      Module4G_AT_Init
 *
 * @brief   初始化4G AT模块
 *
 * @param   void
 *
 * @return  ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
bool Module4G_AT_Init(void)
{
    Module4G_AT_unInit();

    pthread_mutex_lock(&mutex_4gInit);

    static int at_init_cnt=0;
    at_init_cnt++;
    module_4g_tty_at_fd=Init_Serial_Port(MODULE_4G_USB_AT_TTY_NAME,115200);
    if(module_4g_tty_at_fd>=0)
    {
        //代表已经存在usb tty，可能是软件reboot后
        g_module_4G_status = MODULE_4G_PREPARING;
    }
    else
    {
        module_4g_tty_at_fd=Init_Serial_Port(MODULE_4G_USB_AT_TTY1_NAME,115200);
        if(module_4g_tty_at_fd>=0)
        {
            //代表已经存在usb tty，可能是软件reboot后
            g_module_4G_status = MODULE_4G_PREPARING;
            printf("Module4G_AT_Init:Try TTY1 OK!\n");
        }
        else
        {
            module_4g_tty_at_fd=Init_Serial_Port(MODULE_4G_USB_AT_TTY2_NAME,115200);
            if(module_4g_tty_at_fd>=0)
            {
                //代表已经存在usb tty，可能是软件reboot后
                g_module_4G_status = MODULE_4G_PREPARING;
                printf("Module4G_AT_Init:Try TTY2 OK!\n");
            }
            else
            {
                module_4g_tty_at_fd=Init_Serial_Port(MODULE_4G_USB_AT_TTY3_NAME,115200);
                if(module_4g_tty_at_fd>=0)
                {
                    //代表已经存在usb tty，可能是软件reboot后
                    g_module_4G_status = MODULE_4G_PREPARING;
                    printf("Module4G_AT_Init:Try TTY3 OK!\n");
                }
                else
                {
                    pthread_mutex_unlock(&mutex_4gInit);
                    return false;
                }
            }
        }
    }

    module_4g_product = Get_4G_Card_Model();
    #if 0
    if(module_4g_product == MODULE_4G_PRODUCT_UNKNOWN)
    {
        printf("4G error:module_4g_product is unknown!\n");
        g_module_4G_status = MODULE_4G_OFF;
        pthread_mutex_unlock(&mutex_4gInit);
        return false;
    }
    #endif

    if(at_init_cnt == 1 && g_module_4G_status == MODULE_4G_PREPARING)
    {
        g_module_4G_status = MODULE_4G_OFF;
        close(module_4g_tty_at_fd);
        module_4g_tty_at_fd=-1;
        Module_4G_reset();
        pthread_mutex_unlock(&mutex_4gInit);
        return true;
    }

    //重置4G模块信息
    memset(&stModule4GInfo,0,sizeof(stModule4GInfo));

	/*创建一个线程单独接收AT串口数据*/
    pthread_create(&t_at_tty_Pthread, NULL, (void *)Recv_Module4G_AT, NULL);

    config_module_4g_network();

    pthread_mutex_unlock(&mutex_4gInit);
    return true;
}


//4G模块复位
void Module_4G_reset()
{
    printf("Module_4G_reset...\n");
    GPIO_OutPut_Module4G_Reset(1);
    usleep(200000);
    GPIO_OutPut_Module4G_Reset(0);

    //air780e:RESET_N 复位管脚拉低释放后，模块会处于硬件关机状态，如果想要重启功能，需要在RESET_N复位后重新拉低 POWERKEY 关机进行开机动作。
    if(module_4g_product == MODULE_4G_PRODUCT_AIR780E)
    {
        GPIO_OutPut_Module4G_POWER(1);
        usleep(1600*1000);
        GPIO_OutPut_Module4G_POWER(0);
    }
}