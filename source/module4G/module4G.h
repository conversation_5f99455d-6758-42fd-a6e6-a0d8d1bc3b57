#ifndef _MODULE_4G_PROCESS_H_
#define _MODULE_4G_PROCESS_H_

#include "const.h"


enum
{
    MODULE_4G_PRODUCT_UNKNOWN = 0,
    MODULE_4G_PRODUCT_AIR724UG = 1,
    MODULE_4G_PRODUCT_AIR780E = 2
};

#define MODULE_4G_USB_AT_TTY_NAME  "/dev/ttyUSB0"
#define MODULE_4G_USB_AT_TTY1_NAME  "/dev/ttyUSB1"
#define MODULE_4G_USB_AT_TTY2_NAME  "/dev/ttyUSB2"
#define MODULE_4G_USB_AT_TTY3_NAME  "/dev/ttyUSB3"

enum{
    MODULE_4G_NOTFOUND=-1,  //未找到4G模块
    MODULE_4G_OFF,          //关机
    MODULE_4G_PREPARING,    //准备中
    MODULE_4G_WORKING,      //工作中
    MODULE_4G_UNUSED,       //未使用（有线网卡插入)
};

enum{
    MODULE_4G_EVENT_NONE=0,  //无
    MODULE_4G_EVENT_INSERT,  //插入
    MODULE_4G_EVENT_REMOVE,  //拔出
};

enum{
    MODULE_4G_CALL_STATUS_IDLE=0,  //呼叫状态-空闲
    MODULE_4G_CALL_STATUS_CONNECT, //呼叫状态-接听
    MODULE_4G_CALL_STATUS_MESSAGE_READY,    //接收到短信
    MODULE_4G_CALL_STATUS_MESSAGE_TTS,      //正在短信播报
};



typedef struct{
    float latitude;     //纬度
    float longitude;    //经度
}_stGPSInfo;

typedef struct{
    int hasReady;         //是否准备就绪
    int creg_status;      //当前网络注册状态
    int csq_rssi;         //信号强度
    char iccid[32];       //iccid号码,共20位数字
    int at_send_cnt;      //AT发送计数
    int at_receive_cnt;   //AT接收计算
    char cgmm[64];        //模块型号

    int callStatus;         //呼叫状态
    char message_text[256];   //短信息

    _stGPSInfo gpsInfo;   //GPS信息结构体
}_stModule4GInfo;

extern _stModule4GInfo stModule4GInfo;

extern int g_module_4G_status;    //4G模块状态

void InitModule4G();
void Disable_module_4g();
void config_module_4g_network();
void module_4g_play_message();
#endif