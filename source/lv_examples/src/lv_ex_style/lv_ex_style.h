/**
 * @file lv_ex_widgets.h
 *
 */

#ifndef LV_EX_STYLE_H
#define LV_EX_STYLE_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_ex_style_1(void);
void lv_ex_style_2(void);
void lv_ex_style_3(void);
void lv_ex_style_4(void);
void lv_ex_style_5(void);
void lv_ex_style_6(void);
void lv_ex_style_7(void);
void lv_ex_style_8(void);
void lv_ex_style_9(void);
void lv_ex_style_10(void);
void lv_ex_style_11(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /*LV_EX_STYLE_H*/
