C
^

Label recoloring and scrolling 
"""""""""""""""""""""""""""""""

.. image:: /lv_examples/src/lv_ex_widgets/lv_ex_label/lv_ex_label_1.*
  :alt: Label example in LittlevGL

.. container:: toggle

    .. container:: header
    
      code

    .. literalinclude:: /lv_examples/src/lv_ex_widgets/lv_ex_label/lv_ex_label_1.c
      :language: c


Text shadow 
""""""""""""

.. image:: /lv_examples/src/lv_ex_widgets/lv_ex_label/lv_ex_label_2.*
  :alt: Label with shadow in LittlevGL

.. container:: toggle

    .. container:: header
    
      code

    .. literalinclude:: /lv_examples/src/lv_ex_widgets/lv_ex_label/lv_ex_label_2.c
      :language: c

Align labels 
""""""""""""

.. image:: /lv_examples/src/lv_ex_widgets/lv_ex_label/lv_ex_label_3.*
  :alt: Align labels in LittlevGL

.. container:: toggle

    .. container:: header
    
      code

    .. literalinclude:: /lv_examples/src/lv_ex_widgets/lv_ex_label/lv_ex_label_3.c
      :language: c


MicroPython
^^^^^^^^^^^

No examples yet.
