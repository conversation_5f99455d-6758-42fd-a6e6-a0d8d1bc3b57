C
^

Drawing on the Canvas and rotate 
""""""""""""""""""""""""""""""""""

.. image:: /lv_examples/src/lv_ex_widgets/lv_ex_canvas/lv_ex_canvas_1.*
  :alt: Simple Canvas example in LittlevGL

.. container:: toggle

    .. container:: header
    
      code

    .. literalinclude:: /lv_examples/src/lv_ex_widgets/lv_ex_canvas/lv_ex_canvas_1.c
      :language: c

Transparent Canvas with chroma keying
""""""""""""""""""""""""""""""""""""""

.. image:: /lv_examples/src/lv_ex_widgets/lv_ex_canvas/lv_ex_canvas_2.*
  :alt: Handle transparency on a canvas with indexed color format

.. container:: toggle

    .. container:: header
    
      code

    .. literalinclude:: /lv_examples/src/lv_ex_widgets/lv_ex_canvas/lv_ex_canvas_2.c
      :language: c


MicroPython
^^^^^^^^^^^

No examples yet.
