#include "../../../lv_examples.h"
#if LV_USE_DEMO_PRINTER

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG_LV_DEMO_PRINTER_ICON_BRIGHT
#define LV_ATTRIBUTE_IMG_LV_DEMO_PRINTER_ICON_BRIGHT
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_IMG_LV_DEMO_PRINTER_ICON_BRIGHT uint8_t lv_demo_printer_icon_bright_map[] = {
#if LV_COLOR_DEPTH == 1 || LV_COLOR_DEPTH == 8
  /*Pixel format: Alpha 8 bit, Red: 3 bit, Green: 3 bit, Blue: 2 bit*/
  0xb6, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xb7, 0x00, 0x49, 0x00, 0x6e, 0x00, 0xb7, 0x00, 0xb6, 0x00, 0x6e, 0x00, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x6e, 0x23, 0x24, 0xc8, 0x6d, 0x24, 0x6e, 0x00, 0x6e, 0x00, 0x6e, 0x00, 0x6e, 0x00, 0x6d, 0x00, 0x92, 0x00, 0xb7, 0x00, 0x92, 0x00, 0x49, 0x00, 0x92, 0x00, 0xb6, 0x00, 0x92, 0x00, 0x92, 0x00, 
  0xb6, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xb7, 0x00, 0x49, 0x00, 0x6e, 0x00, 0xb7, 0x00, 0xb6, 0x00, 0x6d, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x3c, 0x00, 0xff, 0x49, 0x3f, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0x92, 0x00, 0x49, 0x00, 0x92, 0x00, 0xb6, 0x00, 0x92, 0x00, 0x92, 0x00, 
  0xb6, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xb7, 0x00, 0x49, 0x00, 0x6e, 0x00, 0xb7, 0x00, 0xb7, 0x00, 0x92, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6d, 0x00, 0x49, 0x34, 0x00, 0xff, 0x49, 0x37, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x92, 0x00, 0xb7, 0x00, 0xb6, 0x00, 0x92, 0x00, 0x49, 0x00, 0x92, 0x00, 0xb6, 0x00, 0x92, 0x00, 0x92, 0x00, 
  0xb7, 0x00, 0xb7, 0x00, 0xb7, 0x00, 0xdb, 0x00, 0x6d, 0x00, 0x6e, 0x00, 0xb7, 0x00, 0xb6, 0x00, 0xb7, 0x00, 0x6e, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6d, 0x00, 0x49, 0x3c, 0x00, 0xff, 0x25, 0x3f, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6d, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0x92, 0x00, 0x49, 0x00, 0x92, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xb6, 0x00, 
  0x6e, 0x00, 0x6e, 0x00, 0x6e, 0x00, 0x92, 0x00, 0x00, 0xcb, 0x49, 0x9f, 0xdb, 0x00, 0xb7, 0x00, 0xb6, 0x00, 0x92, 0x00, 0x6d, 0x00, 0x49, 0x00, 0x6e, 0x00, 0x92, 0x1b, 0x49, 0xaf, 0x6e, 0x1f, 0x6e, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6e, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xdb, 0x00, 0x6d, 0x63, 0x00, 0xe4, 0x6e, 0x10, 0x92, 0x00, 0x6e, 0x00, 0x6e, 0x00, 
  0x6e, 0x00, 0x6e, 0x00, 0x6e, 0x00, 0x92, 0x00, 0x49, 0x80, 0x00, 0xff, 0x49, 0x94, 0xdb, 0x00, 0xb7, 0x00, 0x92, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6e, 0x00, 0x6e, 0x00, 0x49, 0x00, 0x6d, 0x00, 0x6e, 0x00, 0x6d, 0x00, 0x49, 0x00, 0x6e, 0x00, 0xb7, 0x00, 0xdb, 0x00, 0x6e, 0x54, 0x00, 0xff, 0x49, 0xbb, 0x92, 0x07, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 
  0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0xb6, 0x00, 0x49, 0x6f, 0x00, 0xff, 0x49, 0xac, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0x3c, 0x25, 0xa4, 0x00, 0xdf, 0x00, 0xd3, 0x00, 0xb4, 0x25, 0x8c, 0x49, 0x48, 0x00, 0x00, 0x92, 0x00, 0x92, 0x00, 0x6d, 0x6b, 0x00, 0xff, 0x49, 0xa7, 0xb7, 0x00, 0xb6, 0x00, 0x92, 0x00, 0xb6, 0x00, 0xb6, 0x00, 
  0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x49, 0x7b, 0x00, 0xd7, 0x00, 0x00, 0x6d, 0x20, 0x24, 0xd4, 0x00, 0xff, 0x00, 0xfc, 0x00, 0xfc, 0x00, 0xfc, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x24, 0xcf, 0x6d, 0x3f, 0x92, 0x00, 0x24, 0xa7, 0x25, 0xaf, 0xb7, 0x00, 0xb6, 0x00, 0x92, 0x00, 0xb6, 0x00, 0x92, 0x00, 0x92, 0x00, 
  0x6e, 0x00, 0x49, 0x00, 0x6e, 0x00, 0x92, 0x00, 0x92, 0x00, 0x6e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6e, 0x38, 0x00, 0xff, 0x00, 0xfc, 0x00, 0xfb, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x6e, 0x63, 0xb6, 0x00, 0x92, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0x92, 0x00, 0x6d, 0x00, 0x6d, 0x00, 
  0x6e, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6e, 0x00, 0x92, 0x00, 0x92, 0x00, 0x00, 0x00, 0x6e, 0x08, 0x00, 0xff, 0x00, 0xfc, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x6e, 0x34, 0xb6, 0x00, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6e, 0x00, 
  0x6e, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6d, 0x00, 0x92, 0x00, 0x92, 0x00, 0x00, 0x00, 0x24, 0xd7, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xe8, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x6d, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6e, 0x00, 
  0x6e, 0x00, 0x49, 0x00, 0x6d, 0x00, 0x49, 0x00, 0x6e, 0x00, 0x92, 0x00, 0x49, 0x2b, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x49, 0x5c, 0x92, 0x00, 0x6e, 0x00, 0x6d, 0x00, 0x6d, 0x00, 0x49, 0x00, 0x6e, 0x00, 
  0x92, 0x00, 0x6d, 0x00, 0x6d, 0x00, 0x6d, 0x00, 0x6e, 0x00, 0x6e, 0x00, 0x24, 0xab, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x24, 0xbb, 0x6e, 0x00, 0x6e, 0x00, 0x6d, 0x00, 0x6d, 0x00, 0x6d, 0x00, 0x6e, 0x00, 
  0x6d, 0x33, 0x49, 0x47, 0x49, 0x47, 0x49, 0x47, 0x49, 0x33, 0x6e, 0x00, 0x00, 0xe7, 0x00, 0xfc, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xdf, 0x49, 0x00, 0x6d, 0x28, 0x49, 0x47, 0x49, 0x47, 0x49, 0x47, 0x6d, 0x37, 
  0x24, 0xd8, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x24, 0xd4, 0x92, 0x00, 0x00, 0xeb, 0x00, 0xfc, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xe4, 0x49, 0x00, 0x25, 0xb0, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xe7, 
  0x49, 0x1f, 0x49, 0x33, 0x49, 0x30, 0x49, 0x33, 0x6d, 0x1f, 0x92, 0x00, 0x00, 0xe3, 0x00, 0xfc, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xdc, 0x6d, 0x00, 0x6e, 0x18, 0x49, 0x33, 0x49, 0x33, 0x49, 0x33, 0x49, 0x23, 
  0x6d, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6d, 0x00, 0x6e, 0x00, 0x25, 0x94, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x24, 0xb4, 0x92, 0x00, 0x6d, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6d, 0x00, 
  0x6d, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6d, 0x00, 0x92, 0x00, 0x6d, 0x17, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x49, 0x4f, 0x92, 0x00, 0x6e, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6d, 0x00, 
  0x6d, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x92, 0x00, 0xb6, 0x00, 0x00, 0x00, 0x25, 0xbc, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x24, 0xd8, 0xb6, 0x00, 0x92, 0x00, 0x92, 0x00, 0x6d, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6d, 0x00, 
  0x6d, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6e, 0x00, 0x92, 0x00, 0x92, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfb, 0x00, 0xfc, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x6e, 0x23, 0xb6, 0x00, 0x92, 0x00, 0x92, 0x00, 0x6e, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 
  0x49, 0x00, 0x49, 0x00, 0x92, 0x00, 0xb6, 0x00, 0x92, 0x00, 0x6d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6e, 0x27, 0x00, 0xfb, 0x00, 0xff, 0x00, 0xfc, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xfc, 0x6d, 0x4c, 0x92, 0x00, 0x6e, 0x00, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x6d, 0x00, 0x49, 0x00, 
  0x92, 0x00, 0x92, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0x92, 0x00, 0x92, 0x00, 0x49, 0x7b, 0x00, 0xd4, 0x00, 0x00, 0x49, 0x0f, 0x24, 0xbb, 0x00, 0xff, 0x00, 0xff, 0x00, 0xfc, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x24, 0xb7, 0x6d, 0x28, 0x6e, 0x00, 0x24, 0xa7, 0x25, 0xb0, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 
  0xb6, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0x92, 0x00, 0x49, 0x6f, 0x00, 0xff, 0x25, 0xa8, 0x92, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0x23, 0x25, 0x87, 0x00, 0xc0, 0x24, 0xbc, 0x00, 0x9f, 0x25, 0x77, 0x49, 0x2f, 0x00, 0x00, 0x92, 0x00, 0x92, 0x00, 0x6d, 0x6b, 0x00, 0xff, 0x25, 0xac, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 
  0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x92, 0x00, 0x49, 0x80, 0x00, 0xff, 0x25, 0x8f, 0x92, 0x00, 0xb6, 0x00, 0x92, 0x00, 0x6d, 0x00, 0x49, 0x00, 0x6e, 0x00, 0x6d, 0x00, 0x6e, 0x00, 0x6d, 0x00, 0x6e, 0x00, 0x6e, 0x00, 0x6d, 0x00, 0x6e, 0x00, 0xb7, 0x00, 0xdb, 0x00, 0x6e, 0x53, 0x00, 0xff, 0x25, 0xc0, 0x92, 0x07, 0x6e, 0x00, 0x6e, 0x00, 0x6e, 0x00, 
  0x6e, 0x00, 0x6e, 0x00, 0x6e, 0x00, 0x92, 0x00, 0x24, 0xcc, 0x24, 0x9c, 0x6e, 0x00, 0x6e, 0x00, 0x92, 0x00, 0x92, 0x00, 0x6d, 0x00, 0x49, 0x00, 0x6e, 0x00, 0x6e, 0x23, 0x24, 0xc4, 0x6e, 0x24, 0x92, 0x00, 0x49, 0x00, 0x6d, 0x00, 0x6e, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xdb, 0x00, 0x6d, 0x63, 0x00, 0xe4, 0x6e, 0x10, 0x92, 0x00, 0x6e, 0x00, 0x6e, 0x00, 
  0xb7, 0x00, 0xb7, 0x00, 0xb7, 0x00, 0xb7, 0x00, 0x6e, 0x00, 0x49, 0x00, 0x6d, 0x00, 0x6e, 0x00, 0x92, 0x00, 0x6d, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6d, 0x00, 0x49, 0x3c, 0x00, 0xff, 0x25, 0x3f, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6d, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xb7, 0x00, 0x92, 0x00, 0x49, 0x00, 0xb6, 0x00, 0xb7, 0x00, 0xb6, 0x00, 0xb6, 0x00, 
  0xb6, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xb7, 0x00, 0x6e, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6e, 0x00, 0x92, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x6d, 0x00, 0x49, 0x34, 0x00, 0xff, 0x49, 0x37, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x92, 0x00, 0xb7, 0x00, 0xb6, 0x00, 0x92, 0x00, 0x49, 0x00, 0x92, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xb6, 0x00, 
  0xb6, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xb7, 0x00, 0x6d, 0x00, 0x49, 0x00, 0x6d, 0x00, 0x6d, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x3c, 0x00, 0xff, 0x49, 0x3c, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0x49, 0x00, 0xb6, 0x00, 0xb7, 0x00, 0x92, 0x00, 0x49, 0x00, 0x92, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xb6, 0x00, 
  0xb6, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xb7, 0x00, 0x6d, 0x00, 0x49, 0x00, 0x6d, 0x00, 0x6e, 0x00, 0x6e, 0x00, 0x6e, 0x00, 0x6e, 0x00, 0x6e, 0x00, 0x92, 0x00, 0x6e, 0x27, 0x24, 0xd4, 0x6d, 0x28, 0x6e, 0x00, 0x6e, 0x00, 0x6e, 0x00, 0x6e, 0x00, 0x6d, 0x00, 0x92, 0x00, 0xb7, 0x00, 0x92, 0x00, 0x49, 0x00, 0x92, 0x00, 0xb6, 0x00, 0xb6, 0x00, 0xb6, 0x00, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP == 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit*/
  0xd3, 0x9c, 0x00, 0xd3, 0x9c, 0x00, 0xd3, 0x9c, 0x00, 0x55, 0xad, 0x00, 0x49, 0x4a, 0x00, 0x0c, 0x63, 0x00, 0x96, 0xb5, 0x00, 0x92, 0x94, 0x00, 0x6d, 0x6b, 0x00, 0x8e, 0x73, 0x00, 0xae, 0x73, 0x00, 0xae, 0x73, 0x00, 0xcf, 0x7b, 0x00, 0x4d, 0x6b, 0x23, 0xc3, 0x18, 0xc8, 0xab, 0x5a, 0x24, 0x6d, 0x6b, 0x00, 0x2c, 0x63, 0x00, 0x2c, 0x63, 0x00, 0x2c, 0x63, 0x00, 0xcb, 0x5a, 0x00, 0x10, 0x84, 0x00, 0x34, 0xa5, 0x00, 0xcf, 0x7b, 0x00, 0xa7, 0x39, 0x00, 0x31, 0x8c, 0x00, 0xb2, 0x94, 0x00, 0x72, 0x94, 0x00, 0x72, 0x94, 0x00, 
  0xd3, 0x9c, 0x00, 0xd3, 0x9c, 0x00, 0xd3, 0x9c, 0x00, 0x55, 0xad, 0x00, 0x49, 0x4a, 0x00, 0x0c, 0x63, 0x00, 0x76, 0xb5, 0x00, 0x14, 0xa5, 0x00, 0x8a, 0x52, 0x00, 0xa7, 0x39, 0x00, 0x29, 0x4a, 0x00, 0x28, 0x42, 0x00, 0x69, 0x4a, 0x00, 0xa6, 0x31, 0x3c, 0x00, 0x00, 0xff, 0x86, 0x31, 0x3f, 0x49, 0x4a, 0x00, 0x08, 0x42, 0x00, 0x08, 0x42, 0x00, 0xa7, 0x39, 0x00, 0x29, 0x4a, 0x00, 0xb3, 0x9c, 0x00, 0x14, 0xa5, 0x00, 0xcf, 0x7b, 0x00, 0xa7, 0x39, 0x00, 0x31, 0x8c, 0x00, 0xb2, 0x94, 0x00, 0x72, 0x94, 0x00, 0x72, 0x94, 0x00, 
  0xd3, 0x9c, 0x00, 0xd3, 0x9c, 0x00, 0xd3, 0x9c, 0x00, 0x55, 0xad, 0x00, 0x69, 0x4a, 0x00, 0x0c, 0x63, 0x00, 0x55, 0xad, 0x00, 0x55, 0xad, 0x00, 0x51, 0x8c, 0x00, 0x08, 0x42, 0x00, 0x49, 0x4a, 0x00, 0x6a, 0x52, 0x00, 0xab, 0x5a, 0x00, 0x08, 0x42, 0x34, 0x00, 0x00, 0xff, 0xc7, 0x39, 0x37, 0x69, 0x4a, 0x00, 0x29, 0x4a, 0x00, 0x08, 0x42, 0x00, 0xa6, 0x31, 0x00, 0xcf, 0x7b, 0x00, 0x55, 0xad, 0x00, 0xd3, 0x9c, 0x00, 0xcf, 0x7b, 0x00, 0xc7, 0x39, 0x00, 0x31, 0x8c, 0x00, 0xb2, 0x94, 0x00, 0x72, 0x94, 0x00, 0x72, 0x94, 0x00, 
  0x35, 0xad, 0x00, 0x35, 0xad, 0x00, 0x35, 0xad, 0x00, 0xb7, 0xbd, 0x00, 0x8a, 0x52, 0x00, 0x8e, 0x73, 0x00, 0x96, 0xb5, 0x00, 0xf3, 0x9c, 0x00, 0x34, 0xa5, 0x00, 0x2d, 0x6b, 0x00, 0x49, 0x4a, 0x00, 0x8a, 0x52, 0x00, 0xcb, 0x5a, 0x00, 0xc7, 0x39, 0x3c, 0x00, 0x00, 0xff, 0x86, 0x31, 0x3f, 0x6a, 0x52, 0x00, 0x29, 0x4a, 0x00, 0x28, 0x42, 0x00, 0xeb, 0x5a, 0x00, 0xf3, 0x9c, 0x00, 0xf3, 0x9c, 0x00, 0xd3, 0x9c, 0x00, 0x31, 0x8c, 0x00, 0xe7, 0x39, 0x00, 0x72, 0x94, 0x00, 0xf3, 0x9c, 0x00, 0xb3, 0x9c, 0x00, 0xb3, 0x9c, 0x00, 
  0x4d, 0x6b, 0x00, 0x4d, 0x6b, 0x00, 0x4d, 0x6b, 0x00, 0xaf, 0x7b, 0x00, 0x62, 0x10, 0xcb, 0xa6, 0x31, 0x9f, 0xd7, 0xbd, 0x00, 0x35, 0xad, 0x00, 0xf4, 0xa4, 0x00, 0xcf, 0x7b, 0x00, 0x8a, 0x52, 0x00, 0x69, 0x4a, 0x00, 0x6e, 0x73, 0x00, 0x8e, 0x73, 0x1b, 0x86, 0x31, 0xaf, 0x8e, 0x73, 0x1f, 0x8e, 0x73, 0x00, 0x8a, 0x52, 0x00, 0x69, 0x4a, 0x00, 0x8e, 0x73, 0x00, 0xf3, 0x9c, 0x00, 0xd3, 0x9c, 0x00, 0xb6, 0xb5, 0x00, 0xab, 0x5a, 0x63, 0x00, 0x00, 0xe4, 0x2c, 0x63, 0x10, 0x8e, 0x73, 0x00, 0x6d, 0x6b, 0x00, 0x6d, 0x6b, 0x00, 
  0x4d, 0x6b, 0x00, 0x4d, 0x6b, 0x00, 0x4d, 0x6b, 0x00, 0xae, 0x73, 0x00, 0x28, 0x42, 0x80, 0x00, 0x00, 0xff, 0xe8, 0x41, 0x94, 0xf7, 0xbd, 0x00, 0x76, 0xb5, 0x00, 0xaf, 0x7b, 0x00, 0x8a, 0x52, 0x00, 0x8a, 0x52, 0x00, 0x2c, 0x63, 0x00, 0x4d, 0x6b, 0x00, 0x69, 0x4a, 0x00, 0xec, 0x62, 0x00, 0x4d, 0x6b, 0x00, 0x0c, 0x63, 0x00, 0x8a, 0x52, 0x00, 0x4d, 0x6b, 0x00, 0x55, 0xad, 0x00, 0xf8, 0xc5, 0x00, 0x2d, 0x6b, 0x54, 0x00, 0x00, 0xff, 0x86, 0x31, 0xbb, 0x51, 0x8c, 0x07, 0xef, 0x7b, 0x00, 0xcf, 0x7b, 0x00, 0xcf, 0x7b, 0x00, 
  0x51, 0x8c, 0x00, 0x51, 0x8c, 0x00, 0x30, 0x84, 0x00, 0x51, 0x8c, 0x00, 0xb2, 0x94, 0x00, 0x08, 0x42, 0x6f, 0x00, 0x00, 0xff, 0x86, 0x31, 0xac, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc7, 0x39, 0x3c, 0x04, 0x21, 0xa4, 0x00, 0x00, 0xdf, 0x00, 0x00, 0xd3, 0x41, 0x08, 0xb4, 0x45, 0x29, 0x8c, 0x08, 0x42, 0x48, 0x00, 0x00, 0x00, 0xcf, 0x7b, 0x00, 0x51, 0x8c, 0x00, 0xaa, 0x52, 0x6b, 0x00, 0x00, 0xff, 0x86, 0x31, 0xa7, 0x76, 0xb5, 0x00, 0xd3, 0x9c, 0x00, 0x92, 0x94, 0x00, 0xb3, 0x9c, 0x00, 0xd3, 0x9c, 0x00, 
  0xaf, 0x7b, 0x00, 0xef, 0x7b, 0x00, 0x51, 0x8c, 0x00, 0x10, 0x84, 0x00, 0xcf, 0x7b, 0x00, 0x51, 0x8c, 0x00, 0xc7, 0x39, 0x7b, 0x21, 0x08, 0xd7, 0x00, 0x00, 0x00, 0xec, 0x62, 0x20, 0xa3, 0x18, 0xd4, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0xa2, 0x10, 0xcf, 0xaa, 0x52, 0x3f, 0xaf, 0x7b, 0x00, 0xc3, 0x18, 0xa7, 0x45, 0x29, 0xaf, 0x55, 0xad, 0x00, 0xd3, 0x9c, 0x00, 0x51, 0x8c, 0x00, 0xd3, 0x9c, 0x00, 0x92, 0x94, 0x00, 0x10, 0x84, 0x00, 
  0x2c, 0x63, 0x00, 0x6a, 0x52, 0x00, 0x6e, 0x73, 0x00, 0x10, 0x84, 0x00, 0xaf, 0x7b, 0x00, 0x6e, 0x73, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6d, 0x6b, 0x38, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xfb, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x2d, 0x6b, 0x63, 0xf4, 0xa4, 0x00, 0x10, 0x84, 0x00, 0x14, 0xa5, 0x00, 0xb2, 0x94, 0x00, 0xb2, 0x94, 0x00, 0xf0, 0x83, 0x00, 0xab, 0x5a, 0x00, 0xeb, 0x5a, 0x00, 
  0x4d, 0x6b, 0x00, 0x29, 0x4a, 0x00, 0x29, 0x4a, 0x00, 0x4d, 0x6b, 0x00, 0x31, 0x8c, 0x00, 0xef, 0x7b, 0x00, 0x00, 0x00, 0x00, 0x2d, 0x6b, 0x08, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x4d, 0x6b, 0x34, 0xb3, 0x9c, 0x00, 0xf0, 0x83, 0x00, 0x51, 0x8c, 0x00, 0xae, 0x73, 0x00, 0x49, 0x4a, 0x00, 0x08, 0x42, 0x00, 0x0c, 0x63, 0x00, 
  0x4d, 0x6b, 0x00, 0x69, 0x4a, 0x00, 0x6a, 0x52, 0x00, 0xaa, 0x52, 0x00, 0x10, 0x84, 0x00, 0x51, 0x8c, 0x00, 0x00, 0x00, 0x00, 0xa3, 0x18, 0xd7, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x41, 0x08, 0xe8, 0xf0, 0x83, 0x00, 0xcf, 0x7b, 0x00, 0xae, 0x73, 0x00, 0xab, 0x5a, 0x00, 0x6a, 0x52, 0x00, 0x6a, 0x52, 0x00, 0x0c, 0x63, 0x00, 
  0x4d, 0x6b, 0x00, 0x69, 0x4a, 0x00, 0xaa, 0x52, 0x00, 0x6a, 0x52, 0x00, 0x2c, 0x63, 0x00, 0x72, 0x94, 0x00, 0x49, 0x4a, 0x2b, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x49, 0x4a, 0x5c, 0x31, 0x8c, 0x00, 0x4d, 0x6b, 0x00, 0x8a, 0x52, 0x00, 0x8a, 0x52, 0x00, 0x6a, 0x52, 0x00, 0x0c, 0x63, 0x00, 
  0x8e, 0x73, 0x00, 0xab, 0x5a, 0x00, 0xec, 0x62, 0x00, 0x0c, 0x63, 0x00, 0x2c, 0x63, 0x00, 0x4d, 0x6b, 0x00, 0xe4, 0x20, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0xe3, 0x18, 0xbb, 0x8e, 0x73, 0x00, 0x0c, 0x63, 0x00, 0xeb, 0x5a, 0x00, 0x0c, 0x63, 0x00, 0xcb, 0x5a, 0x00, 0x6d, 0x6b, 0x00, 
  0xcb, 0x5a, 0x33, 0xc7, 0x39, 0x47, 0x08, 0x42, 0x47, 0x08, 0x42, 0x47, 0x69, 0x4a, 0x33, 0x2d, 0x6b, 0x00, 0x21, 0x08, 0xe7, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x20, 0x00, 0xdf, 0x28, 0x42, 0x00, 0x8a, 0x52, 0x28, 0x08, 0x42, 0x47, 0x08, 0x42, 0x47, 0xc7, 0x39, 0x47, 0x8a, 0x52, 0x37, 
  0xc3, 0x18, 0xd8, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0xa3, 0x18, 0xd4, 0xcf, 0x7b, 0x00, 0x21, 0x08, 0xeb, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x41, 0x08, 0xe4, 0x8a, 0x52, 0x00, 0x25, 0x29, 0xb0, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x82, 0x10, 0xe7, 
  0x69, 0x4a, 0x1f, 0xc7, 0x39, 0x33, 0xc7, 0x39, 0x30, 0xa6, 0x31, 0x33, 0xec, 0x62, 0x1f, 0xcf, 0x7b, 0x00, 0x21, 0x08, 0xe3, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x20, 0x00, 0xdc, 0x8a, 0x52, 0x00, 0x2d, 0x6b, 0x18, 0xa7, 0x39, 0x33, 0xc7, 0x39, 0x33, 0xc7, 0x39, 0x33, 0x49, 0x4a, 0x23, 
  0xcb, 0x5a, 0x00, 0x49, 0x4a, 0x00, 0x69, 0x4a, 0x00, 0x49, 0x4a, 0x00, 0x0c, 0x63, 0x00, 0x6e, 0x73, 0x00, 0x04, 0x21, 0x94, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0xe4, 0x20, 0xb4, 0xcf, 0x7b, 0x00, 0x0c, 0x63, 0x00, 0x49, 0x4a, 0x00, 0x6a, 0x52, 0x00, 0x49, 0x4a, 0x00, 0xcb, 0x5a, 0x00, 
  0xab, 0x5a, 0x00, 0x08, 0x42, 0x00, 0x29, 0x4a, 0x00, 0x08, 0x42, 0x00, 0xec, 0x62, 0x00, 0x10, 0x84, 0x00, 0x0c, 0x63, 0x17, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x49, 0x4a, 0x4f, 0x31, 0x8c, 0x00, 0x0c, 0x63, 0x00, 0x08, 0x42, 0x00, 0x29, 0x4a, 0x00, 0x28, 0x42, 0x00, 0x8a, 0x52, 0x00, 
  0xab, 0x5a, 0x00, 0x28, 0x42, 0x00, 0xe8, 0x41, 0x00, 0x28, 0x42, 0x00, 0x31, 0x8c, 0x00, 0x92, 0x94, 0x00, 0x00, 0x00, 0x00, 0x04, 0x21, 0xbc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0xc3, 0x18, 0xd8, 0xb3, 0x9c, 0x00, 0x51, 0x8c, 0x00, 0x71, 0x8c, 0x00, 0xaa, 0x52, 0x00, 0xc7, 0x39, 0x00, 0x28, 0x42, 0x00, 0x8a, 0x52, 0x00, 
  0xab, 0x5a, 0x00, 0xa7, 0x39, 0x00, 0xa6, 0x31, 0x00, 0x2d, 0x6b, 0x00, 0x51, 0x8c, 0x00, 0xcf, 0x7b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0xfb, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x4d, 0x6b, 0x23, 0xb2, 0x94, 0x00, 0xef, 0x7b, 0x00, 0x71, 0x8c, 0x00, 0x8e, 0x73, 0x00, 0xc7, 0x39, 0x00, 0xc7, 0x39, 0x00, 0x8a, 0x52, 0x00, 
  0x69, 0x4a, 0x00, 0x69, 0x4a, 0x00, 0xcf, 0x7b, 0x00, 0xd3, 0x9c, 0x00, 0x8e, 0x73, 0x00, 0xeb, 0x5a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2d, 0x6b, 0x27, 0x20, 0x00, 0xfb, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfc, 0xeb, 0x5a, 0x4c, 0x10, 0x84, 0x00, 0x4d, 0x6b, 0x00, 0xcf, 0x7b, 0x00, 0x8e, 0x73, 0x00, 0x31, 0x8c, 0x00, 0xcf, 0x7b, 0x00, 0x8a, 0x52, 0x00, 0x69, 0x4a, 0x00, 
  0xef, 0x7b, 0x00, 0x92, 0x94, 0x00, 0xf3, 0x9c, 0x00, 0xb2, 0x94, 0x00, 0x8e, 0x73, 0x00, 0xae, 0x73, 0x00, 0xa7, 0x39, 0x7b, 0x41, 0x08, 0xd4, 0x00, 0x00, 0x00, 0x49, 0x4a, 0x0f, 0xe3, 0x18, 0xbb, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x04, 0x21, 0xb7, 0xaa, 0x52, 0x28, 0x0c, 0x63, 0x00, 0xa3, 0x18, 0xa7, 0x04, 0x21, 0xb0, 0x31, 0x8c, 0x00, 0x10, 0x84, 0x00, 0xf0, 0x83, 0x00, 0x51, 0x8c, 0x00, 0x51, 0x8c, 0x00, 0xaf, 0x7b, 0x00, 
  0xf4, 0xa4, 0x00, 0xd3, 0x9c, 0x00, 0xb2, 0x94, 0x00, 0xd3, 0x9c, 0x00, 0x71, 0x8c, 0x00, 0xc7, 0x39, 0x6f, 0x00, 0x00, 0xff, 0x45, 0x29, 0xa8, 0x30, 0x84, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x42, 0x23, 0x24, 0x21, 0x87, 0x00, 0x00, 0xc0, 0x82, 0x10, 0xbc, 0x41, 0x08, 0x9f, 0x45, 0x29, 0x77, 0x69, 0x4a, 0x2f, 0x00, 0x00, 0x00, 0xae, 0x73, 0x00, 0x30, 0x84, 0x00, 0xcb, 0x5a, 0x6b, 0x00, 0x00, 0xff, 0x25, 0x29, 0xac, 0x92, 0x94, 0x00, 0x71, 0x8c, 0x00, 0x30, 0x84, 0x00, 0x31, 0x8c, 0x00, 0x71, 0x8c, 0x00, 
  0x8e, 0x73, 0x00, 0x8e, 0x73, 0x00, 0xae, 0x73, 0x00, 0x10, 0x84, 0x00, 0x08, 0x42, 0x80, 0x00, 0x00, 0xff, 0x24, 0x21, 0x8f, 0xcf, 0x7b, 0x00, 0x92, 0x94, 0x00, 0xae, 0x73, 0x00, 0xab, 0x5a, 0x00, 0x49, 0x4a, 0x00, 0x2d, 0x6b, 0x00, 0x0c, 0x63, 0x00, 0x4d, 0x6b, 0x00, 0xec, 0x62, 0x00, 0x4d, 0x6b, 0x00, 0x0c, 0x63, 0x00, 0xaa, 0x52, 0x00, 0x2d, 0x6b, 0x00, 0x35, 0xad, 0x00, 0x38, 0xc6, 0x00, 0x2d, 0x6b, 0x53, 0x00, 0x00, 0xff, 0x25, 0x29, 0xc0, 0xcf, 0x7b, 0x07, 0x8e, 0x73, 0x00, 0x6e, 0x73, 0x00, 0x6e, 0x73, 0x00, 
  0x4d, 0x6b, 0x00, 0x4d, 0x6b, 0x00, 0x4d, 0x6b, 0x00, 0xae, 0x73, 0x00, 0xa3, 0x18, 0xcc, 0x04, 0x21, 0x9c, 0x2c, 0x63, 0x00, 0x2d, 0x6b, 0x00, 0x71, 0x8c, 0x00, 0xcf, 0x7b, 0x00, 0xaa, 0x52, 0x00, 0x69, 0x4a, 0x00, 0x4d, 0x6b, 0x00, 0x4d, 0x6b, 0x23, 0xc3, 0x18, 0xc4, 0x4d, 0x6b, 0x24, 0x8e, 0x73, 0x00, 0x8a, 0x52, 0x00, 0x8a, 0x52, 0x00, 0x6d, 0x6b, 0x00, 0xd3, 0x9c, 0x00, 0xf4, 0xa4, 0x00, 0xd7, 0xbd, 0x00, 0xab, 0x5a, 0x63, 0x20, 0x00, 0xe4, 0x4d, 0x6b, 0x10, 0xaf, 0x7b, 0x00, 0x6e, 0x73, 0x00, 0x6e, 0x73, 0x00, 
  0x55, 0xad, 0x00, 0x55, 0xad, 0x00, 0x55, 0xad, 0x00, 0x76, 0xb5, 0x00, 0x4d, 0x6b, 0x00, 0x8a, 0x52, 0x00, 0x8a, 0x52, 0x00, 0x0c, 0x63, 0x00, 0x71, 0x8c, 0x00, 0x0c, 0x63, 0x00, 0x49, 0x4a, 0x00, 0x8a, 0x52, 0x00, 0xaa, 0x52, 0x00, 0xa7, 0x39, 0x3c, 0x00, 0x00, 0xff, 0x66, 0x31, 0x3f, 0x49, 0x4a, 0x00, 0x28, 0x42, 0x00, 0x28, 0x42, 0x00, 0xcb, 0x5a, 0x00, 0xd3, 0x9c, 0x00, 0xf4, 0xa4, 0x00, 0x14, 0xa5, 0x00, 0x51, 0x8c, 0x00, 0x6a, 0x52, 0x00, 0xd3, 0x9c, 0x00, 0x34, 0xa5, 0x00, 0xf4, 0xa4, 0x00, 0xf4, 0xa4, 0x00, 
  0xf3, 0x9c, 0x00, 0xf3, 0x9c, 0x00, 0xf4, 0xa4, 0x00, 0x14, 0xa5, 0x00, 0x0c, 0x63, 0x00, 0x49, 0x4a, 0x00, 0x8a, 0x52, 0x00, 0x6d, 0x6b, 0x00, 0xae, 0x73, 0x00, 0x28, 0x42, 0x00, 0x69, 0x4a, 0x00, 0x6a, 0x52, 0x00, 0xab, 0x5a, 0x00, 0x08, 0x42, 0x34, 0x00, 0x00, 0xff, 0xc7, 0x39, 0x37, 0x69, 0x4a, 0x00, 0x29, 0x4a, 0x00, 0x08, 0x42, 0x00, 0xa6, 0x31, 0x00, 0xcf, 0x7b, 0x00, 0x55, 0xad, 0x00, 0xf4, 0xa4, 0x00, 0xcf, 0x7b, 0x00, 0x49, 0x4a, 0x00, 0x92, 0x94, 0x00, 0xd3, 0x9c, 0x00, 0xb2, 0x94, 0x00, 0xb2, 0x94, 0x00, 
  0xf3, 0x9c, 0x00, 0xf3, 0x9c, 0x00, 0xf4, 0xa4, 0x00, 0x14, 0xa5, 0x00, 0x0c, 0x63, 0x00, 0x49, 0x4a, 0x00, 0xab, 0x5a, 0x00, 0x0c, 0x63, 0x00, 0x49, 0x4a, 0x00, 0xe7, 0x39, 0x00, 0x28, 0x42, 0x00, 0x08, 0x42, 0x00, 0x69, 0x4a, 0x00, 0xa6, 0x31, 0x3c, 0x00, 0x00, 0xff, 0x86, 0x31, 0x3c, 0x29, 0x4a, 0x00, 0x08, 0x42, 0x00, 0x08, 0x42, 0x00, 0xa6, 0x31, 0x00, 0x28, 0x42, 0x00, 0xd3, 0x9c, 0x00, 0x35, 0xad, 0x00, 0xcf, 0x7b, 0x00, 0x29, 0x4a, 0x00, 0x92, 0x94, 0x00, 0xd3, 0x9c, 0x00, 0xb2, 0x94, 0x00, 0xb2, 0x94, 0x00, 
  0xf3, 0x9c, 0x00, 0xf3, 0x9c, 0x00, 0xf4, 0xa4, 0x00, 0x14, 0xa5, 0x00, 0x0c, 0x63, 0x00, 0x49, 0x4a, 0x00, 0xaa, 0x52, 0x00, 0x0c, 0x63, 0x00, 0x8e, 0x73, 0x00, 0x6e, 0x73, 0x00, 0x6e, 0x73, 0x00, 0x6e, 0x73, 0x00, 0xaf, 0x7b, 0x00, 0x0c, 0x63, 0x27, 0xa2, 0x10, 0xd4, 0x8a, 0x52, 0x28, 0x4d, 0x6b, 0x00, 0x0c, 0x63, 0x00, 0x0c, 0x63, 0x00, 0x0c, 0x63, 0x00, 0xab, 0x5a, 0x00, 0x10, 0x84, 0x00, 0x55, 0xad, 0x00, 0xcf, 0x7b, 0x00, 0x29, 0x4a, 0x00, 0x92, 0x94, 0x00, 0xd3, 0x9c, 0x00, 0xb2, 0x94, 0x00, 0xb2, 0x94, 0x00, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP != 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit  BUT the 2  color bytes are swapped*/
  0x9c, 0xd3, 0x00, 0x9c, 0xd3, 0x00, 0x9c, 0xd3, 0x00, 0xad, 0x55, 0x00, 0x4a, 0x49, 0x00, 0x63, 0x0c, 0x00, 0xb5, 0x96, 0x00, 0x94, 0x92, 0x00, 0x6b, 0x6d, 0x00, 0x73, 0x8e, 0x00, 0x73, 0xae, 0x00, 0x73, 0xae, 0x00, 0x7b, 0xcf, 0x00, 0x6b, 0x4d, 0x23, 0x18, 0xc3, 0xc8, 0x5a, 0xab, 0x24, 0x6b, 0x6d, 0x00, 0x63, 0x2c, 0x00, 0x63, 0x2c, 0x00, 0x63, 0x2c, 0x00, 0x5a, 0xcb, 0x00, 0x84, 0x10, 0x00, 0xa5, 0x34, 0x00, 0x7b, 0xcf, 0x00, 0x39, 0xa7, 0x00, 0x8c, 0x31, 0x00, 0x94, 0xb2, 0x00, 0x94, 0x72, 0x00, 0x94, 0x72, 0x00, 
  0x9c, 0xd3, 0x00, 0x9c, 0xd3, 0x00, 0x9c, 0xd3, 0x00, 0xad, 0x55, 0x00, 0x4a, 0x49, 0x00, 0x63, 0x0c, 0x00, 0xb5, 0x76, 0x00, 0xa5, 0x14, 0x00, 0x52, 0x8a, 0x00, 0x39, 0xa7, 0x00, 0x4a, 0x29, 0x00, 0x42, 0x28, 0x00, 0x4a, 0x69, 0x00, 0x31, 0xa6, 0x3c, 0x00, 0x00, 0xff, 0x31, 0x86, 0x3f, 0x4a, 0x49, 0x00, 0x42, 0x08, 0x00, 0x42, 0x08, 0x00, 0x39, 0xa7, 0x00, 0x4a, 0x29, 0x00, 0x9c, 0xb3, 0x00, 0xa5, 0x14, 0x00, 0x7b, 0xcf, 0x00, 0x39, 0xa7, 0x00, 0x8c, 0x31, 0x00, 0x94, 0xb2, 0x00, 0x94, 0x72, 0x00, 0x94, 0x72, 0x00, 
  0x9c, 0xd3, 0x00, 0x9c, 0xd3, 0x00, 0x9c, 0xd3, 0x00, 0xad, 0x55, 0x00, 0x4a, 0x69, 0x00, 0x63, 0x0c, 0x00, 0xad, 0x55, 0x00, 0xad, 0x55, 0x00, 0x8c, 0x51, 0x00, 0x42, 0x08, 0x00, 0x4a, 0x49, 0x00, 0x52, 0x6a, 0x00, 0x5a, 0xab, 0x00, 0x42, 0x08, 0x34, 0x00, 0x00, 0xff, 0x39, 0xc7, 0x37, 0x4a, 0x69, 0x00, 0x4a, 0x29, 0x00, 0x42, 0x08, 0x00, 0x31, 0xa6, 0x00, 0x7b, 0xcf, 0x00, 0xad, 0x55, 0x00, 0x9c, 0xd3, 0x00, 0x7b, 0xcf, 0x00, 0x39, 0xc7, 0x00, 0x8c, 0x31, 0x00, 0x94, 0xb2, 0x00, 0x94, 0x72, 0x00, 0x94, 0x72, 0x00, 
  0xad, 0x35, 0x00, 0xad, 0x35, 0x00, 0xad, 0x35, 0x00, 0xbd, 0xb7, 0x00, 0x52, 0x8a, 0x00, 0x73, 0x8e, 0x00, 0xb5, 0x96, 0x00, 0x9c, 0xf3, 0x00, 0xa5, 0x34, 0x00, 0x6b, 0x2d, 0x00, 0x4a, 0x49, 0x00, 0x52, 0x8a, 0x00, 0x5a, 0xcb, 0x00, 0x39, 0xc7, 0x3c, 0x00, 0x00, 0xff, 0x31, 0x86, 0x3f, 0x52, 0x6a, 0x00, 0x4a, 0x29, 0x00, 0x42, 0x28, 0x00, 0x5a, 0xeb, 0x00, 0x9c, 0xf3, 0x00, 0x9c, 0xf3, 0x00, 0x9c, 0xd3, 0x00, 0x8c, 0x31, 0x00, 0x39, 0xe7, 0x00, 0x94, 0x72, 0x00, 0x9c, 0xf3, 0x00, 0x9c, 0xb3, 0x00, 0x9c, 0xb3, 0x00, 
  0x6b, 0x4d, 0x00, 0x6b, 0x4d, 0x00, 0x6b, 0x4d, 0x00, 0x7b, 0xaf, 0x00, 0x10, 0x62, 0xcb, 0x31, 0xa6, 0x9f, 0xbd, 0xd7, 0x00, 0xad, 0x35, 0x00, 0xa4, 0xf4, 0x00, 0x7b, 0xcf, 0x00, 0x52, 0x8a, 0x00, 0x4a, 0x69, 0x00, 0x73, 0x6e, 0x00, 0x73, 0x8e, 0x1b, 0x31, 0x86, 0xaf, 0x73, 0x8e, 0x1f, 0x73, 0x8e, 0x00, 0x52, 0x8a, 0x00, 0x4a, 0x69, 0x00, 0x73, 0x8e, 0x00, 0x9c, 0xf3, 0x00, 0x9c, 0xd3, 0x00, 0xb5, 0xb6, 0x00, 0x5a, 0xab, 0x63, 0x00, 0x00, 0xe4, 0x63, 0x2c, 0x10, 0x73, 0x8e, 0x00, 0x6b, 0x6d, 0x00, 0x6b, 0x6d, 0x00, 
  0x6b, 0x4d, 0x00, 0x6b, 0x4d, 0x00, 0x6b, 0x4d, 0x00, 0x73, 0xae, 0x00, 0x42, 0x28, 0x80, 0x00, 0x00, 0xff, 0x41, 0xe8, 0x94, 0xbd, 0xf7, 0x00, 0xb5, 0x76, 0x00, 0x7b, 0xaf, 0x00, 0x52, 0x8a, 0x00, 0x52, 0x8a, 0x00, 0x63, 0x2c, 0x00, 0x6b, 0x4d, 0x00, 0x4a, 0x69, 0x00, 0x62, 0xec, 0x00, 0x6b, 0x4d, 0x00, 0x63, 0x0c, 0x00, 0x52, 0x8a, 0x00, 0x6b, 0x4d, 0x00, 0xad, 0x55, 0x00, 0xc5, 0xf8, 0x00, 0x6b, 0x2d, 0x54, 0x00, 0x00, 0xff, 0x31, 0x86, 0xbb, 0x8c, 0x51, 0x07, 0x7b, 0xef, 0x00, 0x7b, 0xcf, 0x00, 0x7b, 0xcf, 0x00, 
  0x8c, 0x51, 0x00, 0x8c, 0x51, 0x00, 0x84, 0x30, 0x00, 0x8c, 0x51, 0x00, 0x94, 0xb2, 0x00, 0x42, 0x08, 0x6f, 0x00, 0x00, 0xff, 0x31, 0x86, 0xac, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0xc7, 0x3c, 0x21, 0x04, 0xa4, 0x00, 0x00, 0xdf, 0x00, 0x00, 0xd3, 0x08, 0x41, 0xb4, 0x29, 0x45, 0x8c, 0x42, 0x08, 0x48, 0x00, 0x00, 0x00, 0x7b, 0xcf, 0x00, 0x8c, 0x51, 0x00, 0x52, 0xaa, 0x6b, 0x00, 0x00, 0xff, 0x31, 0x86, 0xa7, 0xb5, 0x76, 0x00, 0x9c, 0xd3, 0x00, 0x94, 0x92, 0x00, 0x9c, 0xb3, 0x00, 0x9c, 0xd3, 0x00, 
  0x7b, 0xaf, 0x00, 0x7b, 0xef, 0x00, 0x8c, 0x51, 0x00, 0x84, 0x10, 0x00, 0x7b, 0xcf, 0x00, 0x8c, 0x51, 0x00, 0x39, 0xc7, 0x7b, 0x08, 0x21, 0xd7, 0x00, 0x00, 0x00, 0x62, 0xec, 0x20, 0x18, 0xa3, 0xd4, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x10, 0xa2, 0xcf, 0x52, 0xaa, 0x3f, 0x7b, 0xaf, 0x00, 0x18, 0xc3, 0xa7, 0x29, 0x45, 0xaf, 0xad, 0x55, 0x00, 0x9c, 0xd3, 0x00, 0x8c, 0x51, 0x00, 0x9c, 0xd3, 0x00, 0x94, 0x92, 0x00, 0x84, 0x10, 0x00, 
  0x63, 0x2c, 0x00, 0x52, 0x6a, 0x00, 0x73, 0x6e, 0x00, 0x84, 0x10, 0x00, 0x7b, 0xaf, 0x00, 0x73, 0x6e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6b, 0x6d, 0x38, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xfb, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x6b, 0x2d, 0x63, 0xa4, 0xf4, 0x00, 0x84, 0x10, 0x00, 0xa5, 0x14, 0x00, 0x94, 0xb2, 0x00, 0x94, 0xb2, 0x00, 0x83, 0xf0, 0x00, 0x5a, 0xab, 0x00, 0x5a, 0xeb, 0x00, 
  0x6b, 0x4d, 0x00, 0x4a, 0x29, 0x00, 0x4a, 0x29, 0x00, 0x6b, 0x4d, 0x00, 0x8c, 0x31, 0x00, 0x7b, 0xef, 0x00, 0x00, 0x00, 0x00, 0x6b, 0x2d, 0x08, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x6b, 0x4d, 0x34, 0x9c, 0xb3, 0x00, 0x83, 0xf0, 0x00, 0x8c, 0x51, 0x00, 0x73, 0xae, 0x00, 0x4a, 0x49, 0x00, 0x42, 0x08, 0x00, 0x63, 0x0c, 0x00, 
  0x6b, 0x4d, 0x00, 0x4a, 0x69, 0x00, 0x52, 0x6a, 0x00, 0x52, 0xaa, 0x00, 0x84, 0x10, 0x00, 0x8c, 0x51, 0x00, 0x00, 0x00, 0x00, 0x18, 0xa3, 0xd7, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x08, 0x41, 0xe8, 0x83, 0xf0, 0x00, 0x7b, 0xcf, 0x00, 0x73, 0xae, 0x00, 0x5a, 0xab, 0x00, 0x52, 0x6a, 0x00, 0x52, 0x6a, 0x00, 0x63, 0x0c, 0x00, 
  0x6b, 0x4d, 0x00, 0x4a, 0x69, 0x00, 0x52, 0xaa, 0x00, 0x52, 0x6a, 0x00, 0x63, 0x2c, 0x00, 0x94, 0x72, 0x00, 0x4a, 0x49, 0x2b, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x4a, 0x49, 0x5c, 0x8c, 0x31, 0x00, 0x6b, 0x4d, 0x00, 0x52, 0x8a, 0x00, 0x52, 0x8a, 0x00, 0x52, 0x6a, 0x00, 0x63, 0x0c, 0x00, 
  0x73, 0x8e, 0x00, 0x5a, 0xab, 0x00, 0x62, 0xec, 0x00, 0x63, 0x0c, 0x00, 0x63, 0x2c, 0x00, 0x6b, 0x4d, 0x00, 0x20, 0xe4, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x18, 0xe3, 0xbb, 0x73, 0x8e, 0x00, 0x63, 0x0c, 0x00, 0x5a, 0xeb, 0x00, 0x63, 0x0c, 0x00, 0x5a, 0xcb, 0x00, 0x6b, 0x6d, 0x00, 
  0x5a, 0xcb, 0x33, 0x39, 0xc7, 0x47, 0x42, 0x08, 0x47, 0x42, 0x08, 0x47, 0x4a, 0x69, 0x33, 0x6b, 0x2d, 0x00, 0x08, 0x21, 0xe7, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x20, 0xdf, 0x42, 0x28, 0x00, 0x52, 0x8a, 0x28, 0x42, 0x08, 0x47, 0x42, 0x08, 0x47, 0x39, 0xc7, 0x47, 0x52, 0x8a, 0x37, 
  0x18, 0xc3, 0xd8, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x18, 0xa3, 0xd4, 0x7b, 0xcf, 0x00, 0x08, 0x21, 0xeb, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x08, 0x41, 0xe4, 0x52, 0x8a, 0x00, 0x29, 0x25, 0xb0, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x10, 0x82, 0xe7, 
  0x4a, 0x69, 0x1f, 0x39, 0xc7, 0x33, 0x39, 0xc7, 0x30, 0x31, 0xa6, 0x33, 0x62, 0xec, 0x1f, 0x7b, 0xcf, 0x00, 0x08, 0x21, 0xe3, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x20, 0xdc, 0x52, 0x8a, 0x00, 0x6b, 0x2d, 0x18, 0x39, 0xa7, 0x33, 0x39, 0xc7, 0x33, 0x39, 0xc7, 0x33, 0x4a, 0x49, 0x23, 
  0x5a, 0xcb, 0x00, 0x4a, 0x49, 0x00, 0x4a, 0x69, 0x00, 0x4a, 0x49, 0x00, 0x63, 0x0c, 0x00, 0x73, 0x6e, 0x00, 0x21, 0x04, 0x94, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x20, 0xe4, 0xb4, 0x7b, 0xcf, 0x00, 0x63, 0x0c, 0x00, 0x4a, 0x49, 0x00, 0x52, 0x6a, 0x00, 0x4a, 0x49, 0x00, 0x5a, 0xcb, 0x00, 
  0x5a, 0xab, 0x00, 0x42, 0x08, 0x00, 0x4a, 0x29, 0x00, 0x42, 0x08, 0x00, 0x62, 0xec, 0x00, 0x84, 0x10, 0x00, 0x63, 0x0c, 0x17, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x4a, 0x49, 0x4f, 0x8c, 0x31, 0x00, 0x63, 0x0c, 0x00, 0x42, 0x08, 0x00, 0x4a, 0x29, 0x00, 0x42, 0x28, 0x00, 0x52, 0x8a, 0x00, 
  0x5a, 0xab, 0x00, 0x42, 0x28, 0x00, 0x41, 0xe8, 0x00, 0x42, 0x28, 0x00, 0x8c, 0x31, 0x00, 0x94, 0x92, 0x00, 0x00, 0x00, 0x00, 0x21, 0x04, 0xbc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x18, 0xc3, 0xd8, 0x9c, 0xb3, 0x00, 0x8c, 0x51, 0x00, 0x8c, 0x71, 0x00, 0x52, 0xaa, 0x00, 0x39, 0xc7, 0x00, 0x42, 0x28, 0x00, 0x52, 0x8a, 0x00, 
  0x5a, 0xab, 0x00, 0x39, 0xa7, 0x00, 0x31, 0xa6, 0x00, 0x6b, 0x2d, 0x00, 0x8c, 0x51, 0x00, 0x7b, 0xcf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xfb, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x6b, 0x4d, 0x23, 0x94, 0xb2, 0x00, 0x7b, 0xef, 0x00, 0x8c, 0x71, 0x00, 0x73, 0x8e, 0x00, 0x39, 0xc7, 0x00, 0x39, 0xc7, 0x00, 0x52, 0x8a, 0x00, 
  0x4a, 0x69, 0x00, 0x4a, 0x69, 0x00, 0x7b, 0xcf, 0x00, 0x9c, 0xd3, 0x00, 0x73, 0x8e, 0x00, 0x5a, 0xeb, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6b, 0x2d, 0x27, 0x00, 0x20, 0xfb, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfc, 0x5a, 0xeb, 0x4c, 0x84, 0x10, 0x00, 0x6b, 0x4d, 0x00, 0x7b, 0xcf, 0x00, 0x73, 0x8e, 0x00, 0x8c, 0x31, 0x00, 0x7b, 0xcf, 0x00, 0x52, 0x8a, 0x00, 0x4a, 0x69, 0x00, 
  0x7b, 0xef, 0x00, 0x94, 0x92, 0x00, 0x9c, 0xf3, 0x00, 0x94, 0xb2, 0x00, 0x73, 0x8e, 0x00, 0x73, 0xae, 0x00, 0x39, 0xa7, 0x7b, 0x08, 0x41, 0xd4, 0x00, 0x00, 0x00, 0x4a, 0x49, 0x0f, 0x18, 0xe3, 0xbb, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x21, 0x04, 0xb7, 0x52, 0xaa, 0x28, 0x63, 0x0c, 0x00, 0x18, 0xa3, 0xa7, 0x21, 0x04, 0xb0, 0x8c, 0x31, 0x00, 0x84, 0x10, 0x00, 0x83, 0xf0, 0x00, 0x8c, 0x51, 0x00, 0x8c, 0x51, 0x00, 0x7b, 0xaf, 0x00, 
  0xa4, 0xf4, 0x00, 0x9c, 0xd3, 0x00, 0x94, 0xb2, 0x00, 0x9c, 0xd3, 0x00, 0x8c, 0x71, 0x00, 0x39, 0xc7, 0x6f, 0x00, 0x00, 0xff, 0x29, 0x45, 0xa8, 0x84, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x42, 0x08, 0x23, 0x21, 0x24, 0x87, 0x00, 0x00, 0xc0, 0x10, 0x82, 0xbc, 0x08, 0x41, 0x9f, 0x29, 0x45, 0x77, 0x4a, 0x69, 0x2f, 0x00, 0x00, 0x00, 0x73, 0xae, 0x00, 0x84, 0x30, 0x00, 0x5a, 0xcb, 0x6b, 0x00, 0x00, 0xff, 0x29, 0x25, 0xac, 0x94, 0x92, 0x00, 0x8c, 0x71, 0x00, 0x84, 0x30, 0x00, 0x8c, 0x31, 0x00, 0x8c, 0x71, 0x00, 
  0x73, 0x8e, 0x00, 0x73, 0x8e, 0x00, 0x73, 0xae, 0x00, 0x84, 0x10, 0x00, 0x42, 0x08, 0x80, 0x00, 0x00, 0xff, 0x21, 0x24, 0x8f, 0x7b, 0xcf, 0x00, 0x94, 0x92, 0x00, 0x73, 0xae, 0x00, 0x5a, 0xab, 0x00, 0x4a, 0x49, 0x00, 0x6b, 0x2d, 0x00, 0x63, 0x0c, 0x00, 0x6b, 0x4d, 0x00, 0x62, 0xec, 0x00, 0x6b, 0x4d, 0x00, 0x63, 0x0c, 0x00, 0x52, 0xaa, 0x00, 0x6b, 0x2d, 0x00, 0xad, 0x35, 0x00, 0xc6, 0x38, 0x00, 0x6b, 0x2d, 0x53, 0x00, 0x00, 0xff, 0x29, 0x25, 0xc0, 0x7b, 0xcf, 0x07, 0x73, 0x8e, 0x00, 0x73, 0x6e, 0x00, 0x73, 0x6e, 0x00, 
  0x6b, 0x4d, 0x00, 0x6b, 0x4d, 0x00, 0x6b, 0x4d, 0x00, 0x73, 0xae, 0x00, 0x18, 0xa3, 0xcc, 0x21, 0x04, 0x9c, 0x63, 0x2c, 0x00, 0x6b, 0x2d, 0x00, 0x8c, 0x71, 0x00, 0x7b, 0xcf, 0x00, 0x52, 0xaa, 0x00, 0x4a, 0x69, 0x00, 0x6b, 0x4d, 0x00, 0x6b, 0x4d, 0x23, 0x18, 0xc3, 0xc4, 0x6b, 0x4d, 0x24, 0x73, 0x8e, 0x00, 0x52, 0x8a, 0x00, 0x52, 0x8a, 0x00, 0x6b, 0x6d, 0x00, 0x9c, 0xd3, 0x00, 0xa4, 0xf4, 0x00, 0xbd, 0xd7, 0x00, 0x5a, 0xab, 0x63, 0x00, 0x20, 0xe4, 0x6b, 0x4d, 0x10, 0x7b, 0xaf, 0x00, 0x73, 0x6e, 0x00, 0x73, 0x6e, 0x00, 
  0xad, 0x55, 0x00, 0xad, 0x55, 0x00, 0xad, 0x55, 0x00, 0xb5, 0x76, 0x00, 0x6b, 0x4d, 0x00, 0x52, 0x8a, 0x00, 0x52, 0x8a, 0x00, 0x63, 0x0c, 0x00, 0x8c, 0x71, 0x00, 0x63, 0x0c, 0x00, 0x4a, 0x49, 0x00, 0x52, 0x8a, 0x00, 0x52, 0xaa, 0x00, 0x39, 0xa7, 0x3c, 0x00, 0x00, 0xff, 0x31, 0x66, 0x3f, 0x4a, 0x49, 0x00, 0x42, 0x28, 0x00, 0x42, 0x28, 0x00, 0x5a, 0xcb, 0x00, 0x9c, 0xd3, 0x00, 0xa4, 0xf4, 0x00, 0xa5, 0x14, 0x00, 0x8c, 0x51, 0x00, 0x52, 0x6a, 0x00, 0x9c, 0xd3, 0x00, 0xa5, 0x34, 0x00, 0xa4, 0xf4, 0x00, 0xa4, 0xf4, 0x00, 
  0x9c, 0xf3, 0x00, 0x9c, 0xf3, 0x00, 0xa4, 0xf4, 0x00, 0xa5, 0x14, 0x00, 0x63, 0x0c, 0x00, 0x4a, 0x49, 0x00, 0x52, 0x8a, 0x00, 0x6b, 0x6d, 0x00, 0x73, 0xae, 0x00, 0x42, 0x28, 0x00, 0x4a, 0x69, 0x00, 0x52, 0x6a, 0x00, 0x5a, 0xab, 0x00, 0x42, 0x08, 0x34, 0x00, 0x00, 0xff, 0x39, 0xc7, 0x37, 0x4a, 0x69, 0x00, 0x4a, 0x29, 0x00, 0x42, 0x08, 0x00, 0x31, 0xa6, 0x00, 0x7b, 0xcf, 0x00, 0xad, 0x55, 0x00, 0xa4, 0xf4, 0x00, 0x7b, 0xcf, 0x00, 0x4a, 0x49, 0x00, 0x94, 0x92, 0x00, 0x9c, 0xd3, 0x00, 0x94, 0xb2, 0x00, 0x94, 0xb2, 0x00, 
  0x9c, 0xf3, 0x00, 0x9c, 0xf3, 0x00, 0xa4, 0xf4, 0x00, 0xa5, 0x14, 0x00, 0x63, 0x0c, 0x00, 0x4a, 0x49, 0x00, 0x5a, 0xab, 0x00, 0x63, 0x0c, 0x00, 0x4a, 0x49, 0x00, 0x39, 0xe7, 0x00, 0x42, 0x28, 0x00, 0x42, 0x08, 0x00, 0x4a, 0x69, 0x00, 0x31, 0xa6, 0x3c, 0x00, 0x00, 0xff, 0x31, 0x86, 0x3c, 0x4a, 0x29, 0x00, 0x42, 0x08, 0x00, 0x42, 0x08, 0x00, 0x31, 0xa6, 0x00, 0x42, 0x28, 0x00, 0x9c, 0xd3, 0x00, 0xad, 0x35, 0x00, 0x7b, 0xcf, 0x00, 0x4a, 0x29, 0x00, 0x94, 0x92, 0x00, 0x9c, 0xd3, 0x00, 0x94, 0xb2, 0x00, 0x94, 0xb2, 0x00, 
  0x9c, 0xf3, 0x00, 0x9c, 0xf3, 0x00, 0xa4, 0xf4, 0x00, 0xa5, 0x14, 0x00, 0x63, 0x0c, 0x00, 0x4a, 0x49, 0x00, 0x52, 0xaa, 0x00, 0x63, 0x0c, 0x00, 0x73, 0x8e, 0x00, 0x73, 0x6e, 0x00, 0x73, 0x6e, 0x00, 0x73, 0x6e, 0x00, 0x7b, 0xaf, 0x00, 0x63, 0x0c, 0x27, 0x10, 0xa2, 0xd4, 0x52, 0x8a, 0x28, 0x6b, 0x4d, 0x00, 0x63, 0x0c, 0x00, 0x63, 0x0c, 0x00, 0x63, 0x0c, 0x00, 0x5a, 0xab, 0x00, 0x84, 0x10, 0x00, 0xad, 0x55, 0x00, 0x7b, 0xcf, 0x00, 0x4a, 0x29, 0x00, 0x94, 0x92, 0x00, 0x9c, 0xd3, 0x00, 0x94, 0xb2, 0x00, 0x94, 0xb2, 0x00, 
#endif
#if LV_COLOR_DEPTH == 32
  0x99, 0x99, 0x99, 0x00, 0x99, 0x99, 0x99, 0x00, 0x9a, 0x9a, 0x9a, 0x00, 0xa9, 0xa9, 0xa9, 0x00, 0x4a, 0x4a, 0x4a, 0x00, 0x61, 0x61, 0x61, 0x00, 0xaf, 0xaf, 0xaf, 0x00, 0x91, 0x91, 0x91, 0x00, 0x6c, 0x6c, 0x6c, 0x00, 0x72, 0x72, 0x72, 0x00, 0x73, 0x73, 0x73, 0x00, 0x73, 0x73, 0x73, 0x00, 0x7a, 0x7a, 0x7a, 0x00, 0x67, 0x67, 0x67, 0x23, 0x1a, 0x1a, 0x1a, 0xc8, 0x56, 0x56, 0x56, 0x24, 0x6b, 0x6b, 0x6b, 0x00, 0x64, 0x64, 0x64, 0x00, 0x64, 0x64, 0x64, 0x00, 0x64, 0x64, 0x64, 0x00, 0x5a, 0x5a, 0x5a, 0x00, 0x82, 0x82, 0x82, 0x00, 0xa3, 0xa3, 0xa3, 0x00, 0x78, 0x78, 0x78, 0x00, 0x36, 0x36, 0x36, 0x00, 0x86, 0x86, 0x86, 0x00, 0x93, 0x93, 0x93, 0x00, 0x8d, 0x8d, 0x8d, 0x00, 0x8d, 0x8d, 0x8d, 0x00, 
  0x99, 0x99, 0x99, 0x00, 0x99, 0x99, 0x99, 0x00, 0x9a, 0x9a, 0x9a, 0x00, 0xa9, 0xa9, 0xa9, 0x00, 0x4a, 0x4a, 0x4a, 0x00, 0x61, 0x61, 0x61, 0x00, 0xad, 0xad, 0xad, 0x00, 0x9f, 0x9f, 0x9f, 0x00, 0x51, 0x51, 0x51, 0x00, 0x35, 0x35, 0x35, 0x00, 0x45, 0x45, 0x45, 0x00, 0x43, 0x43, 0x43, 0x00, 0x4c, 0x4c, 0x4c, 0x00, 0x33, 0x33, 0x33, 0x3c, 0x00, 0x00, 0x00, 0xff, 0x32, 0x32, 0x32, 0x3f, 0x48, 0x48, 0x48, 0x00, 0x40, 0x40, 0x40, 0x00, 0x41, 0x41, 0x41, 0x00, 0x35, 0x35, 0x35, 0x00, 0x46, 0x46, 0x46, 0x00, 0x95, 0x95, 0x95, 0x00, 0x9f, 0x9f, 0x9f, 0x00, 0x78, 0x78, 0x78, 0x00, 0x36, 0x36, 0x36, 0x00, 0x86, 0x86, 0x86, 0x00, 0x93, 0x93, 0x93, 0x00, 0x8d, 0x8d, 0x8d, 0x00, 0x8d, 0x8d, 0x8d, 0x00, 
  0x99, 0x99, 0x99, 0x00, 0x99, 0x99, 0x99, 0x00, 0x9a, 0x9a, 0x9a, 0x00, 0xa9, 0xa9, 0xa9, 0x00, 0x4b, 0x4b, 0x4b, 0x00, 0x62, 0x62, 0x62, 0x00, 0xa8, 0xa8, 0xa8, 0x00, 0xaa, 0xaa, 0xaa, 0x00, 0x88, 0x88, 0x88, 0x00, 0x40, 0x40, 0x40, 0x00, 0x49, 0x49, 0x49, 0x00, 0x4e, 0x4e, 0x4e, 0x00, 0x55, 0x55, 0x55, 0x00, 0x3f, 0x3f, 0x3f, 0x34, 0x00, 0x00, 0x00, 0xff, 0x38, 0x38, 0x38, 0x37, 0x4b, 0x4b, 0x4b, 0x00, 0x45, 0x45, 0x45, 0x00, 0x42, 0x42, 0x42, 0x00, 0x33, 0x33, 0x33, 0x00, 0x78, 0x78, 0x78, 0x00, 0xa8, 0xa8, 0xa8, 0x00, 0x99, 0x99, 0x99, 0x00, 0x78, 0x78, 0x78, 0x00, 0x37, 0x37, 0x37, 0x00, 0x86, 0x86, 0x86, 0x00, 0x93, 0x93, 0x93, 0x00, 0x8d, 0x8d, 0x8d, 0x00, 0x8d, 0x8d, 0x8d, 0x00, 
  0xa5, 0xa5, 0xa5, 0x00, 0xa5, 0xa5, 0xa5, 0x00, 0xa6, 0xa6, 0xa6, 0x00, 0xb6, 0xb6, 0xb6, 0x00, 0x51, 0x51, 0x51, 0x00, 0x6f, 0x6f, 0x6f, 0x00, 0xaf, 0xaf, 0xaf, 0x00, 0x9b, 0x9b, 0x9b, 0x00, 0xa3, 0xa3, 0xa3, 0x00, 0x66, 0x66, 0x66, 0x00, 0x48, 0x48, 0x48, 0x00, 0x4f, 0x4f, 0x4f, 0x00, 0x58, 0x58, 0x58, 0x00, 0x37, 0x37, 0x37, 0x3c, 0x00, 0x00, 0x00, 0xff, 0x30, 0x30, 0x30, 0x3f, 0x4d, 0x4d, 0x4d, 0x00, 0x46, 0x46, 0x46, 0x00, 0x43, 0x43, 0x43, 0x00, 0x5c, 0x5c, 0x5c, 0x00, 0x9c, 0x9c, 0x9c, 0x00, 0x9b, 0x9b, 0x9b, 0x00, 0x9a, 0x9a, 0x9a, 0x00, 0x86, 0x86, 0x86, 0x00, 0x3c, 0x3c, 0x3c, 0x00, 0x8d, 0x8d, 0x8d, 0x00, 0x9c, 0x9c, 0x9c, 0x00, 0x96, 0x96, 0x96, 0x00, 0x96, 0x96, 0x96, 0x00, 
  0x68, 0x68, 0x68, 0x00, 0x68, 0x68, 0x68, 0x00, 0x69, 0x69, 0x69, 0x00, 0x76, 0x76, 0x76, 0x00, 0x0e, 0x0e, 0x0e, 0xcb, 0x33, 0x33, 0x33, 0x9f, 0xb9, 0xb9, 0xb9, 0x00, 0xa6, 0xa6, 0xa6, 0x00, 0x9d, 0x9d, 0x9d, 0x00, 0x7a, 0x7a, 0x7a, 0x00, 0x51, 0x51, 0x51, 0x00, 0x4b, 0x4b, 0x4b, 0x00, 0x6d, 0x6d, 0x6d, 0x00, 0x71, 0x71, 0x71, 0x1b, 0x32, 0x32, 0x32, 0xaf, 0x70, 0x70, 0x70, 0x1f, 0x6f, 0x6f, 0x6f, 0x00, 0x50, 0x50, 0x50, 0x00, 0x4c, 0x4c, 0x4c, 0x00, 0x6f, 0x6f, 0x6f, 0x00, 0x9c, 0x9c, 0x9c, 0x00, 0x9a, 0x9a, 0x9a, 0x00, 0xb3, 0xb3, 0xb3, 0x00, 0x55, 0x55, 0x55, 0x63, 0x00, 0x00, 0x00, 0xe4, 0x63, 0x63, 0x63, 0x10, 0x72, 0x72, 0x72, 0x00, 0x6b, 0x6b, 0x6b, 0x00, 0x6b, 0x6b, 0x6b, 0x00, 
  0x67, 0x67, 0x67, 0x00, 0x67, 0x67, 0x67, 0x00, 0x67, 0x67, 0x67, 0x00, 0x73, 0x73, 0x73, 0x00, 0x44, 0x44, 0x44, 0x80, 0x00, 0x00, 0x00, 0xff, 0x3e, 0x3e, 0x3e, 0x94, 0xbc, 0xbc, 0xbc, 0x00, 0xae, 0xae, 0xae, 0x00, 0x75, 0x75, 0x75, 0x00, 0x50, 0x50, 0x50, 0x00, 0x4f, 0x4f, 0x4f, 0x00, 0x64, 0x64, 0x64, 0x00, 0x67, 0x67, 0x67, 0x00, 0x4c, 0x4c, 0x4c, 0x00, 0x5d, 0x5d, 0x5d, 0x00, 0x6a, 0x6a, 0x6a, 0x00, 0x5f, 0x5f, 0x5f, 0x00, 0x50, 0x50, 0x50, 0x00, 0x67, 0x67, 0x67, 0x00, 0xa8, 0xa8, 0xa8, 0x00, 0xbe, 0xbe, 0xbe, 0x00, 0x65, 0x65, 0x65, 0x54, 0x00, 0x00, 0x00, 0xff, 0x32, 0x32, 0x32, 0xbb, 0x88, 0x88, 0x88, 0x07, 0x7b, 0x7b, 0x7b, 0x00, 0x79, 0x79, 0x79, 0x00, 0x79, 0x79, 0x79, 0x00, 
  0x88, 0x88, 0x88, 0x00, 0x88, 0x88, 0x88, 0x00, 0x83, 0x83, 0x83, 0x00, 0x87, 0x87, 0x87, 0x00, 0x94, 0x94, 0x94, 0x00, 0x42, 0x42, 0x42, 0x6f, 0x00, 0x00, 0x00, 0xff, 0x31, 0x31, 0x31, 0xac, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3a, 0x3a, 0x3a, 0x3c, 0x21, 0x21, 0x21, 0xa4, 0x00, 0x00, 0x00, 0xdf, 0x00, 0x00, 0x00, 0xd3, 0x09, 0x09, 0x09, 0xb4, 0x29, 0x29, 0x29, 0x8c, 0x41, 0x41, 0x41, 0x48, 0x00, 0x00, 0x00, 0x00, 0x79, 0x79, 0x79, 0x00, 0x87, 0x87, 0x87, 0x00, 0x53, 0x53, 0x53, 0x6b, 0x00, 0x00, 0x00, 0xff, 0x32, 0x32, 0x32, 0xa7, 0xae, 0xae, 0xae, 0x00, 0x9a, 0x9a, 0x9a, 0x00, 0x90, 0x90, 0x90, 0x00, 0x95, 0x95, 0x95, 0x00, 0x99, 0x99, 0x99, 0x00, 
  0x75, 0x75, 0x75, 0x00, 0x7b, 0x7b, 0x7b, 0x00, 0x87, 0x87, 0x87, 0x00, 0x81, 0x81, 0x81, 0x00, 0x79, 0x79, 0x79, 0x00, 0x88, 0x88, 0x88, 0x00, 0x39, 0x39, 0x39, 0x7b, 0x05, 0x05, 0x05, 0xd7, 0x00, 0x00, 0x00, 0x00, 0x5e, 0x5e, 0x5e, 0x20, 0x16, 0x16, 0x16, 0xd4, 0x00, 0x00, 0x00, 0xff, 0x01, 0x01, 0x01, 0xfc, 0x01, 0x01, 0x01, 0xfc, 0x02, 0x02, 0x02, 0xfc, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x14, 0x14, 0x14, 0xcf, 0x54, 0x54, 0x54, 0x3f, 0x76, 0x76, 0x76, 0x00, 0x17, 0x17, 0x17, 0xa7, 0x2a, 0x2a, 0x2a, 0xaf, 0xa7, 0xa7, 0xa7, 0x00, 0x99, 0x99, 0x99, 0x00, 0x8a, 0x8a, 0x8a, 0x00, 0x97, 0x97, 0x97, 0x00, 0x90, 0x90, 0x90, 0x00, 0x80, 0x80, 0x80, 0x00, 
  0x63, 0x63, 0x63, 0x00, 0x4d, 0x4d, 0x4d, 0x00, 0x6d, 0x6d, 0x6d, 0x00, 0x82, 0x82, 0x82, 0x00, 0x75, 0x75, 0x75, 0x00, 0x6e, 0x6e, 0x6e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6b, 0x6b, 0x6b, 0x38, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xfc, 0x01, 0x01, 0x01, 0xfb, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x66, 0x66, 0x66, 0x63, 0x9d, 0x9d, 0x9d, 0x00, 0x81, 0x81, 0x81, 0x00, 0xa0, 0xa0, 0xa0, 0x00, 0x93, 0x93, 0x93, 0x00, 0x94, 0x94, 0x94, 0x00, 0x7e, 0x7e, 0x7e, 0x00, 0x56, 0x56, 0x56, 0x00, 0x5b, 0x5b, 0x5b, 0x00, 
  0x68, 0x68, 0x68, 0x00, 0x46, 0x46, 0x46, 0x00, 0x46, 0x46, 0x46, 0x00, 0x67, 0x67, 0x67, 0x00, 0x86, 0x86, 0x86, 0x00, 0x7b, 0x7b, 0x7b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x65, 0x65, 0x65, 0x08, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x67, 0x67, 0x67, 0x34, 0x95, 0x95, 0x95, 0x00, 0x7e, 0x7e, 0x7e, 0x00, 0x89, 0x89, 0x89, 0x00, 0x74, 0x74, 0x74, 0x00, 0x47, 0x47, 0x47, 0x00, 0x42, 0x42, 0x42, 0x00, 0x62, 0x62, 0x62, 0x00, 
  0x68, 0x68, 0x68, 0x00, 0x4c, 0x4c, 0x4c, 0x00, 0x4e, 0x4e, 0x4e, 0x00, 0x54, 0x54, 0x54, 0x00, 0x81, 0x81, 0x81, 0x00, 0x87, 0x87, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x16, 0x16, 0x16, 0xd7, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0a, 0x0a, 0x0a, 0xe8, 0x7d, 0x7d, 0x7d, 0x00, 0x78, 0x78, 0x78, 0x00, 0x73, 0x73, 0x73, 0x00, 0x55, 0x55, 0x55, 0x00, 0x4d, 0x4d, 0x4d, 0x00, 0x4e, 0x4e, 0x4e, 0x00, 0x62, 0x62, 0x62, 0x00, 
  0x68, 0x68, 0x68, 0x00, 0x4c, 0x4c, 0x4c, 0x00, 0x54, 0x54, 0x54, 0x00, 0x4d, 0x4d, 0x4d, 0x00, 0x63, 0x63, 0x63, 0x00, 0x8d, 0x8d, 0x8d, 0x00, 0x4a, 0x4a, 0x4a, 0x2b, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x48, 0x48, 0x48, 0x5c, 0x86, 0x86, 0x86, 0x00, 0x68, 0x68, 0x68, 0x00, 0x52, 0x52, 0x52, 0x00, 0x52, 0x52, 0x52, 0x00, 0x4d, 0x4d, 0x4d, 0x00, 0x62, 0x62, 0x62, 0x00, 
  0x71, 0x71, 0x71, 0x00, 0x56, 0x56, 0x56, 0x00, 0x5e, 0x5e, 0x5e, 0x00, 0x5f, 0x5f, 0x5f, 0x00, 0x64, 0x64, 0x64, 0x00, 0x6a, 0x6a, 0x6a, 0x00, 0x1d, 0x1d, 0x1d, 0xab, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x1b, 0x1b, 0x1b, 0xbb, 0x6f, 0x6f, 0x6f, 0x00, 0x61, 0x61, 0x61, 0x00, 0x5c, 0x5c, 0x5c, 0x00, 0x5f, 0x5f, 0x5f, 0x00, 0x57, 0x57, 0x57, 0x00, 0x6b, 0x6b, 0x6b, 0x00, 
  0x59, 0x59, 0x59, 0x33, 0x39, 0x39, 0x39, 0x47, 0x3f, 0x3f, 0x3f, 0x47, 0x40, 0x40, 0x40, 0x47, 0x4b, 0x4b, 0x4b, 0x33, 0x66, 0x66, 0x66, 0x00, 0x06, 0x06, 0x06, 0xe7, 0x01, 0x01, 0x01, 0xfc, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x04, 0x04, 0x04, 0xdf, 0x43, 0x43, 0x43, 0x00, 0x51, 0x51, 0x51, 0x28, 0x3f, 0x3f, 0x3f, 0x47, 0x40, 0x40, 0x40, 0x47, 0x3a, 0x3a, 0x3a, 0x47, 0x52, 0x52, 0x52, 0x37, 
  0x17, 0x17, 0x17, 0xd8, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x15, 0x15, 0x15, 0xd4, 0x78, 0x78, 0x78, 0x00, 0x05, 0x05, 0x05, 0xeb, 0x01, 0x01, 0x01, 0xfc, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x09, 0x09, 0x09, 0xe4, 0x50, 0x50, 0x50, 0x00, 0x26, 0x26, 0x26, 0xb0, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0f, 0x0f, 0x0f, 0xe7, 
  0x4b, 0x4b, 0x4b, 0x1f, 0x37, 0x37, 0x37, 0x33, 0x39, 0x39, 0x39, 0x30, 0x34, 0x34, 0x34, 0x33, 0x5e, 0x5e, 0x5e, 0x1f, 0x78, 0x78, 0x78, 0x00, 0x06, 0x06, 0x06, 0xe3, 0x01, 0x01, 0x01, 0xfc, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x03, 0x03, 0x03, 0xdc, 0x51, 0x51, 0x51, 0x00, 0x65, 0x65, 0x65, 0x18, 0x35, 0x35, 0x35, 0x33, 0x39, 0x39, 0x39, 0x33, 0x38, 0x38, 0x38, 0x33, 0x47, 0x47, 0x47, 0x23, 
  0x5a, 0x5a, 0x5a, 0x00, 0x49, 0x49, 0x49, 0x00, 0x4c, 0x4c, 0x4c, 0x00, 0x48, 0x48, 0x48, 0x00, 0x60, 0x60, 0x60, 0x00, 0x6e, 0x6e, 0x6e, 0x00, 0x21, 0x21, 0x21, 0x94, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x1e, 0x1e, 0x1e, 0xb4, 0x79, 0x79, 0x79, 0x00, 0x5f, 0x5f, 0x5f, 0x00, 0x47, 0x47, 0x47, 0x00, 0x4d, 0x4d, 0x4d, 0x00, 0x4a, 0x4a, 0x4a, 0x00, 0x57, 0x57, 0x57, 0x00, 
  0x55, 0x55, 0x55, 0x00, 0x42, 0x42, 0x42, 0x00, 0x46, 0x46, 0x46, 0x00, 0x3f, 0x3f, 0x3f, 0x00, 0x5d, 0x5d, 0x5d, 0x00, 0x81, 0x81, 0x81, 0x00, 0x5f, 0x5f, 0x5f, 0x17, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x4a, 0x4a, 0x4a, 0x4f, 0x85, 0x85, 0x85, 0x00, 0x62, 0x62, 0x62, 0x00, 0x40, 0x40, 0x40, 0x00, 0x45, 0x45, 0x45, 0x00, 0x43, 0x43, 0x43, 0x00, 0x51, 0x51, 0x51, 0x00, 
  0x55, 0x55, 0x55, 0x00, 0x43, 0x43, 0x43, 0x00, 0x3d, 0x3d, 0x3d, 0x00, 0x43, 0x43, 0x43, 0x00, 0x85, 0x85, 0x85, 0x00, 0x92, 0x92, 0x92, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x22, 0x22, 0xbc, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x19, 0x19, 0x19, 0xd8, 0x96, 0x96, 0x96, 0x00, 0x89, 0x89, 0x89, 0x00, 0x8c, 0x8c, 0x8c, 0x00, 0x53, 0x53, 0x53, 0x00, 0x38, 0x38, 0x38, 0x00, 0x44, 0x44, 0x44, 0x00, 0x51, 0x51, 0x51, 0x00, 
  0x55, 0x55, 0x55, 0x00, 0x36, 0x36, 0x36, 0x00, 0x33, 0x33, 0x33, 0x00, 0x66, 0x66, 0x66, 0x00, 0x88, 0x88, 0x88, 0x00, 0x77, 0x77, 0x77, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x03, 0x03, 0xfb, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x69, 0x69, 0x69, 0x23, 0x93, 0x93, 0x93, 0x00, 0x7c, 0x7c, 0x7c, 0x00, 0x8b, 0x8b, 0x8b, 0x00, 0x70, 0x70, 0x70, 0x00, 0x39, 0x39, 0x39, 0x00, 0x37, 0x37, 0x37, 0x00, 0x4f, 0x4f, 0x4f, 0x00, 
  0x4b, 0x4b, 0x4b, 0x00, 0x4b, 0x4b, 0x4b, 0x00, 0x78, 0x78, 0x78, 0x00, 0x99, 0x99, 0x99, 0x00, 0x71, 0x71, 0x71, 0x00, 0x5b, 0x5b, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x65, 0x65, 0x65, 0x27, 0x03, 0x03, 0x03, 0xfb, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xfc, 0x5b, 0x5b, 0x5b, 0x4c, 0x80, 0x80, 0x80, 0x00, 0x67, 0x67, 0x67, 0x00, 0x79, 0x79, 0x79, 0x00, 0x72, 0x72, 0x72, 0x00, 0x86, 0x86, 0x86, 0x00, 0x79, 0x79, 0x79, 0x00, 0x52, 0x52, 0x52, 0x00, 0x4c, 0x4c, 0x4c, 0x00, 
  0x7b, 0x7b, 0x7b, 0x00, 0x90, 0x90, 0x90, 0x00, 0x9b, 0x9b, 0x9b, 0x00, 0x93, 0x93, 0x93, 0x00, 0x72, 0x72, 0x72, 0x00, 0x74, 0x74, 0x74, 0x00, 0x35, 0x35, 0x35, 0x7b, 0x08, 0x08, 0x08, 0xd4, 0x00, 0x00, 0x00, 0x00, 0x47, 0x47, 0x47, 0x0f, 0x1c, 0x1c, 0x1c, 0xbb, 0x00, 0x00, 0x00, 0xff, 0x01, 0x01, 0x01, 0xff, 0x01, 0x01, 0x01, 0xfc, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x20, 0x20, 0x20, 0xb7, 0x54, 0x54, 0x54, 0x28, 0x62, 0x62, 0x62, 0x00, 0x15, 0x15, 0x15, 0xa7, 0x21, 0x21, 0x21, 0xb0, 0x85, 0x85, 0x85, 0x00, 0x7f, 0x7f, 0x7f, 0x00, 0x7e, 0x7e, 0x7e, 0x00, 0x89, 0x89, 0x89, 0x00, 0x88, 0x88, 0x88, 0x00, 0x76, 0x76, 0x76, 0x00, 
  0x9d, 0x9d, 0x9d, 0x00, 0x97, 0x97, 0x97, 0x00, 0x94, 0x94, 0x94, 0x00, 0x99, 0x99, 0x99, 0x00, 0x8c, 0x8c, 0x8c, 0x00, 0x3a, 0x3a, 0x3a, 0x6f, 0x00, 0x00, 0x00, 0xff, 0x28, 0x28, 0x28, 0xa8, 0x84, 0x84, 0x84, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x3f, 0x3f, 0x23, 0x24, 0x24, 0x24, 0x87, 0x00, 0x00, 0x00, 0xc0, 0x12, 0x12, 0x12, 0xbc, 0x09, 0x09, 0x09, 0x9f, 0x29, 0x29, 0x29, 0x77, 0x4c, 0x4c, 0x4c, 0x2f, 0x00, 0x00, 0x00, 0x00, 0x73, 0x73, 0x73, 0x00, 0x84, 0x84, 0x84, 0x00, 0x57, 0x57, 0x57, 0x6b, 0x00, 0x00, 0x00, 0xff, 0x25, 0x25, 0x25, 0xac, 0x90, 0x90, 0x90, 0x00, 0x8c, 0x8c, 0x8c, 0x00, 0x83, 0x83, 0x83, 0x00, 0x86, 0x86, 0x86, 0x00, 0x8b, 0x8b, 0x8b, 0x00, 
  0x72, 0x72, 0x72, 0x00, 0x72, 0x72, 0x72, 0x00, 0x73, 0x73, 0x73, 0x00, 0x82, 0x82, 0x82, 0x00, 0x41, 0x41, 0x41, 0x80, 0x00, 0x00, 0x00, 0xff, 0x24, 0x24, 0x24, 0x8f, 0x77, 0x77, 0x77, 0x00, 0x91, 0x91, 0x91, 0x00, 0x74, 0x74, 0x74, 0x00, 0x55, 0x55, 0x55, 0x00, 0x4a, 0x4a, 0x4a, 0x00, 0x66, 0x66, 0x66, 0x00, 0x60, 0x60, 0x60, 0x00, 0x6a, 0x6a, 0x6a, 0x00, 0x5e, 0x5e, 0x5e, 0x00, 0x6a, 0x6a, 0x6a, 0x00, 0x62, 0x62, 0x62, 0x00, 0x54, 0x54, 0x54, 0x00, 0x66, 0x66, 0x66, 0x00, 0xa6, 0xa6, 0xa6, 0x00, 0xc4, 0xc4, 0xc4, 0x00, 0x66, 0x66, 0x66, 0x53, 0x00, 0x00, 0x00, 0xff, 0x26, 0x26, 0x26, 0xc0, 0x7a, 0x7a, 0x7a, 0x07, 0x6f, 0x6f, 0x6f, 0x00, 0x6d, 0x6d, 0x6d, 0x00, 0x6d, 0x6d, 0x6d, 0x00, 
  0x69, 0x69, 0x69, 0x00, 0x69, 0x69, 0x69, 0x00, 0x6a, 0x6a, 0x6a, 0x00, 0x74, 0x74, 0x74, 0x00, 0x16, 0x16, 0x16, 0xcc, 0x20, 0x20, 0x20, 0x9c, 0x63, 0x63, 0x63, 0x00, 0x65, 0x65, 0x65, 0x00, 0x8b, 0x8b, 0x8b, 0x00, 0x77, 0x77, 0x77, 0x00, 0x53, 0x53, 0x53, 0x00, 0x4b, 0x4b, 0x4b, 0x00, 0x69, 0x69, 0x69, 0x00, 0x68, 0x68, 0x68, 0x23, 0x19, 0x19, 0x19, 0xc4, 0x6a, 0x6a, 0x6a, 0x24, 0x71, 0x71, 0x71, 0x00, 0x50, 0x50, 0x50, 0x00, 0x51, 0x51, 0x51, 0x00, 0x6c, 0x6c, 0x6c, 0x00, 0x98, 0x98, 0x98, 0x00, 0x9d, 0x9d, 0x9d, 0x00, 0xb9, 0xb9, 0xb9, 0x00, 0x56, 0x56, 0x56, 0x63, 0x03, 0x03, 0x03, 0xe4, 0x67, 0x67, 0x67, 0x10, 0x75, 0x75, 0x75, 0x00, 0x6e, 0x6e, 0x6e, 0x00, 0x6e, 0x6e, 0x6e, 0x00, 
  0xa8, 0xa8, 0xa8, 0x00, 0xa8, 0xa8, 0xa8, 0x00, 0xaa, 0xaa, 0xaa, 0x00, 0xae, 0xae, 0xae, 0x00, 0x67, 0x67, 0x67, 0x00, 0x50, 0x50, 0x50, 0x00, 0x52, 0x52, 0x52, 0x00, 0x62, 0x62, 0x62, 0x00, 0x8b, 0x8b, 0x8b, 0x00, 0x60, 0x60, 0x60, 0x00, 0x49, 0x49, 0x49, 0x00, 0x4f, 0x4f, 0x4f, 0x00, 0x54, 0x54, 0x54, 0x00, 0x36, 0x36, 0x36, 0x3c, 0x00, 0x00, 0x00, 0xff, 0x2e, 0x2e, 0x2e, 0x3f, 0x48, 0x48, 0x48, 0x00, 0x44, 0x44, 0x44, 0x00, 0x43, 0x43, 0x43, 0x00, 0x57, 0x57, 0x57, 0x00, 0x9a, 0x9a, 0x9a, 0x00, 0x9e, 0x9e, 0x9e, 0x00, 0xa1, 0xa1, 0xa1, 0x00, 0x87, 0x87, 0x87, 0x00, 0x4d, 0x4d, 0x4d, 0x00, 0x99, 0x99, 0x99, 0x00, 0xa3, 0xa3, 0xa3, 0x00, 0x9e, 0x9e, 0x9e, 0x00, 0x9e, 0x9e, 0x9e, 0x00, 
  0x9c, 0x9c, 0x9c, 0x00, 0x9c, 0x9c, 0x9c, 0x00, 0x9e, 0x9e, 0x9e, 0x00, 0xa2, 0xa2, 0xa2, 0x00, 0x61, 0x61, 0x61, 0x00, 0x49, 0x49, 0x49, 0x00, 0x50, 0x50, 0x50, 0x00, 0x6c, 0x6c, 0x6c, 0x00, 0x74, 0x74, 0x74, 0x00, 0x44, 0x44, 0x44, 0x00, 0x4b, 0x4b, 0x4b, 0x00, 0x4e, 0x4e, 0x4e, 0x00, 0x56, 0x56, 0x56, 0x00, 0x40, 0x40, 0x40, 0x34, 0x00, 0x00, 0x00, 0xff, 0x39, 0x39, 0x39, 0x37, 0x4c, 0x4c, 0x4c, 0x00, 0x46, 0x46, 0x46, 0x00, 0x42, 0x42, 0x42, 0x00, 0x33, 0x33, 0x33, 0x00, 0x79, 0x79, 0x79, 0x00, 0xaa, 0xaa, 0xaa, 0x00, 0x9e, 0x9e, 0x9e, 0x00, 0x79, 0x79, 0x79, 0x00, 0x47, 0x47, 0x47, 0x00, 0x8f, 0x8f, 0x8f, 0x00, 0x98, 0x98, 0x98, 0x00, 0x93, 0x93, 0x93, 0x00, 0x93, 0x93, 0x93, 0x00, 
  0x9c, 0x9c, 0x9c, 0x00, 0x9c, 0x9c, 0x9c, 0x00, 0x9e, 0x9e, 0x9e, 0x00, 0xa2, 0xa2, 0xa2, 0x00, 0x5f, 0x5f, 0x5f, 0x00, 0x48, 0x48, 0x48, 0x00, 0x55, 0x55, 0x55, 0x00, 0x60, 0x60, 0x60, 0x00, 0x4a, 0x4a, 0x4a, 0x00, 0x3b, 0x3b, 0x3b, 0x00, 0x43, 0x43, 0x43, 0x00, 0x42, 0x42, 0x42, 0x00, 0x4b, 0x4b, 0x4b, 0x00, 0x33, 0x33, 0x33, 0x3c, 0x00, 0x00, 0x00, 0xff, 0x31, 0x31, 0x31, 0x3c, 0x46, 0x46, 0x46, 0x00, 0x3f, 0x3f, 0x3f, 0x00, 0x3f, 0x3f, 0x3f, 0x00, 0x33, 0x33, 0x33, 0x00, 0x44, 0x44, 0x44, 0x00, 0x97, 0x97, 0x97, 0x00, 0xa6, 0xa6, 0xa6, 0x00, 0x79, 0x79, 0x79, 0x00, 0x46, 0x46, 0x46, 0x00, 0x8f, 0x8f, 0x8f, 0x00, 0x98, 0x98, 0x98, 0x00, 0x93, 0x93, 0x93, 0x00, 0x93, 0x93, 0x93, 0x00, 
  0x9c, 0x9c, 0x9c, 0x00, 0x9c, 0x9c, 0x9c, 0x00, 0x9e, 0x9e, 0x9e, 0x00, 0xa2, 0xa2, 0xa2, 0x00, 0x5f, 0x5f, 0x5f, 0x00, 0x48, 0x48, 0x48, 0x00, 0x53, 0x53, 0x53, 0x00, 0x61, 0x61, 0x61, 0x00, 0x6f, 0x6f, 0x6f, 0x00, 0x6e, 0x6e, 0x6e, 0x00, 0x6d, 0x6d, 0x6d, 0x00, 0x6d, 0x6d, 0x6d, 0x00, 0x75, 0x75, 0x75, 0x00, 0x61, 0x61, 0x61, 0x27, 0x13, 0x13, 0x13, 0xd4, 0x52, 0x52, 0x52, 0x28, 0x68, 0x68, 0x68, 0x00, 0x61, 0x61, 0x61, 0x00, 0x61, 0x61, 0x61, 0x00, 0x61, 0x61, 0x61, 0x00, 0x55, 0x55, 0x55, 0x00, 0x82, 0x82, 0x82, 0x00, 0xaa, 0xaa, 0xaa, 0x00, 0x79, 0x79, 0x79, 0x00, 0x46, 0x46, 0x46, 0x00, 0x8f, 0x8f, 0x8f, 0x00, 0x98, 0x98, 0x98, 0x00, 0x93, 0x93, 0x93, 0x00, 0x93, 0x93, 0x93, 0x00, 
#endif
};

const lv_img_dsc_t lv_demo_printer_icon_bright = {
  .header.always_zero = 0,
  .header.w = 29,
  .header.h = 29,
  .data_size = 841 * LV_IMG_PX_SIZE_ALPHA_BYTE,
  .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
  .data = lv_demo_printer_icon_bright_map,
};

#endif /*LV_USE_DEMO_PRINTER*/
