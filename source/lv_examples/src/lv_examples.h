/**
 * @file lv_examples.h
 * This file exists only to be compatible with <PERSON><PERSON><PERSON><PERSON>'s library structure
 */

#ifndef LV_EXAMPLES_SRC_H
#define LV_EXAMPLES_SRC_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

#include "../lv_examples.h"

/*********************
 *      DEFINES
 *********************/
 
/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/


/**********************
 *      MACROS
 **********************/


#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /*LV_EXAMPLES_SRC_H*/
