#ifndef FILEOPERATION_H
#define FILEOPERATION_H

char* space_change(char *src);
void remove_file(const char *file_path);
bool IsFileExist(const char *file_path);
bool CopyFile(const char* src, const char* des);
bool MoveFile(const char* src, const char* des);
unsigned int GetFileLength(const char *file_path);                         
void GetFlashPartitionSize(char *mountDir,unsigned int *total,unsigned int *free);    //获取磁盘总大小及剩余空间(KB)

#endif