/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2022-06-23 14:09:55 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-06-23 14:09:55 
 */

#ifndef _I2C_INTERFACE_H
#define _I2C_INTERFACE_H

#include "stdbool.h"

#define I2C0_FILE_NAME "/dev/i2c-0"
#define I2C1_FILE_NAME "/dev/i2c-1"

bool i2c1_init();
bool i2c1_deinit();
bool i2c1_write(unsigned char slave_addr, unsigned char reg_addr, unsigned char value);
bool i2c1_read(unsigned char slave_addr, unsigned char reg_addr, unsigned char *value);

static bool i2c_write(int fd,unsigned char slave_addr, unsigned char reg_addr, unsigned char value);
static bool i2c_read(int fd, unsigned char slave_addr, unsigned char reg_addr, unsigned char *value);

#endif