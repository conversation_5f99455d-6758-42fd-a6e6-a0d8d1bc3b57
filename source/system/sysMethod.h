/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-04 16:09:55 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-09-06 18:00:45
 */

#ifndef _SYSMETHOD_H_
#define _SYSMETHOD_H_

#include <stdbool.h>
#include <sys/syscall.h>

#ifndef USE_PC_SIMULATOR
#define HTTP_WEB_DIR    "/customer/App/Web"      //WEB工作目录
#else
#define HTTP_WEB_DIR    "/home/<USER>/app/Web"      //WEB工作目录
#endif

typedef struct
{
	int	sec;		/* Seconds: 0-59 (<PERSON>&<PERSON> says 0-61?) */
	int	min;		/* Minutes: 0-59 */
	int	hour;	/* Hours since midnight: 0-23 */
	int	day;	/* Day of the month: 1-31 */
	int	mon;		/* Months *since* january: 0-11 */
	int	year;	/* Years since 1900 */
	int	weekday;	/* Days since Sunday (0-6) */
}_stMyTime;

extern pthread_mutex_t mutex_sysTime;

extern _stMyTime st_CurrentTime;

#define rmmod_module(mod, flags) syscall(__NR_delete_module, mod, flags)

bool IsFileExist(const char *file_path);
int pox_system(const char *cmd_line);
void System_Reboot();
void System_Reboot_DelayMs(int delayMs);
void GetSystemTime_thread();

//MAC字节数组转换为标准字符串
void MacArrayToString(char* szMac, const unsigned char arrayMac[]);
void MacStringToArray(unsigned char arrayMac[],const char *szMac);


void Del_Web_Directory_All_files();

char* Check_FileName(char *path,int utf8Bytes);
char* Get_Source_Name_ById(unsigned char id);

void split_ip(const char* ip_str, unsigned char* t_ip_parts);
#endif