#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <ctype.h>
#include <dirent.h>
#include <sys/sysinfo.h>
#include <sys/statfs.h>
#include "sysMethod.h"

char* space_change(const char *src)
{
	int i=0,j=0;
	int com_len=0;
	int musicLen=strlen(src);
	static char tmp_buf[256]={0};
	memset(tmp_buf,0,sizeof(tmp_buf));
	for(i=0; i< musicLen;i++)
	{
		tmp_buf[com_len+j]=src[i];
		if(src[i]==' ')   //判断空格
		{
			tmp_buf[com_len+j]='\\';
			tmp_buf[com_len+j+1]=' ';
			j =j+1;
		}
		else if((src[i]=='(')||(src[i]==')')||(src[i]=='[')||(src[i]==']')||(src[i]=='{')||(src[i]=='}'))   //判断“(”
		{
			tmp_buf[com_len+j]='\\';
			tmp_buf[com_len+j+1]=src[i];
			j =j+1;
		}
		else if(ispunct(src[i]))  //测试字符是否为特殊符号
		{
			tmp_buf[com_len+j]='\\';
			tmp_buf[com_len+j+1]=src[i];
			j =j+1;
		}
		j++;
	}
	tmp_buf[com_len+j]='\0';

	#if _DEBUG_MP_
	printf("space_change:tmp_buf:%s\n", tmp_buf);
	#endif

	return tmp_buf;
}


/*********************************************************************
 * @fn      remove_file
 *
 * @brief   删除指定文件
 *
 * @param   *file_path  - 需删除文件的绝对路径
 *
 * @return  void
 */
void remove_file(const char *file_path)
{
	unsigned char cmd_buf[512];

	memset(cmd_buf, 0x00, sizeof(cmd_buf));
	sprintf(cmd_buf, "rm -f %s", space_change(file_path));
	pox_system(cmd_buf);
}


/*********************************************************************
 * @fn      IsFileExist
 *
 * @brief   判断文件是否存在
 *
 * @param   *file_path  - 需删除文件的绝对路径
 *
 * @return  成功-true 失败false
 */
bool IsFileExist(const char *file_path)
{
	/*
	mode	Description
	F_OK	测试文件是否存在
	R_OK	测试文件是否有读权限
	W_OK	测试文件是否有写权限
	X_OK	测试文件是否有执行权限
	*/
	if( access(file_path, F_OK) == 0 )
		return true;
	return false;
}


/*********************************************************************
 * @fn      CopyFile
 *
 * @brief   拷贝文件
 *
 * @param   *src  - 原路径
 * 			*des  - 目的路径
 *
 * @return  成功-0 失败-1
 */
bool CopyFile(const char* src, const char* des)
{
	char srcPath[256]={0};
	char desPath[256]={0};
	sprintf(srcPath,"%s",space_change(src));
	sprintf(desPath,"%s",space_change(des));

	char cmd[512]={0};
	sprintf(cmd,"cp -f %s %s",srcPath,desPath);

	pox_system(cmd);

	return true;
}

/*********************************************************************
 * @fn      MoveFile
 *
 * @brief   移动文件
 *
 * @param   *src  - 原路径
 * 			*des  - 目的路径
 *
 * @return  成功-0 失败-1
 */
bool MoveFile(const char* src, const char* des)
{
	char srcPath[256]={0};
	char desPath[256]={0};
	sprintf(srcPath,"%s",space_change(src));
	sprintf(desPath,"%s",space_change(des));

	char cmd[512]={0};
	sprintf(cmd,"mv -f %s %s",srcPath,desPath);
	
	pox_system(cmd);

	return true;
}


/*********************************************************************
 * @fn      GetFileLength
 *
 * @brief   获取文件存储大小
 *
 * @param   *file_path  - 文件绝对路径
 * 			
 *
 * @return  unsigned int文件大小
 */
unsigned int GetFileLength(const char *file_path)
{
	unsigned int filesize = -1;
	struct stat statbuff;
	if(stat(file_path, &statbuff) < 0){
	return filesize;
	}else{
	filesize = statbuff.st_size;
	}
	return filesize;
}


/*********************************************************************
 * @fn      GetFlashPartitionSize
 *
 * @brief   获取磁盘总大小及剩余空间
 *
 * @param   char *mountDir  - 分区挂载路径
 * 			unsigned int *total - 总大小	
 * 			unsigned int *free - 剩余大小	
 *
 * @return  unsigned int文件大小
 */
void GetFlashPartitionSize(char *mountDir,unsigned int *total,unsigned int *free)
{
    struct statfs diskInfo;
    statfs(mountDir,&diskInfo);
    unsigned long blocksize  = diskInfo.f_bsize;
    unsigned long totalDisk = diskInfo.f_blocks*blocksize;
    unsigned long freeDisk = diskInfo.f_bfree*blocksize ;
 
    if(total!=NULL)
        *total = (unsigned int)(totalDisk);
    if(free!=NULL)
        *free = (unsigned int)(freeDisk);

	printf("GetFlashPartitionSize:mount=%s,total=%d,free=%d\n",mountDir,*total,*free);
}