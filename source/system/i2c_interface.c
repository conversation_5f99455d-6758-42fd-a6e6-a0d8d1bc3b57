#include <stdio.h>  
#include <linux/types.h>  
#include <stdlib.h>  
#include <fcntl.h>  
#include <unistd.h>  
#include <sys/types.h>  
#include <sys/ioctl.h>  
#include <errno.h>  
#include <assert.h>  
#include <string.h>  
#include <linux/i2c.h>  
#include <linux/i2c-dev.h> 
#include "i2c_interface.h"

static int i2c1_fd=-1;

bool i2c1_init()
{
    if(i2c1_fd <=0)
    {
        i2c1_fd = open(I2C1_FILE_NAME, O_RDWR);      
        if (i2c1_fd <=0)
        {
            printf("i2c1_init faild: %s\n", I2C1_FILE_NAME);  
            return false;  
        }
    }
    printf("i2c1_init:%s succeed!\n",I2C1_FILE_NAME);
    return true;
}

bool i2c1_deinit()
{
    if(i2c1_fd >0)
    {   
        close(i2c1_fd);
        i2c1_fd=-1;
    }
    printf("i2c1_deinit:%s succeed!\n",I2C1_FILE_NAME);
    return true;
}

bool i2c1_write(unsigned char slave_addr, unsigned char reg_addr, unsigned char value)
{
     if (i2c1_fd <=0)
        return false;
    return i2c_write(i2c1_fd, slave_addr, reg_addr, value);
}

bool i2c1_read(unsigned char slave_addr, unsigned char reg_addr, unsigned char *value)
{
     if (i2c1_fd <=0)
        return false;
    return i2c_read(i2c1_fd, slave_addr, reg_addr, value);
}


static bool i2c_write(int fd,unsigned char slave_addr, unsigned char reg_addr, unsigned char value)
{
    unsigned char outbuf[2];
    struct i2c_rdwr_ioctl_data packets;
    struct i2c_msg messages[1];

    messages[0].addr  = slave_addr;
    messages[0].flags = 0;
    messages[0].len   = sizeof(outbuf);
    messages[0].buf   = outbuf;

    /* The first byte indicates which register we‘ll write */
    outbuf[0] = reg_addr;

    /* 
     * The second byte indicates the value to write.  Note that for many
     * devices, we can write multiple, sequential registers at once by
     * simply making outbuf bigger.
     */
    outbuf[1] = value;

    /* Transfer the i2c packets to the kernel and verify it worked */
    packets.msgs  = messages;
    packets.nmsgs = 1;
    if(ioctl(fd, I2C_RDWR, &packets) < 0) 
    {
        perror("Unable to send data");
        return false;
    }
	
	//printf("write data 0x%02x to slave_addr 0x%02x reg_addr 0x%02x\n", value, slave_addr, reg_addr);

    return true;
}

static bool i2c_read(int fd, unsigned char slave_addr, unsigned char reg_addr, unsigned char *value)
{
    unsigned char inbuf, outbuf;
    struct i2c_rdwr_ioctl_data packets;
    struct i2c_msg messages[2];

    /*
     * In order to read a register, we first do a "dummy write" by writing
     * 0 bytes to the register we want to read from.  This is similar to
     * the packet in set_i2c_register, except it‘s 1 byte rather than 2.
     */
    outbuf = reg_addr;
    messages[0].addr  = slave_addr;
    messages[0].flags = 0;
    messages[0].len   = sizeof(outbuf);
    messages[0].buf   = &outbuf;

    /* The data will get returned in this structure */
    messages[1].addr  = slave_addr;
    messages[1].flags = I2C_M_RD/* | I2C_M_NOSTART*/;
    messages[1].len   = sizeof(inbuf);
    messages[1].buf   = &inbuf;

    /* Send the request to the kernel and get the result back */
    packets.msgs      = messages;
    packets.nmsgs     = 2;
    if(ioctl(fd, I2C_RDWR, &packets) < 0) 
    {
        perror("Unable to send data");
        return false;
    }
    *value = inbuf;
	
	//printf("read data 0x%02x from slave_addr 0x%02x reg_addr 0x%02x\n", *value, slave_addr, reg_addr);

    return true;
}