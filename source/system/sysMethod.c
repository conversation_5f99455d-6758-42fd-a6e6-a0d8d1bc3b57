/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-04 16:13:50 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-09-06 18:01:49
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>

#include <signal.h>
#include <time.h>
#include <pthread.h>
#include "sysconf.h"
#include "mi_gpio.h"
#include "sysMethod.h"
#include "../hardware/tm1640.h"

pthread_mutex_t mutex_sysTime=PTHREAD_MUTEX_INITIALIZER;	//系统时间互斥锁

_stMyTime st_CurrentTime;


typedef void (*sighandler_t)(int);
/*********************************************************************
 * @fn      pox_system
 *
 * @brief   重新封装system函数，以免出现错误
 *
 * @param   *cmd_line - 操作对象
 *
 * @return  ret
 */
int pox_system(const char *cmd_line)
{
	int ret = 0;

	sighandler_t old_handler;
	old_handler = signal(SIGCHLD, SIG_DFL);
	ret = system(cmd_line);
	signal(SIGCHLD, old_handler);

	if((ret == 127) || (ret < 0))
	{
		return -1;
	}
	else
	{
		return 0;
	}
}

void System_Reboot()
{
	printf("System_Reboot...\n");
	#ifndef USE_PC_SIMULATOR
	Enable_Amp_Output(0);       //关闭功放
	Enable_Signal_Output(0);	//关闭信号
	if(ENABLE_DISP_UART_MODULE)
	{
		Disp_Send_control_reboot(DISP_REBOOT_MODE);	//通知显示模块重启
	}
	#else
	return;
	#endif
	pox_system("reboot");
}


// 修正后的rebootDelay函数
static void* rebootDelay(void *delayMs) {
    int delayTime = (int)(intptr_t)delayMs; // 强制转换void*为intptr_t再转为int
    usleep(delayTime*1000);
    System_Reboot();
    return NULL; // 线程函数返回NULL
}

//延迟重启
void System_Reboot_DelayMs(int delayMs) {
	#if USE_PC_SIMULATOR
	return;
	#endif
    pthread_t pid;
    pthread_attr_t Pthread_Attr;
    pthread_attr_init(&Pthread_Attr);
    pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED); // 设置线程为分离状态
    pthread_create(&pid, &Pthread_Attr, rebootDelay, (void*)(intptr_t)delayMs);
    pthread_attr_destroy(&Pthread_Attr);
}


void Del_Web_Directory_All_files()
{
	char rm_web_path[256]={0};
	sprintf(rm_web_path,"rm %s/* -rf",HTTP_WEB_DIR);
    pox_system(rm_web_path);
	printf("Del_Web_Directory_All_files:%s...\n",rm_web_path);
}



bool uartNeedUpdateTime=true;
/**********************************************************************************************************
 *  @File Function:   获取本地时间
 *  @Describe:
 *  @Vision
 */
struct tm* GetSystemTime(time_t *timep)
{
	static _stMyTime st_PreTime={0,0,0,0,0,0,0};
	int i;
    struct tm *p;
    time(timep);
    p=localtime(timep);

    st_CurrentTime.hour=p->tm_hour;
    st_CurrentTime.min=p->tm_min;
    st_CurrentTime.sec=p->tm_sec;
    st_CurrentTime.mon=p->tm_mon+1;
    st_CurrentTime.day=p->tm_mday;
    st_CurrentTime.year=p->tm_year+1900;
    st_CurrentTime.weekday=p->tm_wday;

	#if SUPPORT_TM1640_DISPLAY
	static bool  isFirstGetTime=false;
	if(!isFirstGetTime)
	{
		isFirstGetTime=true;
		unsigned char timeArray[4]={st_CurrentTime.hour/10,st_CurrentTime.hour%10,st_CurrentTime.min/10,st_CurrentTime.min%10};
		TM1640_Display_Time(timeArray,0xff,4);
		TM1640_Display_switch(1);
	}
	#endif

	bool bUpdate = (st_CurrentTime.min != st_PreTime.min);
	#if IS_DEVICE_GPS_SYNCHRONIZER
	bUpdate = true;
	#endif

    if( bUpdate || uartNeedUpdateTime)
	{
		unsigned char dateBuf[20]={0};
		unsigned char timeBuf[20]={0};

		bool isDateCorrect=true;
		if(st_CurrentTime.year<2024)
		{
			isDateCorrect=false;
			st_CurrentTime.year=2024;
		}
		
		sprintf(dateBuf,"%04d-%02d-%02d",st_CurrentTime.year,st_CurrentTime.mon,st_CurrentTime.day);
		if(strcmp(dateBuf,sys_date_buf))
		{
			sprintf(sys_date_buf,"%s",dateBuf);
		}
		sprintf(timeBuf,"%02d:%02d:%02d",st_CurrentTime.hour,st_CurrentTime.min,st_CurrentTime.sec);
		if(strcmp(timeBuf,sys_time_buf))
		{
			sprintf(sys_time_buf,"%s",timeBuf);
		}

		if(ENABLE_DISP_UART_MODULE)
		{
			Disp_Send_time_info();
		}
		//更新界面
		UI_UpdateSystemTime(0);


		if(is_Enable_UART_LED_PLAYER)
		{
			if( st_CurrentTime.day != st_PreTime.day && isDateCorrect)
			{
				uartNeedUpdateTime=true;
			}

			if(uartNeedUpdateTime)
			{
				Uart_led_player_set_parm();
			}
		}
		else if(SUPPORT_TM1640_DISPLAY)
		{
			unsigned char timeArray[4]={st_CurrentTime.hour/10,st_CurrentTime.hour%10,st_CurrentTime.min/10,st_CurrentTime.min%10};
			TM1640_Display_Time(timeArray,0xff,4);
		}

		#if IS_DEVICE_GPS_SYNCHRONIZER
		#if (AIPU_VERSION && SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_2P1_320X240)
		aipu_main_win_gps_time_update(0);
		#endif
		#endif

		if(uartNeedUpdateTime)
		{
			uartNeedUpdateTime=false;
		}


		st_PreTime = st_CurrentTime;
	}
}


void *GetSystemTime_ntp()
{
    while(1)
    {
		usleep(900000);
		time_t Time;
		pthread_mutex_lock(&mutex_sysTime);

		GetSystemTime(&Time);//获取时间

		pthread_mutex_unlock(&mutex_sysTime);
	}
}

void GetSystemTime_thread()
{
    pthread_t pid;
    pthread_attr_t Pthread_Attr;
    pthread_attr_init(&Pthread_Attr);
    pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pid, &Pthread_Attr, (void *)GetSystemTime_ntp, NULL);
    pthread_attr_destroy(&Pthread_Attr);
}
/************************************************************************************************************/


//MAC字节数组转换为标准字符串
void MacArrayToString(char* szMac, const unsigned char arrayMac[])
{
    sprintf(szMac, "%02x:%02x:%02x:%02x:%02x:%02x",arrayMac[0],arrayMac[1],arrayMac[2],arrayMac[3],arrayMac[4],arrayMac[5]);
}

//MAC标准字符串转换为字节数组
void MacStringToArray(unsigned char arrayMac[],const char *szMac)
{
	sscanf(szMac, "%x:%x:%x:%x:%x:%x", &arrayMac[0], &arrayMac[1], &arrayMac[2], &arrayMac[3], &arrayMac[4], &arrayMac[5]);
}



char* remove_extension(const char *filename) {
    // 静态变量，用来存储去除扩展名后的文件名
    static char new_filename[256];  // 假设文件名不会超过255个字符

    // 复制传入的文件名到新的静态变量
    strncpy(new_filename, filename, sizeof(new_filename) - 1);
    new_filename[sizeof(new_filename) - 1] = '\0';  // 确保字符串以 '\0' 结束

    // 查找最后一个 '.' 字符
    char *dot = strrchr(new_filename, '.');
    
    // 如果找到了扩展名（即 '.' 存在且不是文件名的第一个字符）
    if (dot != NULL) {
        *dot = '\0';  // 将最后一个 '.' 替换为字符串结束符 '\0'
    }

    return new_filename;
}

int StrLenU_UTF8(const char* string)
{
     int len = 0 ;
     const char* p = string;
     while(*p++ != '\0')
     {
         if(*p > 0x80 || *p < 0)
         {
			 len+=2;
            p+=2;
         }
         len++;
     }
     return len;
}

/*遍历字符串，非ASCII字符读取2个字节，ASCII读取一个字节，返回指定位置的字符串指针，默认从1开始*/
char* StrSetPosU_UTF8(const char* string,int pos)
{
     char* result;
     result = (char *)string;
     while (result != NULL && *result != '\0' && pos > 1)
     {
         if(*result > 0x80 || *result < 0)
         {
             result+=2;
         }
         result++;
         pos--;
     }
     if(pos!=0)
         return result;
     return '\0';
}
char* StringSubU_UTF8(const char* string,int start,int number)
{
     int len = StrLenU_UTF8(string) ;
     if(start>len)
     {
         printf("Start %d is too big than string length %d!\n",start,len);
         return "";
     }
     int bufsize = 0;
     int num = 1;
     const char* p = string;
     const char* start_char =string;
     /*重置指针，获取指定开始位置*/
     p = StrSetPosU_UTF8(string,start);
     start_char = p;
     /*当取值为负值或字符串长度和取长度相等时，则取全部值*/
     if(number < 0 || number>=len)
     {
         while(*p != '\0')
         {
            p++;
            bufsize++;
         }
     }
     else
     {
         while(1)
         {
            /*当指针移到末尾，而且还没有获取指定数的字符时，说明此时指定字符数过多，将会取剩下的所有值*/
            if(*p == '\0' && num > 0)
            {
            	//printf("Number : %d is to big!\n",number);
				return (char*)string;
            }

            /*当字符为ASCII时，*/

			if(*p > 0x80 || *p < 0 )
			{
				if(num<=number)
				{
					p+=3;
					num+=3;
					bufsize+=3;
				}
				else
				{
					break;
				}
			}
			else
			{
				if(num<number)
				{
					p++;
					num++;
					bufsize++;
				}
				else
				{
					bufsize++;
					break;
				}
			}
         }
     }
     num = bufsize;
     /*开始分配内存*/
#if 0
     char* result ;
     result = (char*)malloc(sizeof(char)*(bufsize+1));
     memset(result,0,sizeof(char)*(bufsize+1));
#endif
     static char result[128]={0};
     memset(result,0,sizeof(result));

     /*开始复制字符串*/
     int i = 0;
     int j = 0;
     while(num != 0)
     {
         result[i++] = start_char[j++];
         num--;
     }
     /*尾部置零*/
     result[bufsize] = '\0';
     return result;
}

char* Check_FileName(char *path,int utf8Bytes)
{
    static char path_convert[128]={0};
	static char file_name[128]={0};
	memset(file_name,0,sizeof(file_name));
	memset(path_convert,0,sizeof(path_convert));

	if( strlen(path) == 0 || strlen(path)>128+8 )
	{
		memset(path,0,sizeof(path));
		strcpy(path,"");

		strcpy(path_convert,"");
		return path_convert;
	}

    char *p=strrchr(path,'.');
    char temp_buf[128]={0};
    if(p!=NULL)
    {
        strncpy(file_name,path,p-path);
        strcpy(temp_buf,file_name);
    }
    else
    {
        strcpy(temp_buf,path);
    }

    strcpy(path_convert,StringSubU_UTF8(temp_buf,1,utf8Bytes));
    
    return path_convert;
}

char* Get_Source_Name_ById(unsigned char id)
{
	static char sourceName[32]={0};

	switch(id)
	{
		case SOURCE_FIRE_ALARM:
			strcpy(sourceName,"消防警报");
			break;
		case SOURCE_NET_PAGING:
			strcpy(sourceName,"网络寻呼");
			break;
		case SOURCE_AUX:
			strcpy(sourceName,"本地播放");
			break;
		case SOURCE_LOCAL_PLAY:
			strcpy(sourceName,"网络点播");
			break;
		case SOURCE_CALL:
			strcpy(sourceName,"网络对讲");
			break;
		case SOURCE_TIMING:
			strcpy(sourceName,"定时播放");
			break;
		case SOURCE_NULL:
			strcpy(sourceName,"空闲");
			break;
		case SOURCE_100V_INPUT:
			strcpy(sourceName,"100V");
			break;
		default:
			if(id >= SOURCE_AUDIO_COLLECTOR_BASE && id <= SOURCE_AUDIO_COLLECTOR_MAX )	//信号采集
			{
				
				char channel_num = (id-SOURCE_AUDIO_COLLECTOR_BASE)%4 +1;
				sprintf(sourceName,"%s(CH%d)","音频采集",channel_num);
			}
			break;
	}

	return sourceName;
}


// 函数定义
void split_ip(const char* ip_str, unsigned char* t_ip_parts) {
    char* token;
    char ip_copy[16]; // 拷贝原字符串，避免修改原字符串

    if(!if_a_string_is_a_valid_ipv4_address(ip_str))
    {
        t_ip_parts[0]=0;
        t_ip_parts[1]=0;
        t_ip_parts[2]=0;
        t_ip_parts[3]=0;
        return;
    }
    strcpy(ip_copy, ip_str);

    token = strtok(ip_copy, ".");

    int i = 0;
    while (token != NULL && i < 4) {
        t_ip_parts[i] = atoi(token);
        token = strtok(NULL, ".");
        i++;
    }
}