#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <semaphore.h>
#include "sysconf.h"
#include "sequencePowerProcess.h"

#if(IS_DEVICE_POWER_SEQUENCE)

st_sequence_power_info sequence_power_info;		//电源时序器信息

int g_seqPwr_timer_can_control=1;      //电源时序器定时能否控制
int g_seqPwr_timer_can_control_timeout=0;


void Init_Sequence_Power()
{
    //发送控制模式
    Disp_Send_SequencePower_ControlMode();
    #if 0
    //如果是手动模式,开启相关通道
    if(sequence_power_info.control_mode == SEQUENCE_POWER_MODE_MANUAL)
    {
        sequence_power_info.channel_status = SEQUENCE_POWER_CHANNEL_OPEN_ALL;
        //发送时序器通道状态
        Disp_Send_SequencePower_ChannelStatus();
        //通知服务器通道变化
        Host_Query_Set_Sequence_Power_Info(NULL);
    }
    #endif
    //发送给电源板获取触发输入状态
	Disp_Send_Get_SequencePower_TriggerInput();
}

void Set_Sequence_Power_Info(int ControlMode,int ChannelStatus,int OpenDelay,int IsQuickResponse)
{
    int isNotify=0;
    int isSaveFile=0;
    if( sequence_power_info.control_mode != ControlMode )
    {
        isNotify=1;
        isSaveFile=1;
        sequence_power_info.control_mode = ControlMode;
        Disp_Send_SequencePower_ControlMode();
    }
    if( sequence_power_info.channel_status != ChannelStatus )
    {
        isNotify=1;
        //如果电源板是自己开发，那么需要逐位比较，提高效率，即已经打开或者已经关闭的通道就不要处理
        sequence_power_info.channel_status = ChannelStatus;
        sequence_power_info.preChannel_status = sequence_power_info.channel_status;
        Disp_Send_SequencePower_ChannelStatus(IsQuickResponse);
    }
    #if 0
    if( sequence_power_info.open_delay_value != OpenDelay )
    {
        isNotify=1;
        isSaveFile=1;
        sequence_power_info.open_delay_value = OpenDelay;
    }
    #endif

    if(isNotify)
    {
        Host_Query_Set_Sequence_Power_Info(NULL);
    }
    if(isSaveFile)
    {
        save_sysconf(INI_SETCION_SEQUENCEPOWER,NULL);//保存时序器参数
    }
}


void Set_Sequence_Power_Trigger_Input(int input)
{   
    if(sequence_power_info.input_trigger != input)
    {
        printf("input_trigger=%d\n",input);
        sequence_power_info.input_trigger = input;
        //如果是AIPU，退出
        if(CUR_POWER_SEQUENCE_TYPE == MODEL_SEQUENCE_POWER_AIPU)
            return;
        if(input)
        {
            //自动模式，触发结束后恢复原来的状态
            if(sequence_power_info.control_mode == SEQUENCE_POWER_MODE_AUTO)
            {
                sequence_power_info.preChannel_status = sequence_power_info.channel_status;
            }
            else
            {
                sequence_power_info.preChannel_status = 0;
            }
            sequence_power_info.channel_status = SEQUENCE_POWER_CHANNEL_OPEN_ALL;
        }
        else
        {
            sequence_power_info.channel_status = sequence_power_info.preChannel_status;
        }
        //如果电源板是自己开发，那么需要逐位比较，提高效率，即已经打开或者已经关闭的通道就不要处理
        Disp_Send_SequencePower_ChannelStatus(0);
        Host_Query_Set_Sequence_Power_Info(NULL);
    }
}

#endif