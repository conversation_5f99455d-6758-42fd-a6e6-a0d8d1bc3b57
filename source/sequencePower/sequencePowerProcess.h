#ifndef _SEQUENCE_POWER_PROCESS_H_
#define _SEQUENCE_POWER_PROCESS_H_

#include "const.h"

#if (IS_DEVICE_POWER_SEQUENCE)

extern int g_seqPwr_timer_can_control;      //电源时序器定时能否控制
extern int g_seqPwr_timer_can_control_timeout;

#define SEQUENCE_POWER_MAX_CHANNEL_COUNT	    16      //常规数量（实际需要根据电源时序器应答数据确认）
#define SEQUENCE_POWER_DEFAULT_DELAY        	500     //500ms

#define SEQUENCE_POWER_MODE_MANUAL		0x01	// 手动模式
#define SEQUENCE_POWER_MODE_AUTO		0x02	// 自动模式

#define SEQUENCE_POWER_CHANNEL_STATE_OFF		0x00	// 关闭
#define SEQUENCE_POWER_CHANNEL_STATE_ON			0x01	// 打开

#define SEQUENCE_POWER_CHANNEL_OPEN_ALL			0xFFFF	// 通道全部打开
#define SEQUENCE_POWER_CHANNEL_CLOSE_ALL		0x00	// 通道全部关闭

typedef struct
{
	unsigned char isUsed;
	unsigned char isValid;
}st_sequence_power_Channel_Timing;


typedef struct {
	unsigned char  control_mode;				//控制模式(默认手动)
	unsigned short open_delay_value;			//开启延时,单位毫秒(默认为500ms)
	unsigned short channel_status;				//通道状态(最高位代表channel16，最低位代表channel1)
	unsigned short preChannel_status;			//上一次的通道状态（用于触发输入还原）
	unsigned char  input_trigger;				//输入触发状态(接收外部触发信号)
	unsigned char  openAllKey;					//按键全开全关(暂无实际用途，因为这个按键不是自锁;伟声版本按键带自锁，按下认为是有触发，所以此标志也无用)						
	st_sequence_power_Channel_Timing  st_channel_timing[SEQUENCE_POWER_MAX_CHANNEL_COUNT];
}st_sequence_power_info;

extern st_sequence_power_info sequence_power_info;




void Init_Sequence_Power();
void Set_Sequence_Power_Info(int ControlMode,int ChannelStatus,int OpenDelay,int IsQuickResponse);
void Set_Sequence_Power_Trigger_Input(int input);

#endif

#endif