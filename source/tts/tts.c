#include "sysconf.h"
#include "file.h"
#include "tts.h"

#if SUPPORT_TTS
tts_parm curTTSParm;

static bool isLogin=false;

static const char* login_params         = "appid = 5d1c566d, work_dir = /tmp/";//登录参数,appid与msc库绑定,请勿随意改动

// 在线发音人列表
char* VoiceNameOnArray[] = {(char *)"xiaoyan", (char *)"aisjiuxu", (char *)"aisxping", (char *)"aisjinger", (char *)"aisbabyxu"} ;
// 离线发音人列表
char* VoiceNameOffArray[] = {(char *)"xiaoyan", (char *)"xiaofeng"};

/* 默认wav音频头部数据 */
wave_pcm_hdr default_wav_hdr =
{
    { 'R', 'I', 'F', 'F' },
    0,
    {'W', 'A', 'V', 'E'},
    {'f', 'm', 't', ' '},
    16,
    1,
    1,
    16000,
    32000,
    2,
    16,
    {'d', 'a', 't', 'a'},
    0
};

int TTSCheckParam(tts_parm *ttsParm)
{
    //printf("TTSCheckParam:m_nPitch=%d,m_nSpeed=%d,m_nVolume=%d,m_playTimes=%d,m_VoiceName=%s,m_strText=%s\n", \
    ttsParm->m_nPitch,ttsParm->m_nSpeed,ttsParm->m_nVolume,ttsParm->m_playTimes,ttsParm->m_VoiceName,ttsParm->m_strText);
    if(ttsParm->m_nPitch < 0 || ttsParm->m_nPitch > 100 ||
       ttsParm->m_nSpeed < 0 || ttsParm->m_nSpeed > 100 ||
       ttsParm->m_nVolume < 0 || ttsParm->m_nVolume > 100 ||
       ttsParm->m_playTimes < 1 || ttsParm->m_playTimes > 32767 ||
       ttsParm->m_playInterval < 1 || ttsParm->m_playInterval > 999 ||
       strlen(ttsParm->m_strText)<=0 ||
       !(strcmp(ttsParm->m_VoiceName,"xiaoyan") == 0 || strcmp(ttsParm->m_VoiceName,"xiaofeng") == 0))
    {
        printf("Parm error\n");
        return 0;
    }


    return 1;
}

int CheckTTSJetExist()
{
    if(IsFileExist("/etc/tts/common.jet") && IsFileExist("/etc/tts/xiaoyan.jet") && IsFileExist("/etc/tts/xiaofeng.jet"))
    {
        return 1;
    }

    return 0;
}



int text_to_speech(bool bTestMode,const char* src_text, const char* des_path, const char* params)
{
    int          ret          = -1;
    FILE*        fp           = NULL;
    const char*  sessionID    = NULL;
    unsigned int audio_len    = 0;
    wave_pcm_hdr wav_hdr = default_wav_hdr;
    int          synth_status = MSP_TTS_FLAG_STILL_HAVE_DATA;
#if 0
    ret = MSPLogin(NULL, NULL, login_params);//第一个参数是用户名，第二个参数是密码，第三个参数是登录参数
    if (MSP_SUCCESS != ret)
    {
        printf("MSPLogin error!\n");
        return ret;
    }
#endif
    if (NULL == src_text || NULL == des_path)
    {
        printf("params is error!\n");
        //MSPLogout(); //退出登录
        return ret;
    }
    
    #if 0
	if ((fp = fopen(des_path, "wb")) == NULL)
    {
        printf("open %s error.\n", des_path);
        //MSPLogout(); //退出登录
        return -1;
    }
    #endif

    if(!bTestMode)
        printf("正在合成 ...\n");

    #if 0
    fwrite(&wav_hdr,sizeof(wav_hdr), 1, fp); //写入wav_hdr
    #endif

    unsigned int nTextLen = strlen(src_text);
    unsigned int nPos = 0;
    unsigned int nStep = 1500;

    ret = MSP_SUCCESS;
    nTextLen=nTextLen>nStep?nStep:nTextLen;

    /* 开始合成 */
    sessionID = QTTSSessionBegin(params, &ret);
    if (sessionID == NULL)
    {
        printf("QTTSSessionBegin failed, error code: %d.\n", ret);
        //fclose(fp);
        //MSPLogout(); //退出登录
        return ret;
    }

    //printf("src_text=%s,src_text=%d\n",src_text,nTextLen);
    ret=QTTSTextPut(sessionID, src_text, nTextLen, NULL);
    if (MSP_SUCCESS != ret)
    {
        printf("QTTSTextPut failed, error code: %d.\n",ret);
        QTTSSessionEnd(sessionID, "TextPutError");
        //fclose(fp);
        //MSPLogout(); //退出登录
        return ret;
    }

    while (g_ApiPlayType == API_PLAY_TTS || bTestMode)
    {
        /* 获取合成音频 */
        const void* data = QTTSAudioGet(sessionID, &audio_len, &synth_status, &ret);
        if (MSP_SUCCESS != ret)
        {
            printf("MSP_SUCCESS2 != ret,result=%d,audio_len=%d,synth_status=%d.....\n",ret,audio_len,synth_status);
            break;
        }
        if (NULL != data)
        {
            #if 0
            fwrite(data, audio_len, 1, fp);
            #endif
            wav_hdr.data_size += audio_len; //计算data_size大小
            //printf("audio_len=%d\n",audio_len);
            //播放
            #if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
            if(!bTestMode)
                mi_audio_write(data,audio_len);
            #endif
        }
        if (MSP_TTS_FLAG_DATA_END == synth_status)
            break;
        usleep(5000);
    }

    if (MSP_SUCCESS != ret)
    {
        printf("QTTSAudioGet failed, error code: %d.\n",ret);
        QTTSSessionEnd(sessionID, "AudioGetError");
        //fclose(fp);
        //MSPLogout(); //退出登录
        return ret;
    }

#if 0
    /* 修正wav文件头数据的大小 */
    wav_hdr.size_8 += wav_hdr.data_size + (sizeof(wav_hdr) - 8);

    /* 将修正过的数据写回文件头部,音频文件为wav格式 */
    fseek(fp, 4, 0);
    fwrite(&wav_hdr.size_8,sizeof(wav_hdr.size_8), 1, fp); //写入size_8的值
    fseek(fp, 40, 0); //将文件指针偏移到存储data_size值的位置
    fwrite(&wav_hdr.data_size,sizeof(wav_hdr.data_size), 1, fp); //写入data_size的值
    fclose(fp);

#endif

    /* 合成完毕 */
    ret = QTTSSessionEnd(sessionID, "Normal");
    if (MSP_SUCCESS != ret)
    {
        printf("QTTSSessionEnd failed, error code: %d.\n",ret);
    }
    //MSPLogout(); //退出登录
    return ret;
}



void *GeneratingTTSThread(void *lpParam)
{
    tts_parm* tts = (tts_parm*)lpParam;
    int         ret                  = MSP_SUCCESS;
    if(!tts->m_nTestMode)
    {
        g_ApiPlayType = API_PLAY_TTS;
        set_system_source(SOURCE_API_TTS_MUSIC);
        pkg_query_current_status(NULL);	//发送当前状态
        Open_Audio_Out(16000,16,1);
    }
#if 1
    /* 用户登录 */
    if(!isLogin)
    {
        ret = MSPLogin(NULL, NULL, login_params);//第一个参数是用户名，第二个参数是密码，第三个参数是登录参数
        if (MSP_SUCCESS != ret)
        {
            printf("MSPLogin error!\n");
            if(g_ApiPlayType == API_PLAY_TTS)
            {
                Set_zone_idle_status(NULL,__func__, __LINE__,true);
                g_ApiPlayType=API_PLAY_NULL;
            }
            return NULL;
        }
        isLogin=true;
    }
#endif
    char *strFilePath="/tmp/test.wav";
    char m_strSessionParam[256]={0};
    sprintf(m_strSessionParam,"engine_type = local,voice_name=%s, text_encoding = UTF8, tts_res_path = fo|/etc/tts/%s.jet;"
                                 "fo|/etc/tts/common.jet, sample_rate = %d, speed = %d, volume = %d, pitch = %d, rdn = %d",
                                 tts->m_VoiceName, tts->m_VoiceName, tts->m_nRate, tts->m_nSpeed, 100, tts->m_nPitch, tts->m_nRdn);

    //登录后，开始合成
    //printf("parm=%s...\n",m_strSessionParam);

    int playTimesRemain=tts->m_playTimes;
    int playCnt=0;
    //printf("playTimesRemain=%d,g_ApiPlayType=%d,g_media_source=%d\n",\
        playTimesRemain,g_ApiPlayType,g_media_source);

    if(!tts->m_nTestMode)
    {
        while(playTimesRemain-- && (g_ApiPlayType == API_PLAY_TTS))
        {
            system("rm /tmp/msc -rf");
            printf("ready tts:playCnt=%d\n",playCnt+1);
            ret = text_to_speech(tts->m_nTestMode,tts->m_strText, strFilePath, m_strSessionParam);
            if (MSP_SUCCESS != ret)
            {
                printf("text_to_speech failed, error code: %d.\n", ret);
                break;
            }
            printf("合成完毕 ...\n", ret);
            int cnt=tts->m_playInterval*1000000/50000;
            while(cnt-- && playTimesRemain)
            {
                usleep(50000);
                if(g_ApiPlayType == API_PLAY_NULL)
                {
                    break;
                }
            }
        }

        int cnt=20;
        while(cnt-- && (g_ApiPlayType == API_PLAY_TTS))
        {
            usleep(50000);
        }
    }
    else
    {
        text_to_speech(tts->m_nTestMode,tts->m_strText, strFilePath, m_strSessionParam);
    }

    if(g_ApiPlayType == API_PLAY_TTS)
    {
        Set_zone_idle_status(NULL,__func__, __LINE__,true);
        g_ApiPlayType=API_PLAY_NULL;
    }
    return NULL;
}


int TextToSpeech(tts_parm *ttsParm)
{
    memcpy(&curTTSParm,ttsParm,sizeof(tts_parm));
    printf("TextToSpeech:TestMode=%d\n,PlayCnt=%d\n",curTTSParm.m_nTestMode,curTTSParm.m_playTimes);
    pthread_t pth;
    pthread_attr_t  attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pth, &attr, GeneratingTTSThread, (void*)&curTTSParm);
    pthread_attr_destroy(&attr);

    return 1;
}

void SystemBootTestTTS()
{
    tts_parm tempTTSParm;
    sprintf(tempTTSParm.m_VoiceName,"xiaoyan");
    tempTTSParm.m_nRate = 16000;
    tempTTSParm.m_nSpeed = 50;
    tempTTSParm.m_nPitch = 50;
    tempTTSParm.m_nVolume = 100;
    tempTTSParm.m_nRdn = 0;
    tempTTSParm.m_playTimes = 1;
    tempTTSParm.m_playInterval = 1;
    tempTTSParm.m_nTestMode = 1;    //测试模式
    sprintf(tempTTSParm.m_strText,"欢迎使用");
    if(TTSCheckParam(&tempTTSParm))
    {
        TextToSpeech(&tempTTSParm);
    }
}

#endif