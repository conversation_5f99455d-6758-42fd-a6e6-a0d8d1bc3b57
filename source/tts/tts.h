#ifndef TTS_H
#define TTS_H

#include <stdlib.h>
#include <stdio.h>
#include <unistd.h>
#include <errno.h>
#include "qtts.h"
#include "msp_cmn.h"
#include "msp_errors.h"


/* wav音频头部格式 */
typedef struct _wave_pcm_hdr
{
    char            riff[4];                // = "RIFF"
    int				size_8;                 // = FileSize - 8
    char            wave[4];                // = "WAVE"
    char            fmt[4];                 // = "fmt "
    int				fmt_size;				// = 下一个结构体的大小 : 16

    short int       format_tag;             // = PCM : 1
    short int       channels;               // = 通道数 : 1
    int				samples_per_sec;        // = 采样率 : 8000 | 6000 | 11025 | 16000
    int				avg_bytes_per_sec;      // = 每秒字节数 : samples_per_sec * bits_per_sample / 8
    short int       block_align;            // = 每采样点字节数 : wBitsPerSample / 8
    short int       bits_per_sample;        // = 量化比特数: 8 | 16

    char            data[4];                // = "data";
    int				data_size;              // = 纯数据长度 : FileSize - 44
} wave_pcm_hdr;


typedef struct
{
    int    m_nOnline;             // 1：在线 2：离线
    int    m_nVolume;             // 音量
    int    m_nRate;    // 采样率
    int    m_nPitch;              // 音调
    int    m_nSpeed;              // 语速
    int    m_nRdn;                // 音频数字发音方式
    char m_VoiceName[32];         // 发音人
    char m_strText[1501];         // 合成文本
    char m_strFileName[128];      // 文件名
    int m_playTimes;             // 播放次数
    int m_playInterval;          // 间隔时间
    int m_nTestMode;              // 测试模式不播放声音（用于第一次启动，提前缓存，否则容易卡顿）
}tts_parm;

extern tts_parm curTTSParm;
// 在线发音人列表
extern char* VoiceNameOnArray[];
// 离线发音人列表
extern char* VoiceNameOffArray[];


void SystemBootTestTTS();
int TextToSpeech(tts_parm *ttsParm);
int TTSCheckParam(tts_parm *ttsParm);
int CheckTTSJetExist();

#endif // TTS_H





