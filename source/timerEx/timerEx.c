/**
 * 定时器模块
 * 主要功能：
 *
 * 
 * 	依赖模块：
 *  	1.错误列表
 * 
 * 
 */

#include "timerEx.h"
#include <pthread.h>
#include <semaphore.h>  
#include <stdio.h>

#include "errorList.h"
#include <unistd.h>

stTimer TimerList[TIMER_LIST_MAX]={0};//定时器列表
static sem_t sem;//信号量

void * TimerList_Pthread(void)
{
	NOTICE("Timer List Pthread Start Succeed!");
	while(1){
		usleep(TIMER_UNIT);
		TimerListInt_1ms();
	}
}
/**
 * [TimerList_PthreadStart description]
 */
void TimerList_PthreadStart()
{
    pthread_t pid;
    pthread_attr_t Pthread_Attr;
    pthread_attr_init(&Pthread_Attr);
    pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pid, &Pthread_Attr, (void *)TimerList_Pthread, NULL);
    pthread_attr_destroy(&Pthread_Attr);
}

void *TimerLoop(void )
{
	unsigned char i=0;
	NOTICE("Timer Function Run Pthread Start Succeed!");
	while(1){
		sem_wait(&sem);//没有定时点触发时等待
		for (i = 0; i < TIMER_LIST_MAX; ++i)
		{
			if(TimerList[i].used == 0 || TimerList[i].start == 0) continue;
			if(TimerList[i].value == 0 ) continue;
			if(TimerList[i].count != 0) continue; //计数器满
		//	NOTICE("A Timer Activation...\n");
			//printf("Timer pHandle running\n");			
			TimerList[i].pHandle(i);			
			TimerList[i].count = TimerList[i].value;
			//break;//
		}
	}
	
}
void TimerFuncRun_PthreadStart()
{
    pthread_t pid;
    pthread_attr_t Pthread_Attr;
    pthread_attr_init(&Pthread_Attr);
    pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pid, &Pthread_Attr, (void *)TimerLoop, NULL);
    pthread_attr_destroy(&Pthread_Attr);
}

void InitTimerEx()
{
	unsigned char i=0;
	for (i = 0; i < TIMER_LIST_MAX; ++i)
	{
		TimerList[i].value = 0;
		TimerList[i].count = 0;
		TimerList[i].start = 0;
		TimerList[i].used = 0;
		TimerList[i].pHandle = (void *)0;
	}
	TimerList_PthreadStart();
	//初始化信号量，其初值为0  
   	int res = sem_init(&sem, 0, 0);  
    if(res == -1)  
    {  
        perror("semaphore intitialization failed\n");
        //SystemErrorExit(ERRNO_SEM_INIT_FAILED);       
    }
	TimerFuncRun_PthreadStart();
}


int CheckTimerUsed(unsigned char id)
{	
	if(id >= TIMER_LIST_MAX) return T_ERROR;
	return TimerList[id].used;	
}

int CheckTimerActivate(unsigned char id)
{	
	if(id >= TIMER_LIST_MAX) return T_ERROR;
	return TimerList[id].start;	
}

int SetTimerRun(unsigned char id)
{
	NOTICE("Run Timer(%d)",id);
	if(id >= TIMER_LIST_MAX) return T_ERROR;	
	if(TimerList[id].value == 0)  return T_ERROR;
	if(TimerList[id].used == 0) {
		WARNINGP("Run Timer %d Wrong Is Not used!",id);
		return T_ERROR;
	}
	TimerList[id].start = 1;
	TimerList[id].count = TimerList[id].value ;
	return id;
}
int SetTimerStop(unsigned char id)
{
	if(id >= TIMER_LIST_MAX) return T_ERROR;
	NOTICE("Stop Timer(%d)",id);
	TimerList[id].start = 0;
	return id;
}

int CreateTimer(unsigned short value,unsigned char enable,TimerHandleFunc_ptr pFunc)
{
	unsigned char freeID = T_ERROR,i;

	for (i = 0; i < TIMER_LIST_MAX; ++i)
	{
		if( CheckTimerUsed(i) == 0){
			freeID =  i;
			break;
		}
	}
	if(freeID == T_ERROR) return T_ERROR;
	NOTICE("Create Timer Succeed ID(%d)",freeID);
	TimerList[freeID].used = 1;
	TimerList[freeID].value = value/(TIMER_UNIT/1000);//10  100000

	if(TimerList[freeID].value == 0 ){
		TimerList[freeID].value = 1;
		NOTICE(" Timer(%d) Set Value Is 0 default Min 1",freeID);
	}

	TimerList[freeID].count = TimerList[freeID].value;
	TimerList[freeID].start = enable;
	TimerList[freeID].pHandle = pFunc;
	TimerList[freeID].pHandleUserData = NULL;
	NOTICE(" Timer(%d) Count:%d",freeID,TimerList[freeID].count);
	return freeID;
}


int CreateTimerData(unsigned short value,unsigned char enable,TimerHandleFuncUserData_ptr pFunc)
{
	unsigned char freeID = T_ERROR,i;

	for (i = 0; i < TIMER_LIST_MAX; ++i)
	{
		if( CheckTimerUsed(i) == 0){
			freeID =  i;
			break;
		}
	}
	if(freeID == T_ERROR) return T_ERROR;
	NOTICE("Create Timer Succeed ID(%d)",freeID);
	TimerList[freeID].used = 1;
	TimerList[freeID].value = value/(TIMER_UNIT/1000);//10  100000

	if(TimerList[freeID].value == 0 ){
		TimerList[freeID].value = 1;
		NOTICE(" Timer(%d) Set Value Is 0 default Min 1",freeID);
	}

	TimerList[freeID].count = TimerList[freeID].value;
	TimerList[freeID].start = enable;
	TimerList[freeID].pHandle = NULL;
	TimerList[freeID].pHandleUserData = pFunc;
	NOTICE(" Timer(%d) Count:%d",freeID,TimerList[freeID].count);
	return freeID;
}



unsigned char DeleteTimer(unsigned char id)
{
	if(id >= TIMER_LIST_MAX) return T_ERROR;
	TimerList[id].value = 0;
	TimerList[id].count = 0;
	TimerList[id].start = 0;
	TimerList[id].used = 0;
	TimerList[id].pHandle = (void*)0;
	TimerList[id].pHandleUserData = (void*)0;
	return id;
}


/**
 * [TimerListInt_1ms 定时器中断调用]
 */
void TimerListInt_1ms()
{
	unsigned char i=0;
	for (i = 0; i < TIMER_LIST_MAX; ++i)
	{
		if(TimerList[i].used == 0 || TimerList[i].start == 0) continue;
		if(TimerList[i].value == 0 ) continue;
		if(TimerList[i].count == 0) continue; //计数器满	
		TimerList[i].count -- ;
		//printf("TimerList[%d].count=%d\n",i,TimerList[i].count);
		if(TimerList[i].count == 0){
			//printf("--------------Timer timerout\n");
			sem_post(&sem); //发信号触发执行函数
		}
	}
}




