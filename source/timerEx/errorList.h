#ifndef __ERRORLIST_H__
#define __ERRORLIST_H__


extern int LastErrorNo;

typedef struct ErrorList
{
	int errnob;
	const char * errorStr;
}ErrorList;

enum ErrorNum
{
	ERRNO_SUCCEED=0,
	ERRNO_ARRAY_OVER_RANGE,
	ERRNO_DATA_RECV_LEN_DIFF,
	ERRNO_DATA_CHECK_FAILED,
	ERRNO_PKG_MODE_FAILED,
	ERRNO_MULTIADDR_REGISTER_FAILED,
	ERRNO_PTHREAD_CREATE_FAILED,
	ERRNO_NO_SET_DEVICE_MODE,
	ERRNO_SOCKET_CREATE_FAILED,
	ERRNO_SOCKET_ADDR_SEND_FAILED,
	ERRNO_NOTIFY_MSG_RECV_FAILED,
	ERRNO_NOTIFY_MSG_SEND_FAILED,
	ERRNO_NOTIFY_MSG_CREAT_FAILED,
	ERRNO_SEM_INIT_FAILED,
	ERRNO_CREATE_TIMER_OVERFLOW,
	ERRNO_SOCKET_PORT_BIND,
	ERRNO_CURL_DOWNLOAD_PTHREAD_EXIT,
	ERRNO_MALLOC_FAILED,
	ERRNO_SYSTEM_ERRNO,//系统错误信息
	ERRNO_MAX,
};

#endif