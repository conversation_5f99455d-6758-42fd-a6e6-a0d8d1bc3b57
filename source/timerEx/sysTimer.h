#ifndef SYS_TIMER_H
#define SYS_TIMER_H

// 定时器数组，用于存储多个定时器的配置和处理函数

enum{
    SYS_TIMERS_ID_LVGL_UI=0,
    MAX_SYS_TIMERS=20
};


// 定义定时器处理函数类型
typedef void (*TimerHandler)(int);

// 定义定时器结构体
typedef struct {
    int id;
    TimerHandler handler;
    int interval_ms; // 间隔时间（毫秒）
    int delay_ms;    // 启动延迟（毫秒）
    int totalCount;  // 总次数，0表示不执行，1表示执行一次，-1表示无限次
    int RemainCount; // 剩余次数
    int used;        // 使用状态，0表示未使用，1表示使用中
} SysTimer;


void Timer_Init(int id, TimerHandler handler, int delay_ms, int interval_ms, int totalCount);

#endif