#ifndef _TIMER_EX_H_
#define _TIMER_EX_H_
#include "typedef.h"
#include "debug.h"

#define TIMER_UNIT 		100000		//最小单位1000us
#define TIMER_LIST_MAX 	20 			//最大定时器个数
#define T_ERROR 	-1

#define TIMER_ENABLE 	1
#define TIMER_DISENABLE 0



typedef void (* TimerHandleFunc_ptr)(int ); 
typedef void (* TimerHandleFuncUserData_ptr)(void* );
typedef struct stTimer
{
	unsigned short value;//单位1ms
	unsigned short count;//定时计数
	unsigned char 	start;//是否启动
	unsigned char 	used;//是否使用
	TimerHandleFunc_ptr pHandle;
	TimerHandleFuncUserData_ptr pHandleUserData;
}stTimer;

//void TimerLoop();	//主循环中调用
void TimerListInt_1ms(); //定时中断中调用
unsigned char DeleteTimer(unsigned char id); //删除一个定时点
int CreateTimer(unsigned short value,unsigned char enable,TimerHandleFunc_ptr pFunc); //创建一个定时点
int CreateTimerData(unsigned short value,unsigned char enable,TimerHandleFuncUserData_ptr pFunc); //创建一个定时点
int SetTimerStop(unsigned char id);//停止定时点
int SetTimerRun(unsigned char id);//启动定时点
int CheckTimerActivate(unsigned char id);//定时点是否启动
void InitTimerEx(); //初始化

#endif