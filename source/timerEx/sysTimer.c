#include <signal.h>
#include <sys/time.h>
#include <stdio.h>
#include <unistd.h>
#include "sysTimer.h"

SysTimer timers[MAX_SYS_TIMERS];
int timer_count = 0;

// 信号编号数组，用于分配不同的信号给每个定时器
int sig_nums[MAX_SYS_TIMERS] = {0};


void Timer_Handler(int sig, siginfo_t *si, void *uc) {
    // 从信号编号获取定时器ID
    int timer_id = si->si_value.sival_int;
    SysTimer *timer = &timers[timer_id];

    // 调用对应的定时器处理函数，并传递id
    timer->handler(timer_id);

    // 如果是无限次重复，则重新设置定时器
    if (timer->totalCount != -1)
    {
        timer->RemainCount--;
        if (timer->RemainCount == 0)
        {
            timer->used = 0;
            struct itimerval timer_val_stop;
            timer_val_stop.it_value.tv_sec = 0;
            timer_val_stop.it_value.tv_usec = 0;
            timer_val_stop.it_interval.tv_sec = 0;
            timer_val_stop.it_interval.tv_usec = 0;
            if (setitimer(ITIMER_REAL, &timer_val_stop, NULL) == -1) {
                perror("setitimer to stop failed");
            }
        }
    }
}


void timer_handler(int sig) {
    //printf("Timer triggered!\n");
    //lv_task_handler();
}


void Timer_Init(int id, TimerHandler handler, int delay_ms, int interval_ms, int timerCount) {


    #if 0
    if (timer_count >= MAX_SYS_TIMERS) {
        fprintf(stderr, "Timer_Init Error: Maximum number of timers reached.\n");
        return;
    }
    

    if(timerCount  == 0)
    {
        fprintf(stderr, "Timer_Init Error: Timer Count=0!\n");
        return;
    }

    if(timers[timer_count].used  == 1)
    {
        fprintf(stderr, "Timer_Init Error: Timer used!\n");
        return;
    }


    timers[timer_count].id = id;
    timers[timer_count].handler = handler;
    timers[timer_count].interval_ms = interval_ms;
    timers[timer_count].delay_ms = delay_ms;
    timers[timer_count].totalCount = timerCount;
    timers[timer_count].RemainCount = timerCount;
    timers[timer_count].used = 1;

    struct sigaction sa;
    struct itimerval timer;

    // 设置定时器信号处理函数
    sa.sa_flags = SA_SIGINFO;
    sa.sa_sigaction  = Timer_Handler;
    sigemptyset(&sa.sa_mask);

    if (sigaction(SIGRTMIN+id, &sa, NULL) == -1) {
        perror("sigaction failed");
        return;
    }

    // 设置定时器的时间间隔和延迟
    timer.it_value.tv_sec = delay_ms / 1000;
    timer.it_value.tv_usec = (delay_ms % 1000) * 1000;
    timer.it_interval.tv_sec = interval_ms / 1000;
    timer.it_interval.tv_usec = (interval_ms % 1000) * 1000;

    // 启动定时器
    if (setitimer(ITIMER_REAL, &timer, NULL) == -1) {
        perror("setitimer failed");
        return;
    }

    timer_count++;

    printf("Timer_Init:%d OK!!!\n",id);

    #else
    struct sigaction sa;
    struct itimerval timer;
    sa.sa_handler = timer_handler; // 使用 sa_handler 简化测试
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0; // 不使用 SA_SIGINFO

    if (sigaction(SIGALRM, &sa, NULL) == -1) {
        perror("sigaction failed");
        return;
    }

    timer.it_value.tv_sec = 0;      // 1 秒后触发
    timer.it_value.tv_usec = 10000;
    timer.it_interval.tv_sec = 0;   // 每 1 秒重复触发
    timer.it_interval.tv_usec = 10000;

    if (setitimer(ITIMER_REAL, &timer, NULL) == -1) {
        perror("setitimer failed");
        return;
    }
    #endif
}


#if 0

// 定义一个具体的定时器处理函数
void MyTimerHandler(int id) {
    // 根据id处理定时器事件
    printf("SysTimer %d triggered\n", id);
}
int main() {
    // 初始化两个定时器，并传递处理函数、id、延迟时间、间隔时间和运行次数
    Timer_Init(1, MyTimerHandler, 1000, 10000, -1); // 1秒后启动，每隔10秒触发一次，无限次

    // 其他代码...
    while (1) {
        pause(); // 暂停进程，等待信号
    }
    return 0;
}
#endif