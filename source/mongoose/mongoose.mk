CSRCS += mg_httpServer.c
SRC_DIR := $(BUILD_DIR)/$(SOURCE_DIR_NAME)/mongoose/src
#CSRCS += g722_encode.c
#CSRCS += g722_decode.c
CSRCS += $(notdir $(wildcard $(SRC_DIR)/*.c))


DEPPATH += --dep-path $(BUILD_DIR)/$(SOURCE_DIR_NAME)/mongoose
DEPPATH += --dep-path $(BUILD_DIR)/$(SOURCE_DIR_NAME)/mongoose/src
VPATH += :$(BUILD_DIR)/$(SOURCE_DIR_NAME)/mongoose
VPATH += :$(BUILD_DIR)/$(SOURCE_DIR_NAME)/mongoose/src
CFLAGS += "-I$(BUILD_DIR)/$(SOURCE_DIR_NAME)/mongoose"
CFLAGS += "-I$(BUILD_DIR)/$(SOURCE_DIR_NAME)/mongoose/src"