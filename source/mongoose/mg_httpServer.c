// Copyright (c) 2020 Cesanta Software Limited
// All rights reserved

#include <signal.h>
#include <pthread.h>
#include "mongoose.h"
#include "sysconf.h"
#include <ctype.h>  // For isalnum
#include <time.h>   // For time, needed for ping session ID generation
#include <errno.h>  // For errno checks in kill operations
#if SUPPORT_SIP
#include "../pjsip/appConfig.h"
#endif

enum{
    HTTP_RESULT_INVALID_COMMAND=-2,     //非法指令
    HTTP_RESULT_PARM_ERROR=-1,          //参数错误
    HTTP_RESULT_OK=0,                   //正常请求
    HTTP_RESULT_FAILED=1,              //请求失败
    HTTP_RESULT_CHANGE_NETWORK_MODE_OK=100,    //修改网络模式成功
    HTTP_RESULT_CHANGE_NETWORK_IP_OK=101,      //修改网络IP成功
    HTTP_RESULT_CHANGE_NETWORK_IP_MODE_OK=102, //同时修改网络IP和网络模式成功
    HTTP_RESULT_NOT_CHANGE_NETWORK_IP_MODE=103, //未修改网络IP和网络模式
    HTTP_RESULT_ERROR_PASSWORD=998,    // 密码错误
    HTTP_RESULT_SESSION_EXPIRED=999,    //会话过期
};

#if SUPPORT_HTTP_SERVER

#define HTTP_POST_MAX_BODY 1400

static int s_debug_level = MG_LL_INFO;
static const char *s_root_dir = ".";
static const char *s_tts_listening_address = "http://0.0.0.0:80";
static const char *s_web_listening_address = "http://0.0.0.0:8080";
static const char *s_enable_hexdump = "no";
static const char *s_ssi_pattern = "#.html";


// 定义跨域允许的配置
static const char *s_allowed_origin = "*"; // 允许所有来源
static const char *s_allowed_methods = "GET, POST, PUT, DELETE, OPTIONS"; // 允许的方法
static const char *s_allowed_headers = "Content-Type, SESSION, Origin, Authorization"; // 允许的头部

// CORS 处理函数
static void handle_cors(struct mg_connection *c, struct mg_http_message *hm) {
    mg_printf(c,
              "HTTP/1.1 200 OK\r\n"
              "Access-Control-Allow-Origin: %s\r\n"
              "Access-Control-Allow-Methods: %s\r\n"
              "Access-Control-Allow-Headers: %s\r\n"
              "Access-Control-Max-Age: 3600\r\n"
              "Content-Length: 0\r\n\r\n",
              s_allowed_origin, s_allowed_methods, s_allowed_headers);
}

// Ping session tracking
#define MAX_SESSION_ID_LEN 33  // 32 chars + null terminator
#define MAX_PING_RESULTS_SIZE 512  // Reduced from 8192 to 2048, since client polls frequently

// Structure to hold ping session information
typedef struct {
    pid_t pingPid;
    char results[MAX_PING_RESULTS_SIZE];
    int isCompleted;
    time_t lastAccess;  // Last time this session was accessed
    int isActive;       // Flag to indicate if the session is active
} PingSession;

// Single ping session instance
static PingSession pingSession;
static pthread_mutex_t pingSessionsMutex = PTHREAD_MUTEX_INITIALIZER;

// Initialize ping session
void init_ping_session() {
    pthread_mutex_lock(&pingSessionsMutex);
    memset(&pingSession, 0, sizeof(PingSession));
    pthread_mutex_unlock(&pingSessionsMutex);
}

// Clear the ping session
void clear_ping_session() {
    pthread_mutex_lock(&pingSessionsMutex);
    if (pingSession.pingPid > 1) {  // Only kill if PID is > 1 (for safety)
        // Try to kill the process
        int kill_result = kill(pingSession.pingPid, SIGTERM);
        if (kill_result != 0) {
            // If SIGTERM failed, try SIGKILL as a last resort
            kill(pingSession.pingPid, SIGKILL);
        }
    }
    memset(&pingSession, 0, sizeof(PingSession));
    pthread_mutex_unlock(&pingSessionsMutex);
}

// Thread function to execute a ping command with output streaming
void* ping_thread_func(void *arg) {
    // Get the ping command to run
    char command[256] = {0};
    pthread_mutex_lock(&pingSessionsMutex);
    strcpy(command, pingSession.results);
    memset(pingSession.results, 0, MAX_PING_RESULTS_SIZE);
    pthread_mutex_unlock(&pingSessionsMutex);
    
    // Open the pipe to the command
    FILE* pipe = popen(command, "r");
    if (pipe == NULL) {
        pthread_mutex_lock(&pingSessionsMutex);
        strcpy(pingSession.results, "Error: Failed to start ping command");
        pingSession.isCompleted = 1;
        pthread_mutex_unlock(&pingSessionsMutex);
        return NULL;
    }
    
    // Get the child process PID
    pid_t child_pid = 0;
    
    // Try to get the real PID of the process
    #if defined(_WIN32) || defined(_WIN64)
        // On Windows, we can't easily get the PID from popen
        // Just use a placeholder value to indicate it's active
        child_pid = 1;
    #else
        // On Linux/Unix we can use /proc to find the child PID
        // Get our own PID first
        pid_t my_pid = getpid();
        
        // Command to find child PIDs
        char find_pid_cmd[128];
        snprintf(find_pid_cmd, sizeof(find_pid_cmd), "ps -o pid --ppid %d --no-headers", my_pid);
        
        FILE* pid_pipe = popen(find_pid_cmd, "r");
        if (pid_pipe) {
            char buffer[32];
            // Read the first child PID (there should only be one)
            if (fgets(buffer, sizeof(buffer), pid_pipe) != NULL) {
                child_pid = atoi(buffer);
            }
            pclose(pid_pipe);
        }
        
        // If we couldn't get the PID, use a placeholder
        if (child_pid <= 0) {
            child_pid = 1; // Just a placeholder to indicate it's active
        }
    #endif
    
    // Store the child process PID
    pthread_mutex_lock(&pingSessionsMutex);
    pingSession.pingPid = child_pid;
    pthread_mutex_unlock(&pingSessionsMutex);
    
    // Read output and update the session's results
    char buffer[128];
    while (fgets(buffer, sizeof(buffer), pipe) != NULL) {
        pthread_mutex_lock(&pingSessionsMutex);
        
        // Make sure we don't overflow the results buffer
        size_t currentLen = strlen(pingSession.results);
        size_t bufferLen = strlen(buffer);
        
        if (currentLen + bufferLen < MAX_PING_RESULTS_SIZE - 1) {
            // Enough space, simply append
            strcat(pingSession.results, buffer);
        } else {
            // Not enough space - we'll handle this in a more elegant way
            // Option 1: Remove oldest lines to make space for new content
            if (currentLen > bufferLen * 2) {
                // Find a position about halfway through the buffer to start keeping
                char *startPos = pingSession.results + (currentLen / 2);
                // Find the next line break to ensure we don't cut in the middle of a line
                char *nextLine = strchr(startPos, '\n');
                if (nextLine) {
                    nextLine++; // Move past the newline
                    
                    // Shift the content, effectively removing older lines
                    size_t remainingLen = strlen(nextLine);
                    memmove(pingSession.results, nextLine, remainingLen + 1); // +1 for null terminator
                    
                    // Now append the new data
                    strcat(pingSession.results, buffer);
                } else {
                    // If we can't find a newline, just clear and add new content
                    strcpy(pingSession.results, "...\n");
                    strcat(pingSession.results, buffer);
                }
            } else {
                // Buffer too small, clear it and add abbreviated indicator
                strcpy(pingSession.results, "...\n");
                strcat(pingSession.results, buffer);
            }
        }
        
        pthread_mutex_unlock(&pingSessionsMutex);
    }
    
    // Wait for the command to finish
    int status = pclose(pipe);
    
    // Mark the session as completed
    pthread_mutex_lock(&pingSessionsMutex);
    pingSession.isCompleted = 1;
    pingSession.pingPid = 0;
    pthread_mutex_unlock(&pingSessionsMutex);
    
    return NULL;
}

// Start a new ping session
int start_ping_command(const char* target, int count) {
    if (target == NULL || strlen(target) == 0) {
        return 0; // Failed
    }
    
    // Validate count
    if (count <= 0 || count > 3600) {
        count = 4; // Default to 4 pings if out of range
    }
    
    // Sanitize the target to prevent command injection
    // Only allow alphanumeric characters, dots, dashes, and colons (for IPv6)
    for (int i = 0; target[i] != '\0'; i++) {
        if (!isalnum(target[i]) && target[i] != '.' && target[i] != '-' && target[i] != ':') {
            return 0; // Failed
        }
    }
    
    // Before starting a new ping, kill any existing one
    clear_ping_session();
    
    // Create the ping command based on the OS
    char command[256];
    #if defined(_WIN32) || defined(_WIN64)
        // Windows uses -n for count
        snprintf(command, sizeof(command), "ping -n %d %s", count, target);
    #else
        // Linux/Unix uses -c for count
        snprintf(command, sizeof(command), "ping -c %d %s", count, target);
    #endif
    
    // Initialize the session
    pthread_mutex_lock(&pingSessionsMutex);
    strcpy(pingSession.results, command); // Temporarily store command in results
    pingSession.isCompleted = 0;
    pingSession.lastAccess = time(NULL);
    pingSession.isActive = 1;
    pthread_mutex_unlock(&pingSessionsMutex);
    
    // Create a thread to run the ping command
    pthread_t pingThread;
    if (pthread_create(&pingThread, NULL, ping_thread_func, NULL) != 0) {
        clear_ping_session();
        return 0; // Failed
    }
    
    // Detach the thread so it can clean up itself when done
    pthread_detach(pingThread);
    
    return 1; // Success
}

// Get the results of the ping session
char* get_ping_results(int *isCompleted) {
    pthread_mutex_lock(&pingSessionsMutex);
    
    // Check if we have an active session
    if (!pingSession.isActive) {
        *isCompleted = 1;
        pthread_mutex_unlock(&pingSessionsMutex);
        return strdup("No active ping session");
    }
    
    // Update last access time
    pingSession.lastAccess = time(NULL);
    *isCompleted = pingSession.isCompleted;
    char *results = strdup(pingSession.results);
    
    pthread_mutex_unlock(&pingSessionsMutex);
    return results;
}

// Stop the ping session
void stop_ping_session() {
    pthread_mutex_lock(&pingSessionsMutex);
    
    // Check if we have an active session
    if (!pingSession.isActive) {
        pthread_mutex_unlock(&pingSessionsMutex);
        return;
    }
    
    // Store PID value for logging
    pid_t pid_to_kill = pingSession.pingPid;
    
    // Only attempt to kill if PID is greater than 1 (for safety)
    if (pid_to_kill > 1) {
        // Try to kill the process
        int kill_result = kill(pid_to_kill, SIGTERM);
        if (kill_result != 0) {
            // If SIGTERM failed and it's not because the process doesn't exist
            if (errno != ESRCH) {
                // Try SIGKILL as a last resort
                kill(pid_to_kill, SIGKILL);
            }
        }
    }
    
    // Mark as completed and zero out PID regardless of kill result
    pingSession.isCompleted = 1;
    pingSession.pingPid = 0;

    // Add
    pox_system("pidof ping | xargs kill");
    
    // We don't clear the session immediately to allow the client
    // to retrieve final results.
    pthread_mutex_unlock(&pingSessionsMutex);
}

// Cleanup the ping session if it's old
void cleanup_ping_session() {
    time_t now = time(NULL);
    pthread_mutex_lock(&pingSessionsMutex);
    
    // Process active session
    if (pingSession.isActive) {
        int shouldCleanup = 0;
        
        // Clean up session if:
        // 1. Completed and hasn't been accessed in 60 seconds
        // 2. Any session not accessed in 5 minutes (300 seconds)
        if ((pingSession.isCompleted && now - pingSession.lastAccess > 60) || 
            (now - pingSession.lastAccess > 300)) {
            shouldCleanup = 1;
        }
        
        if (shouldCleanup) {
            // Kill the process if it's running and PID is > 1 (for safety)
            if (pingSession.pingPid > 1) {
                // Try SIGTERM first
                int kill_result = kill(pingSession.pingPid, SIGTERM);
                if (kill_result != 0) {
                    // If SIGTERM failed and it's not because the process doesn't exist
                    if (errno != ESRCH) {
                        // Try SIGKILL
                        kill(pingSession.pingPid, SIGKILL);
                    }
                }
            }
            // Clear the session
            memset(&pingSession, 0, sizeof(PingSession));
        }
    }
    
    pthread_mutex_unlock(&pingSessionsMutex);
}

// Initialize ping session tracking
void init_ping_tracking() {
    init_ping_session();
}

char g_login_token[64]={0};


void generateToken(char *token) {
    const char charset[] = "0123456789abcdef";
    if (token == NULL) return;

    srand((unsigned int)time(NULL)); // 使用当前时间作为随机种子

    for (int i = 0; i < 32; i++) {
        token[i] = charset[rand() % (sizeof(charset) - 1)];
    }
    token[32] = '\0'; // 确保字符串以空字符结尾
}


// 提取 session 的函数
void extract_session(char *session, struct mg_http_message *hm) {
    session[0] = '\0'; // 初始化为一个空字符串

    // 假设请求头存储在 hm->header.ptr 中
    const char *headers_start = hm->head.ptr; //与uri一样
    const char *headers_end = headers_start + hm->head.len;

    // 查找 "Session: " 字段
    const char *session_header = "Session: ";
    const char *session_pos = strstr(headers_start, session_header);
    
    if (session_pos) {
        session_pos += strlen(session_header);  // 跳过 "Session: "
        const char *session_end = strstr(session_pos, "\r\n");
        
        if (!session_end || session_end > headers_end) {
            session_end = headers_end;  // 如果没有找到结束符，就到头部结束
        }
        // 提取 session 字符串
        size_t session_length = session_end - session_pos;
        strncpy(session, session_pos, session_length);
        session[session_length] = '\0';  // 添加结束符
    }
}



// Handle interrupts, like Ctrl-C
static int s_signo;
static void signal_handler(int signo) {
  s_signo = signo;
}

void http_tts_response_normal(char *szBuf,char *command,int resultCode,char *msg)
{
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString(command));
    if(msg!=NULL)
    {
        cJSON_AddItemToObject(root, "msg", cJSON_CreateString(msg));
    }
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(resultCode));
    char*  jsonBuf = cJSON_Print(root);
    sprintf(szBuf,"%s",jsonBuf);
    free(jsonBuf);
    cJSON_Delete(root);
}


void http_tts_response_get_device_info(char *szBuf, char *command, int resultCode, char *msg)
{
    cJSON *root = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "command", cJSON_CreateString(command));

    char str_mac[32]={0};
    MacArrayToString(str_mac,g_mac_addr);
    cJSON_AddItemToObject(root, "mac", cJSON_CreateString(str_mac));
    cJSON_AddItemToObject(root, "device_model", cJSON_CreateNumber(CURRENT_DEVICE_MODEL));
    cJSON_AddItemToObject(root, "name", cJSON_CreateString(g_device_alias));
    cJSON_AddItemToObject(root, "volume", cJSON_CreateNumber(g_system_volume));
    cJSON_AddItemToObject(root, "source", cJSON_CreateNumber(get_system_source()));
    cJSON_AddItemToObject(root, "source_name", cJSON_CreateString(g_media_name));
    cJSON_AddItemToObject(root, "play_status", cJSON_CreateNumber(g_media_status));
    cJSON_AddItemToObject(root, "firmware_version", cJSON_CreateString(LOCAL_FIRMWARE_VERSION));

    if (msg != NULL)
    {
      cJSON_AddItemToObject(root, "msg", cJSON_CreateString(msg));
    }
    
    cJSON_AddItemToObject(root, "result", cJSON_CreateNumber(resultCode));

    char *jsonBuf = cJSON_Print(root);
    sprintf(szBuf, "%s", jsonBuf);
    free(jsonBuf);
    cJSON_Delete(root);
}




/*******************
 * 
 */

void http_web_response_login(char *szBuf, int resultCode, char *msg)
{
    cJSON *root = cJSON_CreateObject();

    if (msg != NULL)
    {
      cJSON_AddItemToObject(root, "msg", cJSON_CreateString(msg));
    }
    
    cJSON_AddItemToObject(root, "code", cJSON_CreateNumber(resultCode));
    if(resultCode == 0)
    {
      cJSON_AddItemToObject(root, "token", cJSON_CreateString(g_login_token));
    }

    char *jsonBuf = cJSON_Print(root);
    sprintf(szBuf, "%s", jsonBuf);
    free(jsonBuf);
    cJSON_Delete(root);
}

void http_web_response_normal(char *szBuf, int resultCode, char *msg)
{
    cJSON *root = cJSON_CreateObject();

    if (msg != NULL)
    {
      cJSON_AddItemToObject(root, "msg", cJSON_CreateString(msg));
    }
    
    cJSON_AddItemToObject(root, "code", cJSON_CreateNumber(resultCode));

    char *jsonBuf = cJSON_Print(root);
    sprintf(szBuf, "%s", jsonBuf);
    free(jsonBuf);
    cJSON_Delete(root);
}


void http_web_response_get_sipInfo(char *szBuf, int resultCode, char *msg)
{
    cJSON *root = cJSON_CreateObject();

    if (msg != NULL)
    {
      cJSON_AddItemToObject(root, "msg", cJSON_CreateString(msg));
    }
    
    cJSON_AddItemToObject(root, "code", cJSON_CreateNumber(resultCode));

    #if SUPPORT_SIP
    int sip_auth_status = IS_EXTENSION_HAS_SIP;
    cJSON_AddItemToObject(root, "sip_auth_status", cJSON_CreateBool(sip_auth_status));
    if(sip_auth_status)
    {
      cJSON_AddItemToObject(root, "sip_reg_switch", cJSON_CreateBool(appData.SipUserInfo[0].registerSwitch));
      cJSON_AddItemToObject(root, "sip_reg_status", cJSON_CreateNumber(getSipCurStatus()));
      cJSON_AddItemToObject(root, "sip_server_addr", cJSON_CreateString(appData.SipUserInfo[0].serverAddress));
      cJSON_AddItemToObject(root, "sip_server_port", cJSON_CreateNumber(appData.SipUserInfo[0].serverPort));
      cJSON_AddItemToObject(root, "sip_account", cJSON_CreateString(appData.SipUserInfo[0].user));
      cJSON_AddItemToObject(root, "sip_password", cJSON_CreateString(appData.SipUserInfo[0].password));
      cJSON_AddItemToObject(root, "sip_transProtocol", cJSON_CreateNumber(appData.SipUserInfo[0].registerProtocolType));
    }
    #else
    int sip_auth_status = 0;
    cJSON_AddItemToObject(root, "sip_auth_status", cJSON_CreateBool(sip_auth_status));
    #endif

    char *jsonBuf = cJSON_Print(root);
    sprintf(szBuf, "%s", jsonBuf);
    free(jsonBuf);
    cJSON_Delete(root);
}

void http_web_response_get_sipRegStatus(char *szBuf, int resultCode, char *msg)
{
    cJSON *root = cJSON_CreateObject();

    if (msg != NULL)
    {
      cJSON_AddItemToObject(root, "msg", cJSON_CreateString(msg));
    }
    
    cJSON_AddItemToObject(root, "code", cJSON_CreateNumber(resultCode));

    #if SUPPORT_SIP
    cJSON_AddItemToObject(root, "sip_reg_status", cJSON_CreateNumber(getSipCurStatus()));
    #else
    cJSON_AddItemToObject(root, "sip_reg_status", cJSON_CreateNumber(0));
    #endif

    char *jsonBuf = cJSON_Print(root);
    sprintf(szBuf, "%s", jsonBuf);
    free(jsonBuf);
    cJSON_Delete(root);
}

void http_web_response_get_deviceInfo(char *szBuf, int resultCode, char *msg)
{
    cJSON *root = cJSON_CreateObject();

    if (msg != NULL)
    {
      cJSON_AddItemToObject(root, "msg", cJSON_CreateString(msg));
    }
    
    cJSON_AddItemToObject(root, "code", cJSON_CreateNumber(resultCode));

    char *deviceName=NULL;
    if(strlen(g_device_alias)>0)
    {
      deviceName=g_device_alias;
    }
    else
    {
      deviceName=g_ipAddress;
    }
    cJSON_AddItemToObject(root, "device_name", cJSON_CreateString(deviceName));
    cJSON_AddItemToObject(root, "device_model", cJSON_CreateNumber(CURRENT_DEVICE_MODEL));
    char str_mac[32]={0};
    MacArrayToString(str_mac,g_mac_addr);
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(str_mac));
    cJSON_AddItemToObject(root, "device_ip", cJSON_CreateString(g_ipAddress));
    cJSON_AddItemToObject(root, "device_network_mode", cJSON_CreateNumber(g_network_mode));
    cJSON_AddItemToObject(root, "server_connected", cJSON_CreateBool(IS_SERVER_CONNECTED));
    cJSON_AddItemToObject(root, "firmware_version", cJSON_CreateString(LOCAL_FIRMWARE_VERSION));
    

    char *jsonBuf = cJSON_Print(root);
    sprintf(szBuf, "%s", jsonBuf);
    free(jsonBuf);
    cJSON_Delete(root);
}


void http_web_response_get_networkInfo(char *szBuf, int resultCode, char *msg)
{
    cJSON *root = cJSON_CreateObject();

    if (msg != NULL)
    {
      cJSON_AddItemToObject(root, "msg", cJSON_CreateString(msg));
    }
    
    cJSON_AddItemToObject(root, "code", cJSON_CreateNumber(resultCode));

    cJSON_AddItemToObject(root, "network_assign", cJSON_CreateNumber(g_IP_Assign));
    if(g_IP_Assign == IP_ASSIGN_STATIC) {
      cJSON_AddItemToObject(root, "network_ipaddr", cJSON_CreateString(g_Static_ip_address));
      cJSON_AddItemToObject(root, "network_sub_netmask", cJSON_CreateString(g_Subnet_Mask));
      cJSON_AddItemToObject(root, "network_gateway", cJSON_CreateString(g_GateWay));
      cJSON_AddItemToObject(root, "network_primary_dns", cJSON_CreateString(g_Primary_DNS));
      cJSON_AddItemToObject(root, "network_secondary_dns", cJSON_CreateString(g_Alternative_DNS));
    }
    cJSON_AddItemToObject(root, "network_mode", cJSON_CreateNumber(g_network_mode));
    cJSON_AddItemToObject(root, "network_server_addr", cJSON_CreateString(g_host_tcp_addr_domain));
    cJSON_AddItemToObject(root, "network_server_port", cJSON_CreateNumber(g_host_tcp_port));
    
    

    char *jsonBuf = cJSON_Print(root);
    sprintf(szBuf, "%s", jsonBuf);
    free(jsonBuf);
    cJSON_Delete(root);
}


void http_web_response_get_terminalInfo(char *szBuf, int resultCode, char *msg)
{
    cJSON *root = cJSON_CreateObject();

    if (msg != NULL)
    {
      cJSON_AddItemToObject(root, "msg", cJSON_CreateString(msg));
    }
    
    cJSON_AddItemToObject(root, "code", cJSON_CreateNumber(resultCode));

    cJSON_AddItemToObject(root, "dev_name", cJSON_CreateString(g_device_alias));
    cJSON_AddItemToObject(root, "aux_volume", cJSON_CreateNumber(g_aux_volume));
    #if SUPPORT_SIP
    cJSON_AddItemToObject(root, "sip_output_volume", cJSON_CreateNumber(appData.voiceInfoSettings.callVolume));
    #else
    cJSON_AddItemToObject(root, "sip_output_volume", cJSON_CreateNumber(100));
    #endif
    cJSON_AddItemToObject(root, "system_language", cJSON_CreateNumber(g_system_language));
    

    char *jsonBuf = cJSON_Print(root);
    sprintf(szBuf, "%s", jsonBuf);
    free(jsonBuf);
    cJSON_Delete(root);
}


// 用于提取 HTTP 请求中的 body 数据
char* extract_body(const char* http_request) {
    // 寻找 Content-Length 头部和请求体的结束标志（两个 CRLF）
    const char* content_length_header = "Content-Length: ";
    const char* end_of_headers = "\r\n\r\n";
    const char* body_end_marker = "\r\n"; // 单行 CRLF 用于标记 body 结尾

    // 定位 Content-Length 头部和 end_of_headers
    char* content_length_pos = strstr(http_request, content_length_header);
    char* end_of_headers_pos = strstr(http_request, end_of_headers);

    // 确保找到 Content-Length 头部和分隔符
    if (content_length_pos == NULL || end_of_headers_pos == NULL) {
        return NULL;
    }

    // 计算 Content-Length 的值
#if 0
    char content_length_str[64];
    int content_length_index = strlen(content_length_header);
    strncpy(content_length_str, content_length_pos + content_length_index, end_of_headers_pos - content_length_pos - content_length_index);
    content_length_str[strcspn(content_length_str, "0123456789") + content_length_index] = '\0'; // 去除非数字字符
#else
    char content_length_str[64];
    int content_length_index = strlen(content_length_header);
    // 确保不会超出content_length_str的边界
    size_t length_to_copy = end_of_headers_pos - content_length_pos - content_length_index;
    if (length_to_copy >= sizeof(content_length_str)) {
        length_to_copy = sizeof(content_length_str) - 1; // 防止溢出
    }
    strncpy(content_length_str, content_length_pos + content_length_index, length_to_copy);
    content_length_str[length_to_copy] = '\0'; // 确保字符串以空字符结尾

    // 去除非数字字符
    char *ptr = content_length_str;
    while (*ptr) {
        if (strchr("0123456789", *ptr) == NULL) {
            *ptr = '\0';
            break;
        }
        ptr++;
    }
#endif
    int content_length = atoi(content_length_str);

    // 定位 body 的开始和结束位置
    char* body_start = end_of_headers_pos + strlen(end_of_headers);
    char* body_end = body_start + content_length;

    // 确保 body 结束位置不超过 http_request 的长度
    if (body_end > http_request + strlen(http_request)) {
        return NULL;
    }

    // 提取 body 数据并复制到新分配的内存中
    char* body = (char*)malloc(content_length + 1); // +1 用于字符串的结束符 '\0'
    if (body == NULL) {
        return NULL;
    }

    strncpy(body, body_start, content_length);
    body[content_length] = '\0'; // 确保字符串以 '\0' 结尾

    return body;
}

// Event handler for the listening connection.
// Simply serve static files from `s_root_dir`
static void tts_cb(struct mg_connection *c, int ev, void *ev_data) {
  if (ev == MG_EV_HTTP_MSG) {
    struct mg_http_message *hm = (struct mg_http_message *)ev_data;
    if (mg_match(hm->uri, mg_str("/v1/api"), NULL) || mg_match(hm->uri, mg_str("/v1/api/"), NULL)) {
              // 检查请求方法是否为POST
        if (mg_vcmp(&hm->method, "POST") == 0) {
              // 获取 POST 请求的数据
              //struct mg_str *body = &hm->body;
              //printf("Received POST data: %d,content=%s\n", (int) hm->method.len, hm->method.ptr);

              char *cCommand=NULL;
              cJSON *json=NULL;
              char* body = extract_body(hm->method.ptr);
              if (body != NULL && strlen(body)<HTTP_POST_MAX_BODY) {
                  //printf("Context Length=%d:\n%s\n", strlen(body),body);

                  //处理数据并发送给服务器
                  // 解析 JSON 数据
                  json = cJSON_ParseWithLength(body,strlen(body));
                  if(json)
                  {
                      cJSON* jsCommand = cJSON_GetObjectItem(json, "command");      //命令
                      if(jsCommand)
                      {
                        cCommand = jsCommand->valuestring;
                        printf("command=%s\n",cCommand);

                        char jsonResponse[1024]={0};
                        char errorMsg[128]={0};
                        
                        if(strcmp(cCommand,"play_local_tts") == 0)
                        {
                          if(!IS_EXTENSION_HAS_TTS)
                          {
                            sprintf(errorMsg,"终端未授权！");
                            http_tts_response_normal(jsonResponse,cCommand,HTTP_RESULT_PARM_ERROR,errorMsg);
                          }
                          else
                          {
                            cJSON* jsVolume = cJSON_GetObjectItem(json, "volume");           //播放音量，0-100
                            cJSON* jsPlayCount = cJSON_GetObjectItem(json, "play_count");    // 播放次数，取值范围:1~999
                            cJSON* jsPlayInterval = cJSON_GetObjectItem(json, "play_interval");    // 播放次数，取值范围:1~999
                            cJSON* jsTTSPitch = cJSON_GetObjectItem(json, "tts_pitch");      // tts音调： 0-100(默认50)
                            cJSON* jsTTSSpeed = cJSON_GetObjectItem(json, "tts_speed");      // tts语速： 0-100(默认50)
                            cJSON* jsVoiceName = cJSON_GetObjectItem(json, "tts_voice_name"); // tts发音人： 男生：xiaofeng 女生：xiaoyan
                            cJSON* jsTTSText = cJSON_GetObjectItem(json, "tts_text");         // tts文本

                            if(jsVolume && jsPlayCount && jsTTSPitch && jsTTSSpeed && jsVoiceName && jsTTSText)
                            {
                              int nVolume = jsVolume->valueint;
                              int nPlayCount = jsPlayCount->valueint;
                              int nTTSPitch = jsTTSPitch->valueint;
                              int nTTSSpeed = jsTTSSpeed->valueint;
                              char *cTTSVoiceName=jsVoiceName->valuestring;
                              char *cTTSText=jsTTSText->valuestring;
                              int nPlayInterval=1;
                              if(jsPlayInterval)
                              {
                                nPlayInterval = jsPlayInterval->valueint;
                              }

                              //启动播报
                              //优先级的处理，以及如何停止播放
                              //20240918 暂时以本地播放处理
                              if(PriorityIsValid(PRIORITY_API_TTS_MUSIC))
                              {
                                Set_zone_idle_status(NULL,__func__, __LINE__,true);

                                #if SUPPORT_TTS
                                tts_parm ttsParm;
                                ttsParm.m_nTestMode = 0;
                                ttsParm.m_nRate = 16000;
                                ttsParm.m_nSpeed = nTTSSpeed;
                                ttsParm.m_nPitch = nTTSPitch;
                                ttsParm.m_nVolume = nVolume;
                                ttsParm.m_playTimes = nPlayCount;
                                ttsParm.m_nRdn = 0;
                                ttsParm.m_playInterval = nPlayInterval;
                                snprintf(ttsParm.m_VoiceName,sizeof(ttsParm.m_VoiceName),cTTSVoiceName);
                                snprintf(ttsParm.m_strText,sizeof(ttsParm.m_strText),cTTSText);
  
                                int isTTSCheckParm=TTSCheckParam(&ttsParm);
                                int isTTSCheckJetExist=CheckTTSJetExist();                
                                if(!isTTSCheckParm || !isTTSCheckJetExist)
                                {
                                  if(!isTTSCheckParm)
                                  {
                                    sprintf(errorMsg,"参数错误");
                                  }
                                  else if(!isTTSCheckJetExist)
                                  {
                                    sprintf(errorMsg,"资源文件缺失");
                                  }
                                  http_tts_response_normal(jsonResponse,cCommand,HTTP_RESULT_PARM_ERROR,errorMsg);
                                }
                                else
                                {
                                  http_tts_response_normal(jsonResponse,cCommand,HTTP_RESULT_OK,NULL);

                                  TextToSpeech(&ttsParm);
                                }

                                #endif
                              }
                            }
                            else
                            {
                              sprintf(errorMsg,"参数错误");
                              http_tts_response_normal(jsonResponse,cCommand,HTTP_RESULT_PARM_ERROR,errorMsg);
                            }
                          }
                        }
                        else if(strcmp(cCommand,"play_url") == 0)
                        {
                          if(!IS_EXTENSION_HAS_TTS)
                          {
                            sprintf(errorMsg,"终端未授权！");
                            http_tts_response_normal(jsonResponse,cCommand,HTTP_RESULT_PARM_ERROR,errorMsg);
                          }
                          else
                          {
                            cJSON* jsVolume = cJSON_GetObjectItem(json, "volume");           //播放音量，0-100
                            cJSON* jsPlayCount = cJSON_GetObjectItem(json, "play_count");    // 播放次数，取值范围:1~999
                            cJSON* jsPlayInterval = cJSON_GetObjectItem(json, "play_interval");    // 播放间隔时间，取值范围:1~999
                            cJSON* jsUrl = cJSON_GetObjectItem(json, "url");                // 播放URL

                            if(jsVolume && jsPlayCount && jsUrl)
                            {
                              int nVolume = jsVolume->valueint;
                              int nPlayCount = jsPlayCount->valueint;
                              char *cUrl = jsUrl->valuestring;
                              int nPlayInterval=1;
                              if(jsPlayInterval)
                              {
                                nPlayInterval = jsPlayInterval->valueint;
                              }

                              //启动播报
                              //优先级的处理，以及如何停止播放
                              //20240918 暂时以本地播放处理
#if SUPPORT_FFMPEG
                              if(PriorityIsValid(PRIORITY_API_TTS_MUSIC))
                              {
                                Set_zone_idle_status(NULL,__func__, __LINE__,true);

                                url_play_parm urlPlayParm;
                                urlPlayParm.m_nVolume = nVolume;
                                urlPlayParm.m_playTimes = nPlayCount;
                                urlPlayParm.m_playInterval = nPlayInterval;
                                snprintf(urlPlayParm.m_strUrl,sizeof(urlPlayParm.m_strUrl),cUrl);

                                int isUrlCheckParm=UrlPlayCheckParam(&urlPlayParm);
                                if(!isUrlCheckParm)
                                {
                                  sprintf(errorMsg,"参数错误");
                                  http_tts_response_normal(jsonResponse,cCommand,HTTP_RESULT_PARM_ERROR,errorMsg);
                                }
                                else
                                {
                                  http_tts_response_normal(jsonResponse,cCommand,HTTP_RESULT_OK,NULL);

                                  Create_url_play_task(&urlPlayParm);
                                }
                              }
#endif
                            }
                            else
                            {
                              sprintf(errorMsg,"参数错误");
                              http_tts_response_normal(jsonResponse,cCommand,HTTP_RESULT_PARM_ERROR,errorMsg);
                            }
                          }
                        }
                        else if(strcmp(cCommand,"set_idle") == 0)
                        {
                          //设置空闲
                          Set_zone_idle_status(NULL,__func__, __LINE__,false);
                          http_tts_response_normal(jsonResponse,cCommand,HTTP_RESULT_OK,NULL);
                        }
                        else if(strcmp(cCommand,"get_device_info") == 0)
                        {
                          http_tts_response_get_device_info(jsonResponse,cCommand,HTTP_RESULT_OK,NULL);
                        }
                        else
                        {
                          sprintf(errorMsg,"不支持的操作");
                          http_tts_response_normal(jsonResponse,cCommand,HTTP_RESULT_PARM_ERROR,NULL);
                        }

                        if(strlen(jsonResponse)>0)
                        {
                          mg_http_reply(c, 200, "", jsonResponse);
                        }
                      }
                  }

              } else {
                  printf("Failed to extract body.\n");
              }

              if(body)
              {
                free(body);
              }
              if(json)
              {
                free(json);
              }
        }
    }
    else {
      // Serve web root directory
      #if 0 //20240824 禁止显示服务器目录文件
      struct mg_http_serve_opts opts = {0};
      opts.root_dir = s_root_dir;
      opts.ssi_pattern = s_ssi_pattern;
      mg_http_serve_dir(c, hm, &opts);
      #endif
    }
#if 0
    // Log request
    MG_INFO(("%.*s %.*s %lu -> %.*s %lu", hm->method.len, hm->method.ptr,
             hm->uri.len, hm->uri.ptr, hm->body.len, 3, c->send.buf + 9,
             c->send.len));
#endif

    c->is_draining = 1;     // 在处理完成后，强制关闭连接，避免keep-alive复用的问题
  }
}



static void web_cb(struct mg_connection *c, int ev, void *ev_data) {
  if (ev == MG_EV_HTTP_MSG) {
    struct mg_http_message *hm = (struct mg_http_message *)ev_data;
    // 打印 URI
    //printf("method:%.*s,URI: %.*s\n",(int)hm->method.len, hm->method.ptr, (int)hm->uri.len, hm->uri.ptr);
    // 处理 OPTIONS 请求（预检请求）
    if (mg_vcasecmp(&hm->method, "OPTIONS") == 0) {
        handle_cors(c, hm);
    }

    struct mg_str caps[5];  // 定义一个数组用于捕获匹配部分
    if (mg_match(hm->uri, mg_str("/web/*"), caps)) {
      char cmd[512]={0};
      snprintf(cmd,caps[0].len+1,caps[0].ptr);
      char jsonResponse[1024]={0};
      char errorMsg[128]={0};
      if (mg_vcmp(&hm->method, "POST") == 0)
      {
        printf("POST:cmd=%s\n",cmd);
        cJSON *json=NULL;
        char* body = extract_body(hm->method.ptr);
        bool needJsonBody=true;
        if(strcmp(cmd,"softwareUpdateByFile") == 0)
        {
          needJsonBody=false;
        }
        if ( !needJsonBody || (body != NULL && strlen(body)<HTTP_POST_MAX_BODY) )
        {
            if(needJsonBody)
            {
              //printf("Context Length=%d:\n%s\n", strlen(body),body);
              // 解析 JSON 数据
              json = cJSON_ParseWithLength(body,strlen(body));
            }
            if(strcmp(cmd,"login") == 0)
            {
                if(json)
                {
                    cJSON* jsUser = cJSON_GetObjectItem(json, "username");
                    cJSON* jsPassword = cJSON_GetObjectItem(json, "password");
                    if(jsUser && jsPassword)
                    {
                        char *strUser=jsUser->valuestring;
                        char *strPassword=jsPassword->valuestring;
                        //对strPassword进行base64解密
                        char base64de_password[128]; // 确保这个缓冲区足够大以存放解码后的数据
                        // 调用mg_base64_decode函数进行解码
                        size_t len = mg_base64_decode(strPassword, strlen(strPassword), base64de_password, sizeof(base64de_password));
                        if(strcmp(strUser,"admin") == 0 && strcmp(base64de_password,"888888") == 0)
                        {
                            //登录成功,生成一个token
                            generateToken(g_login_token); 
                            http_web_response_login(jsonResponse,HTTP_RESULT_OK,NULL);
                        }
                        else
                        {
                            //登录失败
                            http_web_response_login(jsonResponse,HTTP_RESULT_ERROR_PASSWORD,NULL);
                        }
                    }
                    else
                    {
                      http_web_response_login(jsonResponse,HTTP_RESULT_PARM_ERROR,NULL);
                    }

                }
            }
            else
            {
                //提取session,如果不对，需要退出
                char session[128]={0};
                extract_session(session,hm);
                //printf("POST:session=%s,g_login_token=%s\n",session,g_login_token);
                if(strcmp(session,g_login_token) || strlen(g_login_token) == 0)
                {
                  http_web_response_normal(jsonResponse,HTTP_RESULT_SESSION_EXPIRED,NULL);
                }
                else
                {
                    if(strcmp(cmd,"softwareUpdateByFile") == 0)
                    {
                        static bool isUpdateing=false;
                        if(!isUpdateing)
                        {
                            isUpdateing=true;
                            struct mg_http_part part;
                            size_t pos = 0, file_bytes = 0, total_write_bytes = 0, num_files = 0;
                            size_t expected_size = 0;  // 用于存储前端传来的文件大小
                            char path[MG_PATH_MAX]={0};
                            sprintf(path,"%s","/customer/App/Update.temp");
                            
                            // 首先遍历找到size字段
                            while ((pos = mg_http_next_multipart(hm->body, pos, &part)) > 0) {
                                if (strncmp(part.name.ptr, "size", part.name.len) == 0) {
                                    char size_str[32] = {0};
                                    strncpy(size_str, part.body.ptr, part.body.len);
                                    expected_size = atoll(size_str);  // 转换为数字
                                    break;
                                }
                            }
                            
                            // 重置pos重新遍历
                            pos = 0;
                            while ((pos = mg_http_next_multipart(hm->body, pos, &part)) > 0) {
                                if (strncmp(part.name.ptr, "file", part.name.len) == 0) {  // 只处理file字段
                                    MG_INFO(("Chunk name: [%.*s] filename: [%.*s] length: %lu bytes",
                                            part.name.len, part.name.ptr, part.filename.len,
                                            part.filename.ptr, part.body.len));
                                    file_bytes = part.body.len;
                                    
                                    mg_file_write(&mg_fs_posix, path, part.body.ptr, part.body.len);
                                    total_write_bytes += part.body.len;
                                    num_files++;
                                }
                            }
                            
                            // 验证文件大小是否符合预期
                            if(total_write_bytes > 0 && 
                              total_write_bytes == file_bytes && 
                              total_write_bytes == expected_size &&  // 增加与前端传来的大小比对
                              file_bytes < MG_MAX_RECV_SIZE) 
                            {
                                printf("Firmware Upgrade Succeed!\n");
                                http_web_response_normal(jsonResponse,HTTP_RESULT_OK,NULL);
                                
                                char mv_buf[512]={0};
                                sprintf(mv_buf,"mv %s %s",path,UPDATE_REAL_PATH);
                                pox_system(mv_buf);

                                Del_Web_Directory_All_files();
                                System_Reboot_DelayMs(300);
                            }
                            else
                            {
                                printf("Firmware Upgrade Failed! Expected size: %zu, Actual size: %zu\n", 
                                      expected_size, total_write_bytes);
                                http_web_response_normal(jsonResponse,HTTP_RESULT_FAILED,NULL);
                                remove_file(path);
                            }
                            isUpdateing=false;
                        }
                        else {
                            http_web_response_normal(jsonResponse,HTTP_RESULT_FAILED,NULL);
                        }
                    }
                    else if(strcmp(cmd,"sipInfo") == 0)
                    {
                        if(!IS_EXTENSION_HAS_SIP) 
                        {
                            http_web_response_normal(jsonResponse,HTTP_RESULT_PARM_ERROR,NULL);
                        }
                        else
                        {
                            cJSON* jsSipRegSwitch = cJSON_GetObjectItem(json, "sip_reg_switch");
                            cJSON* jsSipServerAddr = cJSON_GetObjectItem(json, "sip_server_addr");
                            cJSON* jsSipServerPort = cJSON_GetObjectItem(json, "sip_server_port");
                            cJSON* jsSipAccount = cJSON_GetObjectItem(json, "sip_account");
                            cJSON* jsSipPassword = cJSON_GetObjectItem(json, "sip_password");
                            cJSON* jsSipProtocol = cJSON_GetObjectItem(json, "sip_transProtocol");
                            if(jsSipRegSwitch && jsSipServerAddr && jsSipServerPort && jsSipAccount && jsSipPassword && jsSipProtocol)
                            {
                              int nSipRegSwitch=jsSipRegSwitch->valueint;
                              char *strSipServerAddr=jsSipServerAddr->valuestring;
                              int nSipServerPort=jsSipServerPort->valueint;
                              char *strSipAccount=jsSipAccount->valuestring;
                              char *strSipPassword=jsSipPassword->valuestring;
                              int strSipProtocol=jsSipProtocol->valueint;
                              if(get_system_source() != SOURCE_SIP_CALLING)
                              {
                                #if SUPPORT_SIP
                                PJ_THREAD_REGISTER_MACRO(web_cb);
                                HostModifyAccountInfo(nSipRegSwitch,255,strSipServerAddr,nSipServerPort,strSipAccount,strSipPassword,strSipProtocol);
                                #endif
                                http_web_response_normal(jsonResponse,HTTP_RESULT_OK,NULL);
                              }
                              else
                              {
                                http_web_response_normal(jsonResponse,HTTP_RESULT_FAILED,"Device is busy, please uncall and then set!");
                              }
                            }
                            else
                            {
                                http_web_response_normal(jsonResponse,HTTP_RESULT_PARM_ERROR,NULL);
                            }
                        }
                    }
                    else if(strcmp(cmd,"terminalInfo") == 0)
                    {
                        cJSON* jsDevName = cJSON_GetObjectItem(json, "dev_name");
                        cJSON* jsAuxVolume = cJSON_GetObjectItem(json, "aux_volume");
                        cJSON* jsSipOutputVolume = cJSON_GetObjectItem(json, "sip_output_volume");
                        cJSON* jsSystemLanguage = cJSON_GetObjectItem(json, "system_language");
                        
                        bool bUpdate=false;
                        if(jsDevName) {
                            bUpdate=true;
                            int valueLen=strlen(jsDevName->valuestring);
                            snprintf(g_device_alias,valueLen>32?32+1:valueLen+1,"%s",jsDevName->valuestring);
                            save_sysconf(INI_SECTION_DEVICE,"Device_Name");
                        }
                        if(jsAuxVolume) {
                            g_aux_volume=jsAuxVolume->valueint;
                            if(g_aux_volume>=0 && g_aux_volume<=100)
                            {
                                bUpdate=true;
                                save_sysconf(INI_SECTION_BASIC,"Aux_Volume");
                            }
                        }
                        #if SUPPORT_SIP
                        if(jsSipOutputVolume) {
                            appData.voiceInfoSettings.callVolume=jsSipOutputVolume->valueint;
                            if(appData.voiceInfoSettings.callVolume>=0 && appData.voiceInfoSettings.callVolume<=100)
                            {
                              	if(g_media_source == SOURCE_SIP_CALLING)	
                                {
                                  appData.voiceInfoSettings.callVolumeTemp = appData.voiceInfoSettings.callVolume;
                                  sem_sip_conf_send();
                                }
                                bUpdate=true;
                                saveSIPConfig();
                            }
                        }
                        #endif
                        if(jsSystemLanguage) {
                            g_system_language=jsSystemLanguage->valueint;
                            printf("g_system_language=%d\n",g_system_language);
                            bUpdate=true;
                            save_sysconf(INI_SECTION_DEVICE,"System_Language");
                        }

                        if(bUpdate)
                        {
                            http_web_response_normal(jsonResponse,HTTP_RESULT_OK,NULL);
                        }
                        else
                        {
                            http_web_response_normal(jsonResponse,HTTP_RESULT_PARM_ERROR,NULL);
                        }
                    }
                    else if(strcmp(cmd,"networkInfo") == 0)
                    {
                        cJSON* jsNetworkAssign = cJSON_GetObjectItem(json, "network_assign");
                        cJSON* jsNetworkIpAddr = cJSON_GetObjectItem(json, "network_ipaddr");
                        cJSON* jsNetworkSubNetMask = cJSON_GetObjectItem(json, "network_sub_netmask");
                        cJSON* jsNetworkGateway = cJSON_GetObjectItem(json, "network_gateway");
                        cJSON* jsNetworkPrimaryDns = cJSON_GetObjectItem(json, "network_primary_dns");
                        cJSON* jsNetworkSecondaryDns = cJSON_GetObjectItem(json, "network_secondary_dns");

                        cJSON* jsNetworkMode = cJSON_GetObjectItem(json, "network_mode");
                        cJSON* jsNetworkServerAddr = cJSON_GetObjectItem(json, "network_server_addr");
                        cJSON* jsNetworkServerPort = cJSON_GetObjectItem(json, "network_server_port");
                        
                        bool parm_error = false;
                        if(jsNetworkAssign == NULL || jsNetworkMode == NULL)
                        {
                            parm_error = true;
                            http_web_response_normal(jsonResponse,HTTP_RESULT_PARM_ERROR,NULL);
                        }
                        else
                        {
                            int nNetworkAssign=jsNetworkAssign->valueint;
                            int nNetworkMode=jsNetworkMode->valueint;

                            char *strNetworkIpAddr = NULL;
                            char *strNetworkSubNetMask = NULL;
                            char *strNetworkGateway = NULL;
                            char *strNetworkPrimaryDns = NULL;
                            char *strNetworkSecondaryDns = NULL;

                            char *strNetworkServerAddr = NULL;
                            int nNetworkServerPort = NULL;

                            if(nNetworkAssign == IP_ASSIGN_STATIC)
                            {
                                if(jsNetworkIpAddr == NULL || jsNetworkSubNetMask == NULL || jsNetworkGateway == NULL || jsNetworkPrimaryDns == NULL || jsNetworkSecondaryDns == NULL)
                                {
                                    parm_error = true;
                                    http_web_response_normal(jsonResponse,HTTP_RESULT_PARM_ERROR,NULL);
                                }
                                else
                                {
                                    strNetworkIpAddr = jsNetworkIpAddr->valuestring;
                                    strNetworkSubNetMask = jsNetworkSubNetMask->valuestring;
                                    strNetworkGateway = jsNetworkGateway->valuestring;
                                    strNetworkPrimaryDns = jsNetworkPrimaryDns->valuestring;
                                    strNetworkSecondaryDns = jsNetworkSecondaryDns->valuestring;
                                }
                            }
                            if(nNetworkMode == NETWORK_MODE_WAN)
                            {
                                if(jsNetworkServerAddr == NULL || jsNetworkServerPort == NULL)
                                {
                                    parm_error = true;
                                    http_web_response_normal(jsonResponse,HTTP_RESULT_PARM_ERROR,NULL);
                                }
                                else
                                {
                                    strNetworkServerAddr = jsNetworkServerAddr->valuestring;
                                    nNetworkServerPort = jsNetworkServerPort->valueint;
                                }
                            }

                            if(!parm_error)
                            {
                                bool bNetworkModeChanged=false;
                                bool bNetworkIpChanged=false;
                                //先设置网络模式，再设置IP(因为IP设置需要重启)
                                if( g_network_mode!=nNetworkMode ||\
                                    ( nNetworkMode == NETWORK_MODE_WAN &&\
                                    ( strcmp(strNetworkServerAddr,g_host_tcp_addr_domain)!=0 || nNetworkServerPort != g_host_tcp_port )
                                  )
                                  )
                                {
                                  printf("NetworkMode CHANGE!\n");

                                  //先设置空闲状态
                                  Set_zone_idle_status(NULL,  __func__, __LINE__,true);

                                  //如果旧的是TCP模式,新的是UDP，那么需要关闭TCP线程
                                  if(g_network_mode == NETWORK_MODE_WAN && g_network_mode!=nNetworkMode)
                                  {
                                    g_network_mode=nNetworkMode;
                                    #if !NETWORK_VPN_INTERNET
                                    g_Is_tcp_real_internet=0;
                                    #endif
                                    tcp_client_exit();
                                  }
                                  //如果新的是TCP，两种情况：1、之前是UDP（开启TCP线程），2、TCP参数变化了（重连）
                                  else if(nNetworkMode == NETWORK_MODE_WAN)
                                  {
                                    snprintf(g_host_tcp_addr_domain, sizeof(g_host_tcp_addr_domain), "%s", strNetworkServerAddr);
                                    printf("g_host_tcp_addr_domain:%s\n",g_host_tcp_addr_domain);
                                    if(nNetworkServerPort!=0)
                                    {
                                      g_host_tcp_port = nNetworkServerPort;
                                      if(	g_host_tcp_port != DEFAULT_TCP_PORT )
                                      {
                                        g_host_kcp_port = g_host_tcp_port + 1;
                                      }
                                      else
                                      {
                                        g_host_kcp_port = DEFAULT_KCP_PORT;
                                      }
                                    }
                                    printf("g_host_tcp_port:%d\n", g_host_tcp_port);

                                    if( g_host_device_TimeOut >=0 && g_host_device_TimeOut < HOST_TIMEOUT_VALUE-15 )
                                    {
                                      g_host_device_TimeOut =  HOST_TIMEOUT_VALUE-15;
                                      host_ready_offline_flag = 1;
                                      //关闭网络连接状态IO输出
                                      GPIO_OutPut_Server_Connection(0);
                                    }

                                    //如果之前是UDP,新的是TCP，那么重启TCP线程
                                    if(g_network_mode == NETWORK_MODE_LAN)
                                    {
                                      g_network_mode=nNetworkMode;
                                      TCP_Client_Start();
                                    }
                                    else
                                    {
                                      tcp_client_reconnect();
                                    }
                                  }
                                  
                                  //保存网络信息
                                  save_sysconf(INI_SETCION_NETWORK,NULL);
                                  bNetworkModeChanged=true;
                                }
                                else
                                {
                                  printf("NetworkMode NOT CHANGE!\n");
                                }

                                /************下面设置IP分配方式*/
                                if(nNetworkAssign == IP_ASSIGN_DHCP && g_IP_Assign == IP_ASSIGN_DHCP) //都为DHCP
                                {
                                  printf("nNetworkAssign=g_IP_Assign=IP_ASSIGN_DHCP,No changed!\n");
                                }
                                else
                                {
                                    if(nNetworkAssign == IP_ASSIGN_STATIC)
                                    {
                                      //检测IP、掩码、网关是否输入错误
                                      if(!NET_INFO_ERROR(strNetworkIpAddr, strNetworkSubNetMask, strNetworkGateway) && \
                                        !isGatewayByNetmask_Error(strNetworkIpAddr,strNetworkSubNetMask,strNetworkGateway))
                                      {
                                          if(strcmp(g_Static_ip_address, strNetworkIpAddr) == 0 && strcmp(g_Subnet_Mask, strNetworkSubNetMask) == 0 && strcmp(g_GateWay, strNetworkGateway) == 0 &&\
                                            (g_IP_Assign == nNetworkAssign) &&\
                                            strcmp(g_Primary_DNS, strNetworkPrimaryDns) == 0 && strcmp(g_Alternative_DNS, strNetworkSecondaryDns) == 0)
                                          {
                                            printf("NetworkIP:No parameter changed.%d\n", g_IP_Assign);
                                          }
                                          else
                                          {
                                              sprintf(g_Static_ip_address,"%s",strNetworkIpAddr);
                                              sprintf(g_Subnet_Mask,"%s",strNetworkSubNetMask);
                                              sprintf(g_GateWay,"%s",strNetworkGateway);

                                              //DNS
                                              if(if_a_string_is_a_valid_ipv4_address(strNetworkPrimaryDns))
                                              {
                                                sprintf(g_Primary_DNS,"%s",strNetworkPrimaryDns);
                                              }
                                              else
                                              {
                                                memset(g_Primary_DNS,0,sizeof(g_Primary_DNS));
                                              }
                                              if(if_a_string_is_a_valid_ipv4_address(strNetworkSecondaryDns))
                                              {
                                                sprintf(g_Alternative_DNS,"%s",strNetworkSecondaryDns);
                                              }
                                              else
                                              {
                                                memset(g_Alternative_DNS,0,sizeof(g_Alternative_DNS));
                                              }
                                              bNetworkIpChanged=true;
                                          }
                                      }
                                      else
                                      {
                                          parm_error = true;
                                          http_web_response_normal(jsonResponse,HTTP_RESULT_PARM_ERROR,NULL);
                                      }
                                    }
                                    else
                                    {
                                      bNetworkIpChanged=true;
                                    }

                                    if(bNetworkIpChanged)
                                    {
                                        g_IP_Assign = nNetworkAssign;
                                        //保存网络信息
                                        save_sysconf(INI_SETCION_NETWORK,NULL);	
                                        System_Reboot_DelayMs(500);
                                    }
                                }

                                //发送网络应答
                                if(!parm_error)
                                {
                                  if(bNetworkIpChanged && bNetworkModeChanged)
                                  {
                                    printf("Web chanange network ip and mode ok!");
                                    http_web_response_normal(jsonResponse,HTTP_RESULT_CHANGE_NETWORK_IP_MODE_OK,NULL);
                                  }
                                  else if(bNetworkIpChanged)
                                  {
                                    printf("Web chanange network ip ok!");
                                    http_web_response_normal(jsonResponse,HTTP_RESULT_CHANGE_NETWORK_IP_OK,NULL);
                                  }
                                  else if(bNetworkModeChanged)
                                  {
                                    printf("Web chanange network mode ok!");
                                    http_web_response_normal(jsonResponse,HTTP_RESULT_CHANGE_NETWORK_MODE_OK,NULL);
                                  }
                                  else{
                                    printf("Web not change network ip and mode!");
                                    http_web_response_normal(jsonResponse,HTTP_RESULT_NOT_CHANGE_NETWORK_IP_MODE,NULL);
                                  }
                                }
                            }
                        }
                    }
                    else if(strcmp(cmd,"startPing") == 0)
                    {
                        if(json) {
                            cJSON* jsTarget = cJSON_GetObjectItem(json, "target");
                            cJSON* jsCount = cJSON_GetObjectItem(json, "count");
                            
                            if(jsTarget) {
                                char* pingTarget = jsTarget->valuestring;
                                int pingCount = 4; // Default count
                                
                                // Extract count if provided
                                if(jsCount && jsCount->type == cJSON_Number) {
                                    pingCount = jsCount->valueint;
                                    // Validate count range
                                    if(pingCount <= 0 || pingCount > 3600) {
                                        pingCount = 4; // Reset to default if out of range
                                    }
                                }
                                
                                // Start ping command
                                int pingStartResult = start_ping_command(pingTarget, pingCount);
                                
                                if(pingStartResult) {
                                    // Create response with success status
                                    cJSON* root = cJSON_CreateObject();
                                    cJSON_AddItemToObject(root, "code", cJSON_CreateNumber(HTTP_RESULT_OK));
                                    // Add a dummy pingId for backward compatibility with the client
                                    cJSON_AddItemToObject(root, "pingId", cJSON_CreateString("single-session"));
                                    cJSON_AddItemToObject(root, "msg", cJSON_CreateString("Ping started successfully"));
                                    
                                    char* jsonBuf = cJSON_Print(root);
                                    sprintf(jsonResponse, "%s", jsonBuf);
                                    
                                    free(jsonBuf);
                                    cJSON_Delete(root);
                                } else {
                                    http_web_response_normal(jsonResponse, HTTP_RESULT_FAILED, "Failed to start ping");
                                }
                            } else {
                                http_web_response_normal(jsonResponse, HTTP_RESULT_PARM_ERROR, "Missing target parameter");
                            }
                        } else {
                            http_web_response_normal(jsonResponse, HTTP_RESULT_PARM_ERROR, "Invalid JSON request");
                        }
                    }
                    else if(strcmp(cmd,"getPingResults") == 0)
                    {
                        // Get results for the single ping session
                        // We'll ignore any pingId sent by the client
                        int isCompleted = 0;
                        char* results = get_ping_results(&isCompleted);
                        
                        if(results) {
                            // Create response with ping results
                            cJSON* root = cJSON_CreateObject();
                            cJSON_AddItemToObject(root, "code", cJSON_CreateNumber(HTTP_RESULT_OK));
                            cJSON_AddItemToObject(root, "results", cJSON_CreateString(results));
                            cJSON_AddItemToObject(root, "completed", cJSON_CreateBool(isCompleted));
                            
                            char* jsonBuf = cJSON_Print(root);
                            sprintf(jsonResponse, "%s", jsonBuf);
                            
                            free(jsonBuf);
                            free(results);
                            cJSON_Delete(root);
                        } else {
                            http_web_response_normal(jsonResponse, HTTP_RESULT_FAILED, "Failed to get ping results");
                        }
                    }
                    else if(strcmp(cmd,"stopPing") == 0)
                    {
                        // Stop the ping session
                        // We'll ignore any pingId sent by the client
                        stop_ping_session();
                        http_web_response_normal(jsonResponse, HTTP_RESULT_OK, "Ping stopped");
                    }
                    else if(strcmp(cmd,"reboot") == 0)
                    {
                      http_web_response_normal(jsonResponse,HTTP_RESULT_OK,NULL);
                      System_Reboot_DelayMs(300);
                    }
                    else if(strcmp(cmd,"reset") == 0)
                    {
                      http_web_response_normal(jsonResponse,HTTP_RESULT_OK,NULL);

                      remove_file(BASIC_CONFIG_INI_FILE);
                      remove_file(DEVICE_CONFIG_INI_FILE);
                      remove_file(NETWORK_CONFIG_INI_FILE);
                      remove_file(DSP_EQ_CONFIG_INI_FILE);
                      remove_file(BLUETOOTH_CONFIG_INI_FILE);
                      remove_file(SEQUENCEPOWER_CONFIG_INI_FILE);
                      remove_file(FIRE_CONFIG_INI_FILE);
                      remove_file(INTERCOM_CONFIG_INI_FILE);
                      remove_file(TRIGGER_CONFIG_INI_FILE);
                      remove_file(MIXER_CONFIG_INI_FILE);
                      remove_file(PHONE_GATEWAY_CONFIG_INI_FILE);
                      remove_file(INI_SIP_INI_FILE);
                      remove_file(INI_INFORMATION_PUB_INI_FILE);
                      remove_file(INI_REMOTE_INI_FILE);
                      System_Reboot_DelayMs(300);
                    }
                }
            }

            if(body)
            {
              free(body);
            }
            if(json)
            {
              free(json);
            }

            if(strlen(jsonResponse)>0)
            {
              mg_http_reply(c, 200, "", jsonResponse);
            }
        }
      }
      else if (mg_vcmp(&hm->method, "GET") == 0) 
      {
        printf("GET:cmd=%s\n",cmd);
        char session[128]={0};
        extract_session(session,hm);
        //printf("GET:session=%s,g_login_token=%s\n",session,g_login_token);
        if(strcmp(session,g_login_token) || strlen(g_login_token) == 0)
        {
          http_web_response_normal(jsonResponse,HTTP_RESULT_SESSION_EXPIRED,NULL);
        }
        else
        {
            if(strcmp(cmd,"checklogin") == 0)
            {
              http_web_response_normal(jsonResponse,HTTP_RESULT_OK,NULL);
            }
            else if(strcmp(cmd,"deviceInfo") == 0)
            {
              http_web_response_get_deviceInfo(jsonResponse,HTTP_RESULT_OK,NULL);
            }
            else if(strcmp(cmd,"sipInfo") == 0)
            {
              http_web_response_get_sipInfo(jsonResponse,HTTP_RESULT_OK,NULL);
            }
            else if(strcmp(cmd,"sipRegStatus") == 0)
            {
              http_web_response_get_sipRegStatus(jsonResponse,HTTP_RESULT_OK,NULL);
            }
            else if(strcmp(cmd,"networkInfo") == 0)
            {
              http_web_response_get_networkInfo(jsonResponse,HTTP_RESULT_OK,NULL);
            }
            else if(strcmp(cmd,"terminalInfo") == 0)
            {
              http_web_response_get_terminalInfo(jsonResponse,HTTP_RESULT_OK,NULL);
            }
            else if(strcmp(cmd,"reboot") == 0)
            {
              http_web_response_normal(jsonResponse,HTTP_RESULT_OK,NULL);
              System_Reboot_DelayMs(300);
            }
            else if(strcmp(cmd,"reset") == 0)
            {
              http_web_response_normal(jsonResponse,HTTP_RESULT_OK,NULL);

              remove_file(BASIC_CONFIG_INI_FILE);
              remove_file(DEVICE_CONFIG_INI_FILE);
              remove_file(NETWORK_CONFIG_INI_FILE);
              remove_file(DSP_EQ_CONFIG_INI_FILE);
              remove_file(BLUETOOTH_CONFIG_INI_FILE);
              remove_file(SEQUENCEPOWER_CONFIG_INI_FILE);
              remove_file(FIRE_CONFIG_INI_FILE);
              remove_file(INTERCOM_CONFIG_INI_FILE);
              remove_file(TRIGGER_CONFIG_INI_FILE);
              remove_file(MIXER_CONFIG_INI_FILE);
              remove_file(PHONE_GATEWAY_CONFIG_INI_FILE);
              remove_file(INI_SIP_INI_FILE);
              remove_file(INI_INFORMATION_PUB_INI_FILE);
              remove_file(INI_REMOTE_INI_FILE);
              System_Reboot_DelayMs(300);
            }
        }

        if(strlen(jsonResponse)>0)
        {
          mg_http_reply(c, 200, "", jsonResponse);
        }
      }
    }
    else {
      printf("Serve Web...\n");
      // Serve web root directory
      #if 0 //20240824 禁止显示服务器目录文件
      struct mg_http_serve_opts opts = {0};
      opts.root_dir = s_root_dir;
      opts.ssi_pattern = s_ssi_pattern;
      mg_http_serve_dir(c, hm, &opts);
      #endif

#if 1
      char path[512]={0};
      //snprintf(path,hm->uri.len,hm->uri.ptr);
      strncpy(path,hm->uri.ptr,hm->uri.len);
      //printf("hm->uri.len=%d,path=%s\n",hm->uri.len,path);
      if(strcmp(path,"/") == 0)
      {
        strcat(path,"index.html");
      }
      const char *root = HTTP_WEB_DIR; // 设置文件根目录
      char full_path[512]={0}; // 定义一个足够大的缓冲区来存储完整路径
      // 构建文件的完整路径
      snprintf(full_path, sizeof(full_path), "%s%s", root, path);

      char htmlPath[128]={0};
      sprintf(htmlPath,"%s/index.html",HTTP_WEB_DIR);

      //printf("web:request full_path=%s,htmlPath=%s\n",full_path,htmlPath);

      #if 0
            //任何时候都是重定向到
      if (mg_http_match_uri(hm, "/config")) {
          mg_http_reply(c, 302, "Location: /\r\n", ""); // 重定向到 /
      }
      else
      #endif
      {
        // 检查文件是否存在
        if (IsFileExist(full_path)) { // 如果文件存在
        struct mg_http_serve_opts opts = {0};
          mg_http_serve_file(c, hm, full_path, &opts); // 服务文件
        } else {
          #if 0
          // 发送 404 错误响应
          const char *status = "HTTP/1.1 404 Not Found\r\n";
          const char *headers = "Content-Type: text/plain\r\nConnection: close\r\n";
          const char *body = "File not found\n";
          mg_http_reply(c, 404, headers, body);
          #else
          //返回html文件
          struct mg_http_serve_opts opts = {0};
          mg_http_serve_file(c, hm, htmlPath, &opts); // 服务文件
          #endif
        }
      }
    }
#endif

    c->is_draining = 1;     // 在处理完成后，强制关闭连接，避免keep-alive复用的问题
  }
}


static void mg_hfn(struct mg_connection *c, int ev, void *ev_data) {
  if (ev == MG_EV_HTTP_MSG) {
    struct mg_http_message *hm = (struct mg_http_message *) ev_data;
    if (mg_http_match_uri(hm, "/quit")) {
      mg_http_reply(c, 200, "", "ok\n");
      c->is_draining = 1;
      c->data[0] = 'X';
    } else if (mg_http_match_uri(hm, "/v1/speech")) {
      int level = (int) mg_json_get_long(hm->body, "$.level", MG_LL_DEBUG);
      mg_log_set(MG_LL_VERBOSE);
      mg_http_reply(c, 200, "", "Debug level set to %d\n", level);
      mg_printf(c, "Received POST data: %.*s", (int) hm->body.len, hm->body.ptr);
    } else {
      mg_http_reply(c, 200, "", "hi\n");
    }
  } else if (ev == MG_EV_CLOSE) {
    if (c->data[0] == 'X') *(bool *) c->fn_data = true;
  }
}


static void usage(const char *prog) {
  fprintf(stderr,
          "Mongoose v.%s\n"
          "Usage: %s OPTIONS\n"
          "  -H yes|no - enable traffic hexdump, default: '%s'\n"
          "  -S PAT    - SSI filename pattern, default: '%s'\n"
          "  -d DIR    - directory to serve, default: '%s'\n"
          "  -l ADDR   - listening address, default: '%s'\n"
          "  -u DIR    - file upload directory, default: unset\n"
          "  -v LEVEL  - debug level, from 0 to 4, default: %d\n",
          MG_VERSION, prog, s_enable_hexdump, s_ssi_pattern, s_root_dir,
          s_tts_listening_address, s_debug_level);
  exit(EXIT_FAILURE);
}

void *mg_httpServer_test() {
  char path[MG_PATH_MAX] = ".";
  struct mg_mgr mgr;
  struct mg_connection *c1,*c2;
  int i;

  // Initialize ping session tracking
  init_ping_tracking();

  // Root directory must not contain double dots. Make it absolute
  // Do the conversion only if the root dir spec does not contain overrides
  if (strchr(s_root_dir, ',') == NULL) {
    realpath(s_root_dir, path);
    s_root_dir = path;
  }

#if 0
  // Initialise stuff
  signal(SIGINT, signal_handler);
  signal(SIGTERM, signal_handler);
#endif

  mg_log_set(s_debug_level);
  mg_mgr_init(&mgr);

  const char *url = s_tts_listening_address;
  if ((c1 = mg_http_listen(&mgr, url, tts_cb, &mgr)) == NULL) {
    MG_ERROR(("Cannot listen on %s. Use http://ADDR:PORT or :PORT",
              url));
    exit(EXIT_FAILURE);
  }
  //if (mg_casecmp(s_enable_hexdump, "yes") == 0) c->is_hexdumping = 1;

  // Start infinite event loop
  //MG_INFO(("Mongoose version : v%s", MG_VERSION));
  MG_INFO(("Listening on     : %s", url));
  //MG_INFO(("Web root         : [%s]", s_root_dir));

  const char *url2 = s_web_listening_address;
  if ((c2 = mg_http_listen(&mgr, url2, web_cb, &mgr)) == NULL) {
    MG_ERROR(("Cannot listen on %s. Use http://ADDR:PORT or :PORT",
              url2));
    exit(EXIT_FAILURE);
  }
  MG_INFO(("Listening on     : %s", url2));

  while (s_signo == 0) mg_mgr_poll(&mgr, 100);
  mg_mgr_free(&mgr);
  MG_INFO(("Exiting on signal %d", s_signo));

  return 0;
}



/*********************************************************************
 * @fn      start_mg_httpServer_pthread
 *
 * @brief   启动mg http服务器线程
 *
 * @param   void
 *
 * @return	void
 */
void start_mg_httpServer_pthread(void)
{
	int ret = -1;
	pthread_t Mcast_Pthread;
	pthread_attr_t Pthread_Attr;

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Mcast_Pthread, &Pthread_Attr, (void *)mg_httpServer_test, NULL);
	if (ret < 0)
	{
		printf("start_mg_httpServer_pthread create failed!!!\n");
	}
	else
	{
		printf("start_mg_httpServer_pthread create success!\n");
	}
	pthread_attr_destroy(&Pthread_Attr);
}




#endif