#ifndef _AUDIO_MIXER_PROCESS_H_
#define _AUDIO_MIXER_PROCESS_H_

#define AUDIO_MIXER_SAMPLERATE  32000

#define MAX_AUDIO_MIXER_PKG_NUM     4
#define MAX_AUDIO_MIXER_CHANNEL_NUM 4
#define MAX_AUDIO_MIXER_DATA_SIZE   1024

/*******音频混音器相关变量************/
typedef struct {
    unsigned char m_nMasterSwitch;          //主开关（0/1，默认为0关闭）
    unsigned char m_nPriority;              //优先级(1~9)，高优先级设备可以打断低优先级设备，与寻呼音源类似,默认为1
    unsigned char m_nTriggerType;           //触发类型(1~3) 1：混合触发 2：MIC 3：AUX，默认为1：混合触发
    unsigned char m_nTriggerSensitivity;    //信号触发灵敏度（1~9，数值越大，灵敏度越高）
    unsigned char m_nVolumeFadeLevel;       //有MIC信号时，网络信号、AUX信号的淡化级别（0~9） 默认为5:-15dB，0不淡化，1:-3dB，2：-6dB，3：-9dB，4：-12dB，5:-16dB，6：-19dB，7：-21dB，8：-24dB，9：-27dB
    unsigned char m_nVolume;                //混音音量
    unsigned char m_nDelayMode;             //延时模式（0：低延时模式，1：标准模式  2：高延时模式） 默认为1：标准模式(暂时固定)
    unsigned char m_nSignalValid[3];        //信号有效 0：Network 1：Mic 2：Aux
}st_audio_mixer_info;
extern st_audio_mixer_info audioMixer_info;

extern int g_device_audio_mixer_ready;			//混音器准备好可以发送数据
/*******音频混音器相关变量************/

typedef struct
{
    //unsigned char isValid;  //是否有效
    unsigned char buf[MAX_AUDIO_MIXER_DATA_SIZE];   //缓存大小
    int len;
}_stAudioMixerBuf;

typedef struct
{
    int write_pos;
    int read_pos;
    _stAudioMixerBuf stAudioMixerBuf[MAX_AUDIO_MIXER_PKG_NUM];
}_stAudioMixerData;

void audioMixer_clear();
void audioMixer_Data_Add(signed short int *AllChannelBuf,unsigned int BufferLen);
void start_audioMixer_data_process_pthread(void);

#endif