#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <semaphore.h>
#include "sysconf.h"
#include "../network/udp_client.h"
#include "../network/network_process.h"
#include "AudioMixerProcess.h"

#if(IS_DEVICE_AUDIO_MIXER)

/*******音频混音器相关变量************/
st_audio_mixer_info audioMixer_info;

int g_device_audio_mixer_ready;			// 混音器准备好可以发送数据
/*******音频混音器相关变量************/


static _stAudioMixerData stAudioMixerData;
static pthread_mutex_t mutex_DataPkg=PTHREAD_MUTEX_INITIALIZER;	//采集音频包锁
static sem_t sem_DataPkgRecv;  	//信号量-接收到音频采集包

static unsigned char sendDataBuf[MAX_AUDIO_MIXER_DATA_SIZE];


#if 0
// 256/32000 = 8ms,pkg size = 256*2*1 = 512字节，如果是写入ao，那么数据量为256*2*2 = 1024字节；
// 每收到两个包，开始发送？因为终端处理那边原来是1024字节；
// 新版本如何处理?如果按照新的规则，那么旧的终端将不支持，这样不太好。
/*********************************************************************
 * @fn      audioMixer_Data_Add
 *
 * @brief   音频混音数据加入
 *
 * @param   void
 *
 * @return	void
 */
void audioMixer_Data_Add(signed short int *AllChannelBuf,unsigned int BufferLen)
{
	if(BufferLen > MAX_AUDIO_MIXER_DATA_SIZE)
		return;
	//pthread_mutex_lock(&mutex_DataPkg);
    
	memcpy(stAudioMixerData.stAudioMixerBuf[stAudioMixerData.write_pos].buf,AllChannelBuf,BufferLen);
    
	stAudioMixerData.stAudioMixerBuf[stAudioMixerData.write_pos].len = BufferLen;

	//printf("audioMixer_Data_Add:len=%d\n",BufferLen);
	int bufPos=0;
	stAudioMixerData.write_pos++;
	#if 0
	if( stAudioMixerData.write_pos%2 == 0)
	{
		bufPos = BufferLen;
	}
	#endif

	memcpy(sendDataBuf+bufPos,stAudioMixerData.stAudioMixerBuf[stAudioMixerData.write_pos].buf,BufferLen);
	bufPos+=BufferLen;
#if 1	//其实这里可以直接发送，不需要用到线程
	#if 0
	if( stAudioMixerData.write_pos%2 == 0)
	#endif
	{
		SendAudioMixerStreamToMultiCast(sendDataBuf,bufPos);
		SendAudioMixerStreamToServer(sendDataBuf,bufPos);
	}
#endif

	if(stAudioMixerData.write_pos >= MAX_AUDIO_MIXER_PKG_NUM )
	{
		stAudioMixerData.write_pos = 0;
	}
	//sem_post(&sem_DataPkgRecv);

	//pthread_mutex_unlock(&mutex_DataPkg);
}
#endif


// 256/32000 = 8ms,pkg size = 256*2*1 = 512字节，如果是写入ao，那么数据量为256*2*2 = 1024字节；
// 每收到两个包，开始发送？因为终端处理那边原来是1024字节；
// 新版本如何处理?如果按照新的规则，那么旧的终端将不支持，这样不太好。

void audioMixer_clear()
{
	memset(&stAudioMixerData,0,sizeof(stAudioMixerData));
}

/*********************************************************************
 * @fn      audioMixer_Data_Add
 *
 * @brief   音频混音数据加入
 *
 * @param   void
 *
 * @return	void
 */
void audioMixer_Data_Add(signed short int *AllChannelBuf,unsigned int BufferLen)
{
	if(BufferLen > MAX_AUDIO_MIXER_DATA_SIZE)
		return;
    int i=0;
	pthread_mutex_lock(&mutex_DataPkg);
    
	memcpy(stAudioMixerData.stAudioMixerBuf[stAudioMixerData.write_pos].buf+i,AllChannelBuf,BufferLen);
    
	stAudioMixerData.stAudioMixerBuf[stAudioMixerData.write_pos].len = BufferLen;

	//printf("audioMixer_Data_Add:len=%d\n",BufferLen);
	stAudioMixerData.write_pos++;
	if(stAudioMixerData.write_pos >= MAX_AUDIO_MIXER_PKG_NUM )
	{
		stAudioMixerData.write_pos = 0;
	}
	sem_post(&sem_DataPkgRecv);
	pthread_mutex_unlock(&mutex_DataPkg);
}


/*********************************************************************
 * @fn      audioMixer_data_process
 *
 * @brief   音频混音数据处理
 *
 * @param   void
 *
 * @return	void
 */
void *audioMixer_data_process(void)
{
    unsigned int PkgCnt=0;
	unsigned char sendDataBuf[MAX_AUDIO_MIXER_DATA_SIZE];
	while(1)
	{
		sem_wait(&sem_DataPkgRecv);

		int bufLen=stAudioMixerData.stAudioMixerBuf[stAudioMixerData.read_pos].len;
		static int bufPos=0;
		if(PkgCnt%2 == 0)
		{
			bufPos = 0;
		}
		memcpy(sendDataBuf+bufPos,stAudioMixerData.stAudioMixerBuf[stAudioMixerData.read_pos].buf,bufLen);
		bufPos+=bufLen;

		if(PkgCnt%2 && (g_audio_mixer_signal_valid && g_device_audio_mixer_ready))
		{
			SendAudioMixerStreamToMultiCast(sendDataBuf,bufPos);
			SendAudioMixerStreamToServer(sendDataBuf,bufPos);
		}
		
		
		PkgCnt++;

		memset(&stAudioMixerData.stAudioMixerBuf[stAudioMixerData.read_pos], 0, sizeof(_stAudioMixerBuf));

		stAudioMixerData.read_pos++;
		if(stAudioMixerData.read_pos >= MAX_AUDIO_MIXER_PKG_NUM)
		{
			stAudioMixerData.read_pos = 0;
		}

	}
	pthread_exit(NULL);
}

/*********************************************************************
 * @fn      start_audioMixer_data_process_pthread
 *
 * @brief   启动音频混音数据处理线程
 *
 * @param   void
 *
 * @return	void
 */
void start_audioMixer_data_process_pthread(void)
{
	sem_init(&sem_DataPkgRecv,0,0);

	int ret = -1;
	pthread_t Mcast_Pthread;
	pthread_attr_t Pthread_Attr;

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Mcast_Pthread, &Pthread_Attr, (void *)audioMixer_data_process, NULL);
	if (ret < 0)
	{
		printf("start_audioMixer_data_process_pthread create failed!!!\n");
	}
	else
	{
		printf("start_audioMixer_data_process_pthread create success!\n");
	}
	pthread_attr_destroy(&Pthread_Attr);
}





#endif
