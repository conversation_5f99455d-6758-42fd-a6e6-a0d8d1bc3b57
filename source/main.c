/*
 * @Author: <PERSON><PERSON>.<PERSON> 
 * @Date: 2021-09-03 15:46:24 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2025-01-11 14:18:02
 */
#include <linux/watchdog.h>
#include "sysconf.h"
#include "network/udp_client.h"
#include "network/multicast.h"
#include "network_process.h"
#include "sigmastar/mi_sar.h"
#include "hardware/es7210.h"
#include "win/rotaryEncoder.h"
#if LZY_CUSTOMER_MQTT
#include "mqtt_client/mqtt_client.h"
#endif

/*****************注意事项***********************
yaffs2文件系统没有缓冲，所以不涉及此类问题
1. 如果需要再描述符关闭前，将数据同步刷新到介质，需要调用fync接口，尤其针对一些关键的数据（丢失会引起严重问题的数据）；
2. fopen方式打开的，如果需要调用fsync，正确的调用顺序为：fopen -> fwrite -> fflush -> fsync -> fclose
3. open方式打开的，如果需要调用到fsync，正确的调用顺序为：open -> write -> fsync -> close

***********************************************/

void system_loop_pthread(void);

unsigned int sysRunTime=0;	//系统运行时间（秒）
unsigned char g_sysBootType=3;	//系统启动类型（1：开门狗重启 2：系统复位 3：断电重启) ,默认为3
unsigned char power_on_flag = 0;//仅上电首次通知主机标志

/***************网络相关定义 START **************/
char g_ipAddress[20];          //IP地址
unsigned char g_mac_addr[6];   //MAC ADDR
unsigned char network_init_flag=0;	   //网络初始化完成标志

int g_network_mode=NETWORK_MODE_LAN;	//网络模式
char g_host_tcp_addr_domain[64];	//主机地址(域名或者IP)
char g_host_tcp_prase_ipAddress[16];		//解析后的主机IP
int g_host_tcp_port;				//主机TCP端口
int g_host_kcp_port;        		//KCP端口

int g_current_connect_tcp_id=1;  //当前连接的tcp_id，默认是1-表示主服务器，2表示备用服务器
char g_host_tcp_addr_domain2[64];	//主机地址2(域名或者IP)
int g_host_tcp_port2;				//主机TCP端口2
int g_host_kcp_port2;        		//KCP端口2

int eth_link_status=0;				//有线网卡连接状态

/*****IP属性 ************/
int  g_IP_Assign = IP_ASSIGN_STATIC;	//IP分配方式(static or DHCP)
char g_Static_ip_address[32];		//静态IP地址
char g_Subnet_Mask[32];				//子网掩码
char g_GateWay[32];					//网关
char g_Primary_DNS[32];				//主DNS服务器
char g_Alternative_DNS[32];			//备用DNS服务器

/***************网络相关定义 END **************/

/**************工作模式相关定义 START************/
unsigned int g_system_work_mode = WORK_MODE_CONCENTRATED;
/**************工作模式相关定义 END************/

/**********设备信息START*****************/
unsigned char g_device_alias[128];	//设备别名
/*********设备信息END******************/

/**********基础信息START****************/
unsigned int g_system_volume = DEFAULT_SYSTEM_VOLUME;	//系统音量
unsigned int g_pre_system_volume = DEFAULT_SYSTEM_VOLUME;		//系统前一音量
unsigned int g_sub_volume = DEFAULT_SYSTEM_VOLUME;		//子音量
unsigned int g_volumeCD_aux_volume=DEFAULT_SYSTEM_VOLUME;			//音控器本地音量(存在时才有效)
unsigned int g_volumeCD_net_volume=DEFAULT_SYSTEM_VOLUME;			//音控器网络音量(存在时才有效)
//20241116 支持本地音量设置
unsigned int g_aux_volume=DEFAULT_SYSTEM_VOLUME;			//本地音量

//20250305新增线路音量和麦克风音量
unsigned int g_lineVolume = DEFAULT_SYSTEM_VOLUME;	//线路音量
unsigned int g_micVolume = DEFAULT_SYSTEM_VOLUME;	//麦克风音量

#if YIHUI_VERSION
bool bMicInsert = false;	//默认麦克风未插入
#else
bool bMicInsert = true;		//默认麦克风插入
#endif

unsigned int g_timing_volume = -1;            //定时音量（记录正在定时播放时调节的音量）

signed char host_ready_offline_flag=1;			   // 主机即将离线标志
signed char g_host_device_TimeOut = -1;            //主机离线计数,-1代表已经离线

unsigned int g_paging_status = PAGING_STOP;		   // 寻呼状态
unsigned int g_media_source = SOURCE_NULL;		   // 音源状态
unsigned char g_media_name[128] = {0};//当前节目名称
unsigned char g_media_status = SONG_STOP;	//媒体播放状态

unsigned char g_terminal_control_mode = Terminal_AutoControl;	//程控 手控0x10

int concentrate_repeat_paly_enable = 1; //是否允许播放集中模式下重新请求播放音乐

int paging_repeat_again_enable = 1;     //是否允许重新寻呼

int mixed_source_repeat_again_enable = 1;     //是否允许重新进入混音音源

int phone_gateway_source_repeat_again_enable = 1;     //是否允许重新进入电话网关音源

int g_signal_100v=0;   			//100V信号
int g_signal_aux=0;   //Aux信号

#if NETWORK_VPN_INTERNET
int g_Is_tcp_real_internet=1;      //TCP模式是否处于internet公网上（主机IP是169开头或者192开头代表是内网TCP)
#else
int g_Is_tcp_real_internet=0;      //TCP模式是否处于internet公网上（主机IP是169开头或者192开头代表是内网TCP)
#endif

unsigned char FireAlarm_Status = 0;	//消防告警状态
/**********基础信息END*****************/


/*******集中模式变量************/
unsigned char g_concentrated_song_type = 1;	//集中模式-歌曲类型	1为MP3 2为WAV
unsigned int  g_concentrated_song_sample_rate=44100;	//集中模式-歌曲采样率
unsigned int  g_concentrated_song_fmt=16;	//集中模式-歌曲采样精度
unsigned int  g_concentrated_song_channels=2;	//集中模式-歌曲声道数

unsigned char g_concentrated_multicast_address[16];	//集中模式-组播地址
unsigned int  g_concentrated_multicast_port;		//集中模式-组播端口

unsigned int g_concentrated_need_exit_flag = 0;		//集中模式 退出标志 0-不需要  1-需要退出
unsigned int g_concentrated_start=0;				//集中模式启动标志	
unsigned int g_concentrated_playing = 0;		    //集中模式歌曲正在播放

int g_centralized_mode_timeout_count=0;			//集中模式传输超时计数
int g_centralized_mode_timing_repeat=0;         //集中模式传输超时重复次数，连续5次代表确实收不到组播数据，应该停止重发
int g_centralized_mode_timeout_pause=1;			//集中模式播歌超时检测暂停标志

int g_centralized_mode_is_existLocalSong;		//集中模式是否存在本地歌曲

bool g_IsCentralized_mode_multicast_new_cmd=false;		//集中模式组播播放是否采用新的命令

/*******音频采集变量************/
unsigned int g_ac_sample_rate = 0;							//音频采集器采样率
unsigned int g_ac_channels=1;								//音频采集器通道数
unsigned char g_ac_multicast_address[16]={"**************"};					//音频采集-组播地址
unsigned int g_ac_mcast_port;								//音频采集器的组播端口
unsigned char g_ac_source_id = SOURCE_AUDIO_COLLECTOR_BASE;	//音频采集器音源ID
unsigned char g_ac_source_priority = 1;	//音频采集音源优先级（1-默认，低于定时，2-高优先级，高于定时）

int g_collector_run_flag=0;				//音频采集器运行标志
int g_ac_timing_count=0;				//音频采集器超时计数
int g_ac_timing_wait=1;					//音频超时检测允许标志
int g_ac_exit_flag=0;					//音频采集退出标志 0-不需要  1-需要退出

int g_paging_timing_count=0;			//寻呼数据超时检测计数

int amp_init_ok=0;						//功放初始化成功
int adc_signal_can_detect=0;			//本地信号可以检测标志（开机ADC刚打开的时候不稳定，此时不能进行检测）
int wtg_fd=-1;

char g_device_serialNum[20]={0};    	//设备序列号

int  g_device_moduleId=0;	//设备型号ID

int g_NetAudioSignal_can_close=1;     //网络信号状态是否能够关闭

int g_allow_localSource=1;				//是否允许本地音源，收到在线音源指令时将其置于0，定时计数恢复，避免收到播放指令，还有短暂本地音乐
/************系统时间************/
unsigned char sys_date_buf[20] = {"2022-01-01"};	//系统日期
unsigned char sys_time_buf[20] = {"00:00:00"};		//系统时间
unsigned char HasGotSysTime=0;		     //是否已经获取到系统时间
/****************************/


/***********混音器**********/
#if IS_DEVICE_AUDIO_MIXER
int g_audio_mixer_signal_valid=0;		//音频混音器信号是否有效
unsigned char g_mixer_mac_addr[6];   	//MAC ADDR 音频混音器mixer的MAC地址（2B开头）
#endif
int g_audio_mixer_stream_timing_count=0;//音频混音器数据流超时检测计数

unsigned char g_audio_mixer_mac[6];		//混音器MAC
unsigned char g_audio_mixer_priority;	//混音器优先级
unsigned char g_audio_mixer_volume;		//混音器音量
unsigned int  g_audio_mixer_sampleRate;	//混音器采样率
char g_audio_mixer_broadcast_addr[32];	//混音器广播地址
int g_audio_mixer_broadcast_port;		//混音器广播端口
unsigned char g_audio_mixer_codecs;		//混音器编码算法
unsigned char g_audio_mixer_signalType=1;	//混音器信号类型	1MIC信号 2-AUX信号
/******************************/

/***********电话网关**********/
#if IS_DEVICE_PHONE_GATEWAY
int g_phone_gateway_signal_valid=0;		//电话网关信号是否有效
#endif

int g_phone_gateway_stream_timing_count=0;	//电话网关数据流超时检测计数

unsigned char g_phone_gateway_mac[6];		//电话网关MAC
unsigned char g_phone_gateway_volume;		//电话网关音量
unsigned int  g_phone_gateway_sampleRate;	//电话网关采样率
char g_phone_gateway_broadcast_addr[32];	//电话网关广播地址
int g_phone_gateway_broadcast_port;		  	//电话网关广播端口
unsigned char g_phone_gateway_codecs;		//电话网关编码算法
/******************************/

#if DECODER_LZY_VERESION_FM_C4A1
int g_lzy_fm_signal_detect=0;			//龙之音调频信号检测状态		
#endif

#if (SUPPORT_UART_LED_PLAYER_MODE == 1)
bool is_Enable_UART_LED_PLAYER=1;		//是否支持LED屏
#else
bool is_Enable_UART_LED_PLAYER=0;		//是否支持LED屏
#endif

int	g_system_language = CHINESE;		//系统语言

int g_ApiPlayType=API_PLAY_NULL;     //API播放类型(0表示没有，1表示正在播放TTS，2表示正在播放URL音乐)


#define REMOTE_CONTROLER_DEFAULT_ADDRCODE	170	//远程遥控器地址码默认值
int g_remote_controler_addressCode=REMOTE_CONTROLER_DEFAULT_ADDRCODE;	//远程遥控器地址码
int g_remote_controler_keyCode=0;		//远程遥控器按键码

int g_isPlayIPTone=0;       //是否正在播报IP

char module4g_primary_dns[16]="*******";
char module4g_secondary_dns[16]="*************";
char mqtt_dns_url[32]="udp://*******:53";

void init_sysconf()
{
	char create_config_path[64]={0};
	sprintf(create_config_path,"mkdir -p %s",CONFIG_DIR_PATH);
	pox_system(create_config_path);
}

void* save_sysconf(char *section,char *key)
{
	#ifdef USE_PC_SIMULATOR
	return NULL;
	#endif
	
	printf("save_sysconf:sction=%s,key=%s\n",section,key?key:"");
	if( strcmp(section,INI_SETCION_NETWORK) == 0 )
	{
		ini_putl(INI_SETCION_NETWORK,"Connect_mode",g_network_mode,NETWORK_CONFIG_INI_FILE);
		ini_puts(INI_SETCION_NETWORK,"TcpHost_addr",g_host_tcp_addr_domain,NETWORK_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_NETWORK,"TcpHost_port",g_host_tcp_port,NETWORK_CONFIG_INI_FILE);

		ini_puts(INI_SETCION_NETWORK,"TcpHost_addr2",g_host_tcp_addr_domain2,NETWORK_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_NETWORK,"TcpHost_port2",g_host_tcp_port2,NETWORK_CONFIG_INI_FILE);

		ini_putl(INI_SETCION_NETWORK,"Ip_mode",g_IP_Assign,NETWORK_CONFIG_INI_FILE);
		ini_puts(INI_SETCION_NETWORK,"Static_IP",g_Static_ip_address,NETWORK_CONFIG_INI_FILE);
		ini_puts(INI_SETCION_NETWORK,"Subnet_mask",g_Subnet_Mask,NETWORK_CONFIG_INI_FILE);
		ini_puts(INI_SETCION_NETWORK,"GateWay",g_GateWay,NETWORK_CONFIG_INI_FILE);
		ini_puts(INI_SETCION_NETWORK,"Primary_DNS",g_Primary_DNS,NETWORK_CONFIG_INI_FILE);
		ini_puts(INI_SETCION_NETWORK,"Alternative_DNS",g_Alternative_DNS,NETWORK_CONFIG_INI_FILE);
	}
	else if( strcmp(section,INI_SECTION_DEVICE) == 0 )
	{
		if(key == NULL || strcmp(key,"Device_Name") == 0)
			ini_puts(INI_SECTION_DEVICE,"Device_Name",g_device_alias,DEVICE_CONFIG_INI_FILE);
		if(key == NULL || strcmp(key,"System_Language") == 0)
			ini_putl(INI_SECTION_DEVICE,"System_Language",g_system_language,DEVICE_CONFIG_INI_FILE);	
		if(key == NULL || strcmp(key,"Display_Brightness") == 0)
			ini_putl(INI_SECTION_DEVICE,"Display_Brightness",g_backlight_level,DEVICE_CONFIG_INI_FILE);
	}

	else if( strcmp(section,INI_SECTION_BASIC) == 0 )
	{
		if(key == NULL || strcmp(key,"System_Volume") == 0)
			ini_putl(INI_SECTION_BASIC,"System_Volume",g_system_volume,BASIC_CONFIG_INI_FILE);
		if(key == NULL || strcmp(key,"Exist_DispModule") == 0)
			ini_putl(INI_SECTION_BASIC,"Exist_DispModule",g_PreExistDispModule,BASIC_CONFIG_INI_FILE);
		if(key == NULL || strcmp(key,"Sub_Volume") == 0)
			ini_putl(INI_SECTION_BASIC,"Sub_Volume",g_sub_volume,BASIC_CONFIG_INI_FILE);
		if(key == NULL || strcmp(key,"VolumeControl_Aux") == 0)
			ini_putl(INI_SECTION_BASIC,"VolumeControl_Aux",g_volumeCD_aux_volume,BASIC_CONFIG_INI_FILE);
		if(key == NULL || strcmp(key,"VolumeControl_Net") == 0)
			ini_putl(INI_SECTION_BASIC,"VolumeControl_Net",g_volumeCD_net_volume,BASIC_CONFIG_INI_FILE);	
		if(key == NULL || strcmp(key,"Aux_Volume") == 0)
			ini_putl(INI_SECTION_BASIC,"Aux_Volume",g_aux_volume,BASIC_CONFIG_INI_FILE);

		if(key == NULL || strcmp(key,"Line_Volume") == 0)
			ini_putl(INI_SECTION_BASIC,"Line_Volume",g_lineVolume,BASIC_CONFIG_INI_FILE);
		if(key == NULL || strcmp(key,"Mic_Volume") == 0)
			ini_putl(INI_SECTION_BASIC,"Mic_Volume",g_micVolume,BASIC_CONFIG_INI_FILE);
	}

	#ifndef USE_PC_SIMULATOR
	else if( strcmp(section,INI_SETCION_DSP_Firmware) == 0 )
	{
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_MODEL_ID,g_device_moduleId,DSP_FIRMWARE_CONFIG_INI_FILE);

		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC0_SWITCH,dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC0_GAIN,dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC1_SWITCH,dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC1_GAIN,dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC2_SWITCH,dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC1_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC2_GAIN,dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC1_L],DSP_FIRMWARE_CONFIG_INI_FILE);

		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0L_SWITCH,dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0L_GAIN,dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0R_SWITCH,dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0R_GAIN,dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
	}

	else if( strcmp(section,INI_SETCION_DSP_EQ) == 0 )
	{
		ini_putl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_MODE,dsp_eq_info.eq_mode,DSP_EQ_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN1,dsp_eq_info.gain[0],DSP_EQ_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN2,dsp_eq_info.gain[1],DSP_EQ_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN3,dsp_eq_info.gain[2],DSP_EQ_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN4,dsp_eq_info.gain[3],DSP_EQ_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN5,dsp_eq_info.gain[4],DSP_EQ_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN6,dsp_eq_info.gain[5],DSP_EQ_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN7,dsp_eq_info.gain[6],DSP_EQ_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN8,dsp_eq_info.gain[7],DSP_EQ_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN9,dsp_eq_info.gain[8],DSP_EQ_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN10,dsp_eq_info.gain[9],DSP_EQ_CONFIG_INI_FILE);
	}
	#endif

	else if( strcmp(section,INI_SETCION_MFR) == 0 )
	{
		if(key == NULL || strcmp(key,"SN") == 0)
			ini_puts(INI_SETCION_MFR,key,g_device_serialNum,MFR_CONFIG_INI_FILE);
	}

	else if( strcmp(section,INI_SETCION_BLUETOOTH) == 0 )
	{
		ini_puts(INI_SETCION_BLUETOOTH,INI_KEY_BLUETOOTH_NAME,g_bp1048_bt_info.name,BLUETOOTH_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_BLUETOOTH,INI_KEY_BLUETOOTH_HAS_PIN,g_bp1048_bt_info.hasPassword,BLUETOOTH_CONFIG_INI_FILE);
		ini_puts(INI_SETCION_BLUETOOTH,INI_KEY_BLUETOOTH_PIN,g_bp1048_bt_info.password,BLUETOOTH_CONFIG_INI_FILE);
	}
	else if( strcmp(section,INI_SECTION_TRIGGER) == 0 )
	{
		ini_putl(INI_SECTION_TRIGGER,"TriggerSwitch",st_triggerSong.trigger_switch,TRIGGER_CONFIG_INI_FILE);
		ini_putl(INI_SECTION_TRIGGER,"TriggerMode",st_triggerSong.trigger_mode,TRIGGER_CONFIG_INI_FILE);
		ini_puts(INI_SECTION_TRIGGER,"SongName",st_triggerSong.trigger_song_name,TRIGGER_CONFIG_INI_FILE);
		ini_puts(INI_SECTION_TRIGGER,"SongMd5",st_triggerSong.trigger_song_md5,TRIGGER_CONFIG_INI_FILE);
		ini_putl(INI_SECTION_TRIGGER,"PlayTimes",st_triggerSong.trigger_playTimes,TRIGGER_CONFIG_INI_FILE);
		ini_putl(INI_SECTION_TRIGGER,"Volume",st_triggerSong.trigger_volume,TRIGGER_CONFIG_INI_FILE);
	}

	#if(IS_DEVICE_POWER_SEQUENCE)
	else if( strcmp(section,INI_SETCION_SEQUENCEPOWER) == 0 )
	{
		ini_putl(INI_SETCION_SEQUENCEPOWER,"ControlMode",sequence_power_info.control_mode,SEQUENCEPOWER_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_SEQUENCEPOWER,"OpenDelay",sequence_power_info.open_delay_value,SEQUENCEPOWER_CONFIG_INI_FILE);
	}
	#endif

	#if(IS_DEVICE_FIRE_COLLECTOR)
	else if( strcmp(section,INI_SETCION_FIRE) == 0 )
	{
		ini_putl(INI_SETCION_FIRE,"TriggerMode",fire_collector_info.trigger_mode,FIRE_CONFIG_INI_FILE);
	}
	#endif

	#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
	else if( strcmp(section,INI_SECTION_INTERCOM) == 0 )
	{
		char key1_mac[32]={0};
		char key2_mac[32]={0};
		MacArrayToString(key1_mac,m_stCallDeviceConfig.Key1_mac);
		MacArrayToString(key2_mac,m_stCallDeviceConfig.Key2_mac);

		ini_puts(INI_SECTION_INTERCOM,"Key1_mac",key1_mac,INTERCOM_CONFIG_INI_FILE);
		ini_puts(INI_SECTION_INTERCOM,"Key2_mac",key2_mac,INTERCOM_CONFIG_INI_FILE);
		ini_putl(INI_SECTION_INTERCOM,"AutoAnswerTime",m_stCallDeviceConfig.AutoAnswerTime,INTERCOM_CONFIG_INI_FILE);
		ini_putl(INI_SECTION_INTERCOM,"MicVol",m_stCallDeviceConfig.micVol,INTERCOM_CONFIG_INI_FILE);
		ini_putl(INI_SECTION_INTERCOM,"FarOutVol",m_stCallDeviceConfig.farOutVol,INTERCOM_CONFIG_INI_FILE);
	}
	#endif

	#if(IS_DEVICE_AUDIO_COLLECTOR)
	else if( strcmp(section,INI_SECTION_COLLECTOR) == 0 )
	{
		ini_putl(INI_SECTION_COLLECTOR,"TriggerSwitch",audioCollector_info.m_nTriggerSwitch,COLLECTOR_CONFIG_INI_FILE);
		ini_putl(INI_SECTION_COLLECTOR,"TriggerChannelId",audioCollector_info.m_nTriggerChannelId,COLLECTOR_CONFIG_INI_FILE);
		ini_putl(INI_SECTION_COLLECTOR,"TriggerZoneVolume",audioCollector_info.m_nTriggerZoneVolume,COLLECTOR_CONFIG_INI_FILE);
	}
	#endif

	#if(IS_DEVICE_AUDIO_MIXER)
	else if( strcmp(section,INI_SECTION_MIXER) == 0 )
	{
		ini_putl(INI_SECTION_MIXER,"MasterSwitch",audioMixer_info.m_nMasterSwitch,MIXER_CONFIG_INI_FILE);
		ini_putl(INI_SECTION_MIXER,"Priority",audioMixer_info.m_nPriority,MIXER_CONFIG_INI_FILE);
		ini_putl(INI_SECTION_MIXER,"TriggerType",audioMixer_info.m_nTriggerType,MIXER_CONFIG_INI_FILE);
		ini_putl(INI_SECTION_MIXER,"TriggerSensitivity",audioMixer_info.m_nTriggerSensitivity,MIXER_CONFIG_INI_FILE);
		ini_putl(INI_SECTION_MIXER,"VolumeFadeLevel",audioMixer_info.m_nVolumeFadeLevel,MIXER_CONFIG_INI_FILE);
		ini_putl(INI_SECTION_MIXER,"ZoneVolume",audioMixer_info.m_nVolume,MIXER_CONFIG_INI_FILE);
		ini_putl(INI_SECTION_MIXER,"DelayMode",audioMixer_info.m_nDelayMode,MIXER_CONFIG_INI_FILE);
	}
	#endif

	#if(IS_DEVICE_PHONE_GATEWAY)
	else if( strcmp(section,INI_SECTION_PHONE_GATEWAY) == 0 )
	{
		ini_putl(INI_SECTION_PHONE_GATEWAY,"MasterSwitch",phoneGateway_info.m_nMasterSwitch,PHONE_GATEWAY_CONFIG_INI_FILE);
		ini_putl(INI_SECTION_PHONE_GATEWAY,"ZoneVolume",phoneGateway_info.m_nVolume,PHONE_GATEWAY_CONFIG_INI_FILE);
		ini_puts(INI_SECTION_PHONE_GATEWAY,"TelWhitelist",phoneGateway_info.telWhitelist,PHONE_GATEWAY_CONFIG_INI_FILE);
	}
	#endif

	#if SUPPORT_INFORMATION_PUBLISH
	else if( strcmp(section,INI_SECTION_INFORMATIONPUB) == 0)
	{
		ini_putl(INI_SECTION_INFORMATIONPUB,"EnableDisplay",informationPub_info.m_bEnableDisplay,INI_INFORMATION_PUB_INI_FILE);
		ini_puts(INI_SECTION_INFORMATIONPUB,"Context",informationPub_info.m_szText,INI_INFORMATION_PUB_INI_FILE);
		ini_putl(INI_SECTION_INFORMATIONPUB,"Effects",informationPub_info.m_nEffects,INI_INFORMATION_PUB_INI_FILE);
		ini_putl(INI_SECTION_INFORMATIONPUB,"MoveSpeed",informationPub_info.m_nMoveSpeed,INI_INFORMATION_PUB_INI_FILE);
		ini_putl(INI_SECTION_INFORMATIONPUB,"StayTime",informationPub_info.m_nStayTime,INI_INFORMATION_PUB_INI_FILE);
	}
	#endif

	#if IS_DEVICE_REMOTE_CONTROLER
	else if( strcmp(section,INI_SECTION_REMOTE) == 0)
	{
		ini_putl(INI_SECTION_REMOTE,"addrCode",g_remote_controler_addressCode,INI_REMOTE_INI_FILE);
	}
	#endif

	#if IS_DEVICE_NOISE_DETECTOR
	else if( strcmp(section,INI_SECTION_NOISE_DETECTOR) == 0)
	{
		ini_putl(INI_SECTION_NOISE_DETECTOR,"isEnable",g_noiseDetector_info.isEnable,INI_NOISE_DETECTOR_INI_FILE);
		char segmentName[32]={0};
		for(int i=0;i<NOISE_NUM_SEGMENTS;i++)
		{
			sprintf(segmentName,"segment%d",i);
			ini_putl(INI_SECTION_NOISE_DETECTOR,segmentName,g_noiseDetector_info.segmentVol[i],INI_NOISE_DETECTOR_INI_FILE);
		}
	}
	#endif
	
	return NULL;
}


void* read_sysconf(char *section,char *key)
{
	//网络配置
	if( strcmp(section,INI_SETCION_NETWORK) == 0 )
	{
		g_network_mode=ini_getl(INI_SETCION_NETWORK,"Connect_mode",NETWORK_MODE_LAN,NETWORK_CONFIG_INI_FILE);
		ini_gets(INI_SETCION_NETWORK,"TcpHost_addr","************",g_host_tcp_addr_domain,63,NETWORK_CONFIG_INI_FILE);
		g_host_tcp_port=ini_getl(INI_SETCION_NETWORK,"TcpHost_port",49888,NETWORK_CONFIG_INI_FILE);

		ini_gets(INI_SETCION_NETWORK,"TcpHost_addr2","",g_host_tcp_addr_domain2,63,NETWORK_CONFIG_INI_FILE);
		g_host_tcp_port2=ini_getl(INI_SETCION_NETWORK,"TcpHost_port2",0,NETWORK_CONFIG_INI_FILE);

		g_IP_Assign=ini_getl(INI_SETCION_NETWORK,"Ip_mode",IP_ASSIGN_STATIC,NETWORK_CONFIG_INI_FILE);
		ini_gets(INI_SETCION_NETWORK,"Static_IP","**************",g_Static_ip_address,30,NETWORK_CONFIG_INI_FILE);
		ini_gets(INI_SETCION_NETWORK,"Subnet_mask","*************",g_Subnet_Mask,30,NETWORK_CONFIG_INI_FILE);
		ini_gets(INI_SETCION_NETWORK,"GateWay","************",g_GateWay,30,NETWORK_CONFIG_INI_FILE);
		ini_gets(INI_SETCION_NETWORK,"Primary_DNS","************",g_Primary_DNS,30,NETWORK_CONFIG_INI_FILE);
		ini_gets(INI_SETCION_NETWORK,"Alternative_DNS","************",g_Alternative_DNS,30,NETWORK_CONFIG_INI_FILE);

		printf("Network:Connect_mode=%d\n",g_network_mode);
		if(g_network_mode == NETWORK_MODE_WAN)
		{
			printf("Network:TcpHost_addr=%s\n",g_host_tcp_addr_domain);
			printf("Network:TcpHost_port=%d\n",g_host_tcp_port);
			printf("Network:TcpHost_addr2=%s\n",g_host_tcp_addr_domain2);
			printf("Network:TcpHost_port2=%d\n",g_host_tcp_port2);
		}
		printf("Network:g_IP_Assign=%d\n",g_IP_Assign);
		printf("Network:Static_IP=%s\n",g_Static_ip_address);
		printf("Network:Subnet_mask=%s\n",g_Subnet_Mask);
		printf("Network:GateWay=%s\n",g_GateWay);
		printf("Network:Primary_DNS=%s\n",g_Primary_DNS);
		printf("Network:Alternative_DNS=%s\n",g_Alternative_DNS);

		if(g_network_mode == NETWORK_MODE_WAN)
		{
			if(	g_host_tcp_port != DEFAULT_TCP_PORT )
			{
				g_host_kcp_port = g_host_tcp_port + 1;
			}
			else
			{
				g_host_kcp_port = DEFAULT_KCP_PORT;
			}

			if(	g_host_tcp_port2 != DEFAULT_TCP_PORT )
			{
				g_host_kcp_port2 = g_host_tcp_port2 + 1;
			}
			else
			{
				g_host_kcp_port2 = DEFAULT_KCP_PORT;
			}
		}
	}
	else if( strcmp(section,INI_SECTION_DEVICE) == 0 )
	{
		#if IS_DEVICE_AUDIO_COLLECTOR
		ini_gets(INI_SECTION_DEVICE,"Device_Name","音频采集器",g_device_alias,64,DEVICE_CONFIG_INI_FILE);
		#elif IS_DEVICE_FIRE_COLLECTOR
		ini_gets(INI_SECTION_DEVICE,"Device_Name","消防采集器",g_device_alias,64,DEVICE_CONFIG_INI_FILE);
		#elif IS_DEVICE_POWER_SEQUENCE
		ini_gets(INI_SECTION_DEVICE,"Device_Name","电源时序器",g_device_alias,64,DEVICE_CONFIG_INI_FILE);
		#elif IS_DEVICE_REMOTE_CONTROLER
		ini_gets(INI_SECTION_DEVICE,"Device_Name","远程遥控器",g_device_alias,64,DEVICE_CONFIG_INI_FILE);
		#elif IS_DEVICE_AUDIO_MIXER
		ini_gets(INI_SECTION_DEVICE,"Device_Name","音频中继器",g_device_alias,64,DEVICE_CONFIG_INI_FILE);
		#elif (CURRENT_DEVICE_MODEL == MODEL_IP_SPEAKER_F && SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_RGB_4P3_480X272)
		ini_gets(INI_SECTION_DEVICE,"Device_Name","网络功放",g_device_alias,64,DEVICE_CONFIG_INI_FILE);	
		#elif IS_DEVICE_GPS_SYNCHRONIZER
		ini_gets(INI_SECTION_DEVICE,"Device_Name","校时器",g_device_alias,64,DEVICE_CONFIG_INI_FILE);
		#else
		ini_gets(INI_SECTION_DEVICE,"Device_Name","",g_device_alias,64,DEVICE_CONFIG_INI_FILE);
		#endif
		g_system_language=ini_getl(INI_SECTION_DEVICE,"System_Language",CHINESE,DEVICE_CONFIG_INI_FILE);
		
		g_backlight_level=ini_getl(INI_SECTION_DEVICE,"Display_Brightness",DEFAULT_BRIGHT_LEVEL,DEVICE_CONFIG_INI_FILE);
	}
	else if( strcmp(section,INI_SECTION_BASIC) == 0 )
	{
		g_system_volume=ini_getl(INI_SECTION_BASIC,"System_Volume",DEFAULT_SYSTEM_VOLUME,BASIC_CONFIG_INI_FILE);
		g_pre_system_volume=g_system_volume;

		g_PreExistDispModule=ini_getl(INI_SECTION_BASIC,"Exist_DispModule",0,BASIC_CONFIG_INI_FILE); 

		g_sub_volume=ini_getl(INI_SECTION_BASIC,"Sub_Volume",DEFAULT_SUB_VOLUME,BASIC_CONFIG_INI_FILE);

		g_volumeCD_aux_volume=ini_getl(INI_SECTION_BASIC,"VolumeControl_Aux",DEFAULT_SYSTEM_VOLUME,BASIC_CONFIG_INI_FILE);
		g_volumeCD_net_volume=ini_getl(INI_SECTION_BASIC,"VolumeControl_Net",DEFAULT_SYSTEM_VOLUME,BASIC_CONFIG_INI_FILE);

		g_aux_volume=ini_getl(INI_SECTION_BASIC,"Aux_Volume",DEFAULT_SYSTEM_VOLUME,BASIC_CONFIG_INI_FILE);

		//带屏的时候才处理
		if(isDisplayValid())
		{
			g_lineVolume=ini_getl(INI_SECTION_BASIC,"Line_Volume",DEFAULT_SYSTEM_VOLUME,BASIC_CONFIG_INI_FILE);
			g_micVolume=ini_getl(INI_SECTION_BASIC,"Mic_Volume",DEFAULT_SYSTEM_VOLUME,BASIC_CONFIG_INI_FILE);
		}
		//printf("device volume:system_volume=%d,sub_volume=%d,aux_volume=%d\n",g_system_volume,g_sub_volume,g_aux_volume);
	}
	else if( strcmp(section,INI_SETCION_MFR) == 0 )
	{
		ini_gets(INI_SETCION_MFR,"SN","",g_device_serialNum,sizeof(g_device_serialNum)-1,MFR_CONFIG_INI_FILE);
	}

	#ifndef USE_PC_SIMULATOR
	#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
	else if( strcmp(section,INI_SETCION_DSP_Firmware) == 0 )
	{
		g_device_moduleId=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_MODEL_ID,DEFAULT_MODULE_ID,DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_L]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC0_SWITCH,dsp_default_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_L]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC0_GAIN,dsp_default_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_R]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC1_SWITCH,dsp_default_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_R]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC1_GAIN,dsp_default_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC1_L]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC2_SWITCH,dsp_default_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC1_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC1_L]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC2_GAIN,dsp_default_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC1_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		
		dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_L]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0L_SWITCH,dsp_default_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0L_GAIN,dsp_default_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_R]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0R_SWITCH,dsp_default_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0R_GAIN,dsp_default_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
		
		//如果DSP_FIRMWARE_CONFIG_INI_FILE不存在，那么应立即将其保存下来，因为烧录程序需要区分是否具有相应的输入，如果不保存到文件，升级后可能会改变DSP配置
		if(!IsFileExist(DSP_FIRMWARE_CONFIG_INI_FILE))
		{
			save_sysconf(INI_SETCION_DSP_Firmware,NULL);
		}

		//20220726,NetSpeaker_P13_2x20W_V1.6.4.4_20220706.tar.gz烧录包内部也是NetSpeaker_P17_2x20W_V1.6.4.4_20220706，造成P13 MIC无声 \
		由于 NetSpeaker_P17_2x20W_V1.6.4.4_20220706还没有出去过(涛涛100pcs全部重新烧录最新的P17，所以紧急把所有烧录P17的都变成P13)
		#if 0
		if(IsFileExist("/etc/backup/NetSpeaker_P17_2x20W_V1.6.4.4_20220706.tar.gz"))
		{
			if( g_device_moduleId == POWER_P17_2x20W_V11 )
			{
				g_device_moduleId = POWER_P13_2x20W;
				dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_L]=0x01;
				dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_L]=2933;
				dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_R]=0x01;
				dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_R]=16306;
				dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC1_L]=0x01;
				dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC1_L]=3693;
				
				dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_L]=0x01;
				dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L]=17000;
				dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_R]=0x01;
				dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R]=7118;

				save_sysconf(INI_SETCION_DSP_Firmware,NULL);
			}
		}
		#endif

		#if (SUPPORT_UART_LED_PLAYER_MODE)
		if(g_device_moduleId == POWER_P13_2x20W_LED)
		{
			is_Enable_UART_LED_PLAYER=1;
		}
		#endif
	}

	else if( strcmp(section,INI_SETCION_DSP_EQ) == 0 )
	{
		dsp_eq_info.eq_mode = ini_getl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_MODE,INI_DSP_EQ_VALUE_DEFAULT,DSP_EQ_CONFIG_INI_FILE);
		dsp_eq_info.gain[0] = ini_getl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN1,INI_DSP_EQ_VALUE_DEFAULT,DSP_EQ_CONFIG_INI_FILE);
		dsp_eq_info.gain[1] = ini_getl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN2,INI_DSP_EQ_VALUE_DEFAULT,DSP_EQ_CONFIG_INI_FILE);
		dsp_eq_info.gain[2] = ini_getl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN3,INI_DSP_EQ_VALUE_DEFAULT,DSP_EQ_CONFIG_INI_FILE);
		dsp_eq_info.gain[3] = ini_getl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN4,INI_DSP_EQ_VALUE_DEFAULT,DSP_EQ_CONFIG_INI_FILE);
		dsp_eq_info.gain[4] = ini_getl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN5,INI_DSP_EQ_VALUE_DEFAULT,DSP_EQ_CONFIG_INI_FILE);
		dsp_eq_info.gain[5] = ini_getl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN6,INI_DSP_EQ_VALUE_DEFAULT,DSP_EQ_CONFIG_INI_FILE);
		dsp_eq_info.gain[6] = ini_getl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN7,INI_DSP_EQ_VALUE_DEFAULT,DSP_EQ_CONFIG_INI_FILE);
		dsp_eq_info.gain[7] = ini_getl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN8,INI_DSP_EQ_VALUE_DEFAULT,DSP_EQ_CONFIG_INI_FILE);
		dsp_eq_info.gain[8] = ini_getl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN9,INI_DSP_EQ_VALUE_DEFAULT,DSP_EQ_CONFIG_INI_FILE);
		dsp_eq_info.gain[9] = ini_getl(INI_SETCION_DSP_EQ,INI_KEY_DSP_EQ_GAIN10,INI_DSP_EQ_VALUE_DEFAULT,DSP_EQ_CONFIG_INI_FILE);

		mi_audio_set_eq_mode(dsp_eq_info.eq_mode);
	}
	else if( strcmp(section,INI_SETCION_BLUETOOTH) == 0 )
	{
		ini_gets(INI_SETCION_BLUETOOTH,INI_KEY_BLUETOOTH_NAME,DEFAULT_BLUETOOTH_NAME,g_bp1048_bt_info.name,sizeof(g_bp1048_bt_info.name),BLUETOOTH_CONFIG_INI_FILE);
		g_bp1048_bt_info.hasPassword=ini_getl(INI_SETCION_BLUETOOTH,INI_KEY_BLUETOOTH_HAS_PIN,0,BLUETOOTH_CONFIG_INI_FILE);
		ini_gets(INI_SETCION_BLUETOOTH,INI_KEY_BLUETOOTH_PIN,DEFAULT_BLUETOOTH_PIN,g_bp1048_bt_info.password,sizeof(g_bp1048_bt_info.password),BLUETOOTH_CONFIG_INI_FILE);
	}
	else if( strcmp(section,INI_SECTION_TRIGGER) == 0 )
	{
		st_triggerSong.trigger_switch=ini_getl(INI_SECTION_TRIGGER,"TriggerSwitch",0,TRIGGER_CONFIG_INI_FILE);
		st_triggerSong.trigger_mode=ini_getl(INI_SECTION_TRIGGER,"TriggerMode",0,TRIGGER_CONFIG_INI_FILE);
		ini_gets(INI_SECTION_TRIGGER,"SongName","",st_triggerSong.trigger_song_name,sizeof(st_triggerSong.trigger_song_name),TRIGGER_CONFIG_INI_FILE);
		ini_gets(INI_SECTION_TRIGGER,"SongMd5","",st_triggerSong.trigger_song_md5,sizeof(st_triggerSong.trigger_song_md5),TRIGGER_CONFIG_INI_FILE);
		st_triggerSong.trigger_playTimes=ini_getl(INI_SECTION_TRIGGER,"PlayTimes",0,TRIGGER_CONFIG_INI_FILE);
		st_triggerSong.trigger_volume=ini_getl(INI_SECTION_TRIGGER,"Volume",0,TRIGGER_CONFIG_INI_FILE);
	}

	#endif
	#endif

	#if(IS_DEVICE_POWER_SEQUENCE)
	else if( strcmp(section,INI_SETCION_SEQUENCEPOWER) == 0 )
	{
		sequence_power_info.control_mode=ini_getl(INI_SETCION_SEQUENCEPOWER,"ControlMode",SEQUENCE_POWER_MODE_MANUAL,SEQUENCEPOWER_CONFIG_INI_FILE);
		sequence_power_info.open_delay_value=ini_getl(INI_SETCION_SEQUENCEPOWER,"OpenDelay",SEQUENCE_POWER_DEFAULT_DELAY,SEQUENCEPOWER_CONFIG_INI_FILE);
	}
	#endif

	#if(IS_DEVICE_FIRE_COLLECTOR)
	else if( strcmp(section,INI_SETCION_FIRE) == 0 )
	{
		fire_collector_info.trigger_mode=ini_getl(INI_SETCION_FIRE,"TriggerMode",0,FIRE_CONFIG_INI_FILE);
	}
	#endif

	#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
	else if( strcmp(section,INI_SECTION_INTERCOM) == 0 )
	{
		char key1_mac[32]={0};
		char key2_mac[32]={0};

		ini_gets(INI_SECTION_INTERCOM,"Key1_mac","00:00:00:00:00:00",key1_mac,30,INTERCOM_CONFIG_INI_FILE);
		ini_gets(INI_SECTION_INTERCOM,"key2_mac","00:00:00:00:00:00",key2_mac,30,INTERCOM_CONFIG_INI_FILE);
		m_stCallDeviceConfig.AutoAnswerTime=ini_getl(INI_SECTION_INTERCOM,"AutoAnswerTime",0,INTERCOM_CONFIG_INI_FILE);
		m_stCallDeviceConfig.micVol=ini_getl(INI_SECTION_INTERCOM,"MicVol",DEFAULT_MIC_VOL_LEVEL,INTERCOM_CONFIG_INI_FILE);
		m_stCallDeviceConfig.farOutVol=ini_getl(INI_SECTION_INTERCOM,"FarOutVol",DEFAULT_FAR_OUT_LEVEL,INTERCOM_CONFIG_INI_FILE);

		MacStringToArray(m_stCallDeviceConfig.Key1_mac,key1_mac);
		MacStringToArray(m_stCallDeviceConfig.Key2_mac,key2_mac);
	}
	#endif
	#if(IS_DEVICE_AUDIO_COLLECTOR)
	else if( strcmp(section,INI_SECTION_COLLECTOR) == 0 )
	{
		audioCollector_info.m_nTriggerSwitch=ini_getl(INI_SECTION_COLLECTOR,"TriggerSwitch",0,COLLECTOR_CONFIG_INI_FILE);
		audioCollector_info.m_nTriggerChannelId=ini_getl(INI_SECTION_COLLECTOR,"TriggerChannelId",1,COLLECTOR_CONFIG_INI_FILE);
		audioCollector_info.m_nTriggerZoneVolume=ini_getl(INI_SECTION_COLLECTOR,"TriggerZoneVolume",50,COLLECTOR_CONFIG_INI_FILE);
	}
	#endif
	#if(IS_DEVICE_AUDIO_MIXER)
	else if( strcmp(section,INI_SECTION_MIXER) == 0 )
	{
		audioMixer_info.m_nMasterSwitch=ini_getl(INI_SECTION_MIXER,"MasterSwitch",0,MIXER_CONFIG_INI_FILE);
		audioMixer_info.m_nPriority=ini_getl(INI_SECTION_MIXER,"Priority",1,MIXER_CONFIG_INI_FILE);
		audioMixer_info.m_nTriggerType=ini_getl(INI_SECTION_MIXER,"TriggerType",1,MIXER_CONFIG_INI_FILE);
		audioMixer_info.m_nTriggerSensitivity=ini_getl(INI_SECTION_MIXER,"TriggerSensitivity",5,MIXER_CONFIG_INI_FILE);
		audioMixer_info.m_nVolumeFadeLevel=ini_getl(INI_SECTION_MIXER,"VolumeFadeLevel",5,MIXER_CONFIG_INI_FILE);
		audioMixer_info.m_nVolume=ini_getl(INI_SECTION_MIXER,"ZoneVolume",50,MIXER_CONFIG_INI_FILE);
		audioMixer_info.m_nDelayMode=ini_getl(INI_SECTION_MIXER,"DelayMode",1,MIXER_CONFIG_INI_FILE);
	}
	#endif
	#if(IS_DEVICE_PHONE_GATEWAY)
	else if( strcmp(section,INI_SECTION_PHONE_GATEWAY) == 0 )
	{
		phoneGateway_info.m_nMasterSwitch = ini_getl(INI_SECTION_PHONE_GATEWAY,"MasterSwitch",0,PHONE_GATEWAY_CONFIG_INI_FILE);
		phoneGateway_info.m_nVolume = ini_getl(INI_SECTION_PHONE_GATEWAY,"ZoneVolume",0,PHONE_GATEWAY_CONFIG_INI_FILE);
		//phoneGateway_info.m_nMasterSwitch=1;
		ini_gets(INI_SECTION_PHONE_GATEWAY,"TelWhitelist","",phoneGateway_info.telWhitelist,sizeof(phoneGateway_info.telWhitelist),PHONE_GATEWAY_CONFIG_INI_FILE);
		printf("TelWhitelist=%s\n",phoneGateway_info.telWhitelist);
	}
	#endif

	#if SUPPORT_INFORMATION_PUBLISH
	else if( strcmp(section,INI_SECTION_INFORMATIONPUB) == 0)
	{
		informationPub_info.m_bEnableDisplay = ini_getl(INI_SECTION_INFORMATIONPUB,"EnableDisplay",0,INI_INFORMATION_PUB_INI_FILE);
		ini_gets(INI_SECTION_INFORMATIONPUB,"Context","",informationPub_info.m_szText,sizeof(informationPub_info.m_szText),INI_INFORMATION_PUB_INI_FILE);
		informationPub_info.m_nEffects = ini_getl(INI_SECTION_INFORMATIONPUB,"Effects",0,INI_INFORMATION_PUB_INI_FILE);
		informationPub_info.m_nMoveSpeed = ini_getl(INI_SECTION_INFORMATIONPUB,"MoveSpeed",3,INI_INFORMATION_PUB_INI_FILE);
		informationPub_info.m_nStayTime = ini_getl(INI_SECTION_INFORMATIONPUB,"StayTime",3,INI_INFORMATION_PUB_INI_FILE);
	}
	#endif

	#if IS_DEVICE_REMOTE_CONTROLER
	else if( strcmp(section,INI_SECTION_REMOTE) == 0)
	{
		g_remote_controler_addressCode = ini_getl(INI_SECTION_REMOTE,"addrCode",REMOTE_CONTROLER_DEFAULT_ADDRCODE,INI_REMOTE_INI_FILE);
	}
	#endif

	#if IS_DEVICE_NOISE_DETECTOR
	else if( strcmp(section,INI_SECTION_NOISE_DETECTOR) == 0)
	{
		g_noiseDetector_info.isEnable = ini_getl(INI_SECTION_NOISE_DETECTOR,"isEnable",0,INI_NOISE_DETECTOR_INI_FILE);
		char segmentName[32]={0};
		bool isParmError=false;
		for(int i=0;i<NOISE_NUM_SEGMENTS;i++)
		{
			sprintf(segmentName,"segment%d",i);
			g_noiseDetector_info.segmentVol[i] = ini_getl(INI_SECTION_NOISE_DETECTOR,segmentName,0,INI_NOISE_DETECTOR_INI_FILE);
			if(g_noiseDetector_info.segmentVol[i] == 0 || g_noiseDetector_info.segmentVol[i] > 100)
			{
				isParmError=true;
				break;
			}
		}
		if(isParmError)
		{
			memcpy(g_noiseDetector_info.segmentVol,defaultNoiseSegmentVol,sizeof(defaultNoiseSegmentVol));
		}
		
	}
	#endif

	return NULL;
}



/*********************************************************************
 * @fn      HOST_Control_Device_Check
 *
 * @brief   主机在线超时检测线程
 *
 * @param
 *
 *
 * @return
 *
 */

void* HOST_Control_Device_Check()
{
	while(1)
	{
		if(network_init_flag)
	    {
          if( host_ready_offline_flag == 1  && g_host_device_TimeOut == 0 )
          {
            host_ready_offline_flag = 0;
            //打开网络连接状态IO输出
			GPIO_OutPut_Server_Connection(1);
          }
          
          if(g_host_device_TimeOut>=0)
          {
            g_host_device_TimeOut++;
            if(g_host_device_TimeOut >= HOST_TIMEOUT_VALUE)
            {
              printf("Host offline1:%d\r\n", g_host_device_TimeOut);

              if( g_media_source != SOURCE_NET_PAGING && g_media_source != SOURCE_NULL && g_media_source != SOURCE_AUX && g_media_source != SOURCE_100V_INPUT )
              {
                printf("Host offline2,set Idle!\r\n");
				if(!st_triggerSong.trigger_local_play_flag)
                	Set_zone_idle_status(NULL,  __func__, __LINE__,true);
              }
              
              if(g_network_mode == NETWORK_MODE_WAN && tcp_get_master_connect_status())
              {
                  tcp_client_reconnect();
              }

              g_host_device_TimeOut=-1; //离线
              host_ready_offline_flag = 1;

			  	//关闭网络连接状态IO输出
				GPIO_OutPut_Server_Connection(0);
            }
          }
	    }
		sleep(1);
	}
}



void HOST_Control_Device_Check_Thread()
{
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)HOST_Control_Device_Check, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}




void *HOSTIP_Check()
{
	int link_status_change_cnt=0;
	int reLinked_flag=0;
	int dhcp_runCnt=0;
	char ipAddress_temp[20]={0};

    printf("HOSTIP_Check...\n");
    //GetLocalMAC(g_mac_addr,true);

	 while(1)
	 {
		 if(!network_init_flag)
		 {
			GetLocalIp(g_ipAddress);
			if( ( strlen(g_ipAddress)>=7 && strcmp(g_ipAddress,"127.0.0.1")!=0 ))
			{
				if(g_IP_Assign == IP_ASSIGN_DHCP)
				{
					usleep(100000);	//如果是DHCP需要延时，因为这个时候UDHCPC可能还在设置网关，如果此时初始化UDP加入组播会报错，route: SIOCDELRT: No such process multicast_sockfd SOCKET:: No such device
				}
				network_init_flag=1;
				printf("network_init_flag=1...\n");
				
				start_network_data_process_pthread();
				HOST_Control_Device_Check_Thread();
				#ifdef USE_PC_SIMULATOR
					#if 1
					g_network_mode = NETWORK_MODE_WAN;
					if(strncmp(g_ipAddress,"192.168.3.",strlen("192.168.3.")) == 0)
					{
						sprintf(g_host_tcp_addr_domain,"%s","************");
						g_host_tcp_port = DEFAULT_TCP_PORT;
						g_host_kcp_port = DEFAULT_KCP_PORT;
					}
					else
					{
						sprintf(g_host_tcp_addr_domain,"%s","**************");
						g_host_tcp_port = 2225;
						g_host_kcp_port = 2226;
					}
					#else
					start_udp_client();
					start_multicast_cmd_client();
					#endif
				#else
				start_udp_client();
				start_multicast_cmd_client();
				#endif
				if(g_network_mode == NETWORK_MODE_WAN)
				{
					TCP_Client_Start();
				}
				else
				{
					send_online_info();
				}

				if(ENABLE_DISP_UART_MODULE)
				{
					Disp_Send_network_info();
				}

				serial_display_show_ip();

				display_update_mainWin_external();
			}
		 }
		#ifdef USE_PC_SIMULATOR
		usleep(25000);
        continue;
        #endif
		int temp_link_status=get_netlink_status("eth0");
		if(temp_link_status<0)
		{
			usleep(50000);
			continue;
		}
		if(temp_link_status!=eth_link_status || (g_IP_Assign == IP_ASSIGN_DHCP && eth_link_status && dhcp_runCnt == 0))
		{
			link_status_change_cnt++;
			if(link_status_change_cnt>2)
			{
				link_status_change_cnt=0;
				eth_link_status=temp_link_status;
				printf("eth_link_status=%d\n",eth_link_status);
				if(eth_link_status == 1)
				{
					config_module_4g_network();
					if(g_module_4G_status == MODULE_4G_UNUSED)
					{
						//如果WAN模式下已经连接，需要立即重连
						if(g_network_mode == NETWORK_MODE_WAN && tcp_get_master_connect_status() == 1)
						{
							tcp_client_reconnect();
						}
					}
					if(g_IP_Assign == IP_ASSIGN_DHCP)
					{
						pox_system("ifconfig eth0 down");
						pox_system("ifconfig eth0 up");
						printf("ReStart DHCP...\n");
						pox_system("pidof zcip | xargs kill");
                        pox_system("pidof udhcpc | xargs kill");
						//pox_system("udhcpc -i eth0 -s /etc/init.d/udhcpc.script &");
						pox_system("udhcpc -i eth0 -T 6 -s /customer/App/udhcpc.script &");
						dhcp_runCnt++;
					}
					reLinked_flag=1;
				}
				else
				{
					reLinked_flag=0;
					if(g_module_4G_status <= MODULE_4G_OFF)
					{
						if(g_IP_Assign == IP_ASSIGN_DHCP)
						{
							pox_system("pidof zcip | xargs kill");
							pox_system("pidof udhcpc | xargs kill");
						}
						//如果是WAN模式，需要断开TCP
						#if 0
						if(g_network_mode == NETWORK_MODE_WAN)
							tcp_client_reconnect();
						#endif
						#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
						{
							if(g_signal_100v)   //网线拔出后，如果100V存在，那么切换过去
							{
								Ctrl_Relay_100V(1);	//音频继电器归位，此处先进行切换，避免Set_zone_idle_status占用时间
								Set_zone_idle_status(NULL,  __func__, __LINE__,true);
							}
						}
						#endif
						if( g_host_device_TimeOut >=0 && g_host_device_TimeOut < HOST_TIMEOUT_VALUE-15 )
						{
							g_host_device_TimeOut =  HOST_TIMEOUT_VALUE-15;
							host_ready_offline_flag = 1;
							//关闭网络连接状态IO输出
							GPIO_OutPut_Server_Connection(0);
						}
					}
					else
					{
						config_module_4g_network();
						//如果WAN模式下已经连接，需要立即重连
						if(g_network_mode == NETWORK_MODE_WAN && tcp_get_master_connect_status() == 1)
						{
							tcp_client_reconnect();
						}
					}
				}
			}
		}
		else
		{
			link_status_change_cnt=0;
			//if(g_IP_Assign == IP_ASSIGN_DHCP)
			{
				if(network_init_flag && eth_link_status)
				{
					if(reLinked_flag)		//如果已经连接连接，重新获取IP
					{
						memset(ipAddress_temp,0,sizeof(ipAddress_temp));
						GetLocalIp(ipAddress_temp);
						if( ( strlen(ipAddress_temp)>=7 && strcmp(ipAddress_temp,"127.0.0.1")!=0 ))
						{
							reLinked_flag=0;
							if( strcmp(g_ipAddress,ipAddress_temp) )
							{
								printf("IP not eqaul:%s...\n",ipAddress_temp);
								sprintf(g_ipAddress,"%s",ipAddress_temp);

								if(IS_MODEL_WITH_4G)
								{
									socket_join_multicast_membership();
								}

								if(ENABLE_DISP_UART_MODULE)
								{
									Disp_Send_network_info();
								}

								serial_display_show_ip();

								display_update_mainWin_external();
							}
							else
							{
								printf("IP eqaul...\n");
							}
							send_online_info();
						}
					}
				}
			}
		}
		usleep(25000);
	 }
}

void HOSTIP_Check_THREAD()
{
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)HOSTIP_Check, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}



bool Check_device_status_change()
{
	static signed char preLinkServer = 0;
	static int preVolume = 0;
	static int preSource = SOURCE_NULL;
	static char preMediaName[128]={0};
	static int prePlayStatus = SONG_STOP;
	static int preSubVolume = 100;
	#if IS_DEVICE_AUDIO_MIXER
	static st_audio_mixer_info pre_audioMixer_info;
	#endif
	bool infoChange=false;
	int curLinkServer = (g_host_device_TimeOut == -1)?0:1;
	if( preLinkServer != curLinkServer )
	{
		infoChange=true;
		preLinkServer = curLinkServer;
	}
	if( preVolume != g_system_volume )
	{
		infoChange=true;
		preVolume = g_system_volume;
	}
	if( preSource != g_media_source )
	{
		infoChange=true;
		preSource = g_media_source;
	}
	if( strcmp(preMediaName,g_media_name) )
	{
		infoChange=true;
		sprintf(preMediaName,"%s",g_media_name);
	}
	if( prePlayStatus != g_media_status )
	{
		infoChange=true;
		prePlayStatus = g_media_status;
	}
	if(preSubVolume != g_sub_volume)
	{
		infoChange=true;
		preSubVolume = g_sub_volume;
	}

	#if IS_DEVICE_AUDIO_MIXER
	if(pre_audioMixer_info.m_nMasterSwitch!=audioMixer_info.m_nMasterSwitch ||
		memcmp(pre_audioMixer_info.m_nSignalValid,audioMixer_info.m_nSignalValid,sizeof(audioMixer_info.m_nSignalValid)) )
	{
		infoChange=true;
		pre_audioMixer_info = audioMixer_info;
	}
	#endif
	return infoChange;
}







#define WATCHDOG_IOCTL_BASE 'W'
#define WDIOC_SETTIMEOUT _IOWR(WATCHDOG_IOCTL_BASE, 6, int)
#define WDIOC_KEEPALIVE _IOR(WATCHDOG_IOCTL_BASE, 5, int)

/*********************************************************************
 * @fn      feed_watchdog
 *
 * @brief   喂狗
 *
 * @param
 *
 * @return
 */
void feed_watchdog()
{
	if(wtg_fd>=0)
	{
		ioctl(wtg_fd, WDIOC_KEEPALIVE, 0);
	}
}

/*********************************************************************
 * @fn      Init_watchdog
 *
 * @brief   初始化喂狗
 *
 * @param
 *
 * @return
 */
int Init_watchdog()
{
	#ifdef USE_PC_SIMULATOR
	return -1;
	#endif

	int ret = -1;
	/*打开音频设备*/
	wtg_fd = open("/dev/watchdog", O_WRONLY);
	if(wtg_fd < 0)
	{
		printf("Init_watchdog open error!!!\n");
		return -1;
	}
	
	int timeout = 30;
	ioctl(wtg_fd, WDIOC_SETTIMEOUT, &timeout);
	printf("Init_watchdog succeed,timeout=%ds\n",timeout);

	//关闭
	#if 0
	int option = WDIOS_DISABLECARD;
	ioctl(wtg_fd, WDIOC_SETOPTIONS, &option);

	if (wtg_fd != -1)  
	{  
		close(wtg_fd);  
		wtg_fd = -1;  
	}
	#endif
	return 0;
}



//网络连接状态LED变化线程(音乐信号闪烁，用于P26、P28)
void *Create_LedStatus_Event(void *p_arg)
{
  GPIO_OutPut_LedStatusFlicker(0);
  while(1)
  {
      //0-未连接上服务器 1-连接上服务器 2-有音乐信号

      static int count = 0;
      static int prev_state=-1,state=0;
      static int state_has_changed = 0; // 变态是否已经改变
      static int led_status=0;   //默认灭
      unsigned char sysSource = get_system_source();
      if(!IS_SERVER_CONNECTED)
      {
          state = 0;
      }
      else if(sysSource!=SOURCE_NULL)
      {
          state = 2;
      }
      else
      {
          state = 1;
      }
  
      if (prev_state != state) {
          prev_state = state;
          count = 0;
          state_has_changed=1;
      }
      else
      {
          state_has_changed=0;
      }

      if (state == 0 && state_has_changed) {
        GPIO_OutPut_LedStatusFlicker(0);
        led_status=0;
      }
      else if (state == 1 && state_has_changed) {
          //printf("LED常亮\n");
          GPIO_OutPut_LedStatusFlicker(1); // LED常亮
          led_status=1;
      }
      // 如果状态为正在播放，则LED亮0.3秒，灭0.3秒
      else if (state == 2) {
          if (count < 3) {
              if(!led_status)
              {
                  //printf("LED亮0.3秒\n");
                  GPIO_OutPut_LedStatusFlicker(1); // 亮0.3秒
                  led_status=1;
              }
          } else {
              if(led_status)
              {
                  //printf("LED灭0.3秒\n");
                  GPIO_OutPut_LedStatusFlicker(0); // 灭0.3秒
                  led_status=0;
              }
          }
          count = (count + 1) % 6; // 6个时间单位为一次循环，每个时间单位100ms，所以6个单位为0.6秒
      }

      usleep(100000);
  }
}
//网络连接状态LED变化线程(音乐信号闪烁，用于P26、P28)
void Create_LedStatus_Event_Task(void)
{
  	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, Create_LedStatus_Event, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}


#include "mpg123.h"
int playIPtype=-1;	//-1不播报，0-播报IP，1复位

#define VOICE_DIR "/customer/App/Voice"
/**
 * IP播报功能
 *
 */
void* play_ip_tone(void* arg) {
	printf("play_ip_tone:enter!\n");
    mpg123_handle *mh = NULL;
    unsigned char *audio_data = NULL;
    size_t done = 0;
    int err = MPG123_OK;

    if (get_system_source() != SOURCE_NULL || playIPtype == -1) {
        playIPtype = -1;
        return NULL;
    }

    // Initialize mpg123
    err = mpg123_init();
    if (err != MPG123_OK) {
        fprintf(stderr, "mpg123 initialization failed: %s\n", mpg123_plain_strerror(err));
        playIPtype = -1;
        return NULL;
    }

    // Create mpg123 handle
    mh = mpg123_new(NULL, &err);
    if (!mh) {
        fprintf(stderr, "Unable to create mpg123 handle: %s\n", mpg123_plain_strerror(err));
        mpg123_exit();
        playIPtype = -1;
        return NULL;
    }

	int buffer_size = 2048;
    // Allocate memory for audio data
    audio_data = (unsigned char *)malloc(buffer_size);  // 4KB buffer
    if (!audio_data) {
        fprintf(stderr, "Memory allocation failed.\n");
        mpg123_delete(mh);
        mpg123_exit();
        playIPtype = -1;
        return NULL;
    }

	g_isPlayIPTone=1;
	Open_Audio_Out(16000,16,1);
	Enable_Signal_Output(1);          //打开信号
	Enable_Amp_Output(1);             //打开功放

    if (playIPtype == 0) {
		printf("ready go!\n");
        char filename[128] = {0};
        bool isIPValid = (strlen(g_ipAddress) >= 7);

        for (int j = 0; g_ipAddress[j] != '\0' || !isIPValid; j++) {
			if(get_system_source() != SOURCE_NULL)
			{
				playIPtype=-1;
				break;
			}
			printf("g_ipAddress[j]=%c\n",g_ipAddress[j]);
            if (!isIPValid) {
                sprintf(filename, "%s/noIpaddress.mp3", VOICE_DIR);
            } else if (g_ipAddress[j] == '.') {
                sprintf(filename, "%s/%s", VOICE_DIR, g_system_language == ENGLISH ? "point.mp3" : "zhpoint.mp3");
            } else {
                for (int i = 0; i <= 9; i++) {
                    if (g_ipAddress[j] == i + '0') {
                        sprintf(filename, "%s/%s%d.mp3", VOICE_DIR, g_system_language == ENGLISH ? "" : "zh", i);
                        break;
                    }
                }
            }

            // Open the MP3 file
            err = mpg123_open(mh, filename);
            if (err != MPG123_OK) {
                fprintf(stderr, "Error opening file %s: %s\n", filename, mpg123_plain_strerror(err));
                break;
            }

            // Set audio format
            long rate;
            int channels, encoding;
            mpg123_getformat(mh, &rate, &channels, &encoding);
			printf("rate=%d,channels=%d\n",rate,channels);

            // Read and play the MP3 file
            while ((err = mpg123_read(mh, audio_data, buffer_size, &done)) == MPG123_OK) {
				if(get_system_source() != SOURCE_NULL)
				{
					playIPtype=-1;
					break;
				}
             
                Audio_Write(audio_data, done);
            }

            // Check for errors during reading
            if (err != MPG123_DONE && err != MPG123_OK) {
                fprintf(stderr, "Error decoding file %s: %s\n", filename, mpg123_plain_strerror(err));
            }

            mpg123_close(mh);  // Close the current file
            if (!isIPValid || playIPtype == -1) break;
			if(g_system_language == ENGLISH )
			{
				int delayEndCount=40;
				while(--delayEndCount)
				{
					if(get_system_source() != SOURCE_NULL)
					{
						break;
					}
					usleep(10000);
				}
			}
        }
    }

    // Clean up
    if (audio_data) free(audio_data);
    mpg123_delete(mh);
    mpg123_exit();

	playIPtype = -1;
	g_isPlayIPTone=0;

	int delayEndCount=100;
	while(--delayEndCount)
	{
		if(get_system_source() != SOURCE_NULL)
		{
			break;
		}
		usleep(10000);
	}
	if(get_system_source() == SOURCE_NULL)
	{
		Open_Audio_Out(48000,16,2);
		Enable_Amp_Output(0);             //关闭功放
		Enable_Signal_Output(0);          //关闭信号
	}
	printf("play_ip_tone:exit!\n");

    return NULL;
}


void start_play_ip_tone_thread()
{
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, play_ip_tone, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}

//按键播报IP
void *keyPlayIpAndReset()
{
	Set_Gpio_Input(PAD_KEY2);
	usleep(10000);
	int preGpioValue=Get_Gpio_Value(PAD_KEY2);;
	bool gpioValid=false;
	int changeCount=0;
    while(1)
	{
		int curGpioLevel = Get_Gpio_Value(PAD_KEY2);
		if(curGpioLevel!=preGpioValue)
		{
			gpioValid=true;
			changeCount=0;

			preGpioValue=curGpioLevel;
		}
		else
		{
			if(curGpioLevel == 0)
			{
				changeCount++;
			}
		}
		if(gpioValid)
		{
			if(changeCount == 50*3)		//3秒
			{
				if(playIPtype == -1) 
				{
					//开始播报，继续计时
					playIPtype=0;
					start_play_ip_tone_thread();
					changeCount=0;
					gpioValid=false;
				}
			}
		}

		usleep(20*1000);
	}
}

void start_keyPlayIpAndReset_pthread(void)
{
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)keyPlayIpAndReset, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}


int main()
{
	//看门狗初始化
	#if ENBALE_WATCHDOG
	Init_watchdog();
	#endif

	//获取启动类型
	#ifndef USE_PC_SIMULATOR
	g_sysBootType=sstar_get_reboot_type();
	#else
	g_sysBootType=0;
	#endif

	GetLocalMAC(g_mac_addr,true);


	#ifndef USE_PC_SIMULATOR
	//卸载MI_AI模块并重新装载,启用硬件时钟
	rmmod_module("mi_ai",0);
	system("insmod /config/modules/4.9.84/mi_ai.ko TriggerInterval=5 ThreadPriority=99");
	#endif

	//初始化TCP
	tcp_connect_init();

    //配置文件初始化
	init_sysconf();

	//读取扩展功能配置
	readExtensionConfig();

	read_sysconf(INI_SETCION_NETWORK,NULL);
	read_sysconf(INI_SECTION_DEVICE,NULL);
	read_sysconf(INI_SECTION_BASIC,NULL);
	read_sysconf(INI_SETCION_MFR,NULL);
	#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
		read_sysconf(INI_SETCION_DSP_Firmware,NULL);
		read_sysconf(INI_SETCION_DSP_EQ,NULL);
		read_sysconf(INI_SETCION_BLUETOOTH,NULL);

		read_sysconf(INI_SECTION_INTERCOM,NULL);

		//初始化在线保存模块
		init_onlineSaver();
		//读取触发配置
		read_sysconf(INI_SECTION_TRIGGER,NULL);

	#elif (IS_DEVICE_FIRE_COLLECTOR)
		read_sysconf(INI_SETCION_FIRE,NULL);

	#elif (IS_DEVICE_POWER_SEQUENCE)
		read_sysconf(INI_SETCION_SEQUENCEPOWER,NULL);

	#elif (IS_DEVICE_AUDIO_COLLECTOR)
		read_sysconf(INI_SECTION_COLLECTOR,NULL);

	#elif (IS_DEVICE_AUDIO_MIXER)
		read_sysconf(INI_SECTION_MIXER,NULL);

	#elif (IS_DEVICE_PHONE_GATEWAY)
		read_sysconf(INI_SECTION_PHONE_GATEWAY,NULL);

	#elif (IS_DEVICE_REMOTE_CONTROLER)
		read_sysconf(INI_SECTION_REMOTE,NULL);

	#elif (IS_DEVICE_NOISE_DETECTOR)
		read_sysconf(INI_SECTION_NOISE_DETECTOR,NULL);
	#endif

	//初始化成功，重置检测次数
	int res =system("echo 0 > /customer/App/reset_count");
	//一定要调用sync同步，否则重启后可能文件出错
	res=system("sync");

	//如果是音频采集器，应该初始化ES7210
#if !YIHUI_VERSION
	#if(IS_DEVICE_AUDIO_COLLECTOR || IS_DEVICE_AUDIO_MIXER)
	{
		//将信号状态指示灯熄灭
		GPIO_OutPut_NetAudio_Led(0);
		es7210_adc_init(AUDIO_HAL_32K_SAMPLES);
	}
	#endif
#endif

#if SUPPORT_DISPLAY
	if(isDisplayValid())
	{
		rotaryEncoder_start();
		printf("display_init\n");
		display_init();
		//demo性能测试
		#if (AIPU_VERSION && SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_2P1_320X240)
			aipu_main_win_start();
		#elif YIHUI_VERSION
			yihui_main_win_start();
		#else

		#endif
	}
#endif

	system_loop_pthread();

    while(1)
    {	
		#if SUPPORT_DISPLAY
		if(isDisplayValid())
		{
			pthread_mutex_lock(&lvglMutex);
			unsigned int curr = _GetTime0();
			lv_task_handler();
			pthread_mutex_unlock(&lvglMutex);
			unsigned int time_diff = _GetTime0() - curr;
			if (time_diff < 10) {
				usleep(( 10 - time_diff ) * 1000);
			}
		}
		else
		#endif
		{
			usleep(100000);
		}
    }
}





void *system_loop()
{
	if(IS_MODEL_P26_V12)
	{
		#if !DECODER_RELAY_TRIGGER_MUSIC
		Ctrl_Relay_EMC(0);  	//消防强切继电器关闭
		#endif
	}

	#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_AUDIO_MIXER)
	{
		//先关闭功放及运放输出
		Enable_Signal_Output(0);
		Enable_Amp_Output(0);
		GPIO_OutPut_NetAudio_Signal(0);
	}
	#endif

#if(IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_DECODER_TERMINAL)
	if(IS_DEVICE_SUPPORT_INTERCOM)
	{
		InitCallDevice();
		Create_Intercom_Gpio_Event_Task();
	}
#endif
	
	//如果是户外板，PAD_SD_D3不作为串口1 TX使用，默认关闭网络连接状态IO输出，否则IO口默认会拉高
	if(IS_MODEL_OUTDOOR)
	{
		GPIO_OutPut_Server_Connection(0);
	}
	
	//如果是解码板-龙之音版本，KEY_GPIO8不作为串口2 TX使用，默认关闭网络连接状态IO输出,否则IO口默认会拉高;
	//如果是解码板-龙之音版本，KEY_GPIO9不作为串口2 RX使用，默认关闭音频信号状态IO输出,否则IO口默认会拉高;
	if(IS_MODEL_DECODER_LZY)
	{
		GPIO_OutPut_Server_Connection(0);
		GPIO_OutPut_NetAudio_Led(0);
	}

	//如果是网络时序器，默认关闭网络连接状态IO输出
	if(IS_DEVICE_POWER_SEQUENCE)
	{
		GPIO_OutPut_Server_Connection(0);
	}


	//如果是木箱，需要开启蓝牙通讯
	if(IS_MODEL_WOODEN)
	{
		#if AIPU_VERSION
		Uart_NewDevice_Init();
		#else
		Uart_Bluetooth_Init();
		#endif
	}

	#ifndef USE_PC_SIMULATOR
    init_network();
	nif_init();
    #endif

#if SUPPORT_TTS
	if(IS_EXTENSION_HAS_TTS || LZY_CUSTOMER_MQTT)
	{
		ffplay_init();
		SystemBootTestTTS();
		sleep(1);
	}
#endif

	if(IS_MODEL_WITH_4G)
	{
		//打开Module4G主电源
		GPIO_OutPut_Module4G_Main_Power(1);
		//切换USB总线，以便选中4G模块
		GPIO_set_usb_mutex(1);

		//4G版本用于替代原来的P16，作为机架式解码终端使用。
		//PAD_SD_D3不作为串口1 TX使用，默认关闭网络连接状态IO输出,否则IO口默认会拉高;
		//PAD_SD_D2不作为串口1 RX使用，默认关闭音频信号状态IO输出,否则IO口默认会拉高;
		GPIO_OutPut_Server_Connection(0);
		GPIO_OutPut_NetAudio_Led(0);
	}

#ifndef USE_PC_SIMULATOR
	#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_AUDIO_MIXER || IS_DEVICE_PHONE_GATEWAY)
	{
		#if  SUPPORT_VOLUME_CONTROL_UART
		if(IS_MODEL_WOODEN)
		{
			Uart_VolumeControl_Init();
			usleep(100000);
		}
		#endif

		#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
		Open_Audio_Out(48000,16,2);
		Open_Audio_In(48000,16,1);

		//如果是木箱，需要开启100V检测
		if(IS_MODEL_WOODEN)
		{
			//SAR检测线程
			MI_SAR_CHECK_THREAD();
		}
		#endif

		//混音器不进行本地音源输出，只接收网络并输出
		#if IS_DEVICE_AUDIO_MIXER
		Open_Audio_In(AUDIO_MIXER_SAMPLERATE,16,1);
		start_audioMixer_data_process_pthread();
		#endif

		#if IS_DEVICE_PHONE_GATEWAY
		Open_Audio_In(PHONE_GATEWAY_SAMPLERATE,16,1);
		start_phoneGateway_data_process_pthread();
		#endif

		//延迟1.5S再打开功放
		usleep(1500000);
		if( get_system_source() != SOURCE_NULL)
		{
			Enable_Amp_Output(1);
			Enable_Signal_Output(1);
		}
		amp_init_ok=1;

		#if IS_SUPPORT_SPEECH_RECOGNITION
		Uart_Speech_Init();
		#endif
	}
	#elif(IS_DEVICE_AUDIO_COLLECTOR)
	{
		Audio_Collector_CheckStatus_Thread();
		start_audioCollector_data_process_pthread();

		Open_Audio_Out(AUDIO_COLLECTOR_SAMPLERATE,16,2);
		Open_Audio_In(AUDIO_COLLECTOR_SAMPLERATE,16,1);
	}
	#elif(IS_DEVICE_FIRE_COLLECTOR)
	{
		if(isDeviceExtraFeatureValid())
		{
			printf("Detect ExtraFeature:Fire!\n");
			Uart_Fire_Init();
		}
		else
		{
			printf("Error:ExtraFeature:Fire not detect!\n");
		}
	}

	#elif(IS_DEVICE_REMOTE_CONTROLER)
	{
		#if CURRENT_DEVICE_MODEL == MODEL_REMOTE_CONTROLER_C
		if(isDeviceExtraFeatureValid())
		{
			printf("Detect ExtraFeature:Remote!\n");
			remote_disp_key_refresh_task();
			Uart_Remote_Init();
		}
		else
		{
			printf("Error:ExtraFeature:Remote not detect!\n");
		}
		#endif
	}
	#elif IS_DEVICE_GPS_SYNCHRONIZER
	{
		if(isDeviceExtraFeatureValid())
		{
			printf("Detect ExtraFeature:GPS!\n");
			Uart_GPS_Init();
		}
		else
		{
			printf("Error:ExtraFeature:GPS not detect!\n");
		}
	}
	#endif

#endif


//以前的设备才可能需要启用UART模块显示
if(CURRENT_DEVICE_MODEL == MODEL_IP_SPEAKER_D\
 	|| CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_B\
	|| CURRENT_DEVICE_MODEL == MODEL_AUDIO_COLLECTOR_B\
	|| CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_B\
	|| CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_C\
	|| CURRENT_DEVICE_MODEL == MODEL_REMOTE_CONTROLER\
	|| CURRENT_DEVICE_MODEL == MODEL_AUDIO_MIXER_ENCODER)
{
	if(ENABLE_DISP_UART_MODULE)
	{
		if(CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_C)
		{
			if(isDeviceExtraFeatureValid())
			{
				printf("Detect ExtraFeature:Power Sequence!\n");
				Uart_Disp_Init();
			}
		}
		else
		{
			printf("Enable Old Disp Uart!!!\n");
			Uart_Disp_Init();
		}
	}
	else
	{
		printf("Not Enable Old Disp Uart!!!\n");
	}
}

	#if LZY_COMMERCIAL_VERSION || IS_DEVICE_AMP_CONTROLER
	serial_display_init();
	#endif


	#if SUPPORT_TM1640_DISPLAY
	TM1640_Init();

	#endif



	if(IS_MODEL_WITH_4G)
	{
		InitModule4G();
	}

	#if IS_DEVICE_AMP_CONTROLER
	//start_ampControler_channel_process_pthread();
	Uart_NewDevice_Init();
	#endif

	//系统时间获取线程
	GetSystemTime_thread();

	#if SUPPORT_INFORMATION_PUBLISH
		if(IS_EXTENSION_HAS_INFORMATION_PUB)
		{
			read_sysconf(INI_SECTION_INFORMATIONPUB,NULL);
		}
	#endif


	#if SUPPORT_SIP
	if(IS_EXTENSION_HAS_SIP)
	{
		sem_sip_conf_init();
		pjsipInit(); //未连接网络时初始化pjsip
		Voip_Set_SystemSource_Idle_THREAD();
	}
	#endif

	//网络连接状态LED变化线程(音乐信号闪烁，用于P26、P28等新主板)
	Create_LedStatus_Event_Task();

    //IP检测线程
    HOSTIP_Check_THREAD();

	if(IS_MODEL_WITH_4G)
	{
		InitTrigger();
		
		//龙之音调频版本(C4A1，将UART1_TX(SD_D3)引脚设置为gpio输入，以便检测调频信号
		#if DECODER_LZY_VERESION_FM_C4A1
		Set_Gpio_Input(LZY_FM_SIGNAL_DETECT_GPIO);
		#endif
	}

#if SUPPORT_HTTP_SERVER
	start_mg_httpServer_pthread();
#endif

#if(IS_DEVICE_DECODER_TERMINAL)
	if(g_device_moduleId == POWER_P21_2x18W_V20)
	{
		//启动按键播放IP线程
		#if IS_DEVICE_DECODER_TERMINAL
		start_keyPlayIpAndReset_pthread();
		#endif
	}
#endif

#if(IS_DEVICE_NOISE_DETECTOR)
	uart_noise_detector_init();
#endif


	#if YIHUI_VERSION
	//初始化GPIO
	YH_init_gpio();
	//默认开启线路输出
	YH_control_lineout(1);
	//创建双路SAR（功放温度、按键检测线程）
	YH_board_sar_thread();
	#endif


	int cnt_100ms=0;
	int g_NetAudioSignal_timeout=0;
	int allow_localSource_cnt=0;
	int cnt_100v_netMusic_timeout=0;
    while(1)
    {
		if( (++cnt_100ms)%10 == 0	)
		{
			feed_watchdog();
			sysRunTime++;
			if( (sysRunTime)%10 == 0 )	//每隔10s发送一次上线通知
			{	
				send_online_info();
			}
			
			// 在网络连接建立后启动MQTT客户端
			#if LZY_CUSTOMER_MQTT
			if(IS_MODEL_WITH_4G)
			{
				if(network_init_flag && !g_mqtt_client.running)
				{
					// 初始化MQTT客户端
					mqtt_client_init(&g_mqtt_client);
					printf("MQTT client initialized\n");
					mqtt_client_start(&g_mqtt_client);
					printf("MQTT client started after network initialization\n");
					#if 0
					test_aes_decrypt("NiGVSfCRdOxIz50oSurw+jPZH7LwbijTo4It9togXf/B/JMFxmoPIzc6UPGOmHxs7AxzKHKwBvzD6+MVrB8a1E95oLg3G4tsy7cPUN1BFoc=");
					test_aes_encrypt("cmd=broadcast&content=稳驾慢行&deviceSn=6260370318&num=5");
					test_binary_decrypt_example("NiGVSfCRdOxIz50oSurw+hykhwF0+CsCVBFPcDBoVm+fbbQzyFHYOB7vmQoOgdAclkjul27mjX3eGa7XoQ0motLSaqIcutBeR+33Z7umYfZreMi21+FZYzhcG30McUX1VEDVDl8EqxRWjMhWrATRzNd0Aw+pmCKOHDhvIGsqTdzsoT2QHdYsiqVZMJYFlF1NOYyzQOSa5rrQTTl+L1VVpNc+CrEauA3wFlLi0MlxytGgqhU+kvuAwYwirLw5bTTe");
					#endif
				}
			}
			#endif
			if(sysRunTime == 3)	//3秒后允许检测ADC,因为之前不稳定
			{
				adc_signal_can_detect=1;
			}

			//如果之前存在显示模块,但是现在10秒还没通讯上，则重置存在显示模块标志
			if(g_PreExistDispModule)
			{
				if(!g_CurExistDsipModule && sysRunTime == 10)
				{
					g_PreExistDispModule = 0;
					save_sysconf(INI_SECTION_BASIC,"Exist_DispModule");
				}
			}

			//如果是电源时序器-伟声版本，那么不断发送获取触发器状态指令，直至有应答
			#if (IS_DEVICE_POWER_SEQUENCE && (CUR_POWER_SEQUENCE_TYPE == MODEL_SEQUENCE_POWER_WEISHENG || CUR_POWER_SEQUENCE_TYPE == MODEL_SEQUENCE_POWER_AIPU))
				if(!g_CurExistDsipModule)
				{
					if(CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_C)
					{
						if(isDeviceExtraFeatureValid())
						{
							Disp_Send_Get_SequencePower_TriggerInput();
						}
					}
					else
					{
						Disp_Send_Get_SequencePower_TriggerInput();
					}
				}
			#endif

			if(IS_SERVER_CONNECTED && !HasGotSysTime)
            	requset_Host_Synchroniztion_Time();

			#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
			{
				if(g_NetAudioSignal_can_close == 0)
				{
					g_NetAudioSignal_timeout++;
					if( g_NetAudioSignal_timeout == 15 )
					{
						g_NetAudioSignal_can_close=1;
						if(get_system_source() == SOURCE_NULL)
						{
							GPIO_OutPut_NetAudio_Signal(0);	//关闭网络音频信号IO输出
							if (DECODER_RELAY_TRIGGER_MUSIC || IS_MODEL_WITH_4G)
								Ctrl_Relay_EMC(0);  			//消防强切继电器关闭
						}
					}
				}
				else
				{
					g_NetAudioSignal_timeout=0;
				}

				//如果是木箱，前5秒检测蓝牙模块
				if(IS_MODEL_WOODEN)
				{
					#if AIPU_VERSION
					if(sysRunTime<3 && !g_exist_adcCheck_module)
					{
						ADC_Send_Get_Chip_Info(UART_NEW_DEVICEID_ADC_CHECK);
					}
					#else
					if(sysRunTime<5 && !g_exist_bluetooth_module)
					{
						Bp1048_Send_Get_Info(BP1048_CMD_CHIP_INFO);
					}
					#endif
				}
				//如果收到网络音源指令，那么g_allow_localSource将会置于0，禁止本地音源输出；800ms后解除限制
				//避免正在播放本地音源的时候收到网络音源指令后可能还会听到一丝本地音频的问题
				if(g_allow_localSource == 0)
				{
					if(++allow_localSource_cnt >=8)
					{
						allow_localSource_cnt=0;
						g_allow_localSource=1;
					}
				}
				else
				{
					allow_localSource_cnt = 0;
				}
			}
			
			#elif (IS_DEVICE_POWER_SEQUENCE)
			{
				if(g_seqPwr_timer_can_control == 0)
				{
					g_seqPwr_timer_can_control_timeout++;
					if( g_seqPwr_timer_can_control_timeout == 6 )
					{
						g_seqPwr_timer_can_control=1;
					}
				}
				else
				{
					g_seqPwr_timer_can_control_timeout=0;
				}
			}

			#elif (IS_DEVICE_GPS_SYNCHRONIZER)
			{
				if(isDeviceExtraFeatureValid())
				{
					GPS_Print_Status();
				}
			}
			#endif
		}
		if( cnt_100ms%5 == 0 )
		{
			if(Check_device_status_change())
			{
				if(ENABLE_DISP_UART_MODULE)
				{
					Disp_Send_status_info();
				}
				
				display_update_mainWin_external();
			}
		}

		if (cnt_100ms%1 == 0)
		{
			//播放异常，如果是木箱，且检测到有100V存在，直接切换过去。
			if(IS_MODEL_WOODEN && g_signal_100v)
			{
				if(g_media_source == SOURCE_LOCAL_PLAY || g_media_source == SOURCE_TIMING ||\
					(g_media_source >= SOURCE_AUDIO_COLLECTOR_BASE && g_media_source <= SOURCE_AUDIO_COLLECTOR_MAX))
				{
					if(concentrated_write_need_wait)
					{
						cnt_100v_netMusic_timeout++;
						if(cnt_100v_netMusic_timeout>=5)
						{
							cnt_100v_netMusic_timeout=0;
							Set_zone_idle_status(NULL,__func__, __LINE__,false);
						}
					}
					else
					{
						cnt_100v_netMusic_timeout=0;
					}
				}
				else
				{
					cnt_100v_netMusic_timeout=0;
				}
			}
			else
			{
				cnt_100v_netMusic_timeout=0;
			}
		}

		//龙之音调频版本(C4A1),关闭功放，信号高电平时切换到本地音源（调频），否则网络音源
		#if DECODER_LZY_VERESION_FM_C4A1
		if(Get_Gpio_Value(LZY_FM_SIGNAL_DETECT_GPIO))
		{
			if(!g_lzy_fm_signal_detect)
			{
				g_lzy_fm_signal_detect=1;
				printf("g_lzy_fm_signal_detect=1...\n");
			}
		}
		else
		{
			if(g_lzy_fm_signal_detect)
			{
				g_lzy_fm_signal_detect=0;
				printf("g_lzy_fm_signal_detect=0...\n");
				//无需处理
			}
		}
		#endif


		#if SUPPORT_SIP
		if(IS_EXTENSION_HAS_SIP)
		{
			static bool isInitVoip=false;
			if(network_init_flag && !isInitVoip)
			{
				isInitVoip=true;
				InitVoipInfomation();	//初始化SIP
			}
		}
		#endif

        usleep(100000);
    }
}


void system_loop_pthread(void)
{
	int ret = -1;
	pthread_t Mcast_Pthread;
	pthread_attr_t Pthread_Attr;

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Mcast_Pthread, &Pthread_Attr, (void *)system_loop, NULL);
	if (ret < 0)
	{
		printf("system_loop_pthread create failed!!!\n");
	}
	else
	{
		printf("system_loop_pthread create success!\n");
	}
	pthread_attr_destroy(&Pthread_Attr);
}



bool isDeviceExtraFeatureValid()
{
	if( !( CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_C || CURRENT_DEVICE_MODEL == MODEL_AUDIO_COLLECTOR_C\
		|| CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_C || CURRENT_DEVICE_MODEL == MODEL_REMOTE_CONTROLER_C\
		|| CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_F || CURRENT_DEVICE_MODEL == MODEL_AUDIO_COLLECTOR_F\
		|| CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_F || CURRENT_DEVICE_MODEL == MODEL_REMOTE_CONTROLER_F\
		|| CURRENT_DEVICE_MODEL == MODEL_GPS_SYNCHRONIZER) )
	{
		return true;
	}
	bool hasExtraFeature=false;
	switch(CURRENT_DEVICE_MODEL)
	{
		case MODEL_FIRE_COLLECTOR_C:
		case MODEL_FIRE_COLLECTOR_F:
			if(IS_EXTENSION_HAS_FIRE)
			{
				hasExtraFeature=true;
			}
		break;
		case MODEL_AUDIO_COLLECTOR_C:	//默认就有采集器
		case MODEL_AUDIO_COLLECTOR_F:
			//todo 如果是P030，那么默认就有，如果是新的供易会的，那么默认没有此权限
			hasExtraFeature=true;
		break;
		case MODEL_SEQUENCE_POWER_C:
		case MODEL_SEQUENCE_POWER_F:
			if(IS_EXTENSION_HAS_POWER)
			{
				hasExtraFeature=true;
			}
		break;
		case MODEL_REMOTE_CONTROLER_C:
		case MODEL_REMOTE_CONTROLER_F:
			if(IS_EXTENSION_HAS_REMOTE)
			{
				hasExtraFeature=true;
			}
		break;
		case MODEL_GPS_SYNCHRONIZER:
			if(IS_EXTENSION_HAS_GPS)
			{
				hasExtraFeature=true;
			}
		break;
	}
	return hasExtraFeature;
}