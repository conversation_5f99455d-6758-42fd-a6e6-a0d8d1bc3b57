#include "sysconf.h"
#include "extension.h"

//device-tts|终端TTS, device-sip|终端SIP, device-intercom|终端对讲, device-fire|消防采集器, device-power|电源时序器, device-remote|远程遥控器
char* g_extension_permissions[] = {"device-tts", "device-sip", "device-intercom","device-fire","device-power","device-remote","device-informationPub","device-display","device-gps"};

int g_extension_valid[MAX_AUTH_DEVICE_EXTENSION] ={0};

void saveExtensionConfig(char *permission,uint32_t encryptCode);

// MAC数据转换为字符串
void macArrayToChar(char *szMac, unsigned char*pData)
{
    sprintf(szMac, "%02X:%02X:%02X:%02X:%02X:%02X",
        pData[0],  pData[1],  pData[2],
        pData[3],  pData[4],  pData[5]);
}

// MAC地址转整数
uint64_t mac_to_int(const char* mac) {
    uint64_t mac_int = 0;
    int values[6];
    sscanf(mac, "%x:%x:%x:%x:%x:%x", &values[0], &values[1], &values[2], &values[3], &values[4], &values[5]);
    for (int i = 0; i < 6; i++) {
        mac_int = (mac_int << 8) | values[i];
    }
    return mac_int;
}

// 加密函数
uint32_t encrypt_permission(const char* mac, const char* permission) {
    uint64_t mac_int = mac_to_int(mac);
    uint32_t perm_sum = 0;
    for (int i = 0; permission[i]; i++) {
        perm_sum += permission[i];
    }
    
    return (uint32_t)((mac_int * perm_sum) % 1000000);
}

// 解密函数（实际上是重新加密并比较）
bool decrypt_permission(const char* mac, const char* permission, uint32_t encrypted_value) {
    return encrypt_permission(mac, permission) == encrypted_value;
}


int get_extension_index(char *permission)
{
    int i=0;
    for(;i<MAX_AUTH_DEVICE_EXTENSION;i++)
    {
        if(strcmp(g_extension_permissions[i],permission) == 0)
        {
            return i;
        }
    }
    return -1;
}

bool set_extension_permissions(char *permission,uint32_t encryptCode,int setSwitch,int isTest)
{
    //解密
    int extensionIndex=-1;
    if((extensionIndex=get_extension_index(permission)) == -1)
    {
        return false;
    }
    char szMac[48]={0};
    macArrayToChar(szMac,g_mac_addr);
    bool result=decrypt_permission(szMac,permission,encryptCode);
    printf("set_extension:permission=%s,switch=%d,result=%d,isTest=%d\n",permission,setSwitch,result,isTest);
    if(result)
    {
        if(!isTest)
        {
            g_extension_valid[extensionIndex] = setSwitch;
            //校验正确，写入配置文件
            if(setSwitch == 0 )
            {
                encryptCode=0;
            }
            saveExtensionConfig(permission,encryptCode);
        }
    }
    return result;
}



/**
 * [readSIPConfig 读取扩展功能配置]
 * @return [description]
 */
void readExtensionConfig()
{
    int i=0;
    for(;i<MAX_AUTH_DEVICE_EXTENSION;i++)
    {
        uint32_t code=ini_getl(INI_SECTION_EXTENSION,g_extension_permissions[i],0,INI_EXTENSION_INI_FILE);
        char szMac[48]={0};
        macArrayToChar(szMac,g_mac_addr);
        int result=decrypt_permission(szMac,g_extension_permissions[i],code);
        if(result)
        {
            g_extension_valid[i] = 1;
        }
    }

    //20241019 特定的设备强制打开SIP扩展功能
    char szMac[48]={0};
    macArrayToChar(szMac,g_mac_addr);
    if(strcasecmp(szMac,"2C:73:A9:5F:61:F5") == 0 ||
        strcasecmp(szMac,"2C:08:FC:0E:45:8B") == 0 ||
        strcasecmp(szMac,"2C:08:E4:43:BA:CC") == 0 ||
        strcasecmp(szMac,"2C:08:46:82:74:1B") == 0 ||
        strcasecmp(szMac,"2C:08:A0:99:E0:97") == 0 ||
        strcasecmp(szMac,"2C:08:89:F3:51:73") == 0 ||
        strcasecmp(szMac,"2C:08:5C:9C:12:F0") == 0 ||
        strcasecmp(szMac,"2C:08:AE:D5:E6:9C") == 0
    )
    {
        int index=get_extension_index("device-sip");
        if(index>=0)
        {
            g_extension_valid[index] = 1;
            printf("force extension:Sip!\n");
        }
    }
}

/**
 * [saveSIPConfig 保存扩展功能信息]
 * @return [description]
 */
void saveExtensionConfig(char *permission,uint32_t encryptCode)
{
    ini_putl(INI_SECTION_EXTENSION,permission,encryptCode,INI_EXTENSION_INI_FILE);
}


bool isPermissionNameCorrect(char *permission)
{
    for(int i=0;i<MAX_AUTH_DEVICE_EXTENSION;i++)
    {
        if(strcmp(g_extension_permissions[i],permission) == 0)
        {
            return true;
        }
    }
    return false;
}