#ifndef _EXTENSION_H_
#define _EXTENSION_H_
#include "stdbool.h"
#include "const.h"

enum
{
    AUTH_DEVICE_TTS,
    AUTH_DEVICE_SIP,
    AUTH_DEVICE_INTERCOM,
    AUTH_DEVICE_FIRE,
    AUTH_DEVICE_POWER,
    AUTH_DEVICE_REMOTE,
    AUTH_DEVICE_INFORMATION_PUB,
    AUTH_DEVICE_DISPLAY,
    AUTH_DEVICE_GPS,
    MAX_AUTH_DEVICE_EXTENSION
};

extern char* g_extension_permissions[];
extern int g_extension_valid[MAX_AUTH_DEVICE_EXTENSION];


#define IS_EXTENSION_HAS_TTS		(g_extension_valid[AUTH_DEVICE_TTS] == 1 && IS_DECODER_DEVICE)
#define IS_EXTENSION_HAS_INTERCOM	(g_extension_valid[AUTH_DEVICE_INTERCOM] == 1 && IS_DECODER_DEVICE)
#define IS_EXTENSION_HAS_SIP		(g_extension_valid[AUTH_DEVICE_SIP] == 1 && IS_DECODER_DEVICE)
#define IS_EXTENSION_HAS_INFORMATION_PUB		(g_extension_valid[AUTH_DEVICE_INFORMATION_PUB] == 1 && IS_DECODER_DEVICE)
#define IS_EXTENSION_HAS_DISPLAY	(g_extension_valid[AUTH_DEVICE_DISPLAY] == 1)

#define IS_EXTENSION_HAS_FIRE       (g_extension_valid[AUTH_DEVICE_FIRE] == 1)
#define IS_EXTENSION_HAS_POWER      (g_extension_valid[AUTH_DEVICE_POWER] == 1)
#define IS_EXTENSION_HAS_REMOTE     (g_extension_valid[AUTH_DEVICE_REMOTE] == 1)
#define IS_EXTENSION_HAS_GPS		(g_extension_valid[AUTH_DEVICE_GPS] == 1)

bool isPermissionNameCorrect(char *permission);
int get_extension_index(char *permission);
bool set_extension_permissions(char *permission,unsigned int encryptCode,int setSwitch,int isTest);
void readExtensionConfig();
#endif