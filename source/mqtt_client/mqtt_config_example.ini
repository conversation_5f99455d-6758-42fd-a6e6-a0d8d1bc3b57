# MQTT客户端配置示例文件
# 请根据实际情况修改以下配置参数

[MQTT]
# MQTT服务器地址
broker_host=*************

# MQTT服务器端口
broker_port=1883

# 客户端ID（如果为空，将使用设备MAC地址生成）
client_id=

# 用户名（可选）
username=

# 密码（可选）
password=

# 保持连接间隔（秒）
keepalive=60

# 清除会话标志
clean_session=1

# QoS等级 (0, 1, 2)
qos=1

# 重连间隔（秒）
reconnect_interval=5

# 最大重连次数（0表示无限重连）
max_reconnect_attempts=0

# AES加密密钥（32字节十六进制字符串，可选）
aes_key=

# 设备状态发布间隔（秒）
status_publish_interval=30