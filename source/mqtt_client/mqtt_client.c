#include "sysconf.h"
#if LZY_CUSTOMER_MQTT
#include "mqtt_client.h"
#include "aes.h"

// 使用tiny-aes库的AES实现
// mongoose.h已经在sysconf.h中包含

// mongoose的AES函数已在mongoose.h中声明

// 全局MQTT客户端实例
mqtt_client_t g_mqtt_client;

// AES加密相关
#define AES_KEY "q3FiNTM6CYaVX8RwKI+WVu85K27PKb43Rd6G/cH2+5w="
#define AES_IV_SIZE 16
#define AES_KEY_SIZE 32
#define AES_BLOCK_SIZE 16

// 静态函数声明
static void mqtt_event_handler(struct mg_connection *c, int ev, void *ev_data);
static void *mqtt_thread_func(void *arg);
static int aes_decrypt(const unsigned char *ciphertext, int ciphertext_len,
                      const unsigned char *key, const unsigned char *iv,
                      unsigned char *plaintext);
static char *base64_decode(const char *input, size_t *output_len);
static void subscribe_to_topics(struct mg_connection *c);
static void process_mqtt_message(mqtt_client_t *client, const char *topic, const char *payload);
static char *decrypt_message_payload(const char *payload);

static char *aes_decrypt_wrapper(const char *plaintext_data);
static char *aes_encrypt_wrapper(const char *plaintext_data);

static char *aes_encrypt_binary_wrapper(const unsigned char *data, int data_len);
static int aes_decrypt_binary_wrapper(const char *encrypted_data, unsigned char header_bytes[3], char **json_data);

unsigned char get_sound_state();

/**
 * 初始化MQTT客户端
 */
int mqtt_client_init(mqtt_client_t *client) {
    if (!client) {
        printf("MQTT Client: Invalid client pointer\n");
        return -1;
    }
    
    memset(client, 0, sizeof(mqtt_client_t));
    
    // 设置设备ID为MAC地址
    printf("MQTT Client: g_mac_addr=%02X:%02X:%02X:%02X:%02X:%02X\n", 
           g_mac_addr[0], g_mac_addr[1], g_mac_addr[2], 
           g_mac_addr[3], g_mac_addr[4], g_mac_addr[5]);
    
    mac_to_string(g_mac_addr, client->device_id);
    //打印device_id
    printf("MQTT Client: device_id=%s\n", client->device_id);
    
    // 构建订阅主题 (主题后面带有设备编号，设备编号等同于设备的mac地址)
    snprintf(client->topic, sizeof(client->topic), "%s%s", TOPIC_BROADCAST, client->device_id);
    printf("MQTT Client: topic=%s\n", client->topic);
    
    // 初始化状态发送时间
    client->last_status_time = 0;
    
    client->state = MQTT_STATE_DISCONNECTED;
    client->running = 0;
    
    printf("MQTT Client: Initialized with device ID: %s\n", client->device_id);
    return 0;
}

/**
 * 启动MQTT客户端
 */
int mqtt_client_start(mqtt_client_t *client) {
    if (!client) {
        return -1;
    }
    
    if (client->running) {
        printf("MQTT Client: Already running\n");
        return 0;
    }
    
    client->running = 1;
    
    // 创建MQTT线程
    if (pthread_create(&client->thread, NULL, mqtt_thread_func, client) != 0) {
        printf("MQTT Client: Failed to create thread\n");
        client->running = 0;
        return -1;
    }
    
    printf("MQTT Client: Started\n");
    return 0;
}

/**
 * 停止MQTT客户端
 */
void mqtt_client_stop(mqtt_client_t *client) {
    if (!client || !client->running) {
        return;
    }
    
    client->running = 0;
    
    // 等待线程结束
    pthread_join(client->thread, NULL);
    
    printf("MQTT Client: Stopped\n");
}

/**
 * 销毁MQTT客户端
 */
void mqtt_client_destroy(mqtt_client_t *client) {
    if (!client) {
        return;
    }
    
    mqtt_client_stop(client);
    memset(client, 0, sizeof(mqtt_client_t));
    
    printf("MQTT Client: Destroyed\n");
}

/**
 * MQTT线程函数
 */
static void *mqtt_thread_func(void *arg) {
    mqtt_client_t *client = (mqtt_client_t *)arg;
    
    mg_mgr_init(&client->mgr);
    
    while (client->running) {
        if (client->state == MQTT_STATE_DISCONNECTED) {
            printf("MQTT Client: Connecting to %s\n", MQTT_BROKER_URL);
            
            struct mg_mqtt_opts opts = {
                .client_id = client->device_id,
                .user = mg_str(MQTT_USERNAME),
                .pass = mg_str(MQTT_PASSWORD),
                .keepalive = 60,
                .clean = true,
                .version = 4
            };
            
            client->conn = mg_mqtt_connect(&client->mgr, MQTT_BROKER_URL, &opts, mqtt_event_handler, client);
            if (client->conn) {
                client->state = MQTT_STATE_CONNECTING;
            } else {
                printf("MQTT Client: Failed to create connection\n");
                sleep(5);
                continue;
            }
        }
        
        mg_mgr_poll(&client->mgr, 1000);
        
        // 每隔1分钟发送设备状态
        if (client->state == MQTT_STATE_CONNECTED && client->conn) {
            time_t current_time = time(NULL);
            if (current_time - client->last_status_time >= 60) {  // 60秒 = 1分钟
                publish_device_status(client);
                client->last_status_time = current_time;
            }
        }
        
        // 如果连接断开，重置状态
        if (client->conn && client->conn->is_closing) {
            client->state = MQTT_STATE_DISCONNECTED;
            client->conn = NULL;
        }
    }
    
    mg_mgr_free(&client->mgr);
    return NULL;
}

/**
 * MQTT事件处理函数
 */
static void mqtt_event_handler(struct mg_connection *c, int ev, void *ev_data) {
    mqtt_client_t *client = (mqtt_client_t *)c->fn_data;
    
    switch (ev) {
        case MG_EV_CONNECT:
            printf("MQTT Client: TCP connection established\n");
            break;
            
        case MG_EV_MQTT_OPEN:
            printf("MQTT Client: MQTT connection established\n");
            client->state = MQTT_STATE_CONNECTED;
            subscribe_to_topics(c);
            // MQTT连接成功后主动发送设备状态
            publish_device_status(client);
            client->last_status_time = time(NULL);
            break;
            
        case MG_EV_MQTT_MSG: {
            struct mg_mqtt_message *mm = (struct mg_mqtt_message *)ev_data;
            char topic[256] = {0};
            char payload[2048] = {0};
            
            // 复制主题和负载
            if (mm->topic.len < sizeof(topic)) {
                memcpy(topic, mm->topic.ptr, mm->topic.len);
            }
            if (mm->data.len < sizeof(payload)) {
                memcpy(payload, mm->data.ptr, mm->data.len);
            }
            
            printf("MQTT Client: Received message on topic: %s\n", topic);
            process_mqtt_message(client, topic, payload);
            break;
        }
        
        case MG_EV_CLOSE:
            printf("MQTT Client: Connection closed\n");
            client->state = MQTT_STATE_DISCONNECTED;
            client->conn = NULL;
            break;
            
        case MG_EV_ERROR:
            printf("MQTT Client: Connection error\n");
            client->state = MQTT_STATE_ERROR;
            break;
    }
}

/**
 * 订阅主题
 */
static void subscribe_to_topics(struct mg_connection *c) {
    mqtt_client_t *client = (mqtt_client_t *)c->fn_data;
    
    struct mg_mqtt_opts opts = {
        .topic = mg_str(client->topic),
        .qos = 1
    };
    mg_mqtt_sub(c, &opts);
    printf("MQTT Client: Subscribed to topic: %s\n", client->topic);
}

/**
 * 处理MQTT消息
 */
static void process_mqtt_message(mqtt_client_t *client, const char *topic, const char *payload) 
{
    // 解析JSON消息获取msgid
    cJSON *json = cJSON_Parse(payload);
    //msgid是long类型的时间戳组成,从系统时间得到时间戳，然后转换成字符串
    char msgid[30]={0};
    char msgid_second[20]={0};
    time_t timestamp = time(NULL);
    sprintf(msgid_second, "%ld", timestamp);
    //需要得到毫秒时间戳
    struct timeval tv;
    gettimeofday(&tv, NULL);
    int ms =  tv.tv_usec / 1000;
    //msgid由msgid_second和后面3位毫秒字符串组成
    sprintf(msgid, "%s%03d", msgid_second, ms);

    //打印msgid和msgid_second
    printf("MQTT Client: msgid=%s, msgid_second=%s\n", msgid, msgid_second);

    if (json) {
        cJSON *header = cJSON_GetObjectItem(json, "header");
        if (header) {
            //cJSON *msgid_item = cJSON_GetObjectItem(header, "appId");
        }
    }
    
    // 使用通用解密函数解密消息负载
    char *decrypted_data = decrypt_message_payload(payload);
    if (!decrypted_data) {
        printf("MQTT Client: Failed to decrypt message\n");
        if (json) cJSON_Delete(json);
        return;
    }
    #if 0
    // 检查deviceSn是否存在且匹配本设备
    char *device_sn_pos = strstr(decrypted_data, "deviceSn=");
    if (device_sn_pos) {
        char *device_sn = device_sn_pos + 9;
        // 找到deviceSn值的结束位置（遇到&或字符串结束）
        char *end_pos = strchr(device_sn, '&');
        int sn_len = end_pos ? (end_pos - device_sn) : strlen(device_sn);
        
        // 比较deviceSn是否匹配
        if (strncmp(device_sn, client->device_id, sn_len) != 0 || 
            strlen(client->device_id) != sn_len) {
            printf("MQTT Client: deviceSn mismatch or not for this device\n");
            free(decrypted_data);
            if (json) cJSON_Delete(json);
            return;
        }
    } else {
        printf("MQTT Client: deviceSn not found in message\n");
        free(decrypted_data);
        if (json) cJSON_Delete(json);
        return;
    }
    #endif

    //检查有没有msgid=，如果有，代表是自己的应答，不处理
    char *msgid_pos = strstr(decrypted_data, "msgid=");
    if (msgid_pos) {
        printf("MQTT Client: This is my own response, ignore it\n");
        free(decrypted_data);
        if (json) cJSON_Delete(json);
        return;
    }
    
    // 根据解密后的cmd字段判断消息类型，传递已解密的数据和msgid
    if (strstr(decrypted_data, "cmd=broadcast")) {
        handle_broadcast_message_optimized(decrypted_data, msgid);
    } else if (strstr(decrypted_data, "cmd=reboot")) {
        handle_reboot_message_optimized(decrypted_data, msgid);
    } else if (strstr(decrypted_data, "cmd=stop")) {
        handle_stop_broadcast_message_optimized(decrypted_data, msgid);
    } else if (strstr(decrypted_data, "cmd=play")) {
        handle_volume_control_message_optimized(decrypted_data, msgid);
    }
    
    free(decrypted_data);
    if (json) cJSON_Delete(json);
}

/**
 * 处理广播播放内容消息（优化版本）
 */
void handle_broadcast_message_optimized(const char *decrypted_data, const char *msgid) {
    printf("MQTT Client: Handling broadcast message\n");

    // 解析解密后的内容
    // 格式: cmd=broadcast&content=播报内容&deviceSn=62603703&num=5
    char *content = NULL;
    char *num_str = NULL;
    char *volume_str = NULL;
    
    // 创建decrypted_data的副本用于strtok，因为strtok会修改原字符串
    char *data_copy = strdup(decrypted_data);
    if (!data_copy) {
        printf("MQTT Client: Memory allocation failed\n");
        return;
    }
    
    char *token = strtok(data_copy, "&");
    while (token) {
        if (strncmp(token, "content=", 8) == 0) {
            content = token + 8;
        } else if (strncmp(token, "num=", 4) == 0) {
            num_str = token + 4;
        } else if (strncmp(token, "volume=", 7) == 0) {
            volume_str = token + 7;
        }
        token = strtok(NULL, "&");
    }
    //打印相关参数
    printf("content=%s,num=%s,volume=%s\n", content, num_str, volume_str);
    
    if (content) {
        // 执行TTS播放
        #if SUPPORT_TTS
        if (PriorityIsValid(PRIORITY_API_TTS_MUSIC)) {
            tts_parm ttsParm;
            ttsParm.m_nTestMode = 0;
            ttsParm.m_nRate = 16000;
            ttsParm.m_nSpeed = 50;
            ttsParm.m_nPitch = 50;
            ttsParm.m_nVolume = volume_str ? atoi(volume_str) : curTTSParm.m_nVolume;
            ttsParm.m_playTimes = num_str ? (atoi(num_str) == -1)? 32767 : atoi(num_str) : 1;
            ttsParm.m_nRdn = 0;
            ttsParm.m_playInterval = 2;
            strncpy(ttsParm.m_VoiceName, "xiaoyan", sizeof(ttsParm.m_VoiceName) - 1);
            strncpy(ttsParm.m_strText, content, sizeof(ttsParm.m_strText) - 1);
            
            if (TTSCheckParam(&ttsParm) && CheckTTSJetExist()) {
                printf("MQTT Client: Playing TTS content: %s\n", content);
                TextToSpeech(&ttsParm);
            } else {
                printf("MQTT Client: TTS parameter check failed\n");
            }
        }
        #endif
    }
    
    // 发送成功应答
    send_broadcast_response(&g_mqtt_client, "sucd", msgid);
    
    free(data_copy);
}

/**
 * 处理重启设备消息（优化版本）
 */
void handle_reboot_message_optimized(const char *decrypted_data, const char *msgid) {
    printf("MQTT Client: Handling reboot message\n");
    // 发送成功应答
    send_reboot_response(&g_mqtt_client, "sucd", msgid);
    // 延迟重启，确保应答发送成功
    System_Reboot_DelayMs(3000);
}

/**
 * 处理停止播放消息（优化版本）
 */
void handle_stop_broadcast_message_optimized(const char *decrypted_data, const char *msgid) {
    printf("MQTT Client: Handling stop broadcast message\n");

    // 停止当前播放
    Set_zone_idle_status(NULL,  __func__, __LINE__,true);
    g_ApiPlayType = API_PLAY_NULL;
    // 发送成功应答
    send_stop_response(&g_mqtt_client, "sucd", msgid);
}

/**
 * 处理音量控制消息（优化版本）
 */
void handle_volume_control_message_optimized(const char *decrypted_data, const char *msgid) {
    printf("MQTT Client: Handling volume control message\n");
    
    // 解析消息格式: cmd=play&volume=100
    char *volume_str = strstr(decrypted_data, "volume=");
    if (volume_str) {
        int volume = atoi(volume_str + 7);
        if (volume >= 0 && volume <= 100) {
            printf("MQTT Client: Setting volume to %d\n", volume);
            // 设置音量的具体实现需要根据系统接口调整
            if(g_media_source == SOURCE_API_TTS_MUSIC || g_media_source == SOURCE_NULL)
            {
                curTTSParm.m_nVolume = volume;
            }
            else if(g_media_source == SOURCE_NET_PAGING)
            {
                pager_property.volume = volume;
            }
            else
            {
                g_system_volume = volume;
            }
            // 发送成功应答
            send_volume_response(&g_mqtt_client, "sucd", msgid);
        } else {
            // 音量值无效，发送失败应答
            send_volume_response(&g_mqtt_client, "fail", msgid);
        }
    } else {
        // 没有找到音量参数，发送失败应答
        send_volume_response(&g_mqtt_client, "fail", msgid);
    }
}

/**
 * 处理状态查询请求
 */
void handle_status_request(mqtt_client_t *client, const char *payload) {
    printf("MQTT Client: Handling status request\n");
    
    // 解密消息负载
    char *decrypted_data = decrypt_message_payload(payload);
    if (!decrypted_data) {
        printf("MQTT Client: Failed to decrypt status request message\n");
        return;
    }
    
    // 解析消息格式: cmd=status
    if (strstr(decrypted_data, "cmd=status")) {
        // 发布设备状态
        publish_device_status(client);
    }
    
    free(decrypted_data);
}

/**
 * 发布设备状态
 */
void publish_device_status(mqtt_client_t *client) {
    if (!client || !client->conn || client->state != MQTT_STATE_CONNECTED) {
        return;
    }
    
    // 构建状态数据JSON
    cJSON *status_data = cJSON_CreateObject();
    cJSON *device_info = cJSON_CreateObject();

    cJSON *device_sub_info = cJSON_CreateObject();
    
    cJSON_AddNumberToObject(device_sub_info, "singal_4g", stModule4GInfo.csq_rssi);
    cJSON_AddNumberToObject(device_sub_info, "sound_state",get_sound_state());
    cJSON_AddBoolToObject(device_sub_info, "pa_state",g_media_source!=SOURCE_NULL);

    char firmware_version[32];
    sprintf(firmware_version, "%s.%d.MQ1", LOCAL_FIRMWARE_VERSION,g_device_moduleId);
    cJSON_AddStringToObject(device_sub_info, "sw_version", firmware_version);

    cJSON *sensor_state = cJSON_CreateObject();
    cJSON_AddNumberToObject(sensor_state, "L1_LB_1", 0);
    cJSON_AddItemToObject(device_sub_info, "sensor_state", sensor_state);
    
    cJSON_AddItemToObject(device_info, "S1_ZT_1", device_sub_info);
    cJSON_AddItemToObject(status_data, client->device_id, device_info);
    
    // 将状态数据转换为字符串
    char *status_data_str = cJSON_Print(status_data);
    cJSON_Delete(status_data);
    
    if (!status_data_str) {
        return;
    }
    
    // 构建完整的上报消息，包含header和bodies
    cJSON *report_msg = cJSON_CreateObject();
    cJSON *header = cJSON_CreateObject();
    cJSON *bodies = cJSON_CreateObject();
    
    // 设置header
    cJSON_AddStringToObject(header, "appId", MQTT_CLIENT_ID);
    cJSON_AddItemToObject(report_msg, "header", header);

    //加密前需要在status_data_str前面增加三个字节：1.数据格式类型固定为1，2.status_data_str长度的高位字节，3.status_data_str长度的低位字节。
    int data_len = strlen(status_data_str);
    unsigned char *data_with_header = malloc(data_len + 3);
    if (!data_with_header) {
        free(status_data_str);
        return;
    }
    data_with_header[0] = 1;
    data_with_header[1] = (data_len >> 8) & 0xFF;
    data_with_header[2] = data_len & 0xFF;
    memcpy(data_with_header + 3, status_data_str, data_len);

    // 使用二进制加密函数处理包含3字节头部的数据
    char *encrypted_data = aes_encrypt_binary_wrapper(data_with_header, data_len + 3);
    if (encrypted_data) {
        cJSON_AddStringToObject(bodies, "body", encrypted_data);
        free(encrypted_data);
    }
    cJSON_AddItemToObject(report_msg, "bodies", bodies);
    
    free(data_with_header);
    free(status_data_str);
    
    // 发送上报消息
    char *report_str = cJSON_Print(report_msg);
    if (report_str) {
        char topic[128];
        snprintf(topic, sizeof(topic), "%s%s%s", TOPIC_BROADCAST, client->device_id, TOPIC_STATUS_SUFFIX);
        
        struct mg_mqtt_opts opts = {
            .topic = mg_str(topic),
            .message = mg_str(report_str),
            .qos = 1
        };
        
        mg_mqtt_pub(client->conn, &opts);
        printf("MQTT Client: Published device status with encryption\n");
        
        free(report_str);
    }
    
    cJSON_Delete(report_msg);
}

/**
 * Base64解码函数 - 使用mongoose内置实现
 */
static char *base64_decode(const char *input, size_t *output_len) {
    if (!input) return NULL;
    
    size_t input_len = strlen(input);
    size_t max_output_len = (input_len * 3) / 4 + 1;
    char *buffer = malloc(max_output_len);
    if (!buffer) return NULL;
    
    // 使用mongoose的base64解码
    struct mg_str encoded = mg_str(input);
    int decoded_len = mg_base64_decode(encoded.ptr, encoded.len, buffer, max_output_len);
    
    if (decoded_len <= 0) {
        free(buffer);
        return NULL;
    }
    
    *output_len = decoded_len;
    return buffer;
}

/**
 * Base64编码函数
 */
static char *base64_encode(const unsigned char *input, size_t input_len) {
    if (!input || input_len == 0) return NULL;
    
    // 计算输出长度
    size_t output_len = ((input_len + 2) / 3) * 4 + 1;
    char *output = malloc(output_len);
    if (!output) return NULL;
    
    // 使用mongoose的base64编码
    struct mg_str data = {(char*)input, input_len};
    int encoded_len = mg_base64_encode(data.ptr, data.len, output, output_len);
    
    if (encoded_len <= 0) {
        free(output);
        return NULL;
    }
    
    output[encoded_len] = '\0';
    return output;
}

/**
 * 使用tiny-aes库的AES-256-CBC加密实现
 */
static int aes_encrypt(const unsigned char *plaintext, int plaintext_len,
                      const unsigned char *key, const unsigned char *iv,
                      unsigned char *ciphertext) {
    printf("MQTT Client: Starting tiny-aes AES-256-CBC encryption\n");
    
    // 检查输入参数
    if (!plaintext || !key || !iv || !ciphertext || plaintext_len <= 0) {
        printf("MQTT Client: Invalid encryption parameters\n");
        return -1;
    }
    
    // 计算PKCS7填充后的长度
    int padding = 16 - (plaintext_len % 16);
    int padded_len = plaintext_len + padding;
    
    // 创建填充后的明文
    unsigned char *padded_plaintext = malloc(padded_len);
    if (!padded_plaintext) {
        printf("MQTT Client: Memory allocation failed\n");
        return -1;
    }
    
    // 复制原始明文
    memcpy(padded_plaintext, plaintext, plaintext_len);
    
    // 添加PKCS7填充
    for (int i = plaintext_len; i < padded_len; i++) {
        padded_plaintext[i] = padding;
    }
    #if 0
    printf("MQTT Client: Plaintext length: %d, padded length: %d, padding: %d\n", 
           plaintext_len, padded_len, padding);
    #endif
    // 初始化AES上下文
    struct AES_ctx ctx;
    AES_init_ctx_iv(&ctx, key, iv);
    
    // 复制填充后的明文到密文缓冲区
    memcpy(ciphertext, padded_plaintext, padded_len);
    
    // 使用tiny-aes进行CBC加密
    AES_CBC_encrypt_buffer(&ctx, ciphertext, padded_len);
    
    free(padded_plaintext);

    printf("MQTT Client: AES-256-CBC encryption completed, length: %d\n", padded_len);

    return padded_len;
}

/**
 * 使用tiny-aes库的AES-256-CBC解密实现
 */
static int aes_decrypt(const unsigned char *ciphertext, int ciphertext_len,
                      const unsigned char *key, const unsigned char *iv,
                      unsigned char *plaintext) {
    printf("MQTT Client: Starting tiny-aes AES-256-CBC decryption\n");
    
    // 检查输入参数
    if (!ciphertext || !key || !iv || !plaintext || ciphertext_len <= 0) {
        printf("MQTT Client: Invalid decryption parameters\n");
        return -1;
    }
    
    // 检查密文长度是否为16字节的倍数
    if (ciphertext_len % 16 != 0) {
        printf("MQTT Client: Invalid ciphertext length (not multiple of 16)\n");
        return -1;
    }
    
    // 初始化AES上下文
    struct AES_ctx ctx;
    AES_init_ctx_iv(&ctx, key, iv);
    
    // 复制密文到明文缓冲区
    memcpy(plaintext, ciphertext, ciphertext_len);
    
    // 使用tiny-aes进行CBC解密
    AES_CBC_decrypt_buffer(&ctx, plaintext, ciphertext_len);
    
    // 移除PKCS7填充
    int total_decrypted = ciphertext_len;
    if (total_decrypted > 0) {
        int padding = plaintext[total_decrypted - 1];
        if (padding > 0 && padding <= 16 && padding <= total_decrypted) {
            // 验证填充是否有效
            bool valid_padding = true;
            for (int i = total_decrypted - padding; i < total_decrypted; i++) {
                if (plaintext[i] != padding) {
                    valid_padding = false;
                    break;
                }
            }
            if (valid_padding) {
                total_decrypted -= padding;
            }
        }
    }
    
    printf("MQTT Client: tiny-aes AES-256-CBC decryption completed, length: %d\n", total_decrypted);
    return total_decrypted;
}

/**
 * 简单的AES CBC解密包装函数
 * 注意：这是一个简化的实现，实际生产环境中需要更完整的AES CBC解密
 */
static char *aes_decrypt_wrapper(const char *encrypted_data) {
    if (!encrypted_data) return NULL;
    
    // Base64解码密钥
    size_t key_len;
    unsigned char *key_data = (unsigned char *)base64_decode(AES_KEY, &key_len);
    if (!key_data) {
        printf("MQTT Client: Failed to decode AES key\n");
        return NULL;
    }
    #if 0
    printf("MQTT Client: AES key decoded, length: %zu (expected: %d)\n", key_len, AES_KEY_SIZE);
    // 打印密钥的前几个字节用于调试
    printf("MQTT Client: Key bytes: ");
    for (int i = 0; i < (key_len < 16 ? key_len : 16); i++) {
        printf("%02x ", key_data[i]);
    }
    printf("\n");
    #endif
    
    if (key_len != AES_KEY_SIZE) {
        free(key_data);
        printf("MQTT Client: Invalid AES key length: %zu, expected: %d\n", key_len, AES_KEY_SIZE);
        return NULL;
    }
    
    // Base64解码加密数据
    size_t encrypted_len;
    unsigned char *encrypted_bytes = (unsigned char *)base64_decode(encrypted_data, &encrypted_len);
    if (!encrypted_bytes) {
        free(key_data);
        printf("MQTT Client: Failed to decode encrypted data\n");
        return NULL;
    }
    else
    {
        //printf("MQTT Client: Encrypted data decoded, length: %zu\n", encrypted_len);
    }
    
    // 提取IV（前16字节）
    if (encrypted_len < AES_IV_SIZE) {
        free(key_data);
        free(encrypted_bytes);
        printf("MQTT Client: Encrypted data too short, length: %zu\n", encrypted_len);
        return NULL;
    }
    
    //printf("MQTT Client: Encrypted data length: %zu\n", encrypted_len);
    
    unsigned char *iv = encrypted_bytes;
    unsigned char *ciphertext = encrypted_bytes + AES_IV_SIZE;
    int ciphertext_len = encrypted_len - AES_IV_SIZE;
    #if 0
    // 打印IV用于调试
    printf("MQTT Client: IV bytes: ");
    for (int i = 0; i < AES_IV_SIZE; i++) {
        printf("%02x ", iv[i]);
    }
    printf("\n");
    
    printf("MQTT Client: Ciphertext length: %d\n", ciphertext_len);
    #endif
    // 分配输出缓冲区
    unsigned char *plaintext = malloc(ciphertext_len + 1);
    if (!plaintext) {
        free(key_data);
        free(encrypted_bytes);
        return NULL;
    }
    
    // 使用完整的AES CBC解密
    int decrypted_len = aes_decrypt(ciphertext, ciphertext_len, key_data, iv, plaintext);
    
    //printf("MQTT Client: AES decryption returned length: %d\n", decrypted_len);
    
    free(key_data);
    free(encrypted_bytes);
    
    if (decrypted_len <= 0) {
        printf("MQTT Client: AES decryption failed\n");
        free(plaintext);
        return NULL;
    }
    #if 0
    // 打印解密结果的前几个字节用于调试
    printf("MQTT Client: Decrypted bytes: ");
    for (int i = 0; i < (decrypted_len < 32 ? decrypted_len : 32); i++) {
        if (plaintext[i] >= 32 && plaintext[i] <= 126) {
            printf("%c", plaintext[i]);
        } else {
            printf("[%02x]", plaintext[i]);
        }
    }
    printf("\n");
    #endif
    // 添加字符串结束符
    plaintext[decrypted_len] = '\0';
    
    printf("MQTT Client: Final decrypted string: %s\n", (char *)plaintext);
    
    //printf("MQTT Client: AES decryption completed, length: %d\n", decrypted_len);
    return (char *)plaintext;
}

/**
 * 解密消息负载的通用函数
 */
static char *decrypt_message_payload(const char *payload) {
    if (!payload) return NULL;
    
    // 解析JSON消息
    cJSON *json = cJSON_Parse(payload);
    if (!json) {
        printf("MQTT Client: Invalid JSON payload\n");
        return NULL;
    }
    
    cJSON *header = cJSON_GetObjectItem(json, "header");
    cJSON *bodies = cJSON_GetObjectItem(json, "bodies");
    
    if (!header || !bodies) {
        printf("MQTT Client: Missing header or bodies in JSON\n");
        cJSON_Delete(json);
        return NULL;
    }
    
    cJSON *appId = cJSON_GetObjectItem(header, "appId");
    cJSON *body = cJSON_GetObjectItem(bodies, "body");
    
    if (!appId || !body || !cJSON_IsString(body)) {
        printf("MQTT Client: Invalid message format\n");
        cJSON_Delete(json);
        return NULL;
    }
    #if 0
    // 检查appId是否匹配 (使用MAC地址)
    char mac_str[DEVICE_ID_SIZE];
    mac_to_string(g_mac_addr, mac_str);
    if (strcmp(appId->valuestring, mac_str) != 0) {
        printf("MQTT Client: AppId mismatch, expected: %s, got: %s\n", mac_str, appId->valuestring);
        cJSON_Delete(json);
        return NULL;
    }
    #endif
    
    // AES解密body内容
    char *decrypted_data = aes_decrypt_wrapper(body->valuestring);
    cJSON_Delete(json);
    
    if (!decrypted_data) {
        printf("MQTT Client: Failed to decrypt message\n");
    }
    
    return decrypted_data;
}


/**
 * AES CBC解密包装函数（二进制版本）
 * 专门用于解析包含3字节头部的状态数据
 * @param encrypted_data Base64编码的加密数据
 * @param header_bytes 输出参数，用于存储解析出的3字节头部数据
 * @param json_data 输出参数，用于存储解析出的JSON字符串
 * @return 成功返回0，失败返回-1
 */
static int aes_decrypt_binary_wrapper(const char *encrypted_data, unsigned char header_bytes[3], char **json_data) {
    if (!encrypted_data || !header_bytes || !json_data) return -1;
    
    *json_data = NULL;
    
    // Base64解码密钥
    size_t key_len;
    unsigned char *key_data = (unsigned char *)base64_decode(AES_KEY, &key_len);
    if (!key_data) {
        printf("MQTT Client: Failed to decode AES key\n");
        return -1;
    }
    
    if (key_len != AES_KEY_SIZE) {
        free(key_data);
        printf("MQTT Client: Invalid AES key length: %zu, expected: %d\n", key_len, AES_KEY_SIZE);
        return -1;
    }
    
    // Base64解码加密数据
    size_t encrypted_len;
    unsigned char *encrypted_bytes = (unsigned char *)base64_decode(encrypted_data, &encrypted_len);
    if (!encrypted_bytes) {
        free(key_data);
        printf("MQTT Client: Failed to decode encrypted data\n");
        return -1;
    }
    
    // 提取IV（前16字节）
    if (encrypted_len < AES_IV_SIZE) {
        free(key_data);
        free(encrypted_bytes);
        printf("MQTT Client: Encrypted data too short, length: %zu\n", encrypted_len);
        return -1;
    }
    
    unsigned char *iv = encrypted_bytes;
    unsigned char *ciphertext = encrypted_bytes + AES_IV_SIZE;
    int ciphertext_len = encrypted_len - AES_IV_SIZE;
    
    // 分配输出缓冲区
    unsigned char *plaintext = malloc(ciphertext_len + 1);
    if (!plaintext) {
        free(key_data);
        free(encrypted_bytes);
        return -1;
    }
    
    // 使用完整的AES CBC解密
    int decrypted_len = aes_decrypt(ciphertext, ciphertext_len, key_data, iv, plaintext);
    
    free(key_data);
    free(encrypted_bytes);
    
    if (decrypted_len <= 0 || decrypted_len < 3) {
        printf("MQTT Client: AES decryption failed or data too short\n");
        free(plaintext);
        return -1;
    }
    
    // 提取前3字节头部数据
    header_bytes[0] = plaintext[0];  // 数据格式类型
    header_bytes[1] = plaintext[1];  // JSON数据长度高位字节
    header_bytes[2] = plaintext[2];  // JSON数据长度低位字节
    
    // 计算JSON数据长度
    int json_len = (header_bytes[1] << 8) | header_bytes[2];
    
    // 验证JSON长度是否合理
    if (json_len <= 0 || json_len > (decrypted_len - 3)) {
        printf("MQTT Client: Invalid JSON length in header: %d, available: %d\n", json_len, decrypted_len - 3);
        free(plaintext);
        return -1;
    }
    
    // 分配JSON字符串内存并复制数据
    *json_data = malloc(json_len + 1);
    if (!*json_data) {
        free(plaintext);
        return -1;
    }
    
    memcpy(*json_data, plaintext + 3, json_len);
    (*json_data)[json_len] = '\0';
    
    free(plaintext);
    
    printf("MQTT Client: Decoded header: [%02x %02x %02x], JSON length: %d\n", 
           header_bytes[0], header_bytes[1], header_bytes[2], json_len);
    printf("MQTT Client: Decoded JSON: %s\n", *json_data);
    
    return 0;
}

/**
 * 测试解码函数的示例
 * 演示如何使用aes_decrypt_binary_wrapper函数
 */
void test_binary_decrypt_example(const char *encrypted_data) {
    unsigned char header_bytes[3];
    char *json_data = NULL;
    
    printf("MQTT Client: Testing binary decrypt function...\n");
    
    int result = aes_decrypt_binary_wrapper(encrypted_data, header_bytes, &json_data);
    
    if (result == 0) {
        printf("MQTT Client: Decode successful!\n");
        printf("MQTT Client: Header - Format: 0x%02x, Length: %d (0x%02x%02x)\n", 
               header_bytes[0], (header_bytes[1] << 8) | header_bytes[2], 
               header_bytes[1], header_bytes[2]);
        printf("MQTT Client: JSON Data: %s\n", json_data);
        
        // 释放分配的内存
        if (json_data) {
            free(json_data);
        }
    } else {
        printf("MQTT Client: Decode failed!\n");
    }
}

/**
 * AES CBC加密包装函数（字符串版本）
 */
static char *aes_encrypt_wrapper(const char *plaintext_data) {
    if (!plaintext_data) return NULL;
    
    int plaintext_len = strlen(plaintext_data);
    return aes_encrypt_binary_wrapper((unsigned char *)plaintext_data, plaintext_len);
}

/**
 * AES CBC加密包装函数（二进制版本）
 * @param data 要加密的二进制数据
 * @param data_len 数据长度
 * @return Base64编码的加密结果，需要调用者释放内存
 */
static char *aes_encrypt_binary_wrapper(const unsigned char *data, int data_len) {
    if (!data || data_len <= 0) return NULL;
    
    // Base64解码密钥
    size_t key_len;
    unsigned char *key_data = (unsigned char *)base64_decode(AES_KEY, &key_len);
    if (!key_data) {
        printf("MQTT Client: Failed to decode AES key\n");
        return NULL;
    }
    
    if (key_len != AES_KEY_SIZE) {
        free(key_data);
        printf("MQTT Client: Invalid AES key length: %zu, expected: %d\n", key_len, AES_KEY_SIZE);
        return NULL;
    }
    
    // 使用固定的IV（从期望输出中提取）
    unsigned char iv[AES_IV_SIZE] = {0x36, 0x21, 0x95, 0x49, 0xf0, 0x91, 0x74, 0xec, 
                                     0x48, 0xcf, 0x9d, 0x28, 0x4a, 0xea, 0xf0, 0xfa};
    
    // 计算加密后的最大长度（包含填充）
    int max_ciphertext_len = ((data_len / 16) + 1) * 16;
    unsigned char *ciphertext = malloc(max_ciphertext_len);
    if (!ciphertext) {
        free(key_data);
        return NULL;
    }
    
    // 执行AES加密
    int encrypted_len = aes_encrypt(data, data_len, key_data, iv, ciphertext);
    
    free(key_data);
    
    if (encrypted_len <= 0) {
        printf("MQTT Client: AES encryption failed\n");
        free(ciphertext);
        return NULL;
    }
    
    // 创建包含IV的完整加密数据
    unsigned char *full_encrypted = malloc(AES_IV_SIZE + encrypted_len);
    if (!full_encrypted) {
        free(ciphertext);
        return NULL;
    }
    
    // 将IV放在前面，然后是密文
    memcpy(full_encrypted, iv, AES_IV_SIZE);
    memcpy(full_encrypted + AES_IV_SIZE, ciphertext, encrypted_len);
    
    free(ciphertext);
    
    // Base64编码完整的加密数据
    char *encoded_result = base64_encode(full_encrypted, AES_IV_SIZE + encrypted_len);
    
    free(full_encrypted);
    
    return encoded_result;
}

void test_aes_decrypt(const char* str)
{
    char *decrypted_data = aes_decrypt_wrapper(str);
    if(decrypted_data)
    {
        printf("test_aes_decrypt:%s\n",decrypted_data);
        //如果是一个json，需要打印出来
        cJSON *json = cJSON_Parse(decrypted_data);
        if(json)
        {
            char *json_str = cJSON_Print(json);
            printf("test_aes_decrypt:%s\n",json_str);
            free(json_str);
            cJSON_Delete(json);
        }
        free(decrypted_data);
    }
    else
    {
        printf("test_aes_decrypt:failed\n");
    }
}

void test_aes_encrypt(const char* str)
{
    printf("=== AES加密测试 ===\n\n");
    
    const char *expected_output = "NiGVSfCRdOxIz50oSurw+jPZH7LwbijTo4It9togXf/B/JMFxmoPIzc6UPGOmHxs7AxzKHKwBvzD6+MVrB8a1E95oLg3G4tsy7cPUN1BFoc=";
    
    printf("明文: %s\n", str);
    printf("期望密文: %s\n\n", expected_output);
    
    // Base64解码密钥
    size_t key_len;
    unsigned char *key_data = (unsigned char *)base64_decode(AES_KEY, &key_len);
    if (!key_data || key_len != AES_KEY_SIZE) {
        printf("❌ 密钥解码失败\n");
        if (key_data) free(key_data);
        return;
    }
    
    // 使用固定的IV（从期望输出中提取）
    unsigned char fixed_iv[16] = {0x36, 0x21, 0x95, 0x49, 0xf0, 0x91, 0x74, 0xec, 
                                  0x48, 0xcf, 0x9d, 0x28, 0x4a, 0xea, 0xf0, 0xfa};
    
    printf("使用的IV (hex): ");
    for (int i = 0; i < 16; i++) {
        printf("%02x ", fixed_iv[i]);
    }
    printf("\n\n");
    
    int plaintext_len = strlen(str);
    int max_ciphertext_len = ((plaintext_len / 16) + 1) * 16;
    unsigned char *ciphertext = malloc(max_ciphertext_len);
    if (!ciphertext) {
        free(key_data);
        printf("❌ 内存分配失败\n");
        return;
    }
    
    // 执行AES加密
    int encrypted_len = aes_encrypt((unsigned char *)str, plaintext_len, key_data, fixed_iv, ciphertext);
    
    free(key_data);
    
    if (encrypted_len <= 0) {
        printf("❌ AES加密失败\n");
        free(ciphertext);
        return;
    }
    
    // 创建包含IV的完整加密数据
    unsigned char *full_encrypted = malloc(AES_IV_SIZE + encrypted_len);
    if (!full_encrypted) {
        free(ciphertext);
        printf("❌ 内存分配失败\n");
        return;
    }
    
    // 将IV放在前面，然后是密文
    memcpy(full_encrypted, fixed_iv, AES_IV_SIZE);
    memcpy(full_encrypted + AES_IV_SIZE, ciphertext, encrypted_len);
    
    free(ciphertext);
    
    // Base64编码完整的加密数据
    char *encoded_result = base64_encode(full_encrypted, AES_IV_SIZE + encrypted_len);
    
    free(full_encrypted);
    
    if (!encoded_result) {
        printf("❌ Base64编码失败\n");
        return;
    }
    
    printf("加密结果: %s\n", encoded_result);
    printf("加密长度: %d (不含IV)\n\n", encrypted_len);
    
    // 比较结果
    if (strcmp(encoded_result, expected_output) == 0) {
        printf("✅ 加密成功！结果匹配\n");
    } else {
        printf("❌ 加密失败！结果不匹配\n");
        printf("期望: %s\n", expected_output);
        printf("实际: %s\n", encoded_result);
        printf("期望长度: %zu\n", strlen(expected_output));
        printf("实际长度: %zu\n", strlen(encoded_result));
        
        // 计算字符差异
        int diff_count = 0;
        size_t min_len = strlen(expected_output) < strlen(encoded_result) ? strlen(expected_output) : strlen(encoded_result);
        for (size_t i = 0; i < min_len; i++) {
            if (expected_output[i] != encoded_result[i]) {
                diff_count++;
            }
        }
        printf("字符差异数: %d\n", diff_count);
    }
    
    free(encoded_result);
}

/**
 * MAC地址转字符串函数
 */
void mac_to_string(unsigned char *mac_addr, char *mac_str) {
    snprintf(mac_str, DEVICE_ID_SIZE, "%02X%02X%02X%02X%02X%02X",
             mac_addr[0], mac_addr[1], mac_addr[2],
             mac_addr[3], mac_addr[4], mac_addr[5]);
    //全部转换成大写
    for (int i = 0; i < DEVICE_ID_SIZE; i++) {
        mac_str[i] = toupper(mac_str[i]);
    }
}

/**
 * 通用应答发送函数
 * @param client MQTT客户端
 * @param cmd 命令类型
 * @param result 结果状态
 * @param msgid 消息ID
 */
void send_common_response(mqtt_client_t *client, const char *cmd, const char *result, const char *msgid) 
{
    if (!client || !client->conn || client->state != MQTT_STATE_CONNECTED) {
        return;
    }
    
    // 构建应答消息
    cJSON *response = cJSON_CreateObject();
    cJSON *header = cJSON_CreateObject();
    cJSON *bodies = cJSON_CreateObject();
    
    // 设置header
    cJSON_AddStringToObject(header, "appId", MQTT_CLIENT_ID);
    if (msgid) {
        cJSON_AddStringToObject(header, "msgid", msgid);
    }
    cJSON_AddItemToObject(response, "header", header);
    
    // 设置bodies
    char response_data[128];
    snprintf(response_data, sizeof(response_data), "cmd=%s&result=%s&msgid=%s", 
             cmd, result, msgid ? msgid : "");
    
    // 加密响应数据
    char *encrypted_data = aes_encrypt_wrapper(response_data);
    if (encrypted_data) {
        cJSON_AddStringToObject(bodies, "body", encrypted_data);
        free(encrypted_data);
    }
    cJSON_AddItemToObject(response, "bodies", bodies);
    
    // 发送应答
    char *response_str = cJSON_Print(response);
    if (response_str) {
        char topic[128];
        snprintf(topic, sizeof(topic), "%s%s", TOPIC_BROADCAST, client->device_id);
        
        struct mg_mqtt_opts opts = {
            .topic = mg_str(topic),
            .message = mg_str(response_str),
            .qos = 1
        };
        
        mg_mqtt_pub(client->conn, &opts);
        printf("MQTT Client: Sent %s response: %s\n", cmd, result);
        
        free(response_str);
    }
    
    cJSON_Delete(response);
}

/**
 * 发送广播播放应答
 */
void send_broadcast_response(mqtt_client_t *client, const char *result, const char *msgid) 
{
    send_common_response(client, "broadcast", result, msgid);
}

/**
 * 发送重启设备应答
 */
void send_reboot_response(mqtt_client_t *client, const char *result, const char *msgid) 
{
    send_common_response(client, "reboot", result, msgid);
}

/**
 * 发送停止播放应答
 */
void send_stop_response(mqtt_client_t *client, const char *result, const char *msgid) 
{
    send_common_response(client, "stop", result, msgid);
}

/**
 * 发送音量控制应答
 */
void send_volume_response(mqtt_client_t *client, const char *result, const char *msgid) 
{
    send_common_response(client, "play", result, msgid);
}





//获取音量等级
unsigned char get_sound_state()
{
    if(curTTSParm.m_nVolume == 0)
    {
        return 0;
    }
    else if(curTTSParm.m_nVolume > 0 && curTTSParm.m_nVolume <= 40)
    {
        return 1;
    }
    else if(curTTSParm.m_nVolume > 40 && curTTSParm.m_nVolume <= 70)
    {
        return 2;
    }
    else
    {
        return 3;
    }
}

#endif