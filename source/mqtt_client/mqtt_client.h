#ifndef MQTT_CLIENT_H
#define MQTT_CLIENT_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <time.h>
#include "../mongoose/mongoose.h"
#include "../cJSON/cJSON.h"
#include "../tts/tts.h"

// MQTT连接配置
#define MQTT_BROKER_URL "mqtt://iot.ghatg.com:1883"
#define MQTT_CLIENT_ID "62000000"
#define MQTT_USERNAME "admin"
#define MQTT_PASSWORD "1qaz2wsx"

// Topic定义
#define TOPIC_BROADCAST "/iot/broadcast/"
#define TOPIC_STATUS_SUFFIX "/status"

// 设备编号 - 使用MAC地址
#define DEVICE_ID_SIZE 13  // MAC地址字符串长度 (不包含冒号)

// MQTT客户端状态
typedef enum {
    MQTT_STATE_DISCONNECTED = 0,
    MQTT_STATE_CONNECTING,
    MQTT_STATE_CONNECTED,
    MQTT_STATE_ERROR
} mqtt_state_t;

// MQTT客户端结构体
typedef struct {
    struct mg_mgr mgr;
    struct mg_connection *conn;
    mqtt_state_t state;
    pthread_t thread;
    int running;
    char device_id[DEVICE_ID_SIZE];  // 使用MAC地址作为设备ID
    char topic[64];      // 订阅主题: /iot/broadcast/[MAC地址]
    time_t last_status_time;  // 上次发送状态的时间
} mqtt_client_t;

// 广播播放内容参数
typedef struct {
    int b_num;      // 播报次数，-1为循环播放
    int b_value;    // 播报内容长度
    char *b_content; // 播报内容
    int volume;     // 音量
} broadcast_params_t;

// 函数声明
int mqtt_client_init(mqtt_client_t *client);
int mqtt_client_start(mqtt_client_t *client);
void mqtt_client_stop(mqtt_client_t *client);
void mqtt_client_destroy(mqtt_client_t *client);

// 消息处理函数
void handle_broadcast_message(const char *payload);
void handle_reboot_message(const char *payload);
void handle_stop_broadcast_message(const char *payload);
void handle_volume_control_message(const char *payload);
void handle_status_request(mqtt_client_t *client, const char *payload);

// 优化版本的消息处理函数（避免重复解码）
void handle_broadcast_message_optimized(const char *decrypted_data, const char *msgid);
void handle_reboot_message_optimized(const char *decrypted_data, const char *msgid);
void handle_stop_broadcast_message_optimized(const char *decrypted_data, const char *msgid);
void handle_volume_control_message_optimized(const char *decrypted_data, const char *msgid);

// 状态发布函数
void publish_device_status(mqtt_client_t *client);

// 应答函数
void send_common_response(mqtt_client_t *client, const char *cmd, const char *result, const char *msgid);
void send_broadcast_response(mqtt_client_t *client, const char *result, const char *msgid);
void send_reboot_response(mqtt_client_t *client, const char *result, const char *msgid);
void send_stop_response(mqtt_client_t *client, const char *result, const char *msgid);
void send_volume_response(mqtt_client_t *client, const char *result, const char *msgid);

// 工具函数
void mac_to_string(unsigned char *mac_addr, char *mac_str);

// 测试函数
void test_aes_decrypt(const char* str);
void test_aes_encrypt(const char* str);

void test_binary_decrypt_example(const char *encrypted_data);

// 全局MQTT客户端实例
extern mqtt_client_t g_mqtt_client;

#endif // MQTT_CLIENT_H