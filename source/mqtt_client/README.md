# MQTT客户端模块

## 功能概述

本模块实现了基于MQTT协议的设备控制功能，支持5条核心指令：

1. **广播播放** - 本地TTS语音播放
2. **设备重启** - 远程重启设备
3. **停止播放** - 停止当前播放内容
4. **音量控制** - 调节设备音量
5. **状态查询** - 获取设备当前状态

## MQTT主题结构

### 订阅主题（接收指令）
- `netspeaker/{device_id}/broadcast` - 广播播放指令
- `netspeaker/{device_id}/reboot` - 重启指令
- `netspeaker/{device_id}/stop` - 停止播放指令
- `netspeaker/{device_id}/volume` - 音量控制指令
- `netspeaker/{device_id}/status` - 状态查询指令

### 发布主题（状态上报）
- `netspeaker/{device_id}/status/response` - 设备状态响应
- `netspeaker/{device_id}/heartbeat` - 设备心跳

## 指令格式

### 1. 广播播放指令
```json
{
    "command": "broadcast",
    "text": "播放内容",
    "volume": 80,
    "play_count": 1,
    "voice_name": "xiaoyan",
    "speed": 50,
    "pitch": 50
}
```

### 2. 设备重启指令
```json
{
    "command": "reboot",
    "delay": 5
}
```

### 3. 停止播放指令
```json
{
    "command": "stop"
}
```

### 4. 音量控制指令
```json
{
    "command": "volume",
    "level": 75
}
```

### 5. 状态查询指令
```json
{
    "command": "status"
}
```

## 状态响应格式

```json
{
    "device_id": "AA:BB:CC:DD:EE:FF",
    "status": "online",
    "volume": 80,
    "media_source": "SOURCE_NULL",
    "media_status": "SONG_STOP",
    "ip_address": "*************",
    "timestamp": 1640995200
}
```

## 配置说明

参考 `mqtt_config_example.ini` 文件进行配置：

- `broker_host`: MQTT服务器地址
- `broker_port`: MQTT服务器端口（默认1883）
- `client_id`: 客户端ID（为空时使用MAC地址）
- `username/password`: 认证信息（可选）
- `qos`: 消息质量等级（0-2）
- `aes_key`: AES加密密钥（可选）

## 编译集成

模块已集成到主Makefile系统中：
- 头文件：`mqtt_client/mqtt_client.h`
- 源文件：`mqtt_client/mqtt_client.c`
- 编译配置：`mqtt_client/mqtt_client.mk`

## 使用方法

1. 在main.c中已自动集成初始化代码
2. 系统启动后会自动初始化MQTT客户端
3. 网络连接建立5秒后自动启动MQTT连接
4. 设备会定期发布状态信息和心跳

## 依赖项

- mongoose库（MQTT客户端实现）
- cJSON库（JSON解析）
- TTS模块（语音播放）
- 系统音频模块

## 注意事项

1. 确保网络连接正常
2. MQTT服务器地址配置正确
3. 防火墙允许MQTT端口通信
4. TTS功能需要相应的语音库支持