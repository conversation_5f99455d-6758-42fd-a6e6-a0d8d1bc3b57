#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <semaphore.h>
#include "sysconf.h"
#include "../network/udp_client.h"
#include "../network/network_process.h"
#include "ampControler.h"

#if(IS_DEVICE_AMP_CONTROLER)


st_ampControler_info g_ampControler_info;

/*********************************************************************
 * @fn      ampControler_channel_process
 *
 * @brief   功放控制器通道处理线程
 *
 * @param   void
 *
 * @return	void
 */
void *ampControler_channel_process(void)
{
	unsigned char curBackupChannelId = 0xff;
	unsigned char tmp_masterChannelStatusArray[MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM]={0};
	memcpy(tmp_masterChannelStatusArray,g_ampControler_info.masterChannelStatusArray,sizeof(tmp_masterChannelStatusArray));
	unsigned char tmp_backupChannelStatus = g_ampControler_info.backupChannelStatus;

	unsigned int readyPrintCount = 0;

	// 初始化信号量
    sem_init(&g_ampControler_info.data_ready_sem, 0, 0);

	while(1)
	{
		// 等待信号量通知
        sem_wait(&g_ampControler_info.data_ready_sem);

		bool bChannelStatusChange = false;
		
        for(int i=0;i<MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM;i++)
        {
			if(g_ampControler_info.masterChannelLineIn[i] == g_ampControler_info.masterChannelAmpOut[i])
			{
				if(g_ampControler_info.masterChannelStatusArray[i] != AMP_CONTROLER_CHANNEL_STATUS_FAULT)
				{
					g_ampControler_info.channelRecoveryCount[i]=0;

					if(g_ampControler_info.masterChannelLineIn[i] == 0)
					{
						//g_ampControler_info.masterChannelStatusArray[i] = AMP_CONTROLER_CHANNEL_STATUS_IDLE;
						// 从正常状态转到空闲状态需要增加3秒超时
						if(g_ampControler_info.masterChannelStatusArray[i] == AMP_CONTROLER_CHANNEL_STATUS_NORMAL) 
						{
							if(g_ampControler_info.channelIdleCount[i] < 300) {
								g_ampControler_info.channelIdleCount[i]++;
								continue; // 保持原状态直到超时
							}
						}
						g_ampControler_info.channelIdleCount[i] = 0;
						g_ampControler_info.masterChannelStatusArray[i] = AMP_CONTROLER_CHANNEL_STATUS_IDLE;
					}
					else if(g_ampControler_info.masterChannelLineIn[i] == 1)
					{
						g_ampControler_info.channelIdleCount[i] = 0;
						g_ampControler_info.masterChannelStatusArray[i] = AMP_CONTROLER_CHANNEL_STATUS_NORMAL;
					}
				}
				else	//是故障状态，则需要等待超时时间后恢复
				{
					if(g_ampControler_info.channelRecoveryCount[i] < 800) // 1200次*10ms=12s
					{
						g_ampControler_info.channelRecoveryCount[i]++;
					}
					else
					{
						// 通道已恢复超过10秒
						g_ampControler_info.channelRecoveryCount[i] = 0;
						if(g_ampControler_info.masterChannelLineIn[i] == 0)
						{
							g_ampControler_info.masterChannelStatusArray[i] = AMP_CONTROLER_CHANNEL_STATUS_IDLE;
						}
						else if(g_ampControler_info.masterChannelLineIn[i] == 1)
						{
							g_ampControler_info.masterChannelStatusArray[i] = AMP_CONTROLER_CHANNEL_STATUS_NORMAL;
						}
					}
				}
			}
			else
			{
				//如果不是故障状态
				if(g_ampControler_info.masterChannelStatusArray[i] != AMP_CONTROLER_CHANNEL_STATUS_FAULT)
				{
					if(g_ampControler_info.masterChannelLineIn[i] == 1)	//有输入信号才去设置故障状态，否则就是认为空闲
					{
						if(g_ampControler_info.channelFaultCount[i] < 40) // 30次*10ms=300ms
						{
							g_ampControler_info.channelFaultCount[i]++;
						}
						else
						{
							g_ampControler_info.channelFaultCount[i] = 0; // 重置计数器;
							g_ampControler_info.masterChannelStatusArray[i] = AMP_CONTROLER_CHANNEL_STATUS_FAULT;
						}
					}
					else	//没有输入信号，但是有输出信号，认为是接线错误，空闲状态
					{
						g_ampControler_info.channelFaultCount[i] = 0; // 重置计数器
						g_ampControler_info.masterChannelStatusArray[i] = AMP_CONTROLER_CHANNEL_STATUS_NORMAL;
					}
				}
				else	//如果已经是故障状态，重置计数器
				{
					g_ampControler_info.channelFaultCount[i] = 0; // 重置计数器
				}
			}
        }

		if(g_ampControler_info.backupChannelLineIn != g_ampControler_info.backupChannelAmpOut)
		{
			if(g_ampControler_info.backupChannelLineIn == 1)
			{
				g_ampControler_info.backupChannelStatus = AMP_CONTROLER_CHANNEL_STATUS_FAULT;
			}
			else
			{
				g_ampControler_info.backupChannelStatus = AMP_CONTROLER_CHANNEL_STATUS_NORMAL;
			}
		}
		else if(g_ampControler_info.backupChannelAmpOut == 1)
		{
			g_ampControler_info.backupChannelStatus = AMP_CONTROLER_CHANNEL_STATUS_NORMAL;
		}
		else if(g_ampControler_info.backupChannelAmpOut == 0)
		{
			g_ampControler_info.backupChannelStatus = AMP_CONTROLER_CHANNEL_STATUS_IDLE;
		}


		//判断是否有通道状态改变
		for(int i=0;i<MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM;i++)
		{
			if(tmp_masterChannelStatusArray[i]!=g_ampControler_info.masterChannelStatusArray[i])
			{
				bChannelStatusChange = true;
			}
		}

		//判断是否有故障通道
		bool hasFaultChannel=false;
		for(int i=0;i<MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM;i++)
		{
			if(g_ampControler_info.masterChannelStatusArray[i] == AMP_CONTROLER_CHANNEL_STATUS_FAULT)
			{
				hasFaultChannel=true;
				g_ampControler_info.backupChannelId = i;
				break;
			}
		}
		if(!hasFaultChannel)
		{
			g_ampControler_info.backupChannelId = 0xff;
		}

		//将临时状态数组更新为当前状态数组
		memcpy(tmp_masterChannelStatusArray,g_ampControler_info.masterChannelStatusArray,sizeof(tmp_masterChannelStatusArray));

		if(tmp_backupChannelStatus!=g_ampControler_info.backupChannelStatus)
		{
			bChannelStatusChange = true;
			tmp_backupChannelStatus = g_ampControler_info.backupChannelStatus;
			printf("tmp_backupChannelStatus change:%d...\n",tmp_backupChannelStatus);
		}



		if(bChannelStatusChange)
		{
			//通知服务器
			Host_Query_Set_Amp_Controler_Status(NULL);
			//更新串口显示屏
			amp_controler_uart_ChannelChange();
			#if 0
			//打印结构体信息
			for(int i=0;i<MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM;i++)
			{
				printf("g_ampControler_info.masterChannelStatusArray[%d] = %d\n",i,g_ampControler_info.masterChannelStatusArray[i]);
			}
			printf("g_ampControler_info.backupChannelStatus = %d\n",g_ampControler_info.backupChannelStatus);
			printf("g_ampControler_info.backupChannelId = %d\n",g_ampControler_info.backupChannelId);
			#endif	
		}

		if(curBackupChannelId != g_ampControler_info.backupChannelId)
		{
			curBackupChannelId = g_ampControler_info.backupChannelId;
			//printf("curBackupChannelId = %d\n",curBackupChannelId);
			if(curBackupChannelId == 0xff)
			{
				AMP_CONTROLER_Send_Control_Channel(7);
			}
			else
			{
				AMP_CONTROLER_Send_Control_Channel(curBackupChannelId+1);
			}
		}

#if 0
		readyPrintCount++;
		if(readyPrintCount%50 == 0)	//1秒1次
		{
			//打印结构体信息
			printf("print g_ampControler_info...\n");
			for(int i=0;i<MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM;i++)
			{
				printf("g_ampControler_info.masterChannelLineIn[%d] = %d\n",i,g_ampControler_info.masterChannelLineIn[i]);
			}
			for(int i=0;i<MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM;i++)
			{
				printf("g_ampControler_info.masterChannelAmpOut[%d] = %d\n",i,g_ampControler_info.masterChannelAmpOut[i]);
			}
			printf("g_ampControler_info.backupChannelLineIn = %d\n",g_ampControler_info.backupChannelLineIn);
			printf("g_ampControler_info.backupChannelAmpOut = %d\n",g_ampControler_info.backupChannelAmpOut);
		}
#endif
	}

	// 销毁信号量
    sem_destroy(&g_ampControler_info.data_ready_sem);

	pthread_exit(NULL);
}

/*********************************************************************
 * @fn      start_ampControler_channel_process_pthread
 *
 * @brief   启动功放控制器通道处理线程
 *
 * @param   void
 *
 * @return	void
 */
void start_ampControler_channel_process_pthread(void)
{
	int ret = -1;
	pthread_t Mcast_Pthread;
	pthread_attr_t Pthread_Attr;

	// 初始化信号量
    sem_init(&g_ampControler_info.data_ready_sem, 0, 0);

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Mcast_Pthread, &Pthread_Attr, (void *)ampControler_channel_process, NULL);
	if (ret < 0)
	{
		printf("start_ampControler_channel_process_pthread create failed!!!\n");
	}
	else
	{
		printf("start_ampControler_channel_process_pthread create success!\n");
	}
	pthread_attr_destroy(&Pthread_Attr);
}





#endif
