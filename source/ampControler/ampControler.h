#ifndef _PHONE_GATEWAY_PROCESS_H_
#define _PHONE_GATEWAY_PROCESS_H_

#include <semaphore.h>

#define MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM    5
#define MAX_AMP_CONTROLER_BACKUP_CHANNEL_NUM    1


enum{
    AMP_CONTROLER_CHANNEL_STATUS_IDLE = 0,      //空闲
    AMP_CONTROLER_CHANNEL_STATUS_NORMAL = 1,    //正常
    AMP_CONTROLER_CHANNEL_STATUS_FAULT = 2,     //故障
    AMP_CONTROLER_CHANNEL_STATUS_WIRING = 3,    //接线错误
};

/*******功放控制器相关变量************/
typedef struct {
    unsigned char masterChannelLineIn[MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM];
    unsigned char masterChannelAmpOut[MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM];
    unsigned char backupChannelLineIn;
    unsigned char backupChannelAmpOut;
    unsigned char masterChannelStatusArray[MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM];
    unsigned char backupChannelStatus;
    unsigned char backupChannelId;      //备份通道ID:0~4
    unsigned short channelRecoveryCount[MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM]; // 修改为计数器形式
    unsigned short channelFaultCount[MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM]; // 新增：故障计数变量
    unsigned short channelIdleCount[MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM]; // 新增：空闲状态计数变量
    sem_t data_ready_sem;  // 新增信号量
}st_ampControler_info;
extern st_ampControler_info g_ampControler_info;


void *ampControler_channel_process(void);

#endif