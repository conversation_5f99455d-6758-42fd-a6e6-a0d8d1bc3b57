/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-06 14:30:07 
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2022-02-24 18:09:29
 */
#include <semaphore.h>
#include <time.h>
#include "network_protocol.h"
#include "sysconf.h"
#include "udp_client.h"
#include "network_process.h"
#include "multicast.h"
#include "../kcp/mkcp.h"
#include "uart/bluetooth.h"

#if SUPPORT_SIP
#include "../pjsip/appConfig.h"
#endif

_stNetworkRecv stNetworkRecv;

static pthread_mutex_t mutex_netPkg=PTHREAD_MUTEX_INITIALIZER;	//集中模式歌曲超时检测任务锁
sem_t sem_netPkgRecv;  	//信号量-接收到网络数据

void NetPkg_Add(int NetType,unsigned char *buf,unsigned int len)
{
	if(len > MAX_BUF_SIZE )
		return;
	pthread_mutex_lock(&mutex_netPkg);
	memcpy(stNetworkRecv.stNetworkBuf[stNetworkRecv.write_pos].buf,buf,len);
	stNetworkRecv.stNetworkBuf[stNetworkRecv.write_pos].len = len;
	stNetworkRecv.stNetworkBuf[stNetworkRecv.write_pos].type = NetType;
	stNetworkRecv.write_pos++;
	if(stNetworkRecv.write_pos >= MAX_BUF_NUM )
	{
		stNetworkRecv.write_pos = 0;
	}
	sem_post(&sem_netPkgRecv);
	pthread_mutex_unlock(&mutex_netPkg);
}



/*********************************************************************
 * @fn      network_data_process
 *
 * @brief   网络数据处理
 *
 * @param   void
 *
 * @return	void
 */
void *network_data_process(void)
{
	while(1)
	{
		sem_wait(&sem_netPkgRecv);

		#if 0
		//pthread_mutex_lock(&mutex_netPkg);
		if(stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].type == 0)
		{
			//pthread_mutex_unlock(&mutex_netPkg);
			usleep(2000);		//2ms
			continue;
		}
		#endif

		if(stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].type == NET_TYPE_UNICAST_SERVER || 
			stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].type == NET_TYPE_UNICAST_PAGER ||
			stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].type == NET_TYPE_TCP_SERVER	)
		{
			int device_model = stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].buf[4];

			if(stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].type == NET_TYPE_UNICAST_SERVER)
			{
				if(device_model == DEVICE_MODEL_HOST)
				{
					udp_host_addr=tmp_udp_host_addr;
					//printf("DEVICE_MODEL_HOST1：%d.%d.%d.%d\n", (udp_host_addr.sin_addr.s_addr)&0xff, (udp_host_addr.sin_addr.s_addr>>8)&0xff,(udp_host_addr.sin_addr.s_addr>>16)&0xff,(udp_host_addr.sin_addr.s_addr>>24)&0xff);
				}
				else if(device_model == DEVICE_MODEL_NETWORK_TOOLS)
				{
					udp_networktools_addr=tmp_udp_networktools_addr;
				}
			}
			else if(stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].type == NET_TYPE_UNICAST_PAGER)
			{
				if(device_model == DEVICE_MODEL_PAGING)
				{
					udp_pager_addr=tmp_udp_pager_addr;
				}
			}

			network_pkg_process(stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].type,stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].buf,stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].len);
		}
		else if(stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].type == NET_TYPE_MULTICAST_SERVER)
		{
			Multicast_Pkg_Process(stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].buf,stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].len);
		}

		memset(&stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos], 0, sizeof(_stNetworkBuf));

		stNetworkRecv.read_pos++;
		if(stNetworkRecv.read_pos >= MAX_BUF_NUM )
		{
			stNetworkRecv.read_pos = 0;
		}
		//pthread_mutex_unlock(&mutex_netPkg);
	}
	pthread_exit(NULL);
}

/*********************************************************************
 * @fn      start_network_data_process_pthread
 *
 * @brief   启动网络数据处理线程
 *
 * @param   void
 *
 * @return	void
 */
void start_network_data_process_pthread(void)
{
	sem_init(&sem_netPkgRecv,0,0);

	int ret = -1;
	pthread_t Mcast_Pthread;
	pthread_attr_t Pthread_Attr;

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Mcast_Pthread, &Pthread_Attr, (void *)network_data_process, NULL);
	if (ret < 0)
	{
		printf("start_network_data_process_pthread create failed!!!\n");
	}
	else
	{
		printf("start_network_data_process_pthread create success!\n");
	}
	pthread_attr_destroy(&Pthread_Attr);
}


/*********************************************************************
 * @fn      Calculate_XorDat
 *
 * @brief   计算数据包异或校验和
 *
 * @param   Data - 校验数据
 *			Length - 校验数据长度
 *
 * @return  Xordata - 校验和
 */
unsigned char Calculate_XorDat(unsigned char *Data, int Length)
{
	int i;
	unsigned char Xordata = 0;

	for (i = 0; i < Length; i++)
	{
		Xordata ^= Data[i];
	}

	return Xordata;
}


/*********************************************************************
 * @fn      convert_domain_to_ip
 *
 * @brief   将域名转换为IP地址，如果输入为IP地址转出来也为IP地址
 *
 * @param   domain - 主机域名
 *
 * @return	void
 */
static void convert_domain_to_ip(unsigned char *domain)
{
	struct hostent *host;
	struct in_addr **addr;

	// 通过域名获取IP地址
	if ((host = gethostbyname(domain)) == NULL)
	{
		perror("gethostbyname");
	}
	else
	{
		memset(g_web_server_ip, 0x00, sizeof(g_web_server_ip));
		addr = (struct in_addr**)host->h_addr_list;
		sprintf(g_web_server_ip, "%s", inet_ntoa(*addr[0]));
		printf("g_web_server_ip : %s\n", g_web_server_ip);
	}
}


/****************************************************
 * @fn      Network_Send_Compose_CMD
 *
 * @brief   //发送命令组合
 *
 * @param  unsigned char *sendBuf -发送包指针,unsigned int cmd - 命令字,unsigned char deviceType - 设备类型,unsigned char Datalen-数据负载长度,unsigned char *Data 数据包
 *
 * @return	int 应答包长度
 */

int Network_Send_Compose_CMD(unsigned char *sendBuf,unsigned int cmd,unsigned char deviceType,int Datalen,unsigned char *Data)
{
	int i;
	int SendLen;
	memset(sendBuf,0,sizeof(sendBuf));
	sendBuf[0]=cmd>>8;		//命令字高位
	sendBuf[1]=cmd;			//命令字低位
	sendBuf[2]=0;			//包序号
	sendBuf[3]=0;			//保留
	sendBuf[4]=deviceType;	//设备型号
	sendBuf[5]=0;			//包属性4bit&编码格式4bit
	sendBuf[6]=Datalen>>8;
	sendBuf[7]=Datalen;
	for(i=0;i<Datalen;i++)
	{
		sendBuf[8+i]=Data[i];
	}
	sendBuf[8+Datalen]=Calculate_XorDat(Data,Datalen);

	SendLen=9+Datalen;

	return SendLen;
}



/*********************************************************************
 * @fn      respond_null_payload_pkg
 *
 * @brief   空负载应答包
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void respond_null_payload_pkg(unsigned char *rxbuf)
{
	int payloadSize = 0;
	unsigned char send_buf[MAX_BUF_SIZE];
	unsigned char client;
	memset(send_buf, 0x00, sizeof(send_buf)); // 清空缓存

	client = rxbuf[4];
	// 添加命令字
	send_buf[0] = rxbuf[0];
	send_buf[1] = rxbuf[1];
	// 添加包序号
	send_buf[2] = rxbuf[2];
	// 保留位
	send_buf[3] = RESERVE;
	// 产品型号
	send_buf[4] = CURRENT_DEVICE_MODEL;
	// 包属性及编码格式
	send_buf[5] = rxbuf[5];

	// 添加负载数据长度
	send_buf[6] = payloadSize/256;
	send_buf[7] = payloadSize%256;

	// 计算校验和
	send_buf[payloadSize+PAYLOAD_START] = Calculate_XorDat(&send_buf[PAYLOAD_START], payloadSize);

	if (client == DEVICE_MODEL_HOST || client == DEVICE_MODEL_NETWORK_TOOLS)
	{
		// 发送数据
		host_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}
	else if (client == DEVICE_MODEL_PAGING)
	{
		pager_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}
}



/***********************************  **********************************
 * @fn      pkg_paging_notification
 *
 * @brief   被寻呼通知
 *
 * @param   payload_Buf -- 数据负载缓存
 * 			payload_Length --数据负载长度
 * 			ctl_Model -- 发送端型号
 *			IsWebPagering--是否网页广播寻呼
 *
 * @return	void
 *********************************************************************/
void pkg_paging_notification(unsigned char *payload_Buf,int payload_Length,unsigned char ctl_Model,unsigned char IsWebPagering)
{
	g_allow_localSource=0;

	int i = 0;
	int Temp_Vol = 0;
	int pos=0;

	unsigned char *rxbuf=payload_Buf;
	
	int pager_cmd=rxbuf[pos++];		//寻呼指令（开始OR结束）
	printf("pager_cmd=%x\n",pager_cmd);
	unsigned char *pager_mac=&rxbuf[pos];
	pos+=6;
	int temp_pagingType = PAGING_TYPE_NULL;
	if(	IsWebPagering )
	{
		temp_pagingType = PAGING_TYPE_APP;
	}
	unsigned char authority=rxbuf[pos++];		//优先级
	int volume=rxbuf[pos++];
   	int sample_rate = (rxbuf[pos]<<16)+(rxbuf[pos+1]<<8)+(rxbuf[pos+2]);   //3个字节
   	pos += 3;
	int fmt = rxbuf[pos++];
   	int channels = rxbuf[pos++];
	int decode_pcm_type = rxbuf[pos++];

	int network_type=0;
	int multicastIP_len=0;
	unsigned char *multicstIP=NULL;
	if(	!IsWebPagering )
	{
		network_type=rxbuf[pos++];
		multicastIP_len=rxbuf[pos++];
		multicstIP=&rxbuf[pos];
		//由于寻呼台程序有误，单播通知的时候用的长度不是实际的。
		//Add start 20210825
		multicastIP_len=strlen(MCAST_PAGING_RECV_ADDR);
		//Add end 20210825
		pos+=multicastIP_len;
	}
	int port = (rxbuf[pos]<<8) + rxbuf[pos+1];
	pos+=2;

	//如果有寻呼类型字段,那么保存
	if(	!IsWebPagering )
	{
		if(pos<payload_Length)
		{
			//代表有寻呼类型字段
			temp_pagingType=rxbuf[pos++];
		}
	}


	//寻呼台 停止寻呼
	//如果当前音源优先级比寻呼高，且寻呼命令为开始
	if( !PriorityIsValid(PRIORITY_NET_PAGING) && ( pager_cmd == PAGING_START) )
	{
		return;
	}
	//如果当前音源优先级比寻呼高，且寻呼命令为停止且不为最高优先级
	if(	!PriorityIsValid(PRIORITY_NET_PAGING) && pager_cmd == PAGING_STOP && authority == 0 )
	{
		//普通用户不能停止比寻呼高优先级的音源
		return;
	}

	// 0x08:被寻呼，0x80：取消寻呼
	if ( g_paging_status != pager_cmd )			//控制寻呼命令与当前状态不等
	{       	
	
		int mac_match=1;
		for(i=0;i<6;i++)
		{
			if(pager_mac[i] != pager_property.pager_mac[i])
			{
				mac_match=0;
				break;
			}
		}
		
		//当前已处于寻呼状态，寻呼台发起停止，判断MAC是否匹配,不匹配则不处理
		if(pager_cmd == PAGING_STOP && !mac_match)
		{
			return;
		}
		//当前已处于停止，寻呼台发起寻呼
		if (pager_cmd == PAGING_START)
		{
			g_paging_timing_count=0;	  //避免停止寻呼后又马上进入开始状态时，寻呼超时检测对现有状态的破坏
		}
		else	//当前寻呼台或更高优先级寻呼台发送停止命令
		{
			concentrate_repeat_paly_enable = 1;
		}

		g_paging_status = pager_cmd;
	}
	else						//控制寻呼命令与当前状态一致
	{
		#if !CANCEL_PAGER_SOURCE_PRIORITY
		if(g_paging_status == PAGING_START && authority > pager_property.authority)			//已经处于寻呼，现在又发起新的寻呼且当前
		{
			printf("----PAGING_START2---\n");
		}
		#else
		if(g_paging_status == PAGING_START)			//已经处于寻呼
		{
            printf("----PAGING_START2---\n");
		}
		#endif
		else
		{
			if(g_paging_status == PAGING_STOP)			//现在是寻呼停止，又发一个停止命令下来
			{
#if 0			//寻呼台停止寻呼指令不再结束非寻呼音源
				if(g_media_source != SOURCE_NULL && g_media_source != SOURCE_AUX)		//如果现在有其他音源在播放,则相当于与置空闲状态，禁止重试播放
					Set_zone_idle_status(rxbuf,  __func__, __LINE__,true);
#endif
				return;

			}
			else	//已经处于寻呼，现在又发起新的寻呼且当前优先级<=寻呼优先级
			{
                printf("已经是寻呼状态,新的优先级<=当前优先级\n");
				return;
			}
		}
	}

	if (g_paging_status == PAGING_START)
	{ 
		//记录寻呼台MAC、寻呼优先级、寻呼类型、寻呼组播地址、寻呼采样率、声道数、采样率精度、寻呼音量

		pager_property.authority = authority;
		pager_property.decodeType = decode_pcm_type;
		pager_property.audioRate = sample_rate;
		pager_property.audioFMT = fmt;
		pager_property.audioChannels = channels;
		pager_property.volume = volume;
		pager_property.pagingType=temp_pagingType;
		printf("Start:pager_property.pagingType:%d\r\n",pager_property.pagingType);
		memcpy(pager_property.pager_mac,pager_mac,6);
		if(	!IsWebPagering )
		{
			memset(pager_property.multicastIP,0,sizeof(pager_property.multicastIP));
			memcpy(pager_property.multicastIP,multicstIP,multicastIP_len);
			printf("Paging:pager_property.multicastIP=%s,port=%d\n",pager_property.multicastIP,pager_property.multicastPort);
		}
		pager_property.multicastPort = port;
		
		if(pager_need_exit_flag == 1)
		{
			int count = 300;
			while(count-- && pager_need_exit_flag)
			{
				usleep(10000);
			}
		}
        pager_need_exit_flag=0;

		if(isMulticastPagerRecvThreadRun)
		{
			pager_need_exit_flag = 1;
			usleep(200000);
			pager_need_exit_flag = 0;
		}

		//清空节目名称
		memset(g_media_name,0,sizeof(g_media_name));
		
		Clean_All_Audio(1,1);
		set_system_source(SOURCE_NET_PAGING);
		
		pkg_query_current_status(NULL);	//发送当前状态
		//i2s_write_init(sample_rate, fmt, channels);            		// 初始化dsp
		Open_Audio_Out(sample_rate,fmt,channels);
		
		printf("decode_pcm_type=%d\n",decode_pcm_type);
		if(	!IsWebPagering )
		{
			struct in_addr pager_ipAdder;
    		pager_ipAdder.s_addr = inet_addr(pager_property.multicastIP);  
			if(ip_addr_ismulticast(&pager_ipAdder) && g_network_mode == NETWORK_MODE_LAN)       //组播地址才进行组播接收，否则KCP接收
			{
				start_mcast_paging_recv_pthread();
			}
		}
		else 
		{
			if(g_network_mode == NETWORK_MODE_LAN)
			{
				start_mcast_paging_recv_pthread();
			}
			//KCP接收(TCP模式下)
		}
		Create_Concentrated_Mode_Play_Task();
		Create_Paging_Timing_Task();
		
		paging_repeat_again_enable=1;
	}
	else	//停止
	{
		pre_web_pager_strem_timeout=0;
		memset(pre_web_pager_cmd_pkg,0,sizeof(pre_web_pager_cmd_pkg));
		g_paging_timing_count = PAGING_TIMING_COUNT_MAX;	//关闭寻呼检测线程
		printf("Stop Paging...\n");

		pager_need_exit_flag = 1;
		usleep(200000);
		pager_need_exit_flag = 0;
	}

}



/*********************************************************************
 * @fn      HOST_CONCENTRATED_PLAY_SOURCE
 *
 * @brief   主机通知终端播放节目源（集中模式）
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 *********************************************************************/
void HOST_CONCENTRATED_PLAY_SOURCE(unsigned char *pkg_data)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len = 1;
	data[0] = 1;	//1-接受  2-拒绝
	int sendLen = 0;
	// 发送数据
	unsigned char client=pkg_data[4];

	g_allow_localSource=0;

	#if ENABLE_LISTEN_TEST
	refresh_listen_upload();
	#endif


	if(g_system_work_mode != WORK_MODE_CONCENTRATED)
	{
		g_system_work_mode = WORK_MODE_CONCENTRATED;
		Set_zone_idle_status(NULL,  __func__, __LINE__,true);
	}
	
	//需要确认是否进入播放
	int play_type = pkg_data[3];
	printf("play_type:%d\n", play_type);
	if(play_type == 1)  //控制设备或其它原因使分区变成停止, 而且该分区有播放任务，变为空闲状态，主机会发送命令确认（其它设备停止该分区或者意外停止）
	{
		if(!concentrate_repeat_paly_enable)			//如果不允许播放集中模式下再次请求播放，比如用寻呼台控制停止
		{   
		  	printf("--concentrate_repeat_paly_enable==0--\n");
		 	data[0]=2;		//拒绝
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CONCENTRATED_PLAY_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
			host_udp_send_data(sendBuf, sendLen);
			return;
		}
	}


   	int payload_length = (pkg_data[6]<<8)+pkg_data[7];
	int pos = PAYLOAD_START;
	int play_source = pkg_data[pos++];

	int priority_isOK = 0;	//优先级检测标志
	int priority=0xff;
    printf("[HOST_CONCENTRATED_PLAY] source = 0x%02x\n", play_source);
	switch(play_source)
	{
		case 1:	//歌曲点播 
			priority=PRIORITY_NET_PLAY;
				if(PriorityIsValid(PRIORITY_NET_PLAY))
					priority_isOK=1;
			break;
		case 2:	//歌曲定时 
			priority=PRIORITY_TIMING;
				if(PriorityIsValid(PRIORITY_TIMING))
					priority_isOK=1;
			break;
		#if 0
		case 3:	//钟声 
			priority=PRIORITY_RING;
				if(PriorityIsValid(PRIORITY_RING) )
					priority_isOK=1;
			break;
		#endif
		case 4: //消防告警
			priority=PRIORITY_FIRE_ALARM;
			if(PriorityIsValid(PRIORITY_FIRE_ALARM) )
				priority_isOK = 1;
			break;
		case 5://监控事件
			priority=PRIORITY_MONITOR_EV;
            break;
	}
	if(PriorityIsValid(priority) && !(g_signal_100v && priority<PRIORITY_100V) ) //满足优先级要求，但100V信号存在时且目的优先级小于100V时不进入（消防告警除外）
	{
		priority_isOK = 1;
	}


	if(!priority_isOK)				//不满足优先级要求，不能拒绝，以便主机下次继续重发指令
	{   
		#if LOCAL_SOURCE_PRIORITY_HIGHEST
		//如果是本地音源，那么返回接受
		if(get_system_source() == SOURCE_AUX)
		{
			data[0] = 1;//返回接受
		}
		else
		#endif
		{
			if(play_type == 1)
				data[0] = 1;//返回接受
			else
			{
				data[0] = 2;//返回拒绝
			}
		}
		sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CONCENTRATED_PLAY_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
		host_udp_send_data(sendBuf, sendLen);
		return;
	}

        	
	int t_concentrated_song_type = pkg_data[pos++];
   	int t_concentrated_song_sample_rate = (pkg_data[pos]<<16)+(pkg_data[pos+1]<<8)+(pkg_data[pos+2]);   //3个字节
   	pos += 3;
   	int t_concentrated_song_fmt = pkg_data[pos++];
   	int t_concentrated_song_channels = pkg_data[pos++];

   	//判断参数是否正常，否则返回
   	int parameter_true = 1;
   	if( !( t_concentrated_song_type == 1 || t_concentrated_song_type == 2 ) )
   	{
	 	parameter_true=0;
   	}
   	if( !( t_concentrated_song_sample_rate == 8000 || t_concentrated_song_sample_rate == 11025 || t_concentrated_song_sample_rate == 12000 ||
		t_concentrated_song_sample_rate == 16000 || t_concentrated_song_sample_rate == 22050 || t_concentrated_song_sample_rate == 24000 ||
		 t_concentrated_song_sample_rate == 32000 || t_concentrated_song_sample_rate == 44100 || t_concentrated_song_sample_rate == 48000 ||
		  t_concentrated_song_sample_rate == 88200 || t_concentrated_song_sample_rate == 96000 ) )
   	{
	 	parameter_true=0;
   	}
   	if( !( t_concentrated_song_fmt == 16 || t_concentrated_song_fmt == 24 || t_concentrated_song_fmt == 32 ) )
   	{
	 	parameter_true=0;
   	}
   	if( !( t_concentrated_song_channels == 1 || t_concentrated_song_channels == 2 ) )
   	{
	 	parameter_true=0;
   	}

   	if(t_concentrated_song_type == 0&&
		t_concentrated_song_sample_rate == 0&&
		t_concentrated_song_fmt == 0&&
		t_concentrated_song_channels == 0)
   	{
   		parameter_true =1;
   	}

	if(parameter_true == 0)
	{
        printf("parameter error,reject!\n");
		data[0] = 2;
		sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CONCENTRATED_PLAY_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
		host_udp_send_data(sendBuf, sendLen);
		return;
	}

	if(t_concentrated_song_type == 0 &&
		t_concentrated_song_sample_rate == 0 &&
		t_concentrated_song_fmt == 0 &&
		t_concentrated_song_channels == 0)
	{
		return;
	}


#if 1
   	g_centralized_mode_timeout_count = 0;//超时计数
        
	Clean_All_Audio(0,1);

	g_concentrated_song_type = t_concentrated_song_type;//歌曲类型
	g_concentrated_song_sample_rate = t_concentrated_song_sample_rate;//采样率
	g_concentrated_song_fmt = t_concentrated_song_fmt;//采样精度
	g_concentrated_song_channels = t_concentrated_song_channels;//声道数


	int song_name_length = pkg_data[pos++];
	memset(g_media_name,0,sizeof(g_media_name));
	memcpy(g_media_name,pkg_data+pos,song_name_length);
	pos+=song_name_length;
	int multicast_length=pkg_data[pos++];
	memset(g_concentrated_multicast_address,0,sizeof(g_concentrated_multicast_address));
	memcpy(g_concentrated_multicast_address,pkg_data+pos,multicast_length);

	pos += multicast_length;
	g_concentrated_multicast_port = (pkg_data[pos]<<8)+pkg_data[pos+1];
	pos += 2;
	
	printf("g_concentrated_multicast_address:%s\n", g_concentrated_multicast_address);
	printf("g_concentrated_multicast_port:%d\n", g_concentrated_multicast_port);
	printf("g_concentrated_song_type:%d\n", g_concentrated_song_type);
	printf("Song Name:%s\n", g_media_name);

	int tmp_media_source=SOURCE_LOCAL_PLAY;
	switch(play_source)
	{
		case 1:						//本地播放
			tmp_media_source = SOURCE_LOCAL_PLAY;
			break;
		case 2:						//定时
			tmp_media_source = SOURCE_TIMING;
			break;
		#if 0
		case 3:						//钟声
			tmp_media_source = SOURCE_RING;
			break;
		#endif
		case 4:						//消防告警
			tmp_media_source = SOURCE_FIRE_ALARM;
			break;
		case 5:						//监控事件
			tmp_media_source = SOURCE_MONITOR_EV;
			break;
	}


	if( !set_system_source(tmp_media_source) )
	{
		data[0] = 1;
     	sendLen = Network_Send_Compose_CMD(sendBuf,CMD_CONCENTRATED_PLAY_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
     	host_udp_send_data(sendBuf, sendLen);
		return;
	}

	int timing_vol = pkg_data[pos++];

	//保存音量
	if(play_source == 1)//本地播放
	{
		//20210122 增加音量恢复，避免定时完成后服务器立刻下发恢复本地播放，导致音量没有恢复
		if(timing_vol>=0 && timing_vol<=100)
		{
			g_system_volume=timing_vol;
		}
		else
		{
			g_system_volume=g_pre_system_volume;
		}
	}
	else if(play_source == 2)//定时
	{
		//定时前保存系统音量
		if(timing_vol>=0 && timing_vol<=100)    //0xff跟随系统音量，其余音量需要保存上一次音量
        {
			g_system_volume=timing_vol;
        }
		if( g_timing_volume != -1 )
		{
			g_system_volume = g_timing_volume;
		}
	}
	else if(play_source == 3)//钟声
	{

	}
	else if(play_source == 4)//消防告警
	{
		FireAlarm_Status = 1;//开启报警
		if(timing_vol>=0 && timing_vol<=100)
		{
			g_system_volume=timing_vol;
		}
		else
		{
			g_system_volume = FIRE_ALARM_DEFAULT_VOL;	//设置音量
		}
	}
	else if( play_source == 5)//监控事件
	{
		//监控事件前保存系统音量
		g_pre_system_volume=g_system_volume;
		if(timing_vol>=0 && timing_vol<=100)
			g_system_volume=timing_vol;
	}

	//记录总帧数,新版本服务器程序才支持 20220601
	st_triggerSong.trigger_local_play_flag=0;
	st_triggerSong.trigger_local_play_finish=0;
	g_centralized_mode_is_existLocalSong=0;

	g_IsCentralized_mode_multicast_new_cmd=false;

	if( pos < payload_length + PAYLOAD_START )
	{
		int fileLength=(pkg_data[pos]<<24)+(pkg_data[pos+1]<<16)+(pkg_data[pos+2]<<8)+pkg_data[pos+3];
		pos+=4;
		int totalFramesCnt=(pkg_data[pos]<<24)+(pkg_data[pos+1]<<16)+(pkg_data[pos+2]<<8)+pkg_data[pos+3];
		pos+=4;
		int currentFrame=(pkg_data[pos]<<24)+(pkg_data[pos+1]<<16)+(pkg_data[pos+2]<<8)+pkg_data[pos+3];
		pos+=4;
		int fileMd5Len=pkg_data[pos++];
		char fileMd5[32+1]={0};
		if(fileMd5Len>0 && fileMd5Len<=32)
		{
			memcpy(fileMd5,pkg_data+pos,fileMd5Len);
			pos+=fileMd5Len;
		}

		printf("fileLength=%d,totalFramesCnt=%d,currentFrame=%d\n",fileLength,totalFramesCnt,currentFrame);

		online_song_header_t song_header;

		song_header.songType = g_concentrated_song_type;
		song_header.sampleRate = g_concentrated_song_sample_rate;
		song_header.fmt = g_concentrated_song_fmt;
		song_header.channels = g_concentrated_song_channels;

		song_header.fileLength = fileLength;
		song_header.totalFrameCnt = totalFramesCnt;
		song_header.currentFrame = currentFrame;

		strcpy(song_header.fileMd5,fileMd5);

		switch (tmp_media_source)
		{
			case SOURCE_TIMING:
				song_header.fileType = ONLINE_SAVER_FILE_TYPE_TIMING;
				break;
			case SOURCE_FIRE_ALARM:
				song_header.fileType = ONLINE_SAVER_FILE_TYPE_ALARM;
			break;
			default:
				song_header.fileType = ONLINE_SAVER_FILE_TYPE_NORMAL;
				break;
		}
		struct timeval tnow;
    	gettimeofday(&tnow,NULL);
		song_header.timeStamp = tnow.tv_sec;
		
		//如果本地歌曲已经存在，那么应答
		if(online_saver_init(song_header,g_media_name) == ONLINE_SAVER_RESULT_ALREADY_EXIST)
		{
			g_centralized_mode_is_existLocalSong=1;
		}

		pos++;	//跳过一个Timer Volume

		if( pos < payload_length + PAYLOAD_START )
		{
			//如果有新的字段，从这里进入
			g_IsCentralized_mode_multicast_new_cmd=pkg_data[pos++];
			pos++;	//跳过一个Timer Volume
		}
	}

	data[0] = g_centralized_mode_is_existLocalSong?0x03:0x01;
    sendLen = Network_Send_Compose_CMD(sendBuf,CMD_CONCENTRATED_PLAY_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
    host_udp_send_data(sendBuf, sendLen);

	g_concentrated_start = 1;
    concentrate_repeat_paly_enable = 1;  //允许重新请求播放

	//重新计时
	g_centralized_mode_timeout_count = 0;
	g_centralized_mode_timeout_pause = 0;		//继续检测
	g_concentrated_need_exit_flag = 0;

    g_media_status = SONG_PLAYING;

	#if 1	//增加此段语句是为了在单播循环模式下，服务器能够记录歌曲变化。
	unsigned char t_media_name[128] = {0};
	strcpy(t_media_name,g_media_name);
	memset(g_media_name,0,sizeof(g_media_name));
	pkg_query_current_status(NULL);
	strcpy(g_media_name,t_media_name);
	#endif

	pkg_query_current_status(NULL);

    struct in_addr centralized_ipAdder;
    centralized_ipAdder.s_addr = inet_addr(g_concentrated_multicast_address); 
    
	//i2s_write_init(g_concentrated_song_sample_rate, g_concentrated_song_fmt, g_concentrated_song_channels);
	Open_Audio_Out(g_concentrated_song_sample_rate,g_concentrated_song_fmt,g_concentrated_song_channels);
	
	//如果存在本地歌曲,挂起超时任务
	if(g_centralized_mode_is_existLocalSong)
	{
    	suspend_Centralized_Mode_Timing_Task();
	}
	else
	{
		Create_Centralized_Mode_Timing_Task();
	}
	Create_Concentrated_Mode_Play_Task();
	if(ip_addr_ismulticast(&centralized_ipAdder) && g_network_mode == NETWORK_MODE_LAN)       //组播地址才进行组播接收，否则KCP接收
    {
		printf("ready start multicatRecv...\n");
      	start_concentrated_recv_pthread();
    }

#endif
}



/*********************************************************************
 * @fn      HOST_SET_AUDIO_COLLECOR_SOURCE
 *
 * @brief   //主机向音频采集器/终端设置音频采集音源
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void HOST_SET_AUDIO_COLLECOR_SOURCE(unsigned char *pkg_data)
{
	g_allow_localSource=0;
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=1;
	int sendLen=0;
	// 发送数据
	unsigned char client=pkg_data[4];
	unsigned char play_type=pkg_data[3];

	if(play_type == 1)  //控制设备或其它原因使分区变成停止, 而且该分区有播放任务，变为空闲状态，主机会发送命令确认（其它设备停止该分区或者意外停止）
	{
		if(!concentrate_repeat_paly_enable)			//如果不允许播放集中模式下再次请求播放，比如用寻呼台控制停止
		{   
		  	printf("--Audio collector:concentrate_repeat_paly_enable==0--\n");
		 	data[0]=2;		//拒绝
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_AUDIO_COLLECOR_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
			host_udp_send_data(sendBuf, sendLen);
			return;
		}
	}

       
	printf("HOST_SET_AUDIO_COLLECOR_SOURCE.......\n");
	int valid = 1;

	//判断采样率信息是否正确
	int pos = PAYLOAD_START+4;
	int t_sample_rate = (pkg_data[pos]<<16)+(pkg_data[pos+1]<<8)+(pkg_data[pos+2]);   //3个字节
	pos += 3;
	int sample_fmt = pkg_data[pos++];
	int channels = pkg_data[pos++];
	int audioCodecs = pkg_data[pos++];

	int Payload_Length = (int)pkg_data[PAYLOAD_START-2]*256 + (int)pkg_data[PAYLOAD_START-1]; // 负载数据长度
	if(Payload_Length < 9)  //没有附带采样率信息，当成16K处理
	{
		t_sample_rate = 16000;
		sample_fmt = 16;
		channels = 1;
	}
	else if(! ( t_sample_rate == 16000 || t_sample_rate == 22050 || t_sample_rate == 32000 || t_sample_rate == 44100 || t_sample_rate == 48000 ) )
	{
		valid=0;
		printf("sample_rate:%d error\n",t_sample_rate);
	}
	else	//继续判断采样精度、声道、解码算法
	{
		if( !(sample_fmt == 16 && (channels == 1 || channels == 2) ) )
		{
			valid=0;
			printf("sample_fmt:%d,channels:%d error\n",sample_fmt,channels);
		}
	}

	//保存组播端口跟采集器音源id
	int temp_ac_mcast_port=(pkg_data[PAYLOAD_START]<<8)+pkg_data[PAYLOAD_START+1];
	int temp_ac_source_id=pkg_data[PAYLOAD_START+2];
	int audio_channel=pkg_data[PAYLOAD_START+3];
	temp_ac_source_id+=audio_channel-1;

	unsigned char isTiming = 0;
	unsigned char isTriger = 0;

	int priority=1;	//音频采集音源优先级（1-默认，低于定时，2-高优先级，高于定时）

	int timing_trigger_volume=g_system_volume;

	if( pos < Payload_Length + PAYLOAD_START )
	{
		//是否定时
		isTiming = pkg_data[pos++];
		//音量
		int volume = pkg_data[pos++];
		printf("AudioCollector:isTiming=%d,volume=%d\n",isTiming,volume);

		if(isTiming)
		{
			if(volume>=0 && volume<=100)    //0xff跟随系统音量，其余音量需要保存上一次音量
			{
				timing_trigger_volume=volume;
			}
		}
		
		if( pos < Payload_Length + PAYLOAD_START )
		{
			//是否触发
			isTriger = pkg_data[pos++];
			//音量
			volume = pkg_data[pos++];
			printf("AudioCollector:isTrigger=%d,volume=%d\n",isTriger,volume);

			if(!isTiming && isTriger)
			{
				if(volume>=0 && volume<=100)    //0xff跟随系统音量，其余音量需要保存上一次音量
				{
					timing_trigger_volume=volume;
				}
			}
		}
		if( pos < Payload_Length + PAYLOAD_START )
		{
			//优先级
			priority=pkg_data[pos++];
		}
	}

	//当前是高优先级采集音源，新的是低优先级采集音源，不处理。
	if(get_current_Priority() == PRIORITY_AUDIO_HIGH_PRIORITY && priority == 1)
	{
		return;
	}

	g_ac_source_priority = priority;

	bool ac_source_equal=false;
	if(valid)
	{
		#if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_AUDIO_MIXER)
		if( get_system_source() ==  temp_ac_source_id)
		{
			ac_source_equal=true;
		}
		#endif
		if( !set_system_source(temp_ac_source_id) )
		{
			valid=0;
		}
	}

	if(!valid)
	{
		data[0]=2;
		sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_AUDIO_COLLECOR_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
		host_udp_send_data(sendBuf, sendLen);
        printf("----------refuse 0x02-----------\n");
		return;
	}
	else
	{
		//20220721,如果是定时采集，那么应答result=3
		if(isTiming)
		{
			data[0]=3;
		}
		//20230914,如果是触发采集，那么应答result=4
		else if(isTriger)
		{
			data[0]=4;
		}
		else
		{
			data[0]=1;
		}
		sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_AUDIO_COLLECOR_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
		host_udp_send_data(sendBuf, sendLen);
        printf("----------accept 0x01-----------\n");

		if(ac_source_equal)
		{
			return;
		}
	}


	//音频采集时集中模式播放歌曲时拒绝接收
  	concentrate_repeat_paly_enable = 1;

	//保存组播端口跟采集器音源id
	g_ac_mcast_port=temp_ac_mcast_port;
	g_ac_source_id=temp_ac_source_id;


	//清空节目名称
	memset(g_media_name,0,sizeof(g_media_name));
	pkg_query_current_status(NULL);
	g_ac_channels = channels;
	printf("g_ac_mcast_port=%d,g_ac_source_id=0x%x\n",g_ac_mcast_port, g_ac_source_id);

	#if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_AUDIO_MIXER)

	Clean_All_Audio(1,0);

	g_system_volume=timing_trigger_volume;

	//开启采集器音频数据超时检测任务
	g_ac_timing_count = 0;
	g_ac_timing_wait =  0;
	g_collector_run_flag = 1;
	g_ac_sample_rate = t_sample_rate;

	printf("g_ac_sample_rate:%d, sample_fmt:%d, g_ac_channels:%d\n", g_ac_sample_rate, sample_fmt, g_ac_channels);
	
	Open_Audio_Out(g_ac_sample_rate,sample_fmt,g_ac_channels);

	//开启接收线程
	if( g_network_mode == NETWORK_MODE_LAN )       //组播地址才进行组播接收，否则KCP接收
    {
      	start_concentrated_recv_pthread();
    }
    Create_AudioCollector_Timing_Task();
	Create_Concentrated_Mode_Play_Task();

	#elif IS_DEVICE_AUDIO_COLLECTOR
	//保存音频端口
	audioCollector_info.m_nTransPort[audio_channel-1]=g_ac_mcast_port;
	g_ac_sample_rate = t_sample_rate;
	printf("g_ac_sample_rate:%d, sample_fmt:%d, g_ac_channels:%d,audio_channel=%d\n", g_ac_sample_rate, sample_fmt, g_ac_channels,audio_channel);
	
	#if(IS_DEVICE_AUDIO_COLLECTOR)
	if(audioCollector_info.m_nSelectedStatus == 0)
	{
		GPIO_OutPut_NetAudio_Led(1);
	}
	#endif
	//先给采集状态赋值，避免主机没有及时发送选择状态下来Audio_Collector_CheckStatus任务，temp_channel_timing_count计数到达5次后又停止
	audioCollector_info.m_nSelectedStatus |= g_device_collector_channel_map[audio_channel-1];

	if(ENABLE_DISP_UART_MODULE)
	{
		Disp_Send_Audio_Collector_Channel_Status();
	}

	#if IS_DEVICE_AUDIO_COLLECTOR

	if(isDisplayValid())
	{
		#if (AIPU_VERSION && SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_2P1_320X240)
		aipu_main_win_audioCollectorStatus_update(0);
		#endif
	}
	#endif
	
	#endif
}





/*********************************************************************
 * @fn      Change_Device_Alias
 *
 * @brief   修改设备别名
 *
 * @param   rxbuf--数据缓冲区
 *
 * @return  none
*********************************************************************/
void Change_Device_Alias(unsigned char *rxbuf)
{
	// 应答
	respond_null_payload_pkg(rxbuf);
	int i;
	int alias_len = 0;
	// 判断别名长度是否大于0
	if (rxbuf[PAYLOAD_START] == 0)
	{
		return;
	}
	else
	{
		alias_len = rxbuf[PAYLOAD_START]; // 获取别名长度
	}
	// 获取别名并保存
	memset(g_device_alias, 0x00, sizeof(g_device_alias));
	for (i = 0; i < alias_len; i++)
	{
		g_device_alias[i] = rxbuf[PAYLOAD_START+1+i];
	}
	send_online_info();
	save_sysconf(INI_SECTION_DEVICE,"Device_Name");//主机修改设备别名保存
	printf("g_device_alias:%s\n", g_device_alias);

	if(ENABLE_DISP_UART_MODULE)
	{
		Disp_Send_device_info();
	}

	display_update_mainWin_external();
}



/*********************************************************************
 * @fn      pkg_query_current_status
 *
 * @brief   主机查询本机当前状态(音量、节目源名称、播放状态)
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void pkg_query_current_status(unsigned char *rxbuf)
{
	unsigned char sendBuf[MAX_BUF_SIZE] = {0};
	int i=0;
	unsigned char data[128] = {0};
	int data_len = 0;
	unsigned char client;
	if(rxbuf != NULL)
		client = rxbuf[4];

	if(g_paging_status == PAGING_START)
	{
		data[0] = pager_property.volume;
	}
	#if SUPPORT_SIP
	else if(g_media_source == SOURCE_SIP_CALLING)
	{
		data[0] = appData.voiceInfoSettings.callVolumeTemp;
	}
	#endif
	else if(g_media_source == SOURCE_API_TTS_MUSIC)
	{
		data[0] = curTTSParm.m_nVolume;
	}
	else
	{
		data[0] = g_system_volume;
	}
        
	data[1] = g_media_source;		//音源
	data[2] = g_media_status;	//播放状态
	//本地播放与网络点播
	int media_name_len=0;
	if(g_media_source == SOURCE_LOCAL_PLAY ||g_media_source == SOURCE_FIRE_ALARM || g_media_source == SOURCE_TIMING || g_media_source == SOURCE_MONITOR_EV)
	{
		media_name_len = strlen(g_media_name) > 64 ? 64 : strlen(g_media_name);
	}
	data[3] = media_name_len;
	for(i = 0; i < media_name_len; i++)
	{
		data[4+i] = g_media_name[i];
	}
	data[4+media_name_len] = g_centralized_mode_is_existLocalSong?2:1;
	data_len = 3 + 1 + media_name_len + 1;
	int sendLen = Network_Send_Compose_CMD(sendBuf,CMD_GET_ZONE_DETAIL_INFO,CURRENT_DEVICE_MODEL,data_len,data);
	if(rxbuf == NULL)
	{
		//组播发送
		multicast_send_data(sendBuf, sendLen);
		#if ENABLE_TCP_CLIENT
		if(g_network_mode == NETWORK_MODE_WAN)
			host_tcp_send_data(sendBuf,sendLen);
		#endif
	}
	else
	{
		if (client == DEVICE_MODEL_HOST)
		{
			// 发送数据
			host_udp_send_data(sendBuf, sendLen);
			#if ENABLE_TCP_CLIENT
			if(g_network_mode == NETWORK_MODE_WAN)
				host_tcp_send_data(sendBuf,sendLen);
			#endif
		}
		else if (client == DEVICE_MODEL_PAGING)
		{
			pager_udp_send_data(sendBuf, sendLen);
		}
	}
}

/*********************************************************************
 * @fn      respond_pkg_query_volume
 *
 * @brief   应答音量查询
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void respond_pkg_query_volume(unsigned char *rxbuf)
{
	int payloadSize = 0;
	unsigned char send_buf[MAX_BUF_SIZE];

	memset(send_buf, 0x00, sizeof(send_buf)); // 清空缓存

	// 添加命令字
	send_buf[0] = rxbuf[0];
	send_buf[1] = rxbuf[1];
	// 添加包序号
	send_buf[2] = rxbuf[2];
	// 保留位
	send_buf[3] = RESERVE;
	// 产品型号
	send_buf[4] = CURRENT_DEVICE_MODEL;
	// 包属性及编码格式
	send_buf[5] = rxbuf[5];
	if(g_paging_status == PAGING_START)
		send_buf[PAYLOAD_START] = pager_property.volume;
	#if SUPPORT_SIP
	else if(g_media_source == SOURCE_SIP_CALLING)
		send_buf[PAYLOAD_START] = appData.voiceInfoSettings.callVolumeTemp;
	#endif
	else
		send_buf[PAYLOAD_START] = g_system_volume;
	payloadSize += 1;

	// 添加负载数据长度
	send_buf[6] = payloadSize/256;
	send_buf[7] = payloadSize%256;

	// 计算校验和
	send_buf[payloadSize+PAYLOAD_START] = Calculate_XorDat(&send_buf[PAYLOAD_START], payloadSize);

	// 发送数据
	unsigned char client=rxbuf[4];
	if (client == DEVICE_MODEL_HOST)
	{
		// 发送数据
		host_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}
	else if (client == DEVICE_MODEL_PAGING)
	{
		pager_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}
}

/*********************************************************************
 * @fn      pkg_set_volume
 *
 * @brief   主机设置或查询本机音量
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void pkg_set_volume(unsigned char *rxbuf)
{
	int i = 0, cunt = 0;
	int temp_volume = 0;
	switch (rxbuf[PAYLOAD_START])
	{
		case CMD_QUERY : // 查询
			respond_pkg_query_volume(rxbuf);
			break;
		case CMD_SET : // 设置
			respond_null_payload_pkg(rxbuf);
			if(g_paging_status == PAGING_START)
			{       
                                
				pager_property.volume = rxbuf[PAYLOAD_START+1];
				pkg_query_current_status(NULL);

				printf("set g_paging_vol:%d\n", pager_property.volume);
			}
			#if SUPPORT_SIP
			else if(g_media_source == SOURCE_SIP_CALLING)	
			{
				appData.voiceInfoSettings.callVolumeTemp = rxbuf[PAYLOAD_START+1];
				sem_sip_conf_send();
				pkg_query_current_status(NULL);
			}
			#endif
			else if(g_media_source == SOURCE_API_TTS_MUSIC)
			{
				curTTSParm.m_nVolume = rxbuf[PAYLOAD_START+1];
				pkg_query_current_status(NULL);
			}
			else
			{
				temp_volume = g_system_volume;
				g_system_volume = rxbuf[PAYLOAD_START+1];
				pkg_query_current_status(NULL);
				//if(g_system_volume != 0)
				g_pre_system_volume = g_system_volume;

				//如果是定时音源，那么记录此音量
				if( get_system_source() == SOURCE_TIMING)
				{
					g_timing_volume = g_system_volume;
				}
                                
				if(temp_volume == g_system_volume)
				{
					break;
				}
				save_sysconf(INI_SECTION_BASIC,"System_Volume");//主机设置音量保存
#if 0	//20221223 4G复位测试，实测复位后可能RDNIS异常，重启系统后可以正常工作。故使用复位后，需要重启一遍系统确保网卡正常。
				if(g_system_volume == 0)
				{
					GPIO_OutPut_Module4G_Reset(1);
					usleep(200000);
					GPIO_OutPut_Module4G_Reset(0);
				}
				else if(g_system_volume == 10)
				{
					GPIO_OutPut_Module4G_POWER(1);
					usleep(1600*1000);    //此处开机，需要大于1.2秒，小于1.5秒，否则当模块处于开机状态下，可能会关机
					GPIO_OutPut_Module4G_POWER(0);
				}
#endif
			}
         
			break;
		default :
			break;
	}
}



/*********************************************************************
 * @fn      respond_pkg_query_sub_volume
 *
 * @brief   应答子音量查询
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void respond_pkg_query_sub_volume(unsigned int SetOrQuery,unsigned char *rxbuf)
{
	int payloadSize = 0;
	unsigned char send_buf[MAX_BUF_SIZE];

	memset(send_buf, 0x00, sizeof(send_buf)); // 清空缓存

	// 添加命令字
	send_buf[0] = rxbuf[0];
	send_buf[1] = rxbuf[1];
	// 添加包序号
	send_buf[2] = rxbuf[2];
	// 保留位
	send_buf[3] = RESERVE;
	// 产品型号
	send_buf[4] = CURRENT_DEVICE_MODEL;
	// 包属性及编码格式
	send_buf[5] = rxbuf[5];

	send_buf[PAYLOAD_START] = SetOrQuery;
	send_buf[PAYLOAD_START+1] = g_sub_volume;
	send_buf[PAYLOAD_START+2] = g_aux_volume;
	payloadSize = 3;

	// 添加负载数据长度
	send_buf[6] = payloadSize/256;
	send_buf[7] = payloadSize%256;

	// 计算校验和
	send_buf[payloadSize+PAYLOAD_START] = Calculate_XorDat(&send_buf[PAYLOAD_START], payloadSize);

	// 发送数据
	unsigned char client=rxbuf[4];
	if (client == DEVICE_MODEL_HOST)
	{
		// 发送数据
		host_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}
	else if (client == DEVICE_MODEL_PAGING)
	{
		pager_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}
}
/*********************************************************************
 * @fn      pkg_set_sub_volume
 *
 * @brief   主机设置或查询子音量
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void pkg_set_sub_volume(unsigned char *rxbuf,int payload_Length)
{
	int i = 0, cunt = 0;
	int temp_sub_volume = 0, temp_aux_volume=0;
	int pos=0;
	unsigned char *pData = rxbuf+PAYLOAD_START;
	printf("pkg_set_sub_volume...\n");
	int SetOrQuery=pData[pos++];
	switch (SetOrQuery)
	{
		case CMD_QUERY : // 查询
			respond_pkg_query_sub_volume(SetOrQuery,rxbuf);
			break;
		case CMD_SET : // 设置
			temp_sub_volume = g_sub_volume;
			temp_aux_volume = g_aux_volume;
			g_sub_volume = pData[pos++];
			if(pos<payload_Length)
			{
				//代表有本地音量字段
				g_aux_volume=pData[pos++];
			}
			respond_pkg_query_sub_volume(SetOrQuery,rxbuf);
			if(temp_sub_volume != g_sub_volume)
			{
				save_sysconf(INI_SECTION_BASIC,"Sub_Volume");//子音量保存
			}
			if(temp_aux_volume != g_aux_volume)
			{
				save_sysconf(INI_SECTION_BASIC,"Aux_Volume");//本地音量保存
			}

			break;
		default :
			break;
	}
}


/*********************************************************************
 * @fn      pkg_control_volume_add_min
 *
 * @brief   主机控制音量加/减
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void pkg_control_volume_add_min(unsigned char *rxbuf)
{
	int temp_volume = 0;

	int event_IsAdd=rxbuf[PAYLOAD_START];
	int step = rxbuf[PAYLOAD_START+1];
	if(!event_IsAdd)
	{
		step *=-1;
	}
	respond_null_payload_pkg(rxbuf);

	if(g_paging_status == PAGING_START)
	{
		if( step<0 && (pager_property.volume < abs(step)) )
		{
			pager_property.volume=0;
		}
		else{
			pager_property.volume += step;
		}
		if(pager_property.volume>100)
			pager_property.volume=100;
		pkg_query_current_status(NULL);
		printf("set g_paging_vol:%d\n", pager_property.volume);
	}
	else
	{
		temp_volume = g_system_volume;
		//如果是音量减，且当前系统音量已经比步进小了,那么音量为0
		if( step<0 && (g_system_volume < abs(step)) )
		{
			g_system_volume=0;
		}
		else{
			g_system_volume+=step;
		}
		if(g_system_volume>100)
			g_system_volume=100;
		pkg_query_current_status(NULL);
		g_pre_system_volume = g_system_volume;

		//如果是定时音源，那么记录此音量
		if( get_system_source() == SOURCE_TIMING)
		{
			g_timing_volume = g_system_volume;
		}
		
		if(temp_volume == g_system_volume)
		{
			return;
		}
		save_sysconf(INI_SECTION_BASIC,"System_Volume");//主机设置音量保存
	}
}



/*********************************************************************
 * @fn      HOST_QUERY_SET_WORK_MODE
 *
 * @brief   主机向设备查询/设置工作模式
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void HOST_QUERY_SET_WORK_MODE(unsigned char *pkg_data)
{
	switch (pkg_data[PAYLOAD_START])
	{
		case CMD_QUERY : // 查询
		{
			printf("QUERY work_mode=%d\n",g_system_work_mode);
			unsigned char sendBuf[MAX_BUF_SIZE] = {0};
			int i=0;
			unsigned char data[128] = {0};
			int data_len = 1;
			data[0] = g_system_work_mode;
			int sendLen = Network_Send_Compose_CMD(sendBuf,CMD_QUERY_SET_WORK_MODE,CURRENT_DEVICE_MODEL,data_len,data);
			// 发送数据
			unsigned char client = pkg_data[4];
			if (client == DEVICE_MODEL_HOST)
			{
				// 发送数据
				host_udp_send_data(sendBuf, sendLen);
			}
			else if (client == DEVICE_MODEL_PAGING)
			{
				pager_udp_send_data(sendBuf, sendLen);
			}
		}
			break;
		case CMD_SET : 	//设置
		{
			respond_null_payload_pkg(pkg_data);
			int work_mode = pkg_data[PAYLOAD_START+1];
			printf("SET work_mode=%d\n",work_mode);
			if(g_system_work_mode == work_mode)
			{
				break;
			}
			g_system_work_mode = work_mode;
			if(g_system_work_mode == WORK_MODE_DISTRIBUTIONAL)	//分布模式
			{
				printf("设置分布模式...\n");
				//停止所有播放歌曲
			}
			else	//集中模式
			{
				printf("设置集中模式...\n");
			}
			if( g_media_source != SOURCE_FIRE_ALARM && g_media_source != SOURCE_NET_PAGING )
			{
				printf("将设备设置为空闲状态\n");
				Set_zone_idle_status(NULL,  __func__, __LINE__,true);
			}
			send_online_info();
		}
		break;
		default :
			break;
	}
}


/*********************************************************************
 * @fn      pkg_query_firmware_version
 *
 * @brief   主机查询本机当前固件版本
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void pkg_query_firmware_version(unsigned char *rxbuf)
{
	int i;
	int payloadSize = 0;
	unsigned char data_buf[128];
	unsigned char send_buf[MAX_BUF_SIZE];

	memset(send_buf, 0x00, sizeof(send_buf)); // 清空缓存

	// 添加命令字
	send_buf[0] = rxbuf[0];
	send_buf[1] = rxbuf[1];
	// 添加包序号
	send_buf[2] = rxbuf[2];
	// 保留位
	send_buf[3] = RESERVE;
	// 产品型号
	send_buf[4] = CURRENT_DEVICE_MODEL;
	// 包属性及编码格式
	send_buf[5] = rxbuf[5];

	// 添加版本信息
	memset(data_buf, 0x00, sizeof(data_buf));
	sprintf(data_buf, "%s", LOCAL_FIRMWARE_VERSION);
	send_buf[PAYLOAD_START+payloadSize] = strlen(data_buf);
	payloadSize += 1;
	for (i=0; i<strlen(data_buf); i++)
	{
		send_buf[PAYLOAD_START+payloadSize+i] = data_buf[i];
	}
	payloadSize += strlen(data_buf);

	// 添加负载数据长度
	send_buf[6] = payloadSize/256;
	send_buf[7] = payloadSize%256;

	// 计算校验和
	send_buf[payloadSize+PAYLOAD_START] = Calculate_XorDat(&send_buf[PAYLOAD_START], payloadSize);

	// 发送数据
	unsigned char client=rxbuf[4];
	if (client == DEVICE_MODEL_HOST)
	{
		// 发送数据
		host_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}
	else if (client == DEVICE_MODEL_PAGING)
	{
		pager_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}

}


/*********************************************************************
 * @fn      pkg_get_update_info
 *
 * @brief   获取主机推送的升级信息
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void pkg_get_update_info(unsigned char *rxbuf)
{
	int i;
	int data_len = 0;
	int payloadSize = 0;
	unsigned char data_buf[256];
	unsigned char verison[32]={0};

	unsigned char device_type=rxbuf[PAYLOAD_START+payloadSize];
	payloadSize++;
	if(device_type!=CURRENT_DEVICE_MODEL)
	{
		printf("\ndevice_type error,value=%d\n",device_type);
		return;
	}

	if( g_update_flag )
	{
	  printf("pkg_get_update_info:Upgrading...\r\n");
	  respond_pkg_update_status_code(START_DOWNLOAD);
	  return;
	}

	// 获取主机推送的固件版本
	memset(data_buf, 0x00, sizeof(data_buf));
	if (rxbuf[PAYLOAD_START+payloadSize] <= 0)
	{
		respond_pkg_update_status_code(UPDATE_FAIL);
		return;
	}
	else
	{
		data_len = rxbuf[PAYLOAD_START+payloadSize];
	}
	payloadSize += 1;
	for (i=0; i<data_len; i++)
	{
		data_buf[i] = rxbuf[PAYLOAD_START+payloadSize+i];
	}
	payloadSize += data_len;
#if UDP_DEBUG
	if(g_paging_status != PAGING_START) printf("Host push firmware version is : %s\n", data_buf);
#endif

	strcpy(verison,data_buf);



	// 获取web服务器的域名或IP
	memset(data_buf, 0x00, sizeof(data_buf));
	if (rxbuf[PAYLOAD_START+payloadSize] <= 0)
	{
		respond_pkg_update_status_code(UPDATE_FAIL);
		return;
	}
	else
	{
		data_len = rxbuf[PAYLOAD_START+payloadSize];
	}
	payloadSize += 1;
	for (i=0; i<data_len; i++)
	{
		data_buf[i] = rxbuf[PAYLOAD_START+payloadSize+i];
	}
	payloadSize += data_len;
#if UDP_DEBUG
	if(g_paging_status != PAGING_START) printf("Host web server ip or domain is : %s\n", data_buf);
#endif
	convert_domain_to_ip(data_buf); // 将域名转换为IP地址

	// 获取主机web服务器端口
	g_web_server_port = rxbuf[PAYLOAD_START+payloadSize]*256 + rxbuf[PAYLOAD_START+payloadSize+1];
	payloadSize += 2;

	// 获取主机升级文件存放的绝对路径
	memset(g_url_buf, 0x00, sizeof(g_url_buf));
	if ((rxbuf[PAYLOAD_START+payloadSize]*256+rxbuf[PAYLOAD_START+payloadSize+1]) <= 0)
	{
		respond_pkg_update_status_code(UPDATE_FAIL);
		return;
	}
	else
	{
		data_len = rxbuf[PAYLOAD_START+payloadSize]*256 + rxbuf[PAYLOAD_START+payloadSize+1];
	}
	payloadSize += 2;
	for (i=0; i<data_len; i++)
	{
		g_url_buf[i] = rxbuf[PAYLOAD_START+payloadSize+i];
	}
	payloadSize += data_len;
#if UDP_DEBUG
	if(g_paging_status != PAGING_START) printf("Host update file path is : %s\n", g_url_buf);
#endif


	int payload_len=(rxbuf[6]<<8)+rxbuf[7];
    if( payload_len == payloadSize)	//如果没有附带MD5信息
	{
		printf("No md5 info.\r\n");
	}
	else
	{
		int md5Len=rxbuf[PAYLOAD_START+payloadSize];
		payloadSize += 1;
		memset(upgrade_file_md5_str,0,sizeof(upgrade_file_md5_str));
		strncpy(upgrade_file_md5_str,&rxbuf[PAYLOAD_START+payloadSize],md5Len);
		printf("md5len=%d,val=%s\r\n",md5Len,upgrade_file_md5_str);
		payloadSize+=md5Len;
	}
	
	g_update_type=UPDATE_TYPE_APP;
	//如果升级文件路径包括DISP，则判断DISP模块的版本号
	if(	strstr(g_url_buf,"DISP") )
	{
		//去掉前面的‘V’
		memset(firmaware_versionNoV,0,sizeof(firmaware_versionNoV));
		strncpy(firmaware_versionNoV,verison+1,strlen(verison)-1);
		if( strlen(g_DispVersion) == 0 && !g_CurDispUpgrading)
		{
			printf("The Disp Module is not Exist!\r\n");
			respond_pkg_update_status_code(UPDATE_FAIL);
			return;
		}
		else
		{
			if(strlen(g_DispVersion) >0 && strcasecmp(firmaware_versionNoV,g_DispVersion) <= 0)
			{
				printf("The Disp firmware is newest!\r\n");
				respond_pkg_update_status_code(FIRMWARE_NEWEST);
				return;
			}
		}
		g_update_type=UPDATE_TYPE_DISP;
	}
	else if(strcasecmp(verison, LOCAL_FIRMWARE_VERSION) <= 0)
	{
		printf("The local firmware is newest,not to update!\r\n");
		respond_pkg_update_status_code(FIRMWARE_NEWEST);
		return;
	}
	

	if(g_network_mode == NETWORK_MODE_WAN)
	{
		sprintf(g_web_server_ip,"%s",g_host_tcp_prase_ipAddress);
	}

	printf("g_url_buf=%s,g_update_type=%d\n",g_url_buf,g_update_type);

	memset(g_download_path, 0x00, sizeof(g_download_path));
	sprintf(g_download_path, "%s", UPDATE_SAVE_PATH);
	start_download_file_pthread(); // 启动文件下载线程开始下载升级文件
}



/*********************************************************************
 * @fn      respond_pkg_update_status_code
 *
 * @brief   应答主机升级状态
 *
 * @param   code - 状态码
 *
 * @return	void
 */
int respond_pkg_update_status_code(unsigned char code)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[1024]={0};
	int data_len=0;

	data[0]=code;
	data_len=1;

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_UPDATE_INFO,CURRENT_DEVICE_MODEL,data_len,data);
	// 发送数据
	host_udp_send_data(sendBuf, sendLen);
	return 0;
}



/*********************************************************************
 * @fn      send_download_rate_of_progress
 *
 * @brief   向主机发送下载进度
 *
 * @param   progress - 进度（%0~100%）
 *
 * @return	void
 */
int send_download_rate_of_progress(unsigned char progress)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[1024]={0};
	int data_len=0;

	data[0]=progress;
	data_len=1;

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_RATE_PROGRESS,CURRENT_DEVICE_MODEL,data_len,data);
	// 发送数据
	host_udp_send_data(sendBuf, sendLen);
	return 0;
}



/*********************************************************************
 * @fn      play_status_ctrl
 *
 * @brief   播放状态控制
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void play_status_control(unsigned char *pkg_data)
{
	respond_null_payload_pkg(pkg_data);
	if(g_media_source != SOURCE_LOCAL_PLAY)
		return;
	if(g_system_work_mode == WORK_MODE_DISTRIBUTIONAL)
	{
		switch(pkg_data[PAYLOAD_START])
		{
			case SONG_PLAYING: // 播放
				if(g_media_status==SONG_SUSPEND && PriorityIsValid(PRIORITY_NET_PLAY))
				{
					g_media_status = SONG_PLAYING; //继续播放
				}
				break;

			case SONG_SUSPEND: // 暂停
				if(g_media_status==SONG_PLAYING)
				{
					g_media_status = SONG_SUSPEND; //暂停成功
				}
				break;

			case SONG_STOP: // 停止
				Set_zone_idle_status(pkg_data,  __func__, __LINE__,true);
				break;

			default:
				break;
		}
	}
	else
	{
		switch(pkg_data[PAYLOAD_START])
		{
			case SONG_PLAYING: // 播放
				if(g_media_status==SONG_SUSPEND || g_media_status==SONG_STOP)
				{
					g_media_status = SONG_PLAYING; //继续播放
					//pkg_query_current_status(NULL);
				}
				break;

			case SONG_SUSPEND: // 暂停
				if(g_media_status==SONG_PLAYING)
				{
                    //Set_zone_idle_status(pkg_data,  __func__, __LINE__,true);
					g_media_status = SONG_SUSPEND; //暂停成功
					//pkg_query_current_status(NULL);
					
					Clean_All_Audio(1,1);

					suspend_Centralized_Mode_Timing_Task();;//挂起集中模式播放任务
				}
				break;

			case SONG_STOP: // 停止
                g_media_status = SONG_STOP;
				Set_zone_idle_status(pkg_data,  __func__, __LINE__,true);
				break;

			default:
				break;
		}
	}
        
        pkg_query_current_status(NULL);//2020-3-2 主动回复状态给主机，否则暂停不了
}

/*********************************************************************
 * @fn      response_send_host_xml_file_info
 *
 * @brief   应答主机向终端查询存储在本地的文件信息
 *
 * @param   domain - 主机域名
 *
 * @return	void
 */
void response_send_host_xml_file_info(char *rxbuf)
{
	unsigned char sendBuf[MAX_BUF_SIZE] = {0};
	int i = 0, k = 0;
	unsigned char data[128] = {0};
	int data_len = 0;

	data[0] = rxbuf[PAYLOAD_START];
	char timeBuf[64]={0};
	sprintf(timeBuf,"%s","1970-01-01 01:01:01");
	data[1] = strlen(timeBuf);
	for(i = 0; i < strlen(timeBuf); i++)
	{
		data[2+i] = timeBuf[i];
	}
	data_len = 2+strlen(timeBuf);
	printf("response_send_host_xml_file_info:data_len=%d\n",data_len);
	int sendLen = Network_Send_Compose_CMD(sendBuf, CMD_QUERY_FILE, CURRENT_DEVICE_MODEL, data_len,data);
	// 发送数据
	unsigned char client=rxbuf[4];
	if (client == DEVICE_MODEL_HOST)
	{
		// 发送数据
		host_udp_send_data(sendBuf,sendLen);
	}
	else if (client == DEVICE_MODEL_PAGING)
	{
		pager_udp_send_data(sendBuf,sendLen);
	}
}


/*********************************************************************
 * @fn      host_control_reboot
 *
 * @brief   主机控制终端重启
 *
 * @param   char *rxbuf接收包
 *
 * @return	void
 */
void host_control_reboot(char *rxbuf,unsigned char netType)
{
	respond_null_payload_pkg(rxbuf);

	//如果是组播，MAC不符返回
	if(netType == 1)
	{
		int zone_num = rxbuf[PAYLOAD_START];
		char mac_buf[MAX_BUF_SIZE] = {0};
		memcpy(mac_buf, &rxbuf[PAYLOAD_START+1], 6*zone_num);

		int i = 0,t = 0;
		int mac_ok = 0;
		for(i = 0; i < zone_num; i++)
		{
			if(memcmp(g_mac_addr,mac_buf+i*6,6) == 0)
			{
				mac_ok=1;
				break;
			}
			#if IS_DEVICE_AUDIO_MIXER
			if(memcmp(g_mixer_mac_addr,mac_buf+i*6,6) == 0)
			{
				mac_ok=1;
				break;
			}
			#endif
		}
		if(!mac_ok)
		{
			return;
		}
	}
	
	printf("host_control_reboot...\n");
	System_Reboot();
}


/*********************************************************************
 * @fn      host_control_format
 *
 * @brief   主机向设备请求清除数据
 *
 * @param   char *rxbuf接收包
 *
 * @return	void
 */
void host_control_format(char *rxbuf)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[128]={0};
	int data_len=1;

	int type = rxbuf[PAYLOAD_START];
	printf("host_control_format,type=%d\r\n",type);
	data[0] = type;
	int sendLen = Network_Send_Compose_CMD(sendBuf,CMD_CONTROL_FORMAT,CURRENT_DEVICE_MODEL,data_len,data);
	// 发送数据
	unsigned char client = rxbuf[4];

	host_udp_send_data(sendBuf, sendLen);
	
	switch(type)
	{
		case 0xff://恢复出厂设置，清除除了DSP特性、SN外数据文件
 			remove_file(BASIC_CONFIG_INI_FILE);
			remove_file(DEVICE_CONFIG_INI_FILE);
			remove_file(NETWORK_CONFIG_INI_FILE);
			remove_file(DSP_EQ_CONFIG_INI_FILE);
			remove_file(BLUETOOTH_CONFIG_INI_FILE);
			remove_file(SEQUENCEPOWER_CONFIG_INI_FILE);
			remove_file(FIRE_CONFIG_INI_FILE);
			remove_file(INTERCOM_CONFIG_INI_FILE);
			remove_file(TRIGGER_CONFIG_INI_FILE);
			remove_file(MIXER_CONFIG_INI_FILE);
			remove_file(PHONE_GATEWAY_CONFIG_INI_FILE);
			remove_file(INI_SIP_INI_FILE);
			remove_file(INI_INFORMATION_PUB_INI_FILE);
			remove_file(INI_REMOTE_INI_FILE);
			System_Reboot();
			break;
		case 0x01:		//分区数据
			break;
		case 0x02:		//分组数据
			break;
		case 0x03:		//播放列表（包括已同步文件）
			break;
		case 0x04:		//定时数据
			break;
	}
}



/**
* [host_set_eq 设置EQ]
* @param  rxbuf [description]
* @return       [description]
*/


void host_set_eq(char *rxbuf)
{
#ifndef USE_PC_SIMULATOR
#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
	int pos=PAYLOAD_START;
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[128]={0};
	int data_len=0;
	
	//对比MAC,如果MAC不匹配，直接返回空应答
	if(	memcmp(rxbuf+pos,g_mac_addr,6) != 0)
	{
	  	printf("host_set_eq:mac error\n");
	  	respond_null_payload_pkg(rxbuf);
		return;
	}

	
	pos+=6;
   switch (rxbuf[pos++])
   {
	   case CMD_QUERY : // 查询
	   {
           printf("host_set_eq:query\n");
           memcpy(data,g_mac_addr,6);
		   data_len+=6;
		   data[data_len++]=dsp_eq_info.eq_mode;
		   memcpy(data+data_len,dsp_eq_info.gain,10);
		   data_len+=10;
		   int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_EQ,CURRENT_DEVICE_MODEL,data_len,data);
		   //发送数据
		   host_udp_send_data(sendBuf, sendLen);
	   }
	   break;
	   case CMD_SET : //设置
	   {
           printf("host_set_eq:set\n");
		   respond_null_payload_pkg(rxbuf);

		   dsp_eq_info.eq_mode 	= rxbuf[pos++];
		   printf("bp1048_eq_info.eq_mode=%d\n",dsp_eq_info.eq_mode);
		   if(dsp_eq_info.eq_mode == 1 )		//EQ为自定义时才保存EQ值
		   {
			  memcpy(dsp_eq_info.gain,rxbuf+pos,10);
		   }
		   else
		   {
			   //memset(dsp_eq_info.gain,INI_DSP_EQ_VALUE_DEFAULT,sizeof(dsp_eq_info.gain));
		   }

		   pos+=10;
		   
		   	//保存配置信息
			save_sysconf(INI_SETCION_DSP_EQ,NULL);
		   //todo 设置EQ
		   mi_audio_set_eq_mode(dsp_eq_info.eq_mode);
	   }
	   break;
   }
   return;
#endif
#endif
}



/*********************************************************************
 * @fn      Set_zone_idle_status
 *
 * @brief   设置分区空闲状态
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void Set_zone_idle_status(unsigned char *rxbuf, const char *function,int line, bool canRepeat_paly)
{       
	printf("Set_zone_idle_status:function=%s,line=%d\n", function, line);
      
	if(rxbuf!=NULL)
	{       
		respond_null_payload_pkg(rxbuf);
		if( g_paging_status == PAGING_START )	//正在寻呼且优先级比当前寻呼台低,不处理
		{
		  	int IsValid=0;
			int Payload_Length = rxbuf[PAYLOAD_START-2]*256 + rxbuf[PAYLOAD_START-1]; // 负载数据长度
			if( Payload_Length>0 )
			{
				int authority=rxbuf[PAYLOAD_START];
				printf("[authority = %d]\n", authority);
				#if CANCEL_PAGER_SOURCE_PRIORITY
				IsValid=1;
				#else
				if(authority > pager_property.authority)
				{
					IsValid=1;
				}
				else if(authority == pager_property.authority)		//相同优先级的判断是否同一个MAC
				{
					unsigned char mac[6]={0};
					memcpy(mac,&rxbuf[PAYLOAD_START+1],6);
					if( memcmp(mac,pager_property.pager_mac,6) == 0 )
					{
						IsValid=1;
					}
				}
				if(IsValid == 0)
				{
					unsigned char client=rxbuf[4];
					int forwardPkg=rxbuf[5];
					if (client == DEVICE_MODEL_HOST && authority == 0x10 && !forwardPkg)
					{
						IsValid=1;
					}
				}
				#endif
			}
			if(!IsValid)
			{
			  	printf("Paging,Permission denied!\n");
				return;
			}
			
			paging_repeat_again_enable=0;
			pre_web_pager_strem_timeout=0;
			memset(pre_web_pager_cmd_pkg,0,sizeof(pre_web_pager_cmd_pkg));
			g_paging_timing_count = PAGING_TIMING_COUNT_MAX;	//关闭寻呼检测线程

			printf("need exit paging...\n");
			return;
		}
		if(g_media_source != SOURCE_NULL && g_media_source != SOURCE_AUX)
		{
			printf("[g_media_source != AUX]\n");
			
			#if !LOCAL_SOURCE_PRIORITY_HIGHEST
			concentrate_repeat_paly_enable=0;
			mixed_source_repeat_again_enable=0;
			phone_gateway_source_repeat_again_enable=0;
			#endif
		}
		else
		{       
			//Dbg("[g_media_source == SOURCE_NULL]\n");
			return;
		}
	}
	else //自己设为空闲
	{
		if(g_media_source != SOURCE_NULL && g_media_source != SOURCE_AUX)
		{
		   concentrate_repeat_paly_enable=canRepeat_paly;
		}
	}

	g_centralized_mode_timing_repeat = 0;

	st_triggerSong.trigger_local_play_flag=0;
	st_triggerSong.trigger_local_play_finish=0;
	g_centralized_mode_is_existLocalSong=0;

	#if ENABLE_LISTEN_TEST
	refresh_listen_upload();
	#endif
	
	if( strcmp("Create_Centralized_Mode_TimeOut_Count",function) == 0 )		//集中模式播放歌曲超时
	{
		//如果当前处于寻呼台音源或者音频采集器音源时，不处理；
		if( g_media_source == SOURCE_NET_PAGING || (g_media_source >= SOURCE_AUDIO_COLLECTOR_BASE && g_media_source <= SOURCE_AUDIO_COLLECTOR_MAX) )
		{
		  printf("g_media_source error1,return!\n");
		  return;
		}
	  	printf("ready to clean all audio1\n");
		Clean_All_Audio(0,1);
	}
	else if( strcmp("Audio_Timeout_Count",function) == 0 )				// 音频采集器播放超时
	{
		if( g_media_source == SOURCE_NET_PAGING || g_media_source == SOURCE_LOCAL_PLAY || g_media_source == SOURCE_TIMING ||
		 	g_media_source == SOURCE_MONITOR_EV || g_media_source == SOURCE_FIRE_ALARM )
		{
		  printf("g_media_source error2,return!\n");
		  return;
		}
	  	printf("ready to clean all audio2\n");
		Clean_All_Audio(1,0);
	}
	else
	{
		Clean_All_Audio(1,1);
	}

	if(g_media_source != SOURCE_NULL && g_media_source != SOURCE_AUX)
	{   
		//音量恢复
		if(g_media_source == SOURCE_TIMING)
		{
			//g_system_volume=g_pre_system_volume;
		}
		else if(g_media_source == SOURCE_MONITOR_EV)
		{
			//g_system_volume=g_pre_system_volume;
		}
		else if(g_media_source == SOURCE_FIRE_ALARM)
		{
		   g_system_volume = g_pre_system_volume;	  	   //恢复音量
		}
		else if(g_media_source == SOURCE_NET_PAGING)
		{
			//g_system_volume = g_pre_system_volume;	  //恢复音量
		}
		else if(g_media_source == SOURCE_AUDIO_MIXED)
		{
			g_audio_mixer_stream_timing_count = AUDIO_MIXER_TIMING_COUNT_MAX;
		}
		else if(g_media_source == SOURCE_PHONE_GATEWAY)
		{
			g_phone_gateway_stream_timing_count = PHONE_GATEWAY_TIMING_COUNT_MAX;	//关闭寻呼检测线程
		}
	}
	FireAlarm_Status = 0;//关闭报警
	g_system_volume = g_pre_system_volume;	  			//恢复音量
	g_timing_volume = -1;

	#if SUPPORT_SIP
	//如果是SIP音源，需要挂断
	if(g_media_source == SOURCE_SIP_CALLING)
	{
		//一定要加PJ_THREAD_REGISTER_MACRO，不然程序段错误
		PJ_THREAD_REGISTER_MACRO(Set_zone_idle_status);
		HangupAllCallingStop();
		usleep(50000);
	}
	#endif
	
	#if !LOCAL_SOURCE_PRIORITY_HIGHEST
	set_system_source(g_signal_100v ? SOURCE_100V_INPUT:SOURCE_NULL);
	#else
	set_system_source(g_signal_100v ? SOURCE_100V_INPUT:g_signal_aux?SOURCE_AUX:SOURCE_NULL);
	#endif

	g_media_status = SONG_STOP;	//设置为停止

	if(g_paging_status == PAGING_START)
	{
		g_paging_status = PAGING_STOP;
	}

	#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
	{
		Open_Audio_Out(48000,16,2);
		Open_Audio_In(48000,16,1);
	}
	#elif(IS_DEVICE_AUDIO_COLLECTOR)
	{
		audioCollector_info.m_nSelectedStatus=0;
		audioCollector_info.m_nSignalValid=0;
		if(ENABLE_DISP_UART_MODULE)
		{
			Disp_Send_Audio_Collector_Channel_Status();
		}
		if(isDisplayValid())
		{
			#if (AIPU_VERSION && SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_2P1_320X240)
			aipu_main_win_audioCollectorStatus_update(0);
			#endif
		}
		send_online_info();
	}
	#elif(IS_DEVICE_AUDIO_MIXER)
	{
		g_audio_mixer_signal_valid=0;   //音频混音器信号无效
        g_device_audio_mixer_ready=0;   //混音器准备标志重置
		send_online_info();
	}
	#elif(IS_DEVICE_PHONE_GATEWAY)
	{
		g_phone_gateway_signal_valid=0;   //电话网关信号无效
        g_device_phone_gateway_ready=0;   //电话网关准备标志重置
		send_online_info();
	}
	#endif
	
	memset(g_media_name, 0x00, sizeof(g_media_name));
	pkg_query_current_status(NULL);
	
	printf("Set_zone_idle_status:ok\n");
}


//主机向终端设置网络模式(此条指令一般只允许主机以UDP发送，因为TCP模式下不允许随便设置模式，否则可能上不了线)
void ProcessHostSetNetWork(unsigned char * pkg,int pkg_len,bool isMulticast)
{
	switch (pkg[PAYLOAD_START])
	{
		case CMD_QUERY : // 查询
		{
			printf("ProcessHostSetNetWork Query...\n");
			unsigned char sendBuf[MAX_BUF_SIZE]={0};
			int i=0,data_pos=0;
			unsigned char data[128]={0};
			data[data_pos++]=g_network_mode;

			data[data_pos++]=strlen(g_host_tcp_addr_domain);
			memcpy(&data[data_pos],g_host_tcp_addr_domain,strlen(g_host_tcp_addr_domain));
			data_pos+=strlen(g_host_tcp_addr_domain);
			data[data_pos++]=g_host_tcp_port>>8;
			data[data_pos++]=g_host_tcp_port;

			//备用服务器信息
			data[data_pos++]=strlen(g_host_tcp_addr_domain2);
			memcpy(&data[data_pos],g_host_tcp_addr_domain2,strlen(g_host_tcp_addr_domain2));
			data_pos+=strlen(g_host_tcp_addr_domain2);
			data[data_pos++]=g_host_tcp_port2>>8;
			data[data_pos++]=g_host_tcp_port2;

			int data_len=data_pos;

			int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_QUERY_SET_NET_WORK_MODE,CURRENT_DEVICE_MODEL,data_len,data);
			// 发送数据
			host_udp_send_data(sendBuf, sendLen);
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_NETWORK_MODE_MULTICAST,CURRENT_DEVICE_MODEL,data_len,data);
			//20240928 增加组播查询
			multicast_send_data(sendBuf, sendLen);
		}
			break;
		case CMD_SET : 	//设置
		{
			int pos=PAYLOAD_START+1;
			int temp_network_mode=pkg[pos++];

			char temp_tcp_addr_domain[64]={0};	//主机地址(域名或者IP)
			int temp_host_tcp_port=0;			//主机TCP端口

			char temp_tcp_addr_domain2[64]={0};	//主机地址2(域名或者IP)
			int temp_host_tcp_port2=0;			//主机TCP端口2

			int ip_len=pkg[pos++];
			memset(temp_tcp_addr_domain,0,sizeof(temp_tcp_addr_domain));
			memcpy(temp_tcp_addr_domain,&pkg[pos],ip_len>64?64:ip_len);
			pos+=ip_len;
			temp_host_tcp_port=(pkg[pos]<<8)+pkg[pos+1];
			pos+=2;

			if(pos<pkg_len-1)
			{
				int ip_len=pkg[pos++];
				memset(temp_tcp_addr_domain2,0,sizeof(temp_tcp_addr_domain2));
				memcpy(temp_tcp_addr_domain2,&pkg[pos],ip_len>64?64:ip_len);
				pos+=ip_len;
				temp_host_tcp_port2=(pkg[pos]<<8)+pkg[pos+1];
				pos+=2;

				if(pos<pkg_len-1)
				{
					//存在mac时，判断mac是否和当前mac相同，如果不同，不处理
					if(	memcmp(pkg+pos,g_mac_addr,6)!= 0)
					{
					  	printf("ProcessHostSetNetWork:mac error!\n");
						return;
					}
					else
					{
						printf("ProcessHostSetNetWork:mac match!\n");
					}
				}
			}
			else
			{
				printf("not found tcp2...\n");
			}

			//判断temp_tcp_addr_domain和temp_tcp_addr_domain2是否是正确的IP地址或者域名
			if(temp_network_mode == NETWORK_MODE_WAN)
			{
				if(!if_a_string_is_a_valid_ipv4_address(temp_tcp_addr_domain) &&
				!is_valid_domain(temp_tcp_addr_domain) )
				{
					printf("ProcessHostSetNetWork:temp_tcp_addr_domain error\n");
					return;
				}
				if(!if_a_string_is_a_valid_ipv4_address(temp_tcp_addr_domain2) &&
				!is_valid_domain(temp_tcp_addr_domain2) )
				{
					printf("ProcessHostSetNetWork:temp_tcp_addr_domain2 error\n");
					memset(temp_tcp_addr_domain2,0,sizeof(temp_tcp_addr_domain2));
					temp_host_tcp_port2=0;
				}
			}

			if( g_network_mode!=temp_network_mode ||\
			    ( temp_network_mode == NETWORK_MODE_WAN &&\
				  ( (strcmp(temp_tcp_addr_domain,g_host_tcp_addr_domain)!=0 || temp_host_tcp_port != g_host_tcp_port ) ||\
				  	(strcmp(temp_tcp_addr_domain2,g_host_tcp_addr_domain2)!=0 || temp_host_tcp_port2 != g_host_tcp_port2 )\
				  )
				)
			  )
			{
				printf("Network Info CHANGE!\n");

				//先设置空闲状态
				Set_zone_idle_status(NULL,  __func__, __LINE__,true);

				//如果旧的是TCP模式,新的是UDP，那么需要关闭TCP线程
				if(g_network_mode == NETWORK_MODE_WAN && g_network_mode!=temp_network_mode)
				{
					g_network_mode=temp_network_mode;
					#if !NETWORK_VPN_INTERNET
					g_Is_tcp_real_internet=0;
					#endif
					tcp_client_exit();
				}
				//如果新的是TCP，两种情况：1、之前是UDP（开启TCP线程），2、TCP参数变化了（重连）
				else if(temp_network_mode == NETWORK_MODE_WAN)
				{
					memset(g_host_tcp_addr_domain,0,sizeof(g_host_tcp_addr_domain));
					memcpy(g_host_tcp_addr_domain, temp_tcp_addr_domain, sizeof(g_host_tcp_addr_domain));
					memset(g_host_tcp_addr_domain2,0,sizeof(g_host_tcp_addr_domain2));
					memcpy(g_host_tcp_addr_domain2, temp_tcp_addr_domain2, sizeof(g_host_tcp_addr_domain));
					printf("g_host_tcp_addr_domain:%s,g_host_tcp_addr_domain2:%s\n",g_host_tcp_addr_domain,g_host_tcp_addr_domain2);
					if(temp_host_tcp_port!=0)
					{
						g_host_tcp_port = temp_host_tcp_port;
						if(	g_host_tcp_port != DEFAULT_TCP_PORT )
						{
							g_host_kcp_port = g_host_tcp_port + 1;
						}
						else
						{
							g_host_kcp_port = DEFAULT_KCP_PORT;
						}

						g_host_tcp_port2 = temp_host_tcp_port2;
						if(	g_host_tcp_port2 != DEFAULT_TCP_PORT )
						{
							g_host_kcp_port2 = g_host_tcp_port2 + 1;
						}
						else
						{
							g_host_kcp_port2 = DEFAULT_KCP_PORT;
						}
					}
					printf("g_host_tcp_port:%d,g_host_tcp_port2:%d\n", g_host_tcp_port,g_host_tcp_port2);

					if( g_host_device_TimeOut >=0 && g_host_device_TimeOut < HOST_TIMEOUT_VALUE-15 )
					{
						g_host_device_TimeOut =  HOST_TIMEOUT_VALUE-15;
						host_ready_offline_flag = 1;
						//关闭网络连接状态IO输出
						GPIO_OutPut_Server_Connection(0);
					}

					//如果之前是UDP,新的是TCP，那么重启TCP线程
					if(g_network_mode == NETWORK_MODE_LAN)
					{
						g_network_mode=temp_network_mode;
						TCP_Client_Start();
					}
					else
					{
						tcp_client_reconnect();
					}
				}
				
				//保存网络信息
				save_sysconf(INI_SETCION_NETWORK,NULL);	
			}
			else
			{
				printf("Network Info NOT CHANGE!\n");
			}

			//发送查询结果
			unsigned char sendBuf[128]={0};

			int i=0,data_pos=0;
			unsigned char data[128]={0};
			data[data_pos++]=g_network_mode;

			data[data_pos++]=strlen(g_host_tcp_addr_domain);
			memcpy(&data[data_pos],g_host_tcp_addr_domain,strlen(g_host_tcp_addr_domain));
			data_pos+=strlen(g_host_tcp_addr_domain);
			data[data_pos++]=g_host_tcp_port>>8;
			data[data_pos++]=g_host_tcp_port;

			//备用服务器信息
			data[data_pos++]=strlen(g_host_tcp_addr_domain2);
			memcpy(&data[data_pos],g_host_tcp_addr_domain2,strlen(g_host_tcp_addr_domain2));
			data_pos+=strlen(g_host_tcp_addr_domain2);
			data[data_pos++]=g_host_tcp_port2>>8;
			data[data_pos++]=g_host_tcp_port2;

			int data_len=data_pos;


			int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_QUERY_SET_NET_WORK_MODE,CURRENT_DEVICE_MODEL,data_len,data);
			// 发送数据
			host_udp_send_data(sendBuf, sendLen);
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_NETWORK_MODE_MULTICAST,CURRENT_DEVICE_MODEL,data_len,data);
			//20240928 增加组播查询
			multicast_send_data(sendBuf, sendLen);
		}
			break;
		default :
			break;
	}
}

/*********************************************************************
 * @fn      HOST_QUERY_SET_IP_INFO
 *
 * @brief   //主机向终端设置IP属性
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void HOST_QUERY_SET_IP_INFO(unsigned char *pkg_data)
{
	//respond_null_payload_pkg(pkg_data);
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int pos=PAYLOAD_START+6;
	// 发送数据
	unsigned char client=pkg_data[4];

	int payload_len=(pkg_data[6]<<8)+pkg_data[7];
	printf("IpInfo pkg:");
	for(i=0;i<payload_len;i++)
	{
		printf("0x%x ",pkg_data[PAYLOAD_START+i]);
	}
	printf("\n");

	//判断是否为分区的MAC，否则退出
	int mac_match=1;
	for(i=0;i<6;i++)
	{
		if( pkg_data[PAYLOAD_START+i] != g_mac_addr[i] )
		{
			mac_match=0;
			printf("HOST_QUERY_SET_IP_INFO:MAC not match,return.\n");
			break;
		}
	}
	if(!mac_match)
		return;

	switch (pkg_data[PAYLOAD_START+6])
	{
		case CMD_QUERY : // 查询
		{
			printf("查询IP属性!\n");
			//IP分配方式
			memcpy(&data[0],g_mac_addr,6);
			data[6]=g_IP_Assign;
			if(g_IP_Assign == IP_ASSIGN_DHCP)
			{
				data_len=7;
			}
			else
			{
		        //静态IP地址
		        data_len=7;
		     	data[data_len++] = strlen(g_Static_ip_address);
		        memcpy(&data[data_len],g_Static_ip_address,strlen(g_Static_ip_address));
		     	data_len+=strlen(g_Static_ip_address);
		     	//子网掩码
		        data[data_len++] = strlen(g_Subnet_Mask);
		        memcpy(&data[data_len],g_Subnet_Mask,strlen(g_Subnet_Mask));
		        data_len+=strlen(g_Subnet_Mask);
		        //网关
		        data[data_len++] = strlen(g_GateWay);
		        memcpy(&data[data_len],g_GateWay,strlen(g_GateWay));
		        data_len+=strlen(g_GateWay);
		        //主DNS服务器
		        data[data_len++] = strlen(g_Primary_DNS);
		        memcpy(&data[data_len],g_Primary_DNS,strlen(g_Primary_DNS));
		        data_len+=strlen(g_Primary_DNS);
		        //备用DNS
		        data[data_len++] = strlen(g_Alternative_DNS);
		        memcpy(&data[data_len],g_Alternative_DNS,strlen(g_Alternative_DNS));
		        data_len+=strlen(g_Alternative_DNS);
			}
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_QUERY_SET_IP_INFO,CURRENT_DEVICE_MODEL,data_len,data);
			multicast_send_data(sendBuf, sendLen);

			if(g_network_mode == NETWORK_MODE_WAN)
			{
				#if ENABLE_TCP_CLIENT
				host_tcp_send_data(sendBuf, sendLen);
				#endif
			}
		}
		break;

		case CMD_SET : // 设置
		{
			printf("Set IP INFO!\n");
			pos++;
			int temp_len = 0;
			//IP分配方式
			int IP_Assign_temp = pkg_data[pos++];
			printf("IP_Assign_temp=%d\n",IP_Assign_temp);

			if(IP_Assign_temp == IP_ASSIGN_DHCP && g_IP_Assign == IP_ASSIGN_DHCP) //都为DHCP
			{
				printf("IP_Assign_temp=g_IP_Assign=IP_ASSIGN_DHCP,return!\n");
				break;
			}
			char TempStatic_ip_address[32]={0};
			char TempSubnet_Mask[32]={0};
			char TempGateWay[32]={0};
			char TempPrimary_DNS[32]={0};
			char TempAlternative_DNS[32]={0};

			if(IP_Assign_temp == IP_ASSIGN_STATIC)
			{
				//静态IP地址
				temp_len = pkg_data[pos++];
				memset(TempStatic_ip_address,0,sizeof(TempStatic_ip_address));
				memcpy(TempStatic_ip_address,&pkg_data[pos],temp_len);
				printf("g_Static_ip_address=%s,temp_len=%d\n",TempStatic_ip_address,temp_len);
				pos+=temp_len;
				//子网掩码
				temp_len=pkg_data[pos++];
				memset(TempSubnet_Mask,0,sizeof(TempSubnet_Mask));
				memcpy(TempSubnet_Mask,&pkg_data[pos],temp_len);
				printf("g_Subnet_Mask=%s,temp_len=%d\n",TempSubnet_Mask,temp_len);
				pos+=temp_len;
				//网关
				temp_len=pkg_data[pos++];
				memset(TempGateWay,0,sizeof(TempGateWay));
				memcpy(TempGateWay,&pkg_data[pos],temp_len);
				printf("g_GateWay=%s,temp_len=%d\n",TempGateWay,temp_len);
				pos+=temp_len;
				//主DNS服务器
				temp_len=pkg_data[pos++];
				memset(TempPrimary_DNS,0,sizeof(TempPrimary_DNS));
				memcpy(TempPrimary_DNS,&pkg_data[pos],temp_len);
				printf("g_Primary_DNS=%s,temp_len=%d\n",TempPrimary_DNS,temp_len);
				pos+=temp_len;
				//备用DNS
				temp_len=pkg_data[pos++];
				memset(TempAlternative_DNS,0,sizeof(TempAlternative_DNS));
				memcpy(TempAlternative_DNS,&pkg_data[pos],temp_len);
				printf("g_Alternative_DNS=%s,temp_len=%d\n",TempAlternative_DNS,temp_len);
				pos+=temp_len;

				//检测IP、掩码、网关是否输入错误
				if(!NET_INFO_ERROR(TempStatic_ip_address, TempSubnet_Mask, TempGateWay) && \
					!isGatewayByNetmask_Error(TempStatic_ip_address,TempSubnet_Mask,TempGateWay))
				{
					printf("Net parameter check ok!\n");
				}
				else
				{
					//长度或内容错误,返回错误
					return;
				}

				if(strcmp(g_Static_ip_address, TempStatic_ip_address) == 0 && strcmp(g_Subnet_Mask, TempSubnet_Mask) == 0 && strcmp(g_GateWay, TempGateWay) == 0 &&\
					(g_IP_Assign == IP_Assign_temp) &&\
					strcmp(g_Primary_DNS, TempPrimary_DNS) == 0 && strcmp(g_Alternative_DNS, TempAlternative_DNS) == 0)
				{
					printf("HOST_QUERY_SET_IP_INFO:No parameter changed.%d\n", g_IP_Assign);
					return;
				}

				memcpy(g_Static_ip_address, TempStatic_ip_address, 32);
				memcpy(g_Subnet_Mask, TempSubnet_Mask, 32);
				memcpy(g_GateWay,TempGateWay,32);

				//DNS
				if(if_a_string_is_a_valid_ipv4_address(TempPrimary_DNS))
				{
					memcpy(g_Primary_DNS,TempPrimary_DNS,32);
				}
				else
				{
					memset(g_Primary_DNS,0,sizeof(g_Primary_DNS));
				}
				if(if_a_string_is_a_valid_ipv4_address(TempAlternative_DNS))
				{
					memcpy(g_Alternative_DNS,TempAlternative_DNS,32);
				}
				else
				{
					memset(g_Alternative_DNS,0,sizeof(g_Alternative_DNS));
				}
			}

			g_IP_Assign = IP_Assign_temp;

			Send_Unonline_Info();
			
			//设置成功，发送给主机
			memcpy(data, g_mac_addr, 6);
			data[6]=g_IP_Assign;
			if(g_IP_Assign == IP_ASSIGN_DHCP)
			{
				data_len=7;
			}
			else
			{
		        //静态IP地址
		        data_len=7;
		     	data[data_len++] = strlen(g_Static_ip_address);
		        memcpy(&data[data_len],g_Static_ip_address,strlen(g_Static_ip_address));
		     	data_len+=strlen(g_Static_ip_address);
		     	//子网掩码
		        data[data_len++] = strlen(g_Subnet_Mask);
		        memcpy(&data[data_len],g_Subnet_Mask,strlen(g_Subnet_Mask));
		        data_len+=strlen(g_Subnet_Mask);
		        //网关
		        data[data_len++] = strlen(g_GateWay);
		        memcpy(&data[data_len],g_GateWay,strlen(g_GateWay));
		        data_len+=strlen(g_GateWay);
		        //主DNS服务器
		        data[data_len++] = strlen(g_Primary_DNS);
		        memcpy(&data[data_len],g_Primary_DNS,strlen(g_Primary_DNS));
		        data_len+=strlen(g_Primary_DNS);
		        //备用DNS
		        data[data_len++] = strlen(g_Alternative_DNS);
		        memcpy(&data[data_len],g_Alternative_DNS,strlen(g_Alternative_DNS));
		        data_len+=strlen(g_Alternative_DNS);
			}
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_QUERY_SET_IP_INFO,CURRENT_DEVICE_MODEL,data_len,data);
			multicast_send_data(sendBuf, sendLen);
			if(g_network_mode == NETWORK_MODE_WAN)
			{
				#if ENABLE_TCP_CLIENT
				host_tcp_send_data(sendBuf, sendLen);
				#endif
			}

			//保存网络信息
			save_sysconf(INI_SETCION_NETWORK,NULL);	
			System_Reboot();
		}
		break;
	}
}




/*********************************************************************
 * @fn      Host_Set_Dsp_Firmware_Feature
 *
 * @brief   主机或配置工具向终端查询/设置DSP固件功能特性（组播）
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void Host_Set_Dsp_Firmware_Feature(unsigned char *pkg_data)
{
#ifndef USE_PC_SIMULATOR
	
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int pos=PAYLOAD_START+6;
	// 发送数据
	unsigned char client=pkg_data[4];

	//判断是否为分区的MAC，否则退出
	int mac_match=1;
	for(i=0;i<6;i++)
	{
		if( pkg_data[PAYLOAD_START+i] != g_mac_addr[i] )
		{
			mac_match=0;
			//Dbg("Host_Set_Dsp_Firmware_Feature:MAC not match,return.\r\n");
			break;
		}
	}

	if(!mac_match)
		return;

	switch (pkg_data[PAYLOAD_START+6])
	{
		case CMD_QUERY : // 查询
		{
			printf("Query dsp feature!\n");
			memcpy(data,g_mac_addr,6);
			data_len+=6;
			for(i=0;i<DSP_AUDIO_MODULE_MAX;i++)
			{
				data[data_len++] = dsp_firmware_feature.module_switch[i];
				data[data_len++] = dsp_firmware_feature.module_gain[i]>>8;
				data[data_len++] = dsp_firmware_feature.module_gain[i];
			}
			
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_DSP_FIRMWARE_FEATURE,CURRENT_DEVICE_MODEL,data_len,data);
			multicast_send_data(sendBuf, sendLen);
		}
		break;

		case CMD_SET : // 设置
		{
			printf("Set dsp feature!\n");
		  	pos=PAYLOAD_START+7;
		  	for(i=0;i<DSP_AUDIO_MODULE_MAX;i++)
			{
				dsp_firmware_feature.module_switch[i] = pkg_data[pos];
				dsp_firmware_feature.module_gain[i] = (pkg_data[pos+1]<<8) + (pkg_data[pos+2]);
#if 0
				if(dsp_firmware_feature.module_gain[i]>0x3FFF)
				  dsp_firmware_feature.module_gain[i]=0x3FFF;
				else if(dsp_firmware_feature.module_gain[i]<0x3FF)
				  dsp_firmware_feature.module_gain[i]=0x3FF;
#endif
				pos+=3;
			}
			//dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_L] = 1;
			dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_L] = 1;
			dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_R] = 1;
		  
			for(i = 0;i< DSP_AUDIO_MODULE_MAX ;i++)
			{
				printf("Module[%d]:switch=%d,gain=%d\n",i,dsp_firmware_feature.module_switch[i],dsp_firmware_feature.module_gain[i]);
			}
	
		  	memcpy(data,g_mac_addr,6);
			data_len+=6;
			for(i=0;i<DSP_AUDIO_MODULE_MAX;i++)
			{
				data[data_len++] = dsp_firmware_feature.module_switch[i];
				data[data_len++] = dsp_firmware_feature.module_gain[i]>>8;
				data[data_len++] = dsp_firmware_feature.module_gain[i];
			}
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_DSP_FIRMWARE_FEATURE,CURRENT_DEVICE_MODEL,data_len,data);
			multicast_send_data(sendBuf, sendLen);
		  
			//保存配置信息
			save_sysconf(INI_SETCION_DSP_Firmware,NULL);
			
			System_Reboot();
		}
		break;
	}

#endif
}


/*********************************************************************
 * @fn      Host_Set_BT_Info
 *
 * @brief   //主机或配置工具向终端查询/设置蓝牙信息
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void Host_Set_BT_Info(unsigned char *pkg_data)
{
	if(!(IS_MODEL_WOODEN && g_exist_bluetooth_module))
	{
		return;
	}
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int pos=PAYLOAD_START+6;
	// 发送数据
	unsigned char client=pkg_data[4];

	//判断是否为分区的MAC，否则退出
	int mac_match=1;
	for(i=0;i<6;i++)
	{
		if( pkg_data[PAYLOAD_START+i] != g_mac_addr[i] )
		{
			mac_match=0;
			printf("Host_Set_BT_Info:MAC not match,return.\r\n");
			break;
		}
	}
	if(!mac_match)
    {
        printf("\n");
        for(i=0;i<6;i++)
        {
          printf("%2x:",pkg_data[PAYLOAD_START+i]);
        }
        printf("\n");
		return;
    }
	switch (pkg_data[PAYLOAD_START+6])
	{
		case CMD_QUERY : // 查询
		{
			printf("Query bt info!,g_bp1048_bt_info.name=%s,hasPassword=%d,pin=%s\r\n",g_bp1048_bt_info.name,g_bp1048_bt_info.hasPassword,g_bp1048_bt_info.password);
			memcpy(data,g_mac_addr,6);
			data_len+=6;
			data[data_len++]=strlen(g_bp1048_bt_info.name);
			memcpy(data+data_len,g_bp1048_bt_info.name,strlen(g_bp1048_bt_info.name));
            data_len+=strlen(g_bp1048_bt_info.name);
            data[data_len++]=g_bp1048_bt_info.hasPassword;
            memcpy(data+data_len,g_bp1048_bt_info.password,4);
            data_len+=4;
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_BLUETOOTH,CURRENT_DEVICE_MODEL,data_len,data);
			host_udp_send_data(sendBuf, sendLen);
		}
		break;

		case CMD_SET : // 设置
		{
			printf("Set bt info!\r\n");
		  	pos=PAYLOAD_START+7;
            
            int bt_name_len=pkg_data[pos++];
            printf("bt_name_len=%d\r\n",bt_name_len);
            memset(g_bp1048_bt_info.name,0,sizeof(g_bp1048_bt_info.name));
            memcpy(g_bp1048_bt_info.name,pkg_data+pos,bt_name_len);
 
            pos+=bt_name_len;
            g_bp1048_bt_info.hasPassword=pkg_data[pos++];
            memcpy(g_bp1048_bt_info.password,pkg_data+pos,4);
            printf("btName=%s,g_bp1048_bt_info.password=%s\r\n",g_bp1048_bt_info.name,g_bp1048_bt_info.password);
	
		  	memcpy(data,g_mac_addr,6);
			data_len+=6;
			data[data_len++]=strlen(g_bp1048_bt_info.name);
			memcpy(data+data_len,g_bp1048_bt_info.name,strlen(g_bp1048_bt_info.name));
            data_len+=strlen(g_bp1048_bt_info.name);
            data[data_len++]=g_bp1048_bt_info.hasPassword;
            memcpy(data+data_len,g_bp1048_bt_info.password,4);
            data_len+=4;
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_BLUETOOTH,CURRENT_DEVICE_MODEL,data_len,data);
			host_udp_send_data(sendBuf, sendLen);
		  
			//保存配置信息
			save_sysconf(INI_SETCION_BLUETOOTH,NULL);
			
			//设置蓝牙参数
			Bp1048_Send_Set_Bluetooth();
		}
		break;
	}
}


/*********************************************************************
 * @fn      Host_Set_SerialNumber
 *
 * @brief   //主机或配置工具向终端获取设备序列号（组播)
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void Host_Set_SerialNumber(unsigned char *pkg_data)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int pos=PAYLOAD_START+6;
	// 发送数据
	unsigned char client=pkg_data[4];

	//判断是否为分区的MAC，否则退出
	int mac_match=1;
	for(i=0;i<6;i++)
	{
		if( pkg_data[PAYLOAD_START+i] != g_mac_addr[i] )
		{
			mac_match=0;
			break;
		}
	}

	if(!mac_match)
		return;

	switch (pkg_data[PAYLOAD_START+6])
	{
		case CMD_QUERY : // 查询
		{
			printf("Query SerialNumber!\r\n");
			memcpy(data,g_mac_addr,6);
			data_len+=6;
			data[data_len++]=pkg_data[PAYLOAD_START+6];
			int sn_len=strlen(g_device_serialNum);
			data[data_len++]=sn_len;
			memcpy(data+data_len,g_device_serialNum,sn_len);
			data_len+=sn_len;
			
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_SERIAL_NUMBER,CURRENT_DEVICE_MODEL,data_len,data);
			multicast_send_data(sendBuf, sendLen);
		}
		break;

		case CMD_SET : // 设置
		{
			pos=PAYLOAD_START+7;
			int sn_len=pkg_data[pos++];
			if(sn_len>16)
			{
				break;
			}
			memset(g_device_serialNum,0,sizeof(g_device_serialNum));
			memcpy(g_device_serialNum,pkg_data+pos,sn_len);
			pos+=sn_len;
			
			printf("Set SerialNumber=%s\r\n",g_device_serialNum);
			//应答
			memcpy(data,g_mac_addr,6);
			data_len+=6;
			data[data_len++]=pkg_data[PAYLOAD_START+6];
			data[data_len++]=sn_len;
			memcpy(data+data_len,g_device_serialNum,sn_len);
			data_len+=sn_len;
			
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_SERIAL_NUMBER,CURRENT_DEVICE_MODEL,data_len,data);
			multicast_send_data(sendBuf, sendLen);

			save_sysconf(INI_SETCION_MFR,"SN");

			if(ENABLE_DISP_UART_MODULE)
			{
				Disp_Send_DeviceSN();
			}
		}
		break;
	}
}

/*********************************************************************
 * @fn      host_notify_decoder_ready_timing
 *
 * @brief   //主机通知解码终端即将进入定时音源
 * 			//收到此条指令后，应设一个超时时间，15秒后如果还是空闲状态，那么立即关闭信号状态输出，并允许后续的关闭操作。
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void host_notify_decoder_ready_timing(unsigned char *pkg_data)
{
	printf("host_notify_decoder_ready_timing...\n");
	respond_null_payload_pkg(pkg_data);
	g_NetAudioSignal_can_close = 0;
	GPIO_OutPut_NetAudio_Signal(1);	//打开网络音频信号IO输出
	#if DECODER_RELAY_TRIGGER_MUSIC
	Ctrl_Relay_EMC(1);  			//消防强切继电器打开
	#endif
}


/*********************************************************************
 * @fn      respond_pkg_time_synchronization
 *
 * @brief   向主机应答时间同步情况
 *
 * @param   rxbuf - 接收缓存
 *          code - 应答状态码
 *
 * @return	void
 *********************************************************************/
void respond_pkg_time_synchronization(unsigned char *rxbuf, unsigned char code)
{
	int payloadSize = 0;
	unsigned char send_buf[MAX_BUF_SIZE];

	memset(send_buf, 0x00, sizeof(send_buf)); // 清空缓存

	// 添加命令字
	send_buf[0] = rxbuf[0];
	send_buf[1] = rxbuf[1];
	// 添加包序号
	send_buf[2] = rxbuf[2];
	// 保留位
	send_buf[3] = RESERVE;
	// 产品型号
	send_buf[4] = CURRENT_DEVICE_MODEL;
	// 包属性及编码格式
	send_buf[5] = rxbuf[5];

	send_buf[PAYLOAD_START] = code;
	payloadSize += 1;

	// 添加负载数据长度
	send_buf[6] = payloadSize/256;
	send_buf[7] = payloadSize%256;

	// 计算校验和
	send_buf[payloadSize+PAYLOAD_START] = Calculate_XorDat(&send_buf[PAYLOAD_START], payloadSize);

	// 发送数据
	unsigned char client=rxbuf[4];

	host_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
}


extern bool uartNeedUpdateTime;

/*********************************************************************
 * @fn      pkg_set_local_time
 *
 * @brief   设置本地时间日期
 *
 * @param   rxbuf--接收缓冲区
 *
 * @return	void
 *********************************************************************/
void pkg_set_local_time(unsigned char *rxbuf)
{
	int i;
	int payloadSize = 0;
	int data_len = 0;
	unsigned char time_buf[32];
	unsigned char date_buf[32];

	// 初始化缓存
	memset(date_buf, 0x00, sizeof(date_buf));
	memset(time_buf, 0x00, sizeof(time_buf));

	// 接收日期数据
	if (rxbuf[PAYLOAD_START+payloadSize] < 10)
	{
		respond_pkg_time_synchronization(rxbuf, TIME_FORMAT_ERROR);
		return;
	}
	else
	{
		data_len = rxbuf[PAYLOAD_START+payloadSize];
		payloadSize += 1;
		for (i=0; i<data_len; i++)
		{
			date_buf[i] = rxbuf[PAYLOAD_START+payloadSize+i];
		}
		payloadSize += data_len;
		memcpy(sys_date_buf, date_buf, data_len);
	}

	// 接收时间数据
	if (rxbuf[PAYLOAD_START+payloadSize] < 8)
	{
		respond_pkg_time_synchronization(rxbuf, TIME_FORMAT_ERROR);
		return;
	}
	else
	{
		data_len = rxbuf[PAYLOAD_START+payloadSize];
		payloadSize += 1;
		for (i=0; i<data_len; i++)
		{
			time_buf[i] = rxbuf[PAYLOAD_START+payloadSize+i];
		}
		memcpy(sys_time_buf, time_buf, data_len);
		
		respond_pkg_time_synchronization(rxbuf, TIME_SET_SUCCEED);
	}

	HasGotSysTime=1;

	//如果是GPS校时器，不从主机同步时间
	if(IS_DEVICE_GPS_SYNCHRONIZER)
		return;

	//检测是否需要写入
	int year=0,mon=0,day=0;
	int hour=0,min=0,sec=0;
	sscanf(date_buf,"%d-%d-%d",&year,&mon,&day);
	sscanf(time_buf,"%d:%d:%d",&hour,&min,&sec);
	int update_time_flag=1;
	//printf("year=%d,mon=%d,day=%d,hour=%d,min=%d,sec=%d\n",year,mon,day,hour,min,sec);
	if( st_CurrentTime.year == year && st_CurrentTime.mon == mon && st_CurrentTime.day == day )
	{
		int time_host=hour*3600+min*60+sec;
		int time_current = st_CurrentTime.hour*3600+st_CurrentTime.min*60+st_CurrentTime.sec;
		if( abs(time_host-time_current) <=3 )
		{
			update_time_flag=0;
		}
	}
	if(update_time_flag)
	{
		struct tm tmp_time;
		char date_time_buf[32]={0};
		sprintf(date_time_buf,"%s %s",sys_date_buf,sys_time_buf);
		strptime(date_time_buf,"%Y-%m-%d %H:%M:%S",&tmp_time); //时间24时制
		int timeStack = mktime(&tmp_time);
		struct timeval tnow;
		tnow.tv_sec = timeStack;

		pthread_mutex_lock(&mutex_sysTime);
		settimeofday(&tnow,NULL);
		pthread_mutex_unlock(&mutex_sysTime);

		printf("Set LocalTime:%s %s\n",date_buf,time_buf);

		uartNeedUpdateTime=true;
	}
	else
	{
		printf("time not change\n");
	}
	//pox_system("hwclock -w");

	if(ENABLE_DISP_UART_MODULE)
	{
		Disp_Send_time_info();
	}
}

/*********************************************************************
 * @fn      requset_Host_Synchroniztion_Time
 *
 * @brief   请求主机同步时间
 *
 * @param   void
 *
 * @return  none
 */
void requset_Host_Synchroniztion_Time(void)
{
	printf("requset_Host_Synchroniztion_Time...\n");
	unsigned char sendBuf[MAX_BUF_SIZE] = {0};
	unsigned char data[128] = {0};
	int data_len = 0;
	int sendLen = Network_Send_Compose_CMD(sendBuf,CMD_SYN_HOST_TIME,CURRENT_DEVICE_MODEL,data_len,data);

	host_udp_send_data(sendBuf, sendLen);
}



/*************下列指令为音频采集器专用************************/
#if(IS_DEVICE_AUDIO_COLLECTOR)

/**
 * [ProcHost_Control_SIP_PAGING //主机向采集器主动发送终端音源选择状态]
 * @param rxbuf [description]
 */
void ProcHost_Send_AudioCollector_Selected(unsigned char *rxbuf)
{
	int pos=PAYLOAD_START;
	int status=rxbuf[pos++];
	if(status)
	{
		int value=rxbuf[pos++];
		if(audioCollector_info.m_nSelectedStatus!=value)
		{
			printf("TTTaudioCollector_info.m_nSelectedStatus=%d,value=%d\n",audioCollector_info.m_nSelectedStatus,value);
			if(audioCollector_info.m_nSelectedStatus == 0)
			{
				GPIO_OutPut_NetAudio_Led(1);
			}
			audioCollector_info.m_nSelectedStatus=value;
			if(ENABLE_DISP_UART_MODULE)
			{
				Disp_Send_Audio_Collector_Channel_Status();
			}
			
			if(isDisplayValid())
			{
				#if (AIPU_VERSION && SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_2P1_320X240)
				aipu_main_win_audioCollectorStatus_update(0);
				#endif
			}
		}
		else
		{
			printf("GGGaudioCollector_info.m_nSelectedStatus=%d\n",audioCollector_info.m_nSelectedStatus);
		}
	}
	else
	{
	  	if(audioCollector_info.m_nSelectedStatus!=0)
		{
			GPIO_OutPut_NetAudio_Led(0);
			audioCollector_info.m_nSelectedStatus=0;
			if(ENABLE_DISP_UART_MODULE)
			{
				Disp_Send_Audio_Collector_Channel_Status();
			}
			if(isDisplayValid())
			{
				#if (AIPU_VERSION && SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_2P1_320X240)
				aipu_main_win_audioCollectorStatus_update(0);
				#endif
			}
		}  
	}
	printf("ProcHost_Send_AudioCollector_Selected:%d\n",audioCollector_info.m_nSelectedStatus);
	
	//该条指令不需要应答
}

/**
 * [SendAudioCollectorStreamToServer 发送采集音频流到服务器，以便TCP模式设备接收]
 * @param rxbuf [description]
 */
void SendAudioCollectorStreamToServer(unsigned char *streamBuf,int streamLen,int channelId)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int data_len = 2+streamLen;
	unsigned char data[MAX_BUF_SIZE]={0};
	data[0] = channelId;
	data[1] = 2;	//G722
	memcpy(data+2,streamBuf,streamLen);

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_AUDIO_COLLECTOR_STREAM_TCP,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}


/**
 * [SendAudioCollectorStreamToMultiCast 发送采集音频流到组播上]
 * @param rxbuf [description]
 */
void SendAudioCollectorStreamToMultiCast(unsigned char *streamBuf,int streamLen,int channelId)
{
	//printf("SendAudioCollectorStreamToMultiCast:channelId=%d,streamLen=%d\n",channelId,streamLen);
	//if(g_network_mode == NETWORK_MODE_WAN)
		//return;
	static int multicast_send_socket=0;
	if(multicast_send_socket == 0)
	{
		multicast_send_socket=socket(AF_INET, SOCK_DGRAM, 0);
	}
	struct sockaddr_in multicast_send_sockAddr;
	memset (&multicast_send_sockAddr, 0, sizeof(multicast_send_sockAddr));
    multicast_send_sockAddr.sin_family = AF_INET;
	multicast_send_sockAddr.sin_addr.s_addr = inet_addr(g_ac_multicast_address);
	multicast_send_sockAddr.sin_port = htons (audioCollector_info.m_nTransPort[channelId-1]);

	sendto(multicast_send_socket, streamBuf, streamLen, 0,(struct sockaddr*)&multicast_send_sockAddr, sizeof(multicast_send_sockAddr));
}

#endif
/*************上面指令为音频采集器专用************************/



/*************下列指令为消防时序器专用************************/
#if(IS_DEVICE_FIRE_COLLECTOR)

/*********************************************************************
 * @fn      Host_Query_Fire_Collector_Trig_Status
 *
 * @brief   //主机向消防采集器查询通道触发状态
 *
 * @param   none
 *
 * @return  none
 */
void Host_Query_Fire_Collector_Trig_Status()
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;

	data[data_len++]=32;
	
	unsigned int trig_status = invert_int(fire_collector_info.trigger_status);

	data[data_len++] = trig_status>>24;
	data[data_len++] = trig_status>>16;
	data[data_len++] = trig_status>>8;
	data[data_len++] = trig_status;
	
	sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_QUERY_FIRE_TRIG_STATUS,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}


/*********************************************************************
 * @fn      Fire_Collector_Send_Trig_Status
 *
 * @brief   //消防采集器通道状态发生改变时主动通知主机
 *
 * @param   none
 *
 * @return  none
 */
void Fire_Collector_Send_Trig_Status(unsigned int triggerStatus)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;

	data[data_len++]=32;
	
	unsigned int trig_status = invert_int(triggerStatus);

	data[data_len++] = trig_status>>24;
	data[data_len++] = trig_status>>16;
	data[data_len++] = trig_status>>8;
	data[data_len++] = trig_status;
	
	sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_FIRE_TRIG_STATUS,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}




/*********************************************************************
 * @fn      Host_Set_Fire_Collector_Trig_Mode
 *
 * @brief   //主机向消防采集器查询设置触发模式
 *
 * @param   rxbuf - 包数据
 *
 * @return  none
 */
void Host_Set_Fire_Collector_Trig_Mode(unsigned char *rxbuf)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;


	switch (rxbuf[PAYLOAD_START])
	{
		case CMD_QUERY : // 查询
		{
			printf("Query Fire Collector Trig_Mode!\r\n");
			data[data_len++]=32;
			unsigned int trig_mode = invert_int(fire_collector_info.trigger_mode);
			
			data[data_len++] = trig_mode>>24;
			data[data_len++] = trig_mode>>16;
			data[data_len++] = trig_mode>>8;
			data[data_len++] = trig_mode;
			
			
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_FIRE_TRIG_MODE,CURRENT_DEVICE_MODEL,data_len,data);
			host_udp_send_data(sendBuf, sendLen);
		}
		break;

		case CMD_SET : // 设置
		{
			printf("Set Fire Collector Trig_Mode!\r\n");
			respond_null_payload_pkg(rxbuf);
			unsigned int trig_mode;
            trig_mode=(rxbuf[PAYLOAD_START+2]<<24)+(rxbuf[PAYLOAD_START+3]<<16)+(rxbuf[PAYLOAD_START+4]<<8)+(rxbuf[PAYLOAD_START+5]);
			trig_mode = invert_int(trig_mode);
			if(fire_collector_info.trigger_mode!= trig_mode)
			{
				fire_collector_info.trigger_mode= trig_mode;
				FIRE_Send_Set_TriggerMode();
				save_sysconf(INI_SETCION_FIRE,NULL);
			}
		}
		break;
	}
}

#endif
/*************上面指令为消防采集器专用************************/


/*************下列指令为电源时序器专用************************/
#if(IS_DEVICE_POWER_SEQUENCE)

/*********************************************************************
 * @fn      Host_Query_Set_Sequence_Power_Info
 *
 * @brief   //服务器查询/设置电源时序器信息
 *
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void Host_Query_Set_Sequence_Power_Info(unsigned char *rxbuf)
{
	if(rxbuf)
	{
		printf("Host_Query_Set_Sequence_Power_Info...\n");
	}
	else
	{
		printf("Host_Response_Set_Sequence_Power_Info...\n");
	}
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int pos=PAYLOAD_START;

	int event = CMD_QUERY;
	if(rxbuf)
	{
		event = rxbuf[pos++];
		if( event == CMD_SET)	//设置
		{
			int control_mode = rxbuf[pos++];
			int open_delay_value = (rxbuf[pos]<<8)+rxbuf[pos+1];
			pos+=2;
			int channelNum=rxbuf[pos++];
			unsigned short channel_status = (rxbuf[pos]<<8)+rxbuf[pos+1];
			pos+=2;
			int isQuickResponse=rxbuf[pos++];

			if(sequence_power_info.input_trigger)	//触发中，不改变时序器状态，默认全开
				return;

			printf("control_mode = %d,delay=%d,channelStatus=0x%x\n",control_mode,open_delay_value,channel_status);
			Set_Sequence_Power_Info(control_mode,channel_status,open_delay_value,isQuickResponse);
		}
	}
	
	//设置在Set_Sequence_Power_Info里面会发送一次
	if(event != CMD_SET)
	{
		data[data_len++] = event;
		data[data_len++] = sequence_power_info.control_mode;
		data[data_len++] = sequence_power_info.open_delay_value>>8;
		data[data_len++] = sequence_power_info.open_delay_value;
		data[data_len++] = SEQUENCE_POWER_MAX_CHANNEL_COUNT;
		data[data_len++] = sequence_power_info.channel_status>>8;
		data[data_len++] = sequence_power_info.channel_status;
		sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEQUENCE_POWER_INFO,CURRENT_DEVICE_MODEL,data_len,data);
		host_udp_send_data(sendBuf, sendLen);
	}
}



/*********************************************************************
 * @fn      Host_Send_Sequence_Timing_Info
 *
 * @brief   //服务器发送电源时序器定时点信息
 *
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void Host_Send_Sequence_Timing_Info(unsigned char *rxbuf)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int pos=PAYLOAD_START;

	//判断是否为分区的MAC，否则退出
	int mac_match=1;
	for(i=0;i<6;i++)
	{
		if( rxbuf[pos+i] != g_mac_addr[i] )
		{
			mac_match=0;
			printf("Host_Send_Sequence_Timing_Info:MAC not match,return.\r\n");
			break;
		}
	}
	if(!mac_match)
		return;

	//手动模式不处理
	if(sequence_power_info.control_mode == SEQUENCE_POWER_MODE_MANUAL)
		return;
	//触发中，不处理
	if(sequence_power_info.input_trigger)
	{
		//printf("sequence_power_info.input_trigger...\n");
		return;
	}

	int payload_length = (rxbuf[6]<<8)+rxbuf[7];
	pos+=6;
	int channelNum = rxbuf[pos++];
	int channel_timing_checked = (rxbuf[pos]<<8)+rxbuf[pos+1];
	pos+=2;
	int channel_timing_working = (rxbuf[pos]<<8)+rxbuf[pos+1];
	pos+=2;

	int IsQuickResponse = rxbuf[pos++];
	//如果需要快速响应，则定时信息不能控制
	if(IsQuickResponse)
	{
		g_seqPwr_timer_can_control = 0;
		g_seqPwr_timer_can_control_timeout = 0;
		//printf("IsQuickResponse...\n");
		Set_Sequence_Power_Info(sequence_power_info.control_mode,SEQUENCE_POWER_CHANNEL_OPEN_ALL,sequence_power_info.open_delay_value,1);
	}
	else
	{
		//printf("NotQuickResponse...\n");
	}
	
	if(!g_seqPwr_timer_can_control)
	{
		return;
	}

	if(channel_timing_working != sequence_power_info.channel_status)
	{
		printf("channelNum=%d,timing_checked=0x%x,timing_working=0x%x\n",channelNum,channel_timing_checked,channel_timing_working);
		Set_Sequence_Power_Info(sequence_power_info.control_mode,channel_timing_working,sequence_power_info.open_delay_value,0);
	}

	//Dbg("channelNum=%d,timing_checked=0x%x,timing_working=0x%x\n",channelNum,channel_timing_checked,channel_timing_working);
}

#endif
/*************上面指令为电源时序器专用************************/


void procHost_set_audioMixer_source(unsigned char *rxbuf)
{
	printf("procHost_set_audioMixer_source...\n");

	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[128]={0};
	int data_len = 1;
	data[0] = 1;	//1-接受  2-拒绝
	int sendLen = 0;

	unsigned char tmp_audio_mixer_mac[6]={0};
	unsigned char tmp_audio_mixer_eventType;	//事件类型：1为启动，0为停止，2为重试
	unsigned char tmp_audio_mixer_priority;
	unsigned char tmp_audio_mixer_volume=0;
	unsigned int  tmp_audio_mixer_sampleRate=0;
	char tmp_audio_mixer_broadcast_addr[32]={0};
	int  tmp_audio_mixer_broadcast_port=0;
	int  tmp_audio_mixer_codecs=0;

	int pos=PAYLOAD_START;
	memcpy(tmp_audio_mixer_mac,rxbuf+pos,6);
	pos+=6;

	tmp_audio_mixer_eventType = rxbuf[pos++];

	tmp_audio_mixer_priority = rxbuf[pos++];
	tmp_audio_mixer_volume = rxbuf[pos++];
	tmp_audio_mixer_sampleRate =  (rxbuf[pos]<<16)+(rxbuf[pos+1]<<8)+(rxbuf[pos+2]);   //3个字节
	pos+=3;

	pos+=2;	//跳过采样精度和声道数
	
	int broadcast_addrLen=rxbuf[pos++];
	memcpy(tmp_audio_mixer_broadcast_addr,rxbuf+pos,broadcast_addrLen);
	pos+=broadcast_addrLen;

	tmp_audio_mixer_broadcast_port = (rxbuf[pos]<<8)+rxbuf[pos+1];
	pos+=2;

	tmp_audio_mixer_codecs = rxbuf[pos++];

#if 0
	printf("tmp_audio_mixer_mac:\n");
	for(int i=0;i<6;i++)
	{
		printf("%02x ",tmp_audio_mixer_mac[i]);
	}
	printf("\n");
#endif
	printf("volume=%d,sampleRate=%d,broadcast_addr=%s,port=%d,codecs=%d\n", \
		tmp_audio_mixer_volume,tmp_audio_mixer_sampleRate,tmp_audio_mixer_broadcast_addr,tmp_audio_mixer_broadcast_port, \
		tmp_audio_mixer_codecs);

	#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
	bool isPriority_ok=false;
	bool curIsMixedSource=false;
	if(PriorityIsValid(PRIORITY_AUDIO_MIXED))
	{
		if(get_system_source() == SOURCE_AUDIO_MIXED)
		{
			curIsMixedSource=true;
			if(tmp_audio_mixer_priority>g_audio_mixer_priority)
			{
				isPriority_ok=true;
			}
			else if(tmp_audio_mixer_priority == g_audio_mixer_priority)
			{
				if(memcmp(tmp_audio_mixer_mac,g_audio_mixer_mac,6) == 0)
				{
					isPriority_ok=true;
				}
			}
		}
		else
		{
			isPriority_ok=true;
		}
	}

	if(tmp_audio_mixer_eventType == 0)	//停止
	{
		//如果当前音源是混音音源且之前的mac就是该混音器mac，那么停止，否则不处理
		if(curIsMixedSource && memcmp(tmp_audio_mixer_mac,g_audio_mixer_mac,6) == 0)
		{
			Set_zone_idle_status(NULL,  __func__, __LINE__,true);
		}
		//应答
		data[0]=1;		//接受
		sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_AUDIO_MIXER_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
		host_udp_send_data(sendBuf, sendLen);
		return;
	}
	else
	{
		if(!isPriority_ok)
		{
			//不满足优先级的话，退出
			data[0]=2;		//拒绝
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_AUDIO_MIXER_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
			host_udp_send_data(sendBuf, sendLen);
			return;
		}
	}

	if(tmp_audio_mixer_eventType == 2)	//重发
	{
		if(!mixed_source_repeat_again_enable)
		{
			//应答
			data[0]=2;		//拒绝
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_AUDIO_MIXER_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
			host_udp_send_data(sendBuf, sendLen);
			return;
		}
	}
	mixed_source_repeat_again_enable=1;

	//停止之前的音源
	//先判断目前是否已经处于混音器音源，是的话只需要变更相关参数，或者先不管这些，全部停止再说
	//清空节目名称
	memset(g_media_name,0,sizeof(g_media_name));
	Clean_All_Audio(1,1);
	#endif
	

	memcpy(g_audio_mixer_mac,tmp_audio_mixer_mac,6);
	g_audio_mixer_priority = tmp_audio_mixer_priority;
	g_audio_mixer_volume = tmp_audio_mixer_volume;
	g_audio_mixer_sampleRate = tmp_audio_mixer_sampleRate;
	sprintf(g_audio_mixer_broadcast_addr,"%s",tmp_audio_mixer_broadcast_addr);
	g_audio_mixer_broadcast_port = tmp_audio_mixer_broadcast_port;
	g_audio_mixer_codecs = tmp_audio_mixer_codecs;


	//respond_null_payload_pkg(rxbuf);

	//应答
	data[0]=1;		//接受
	sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_AUDIO_MIXER_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
	
	#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
	{
		//设置音频混音音源
		set_system_source(SOURCE_AUDIO_MIXED);

		if(g_audio_mixer_volume>=0 && g_audio_mixer_volume<=100)
		{
			if(!curIsMixedSource)
			{
				g_pre_system_volume=g_system_volume;
			}
			g_system_volume=g_audio_mixer_volume;
		}
		else
		{
			printf("g_audio_mixer_volume=%d,Follow Device!\n",g_audio_mixer_volume);
		}

		pkg_query_current_status(NULL);

		Open_Audio_Out(g_audio_mixer_sampleRate,16,1);

		Create_Concentrated_Mode_Play_Task();
		
		//开启超时检测线程
		Create_AudioMixer_Timing_Task();
		if(g_network_mode == NETWORK_MODE_LAN)
		{
			//如果是局域网模式，需要开启组播接收线程,注意要关掉原来的线程
			start_AudioMixer_recv_pthread();
		}
	}
	#elif(IS_DEVICE_AUDIO_MIXER)
	{
		//混音器收到指令，应该开始发送数据
		//设置音频混音音源
		//set_system_source(SOURCE_AUDIO_MIXED);
		g_device_audio_mixer_ready=1;
	}
	#endif

}



/*************下面指令为混音器专用************************/

#if(IS_DEVICE_AUDIO_MIXER)

/*********************************************************************
 * @fn      Host_Set_Audio_Mixer_Config
 *
 * @brief   //主机查询/设置音频混音器
 *
 * @param   rxbuf - 包数据
 *
 * @return  none
 */
void Host_Set_Audio_Mixer_Config(unsigned char *rxbuf)
{
	printf("Host_Set_Audio_Mixer_Config...\n");
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int dataPos=PAYLOAD_START;

	int event = rxbuf[dataPos++];
	if(event == CMD_SET)
	{
		int nMasterSwitch = rxbuf[dataPos++];
		int nPriority = rxbuf[dataPos++];
		int nTriggerType = rxbuf[dataPos++];
		int nTriggerSensitivity=rxbuf[dataPos++];
		int nVolumeFadeLevel = rxbuf[dataPos++];
		int nZoneVolume = rxbuf[dataPos++];
		//int nDelayMode = rxbuf[dataPos++];

		bool isUpdate=false;
		if(audioMixer_info.m_nMasterSwitch!=nMasterSwitch)
		{
			if(!nMasterSwitch)	//原来主开关是打开状态，现在变成了关闭状态
			{
				//判断信号是不是存在，如果存在，需要将信号置为0，并立即发送send_online_info,然后由服务器通知绑定的终端退出混音器音源
				//短时间内服务器会立即转变状态并再次通知该混音器和绑定终端
				if(g_audio_mixer_signal_valid)
				{
					g_audio_mixer_signal_valid=0;   //音频混音器信号无效
					g_device_audio_mixer_ready=0;   //混音器准备标志重置
					send_online_info();
				}
			}
			audioMixer_info.m_nMasterSwitch=nMasterSwitch;
			isUpdate=true;
		}
		if(audioMixer_info.m_nPriority!=nPriority)
		{
			audioMixer_info.m_nPriority=nPriority;
			isUpdate=true;
		}
		if(audioMixer_info.m_nTriggerType!=nTriggerType)
		{
			audioMixer_info.m_nTriggerType=nTriggerType;
			isUpdate=true;
		}
		if(audioMixer_info.m_nTriggerSensitivity!=nTriggerSensitivity)
		{
			audioMixer_info.m_nTriggerSensitivity=nTriggerSensitivity;
			isUpdate=true;
		}
		if(audioMixer_info.m_nVolumeFadeLevel!=nVolumeFadeLevel)
		{
			audioMixer_info.m_nVolumeFadeLevel=nVolumeFadeLevel;
			isUpdate=true;
		}
		if(audioMixer_info.m_nVolume!=nZoneVolume)
		{
			audioMixer_info.m_nVolume=nZoneVolume;
			isUpdate=true;
		}
		#if 0
		if(audioMixer_info.m_nDelayMode!=nDelayMode)
		{
			audioMixer_info.m_nDelayMode=nDelayMode;
			isUpdate=true;
		}
		#endif

		if(isUpdate)
		{
			save_sysconf(INI_SECTION_MIXER,NULL);
		}
	}

	data[data_len++]=event;
	data[data_len++]=audioMixer_info.m_nMasterSwitch;
	data[data_len++]=audioMixer_info.m_nPriority;
	data[data_len++]=audioMixer_info.m_nTriggerType;
	data[data_len++]=audioMixer_info.m_nTriggerSensitivity;
	data[data_len++]=audioMixer_info.m_nVolumeFadeLevel;
	data[data_len++]=audioMixer_info.m_nVolume;
	data[data_len++]=audioMixer_info.m_nDelayMode;

	sendLen=Network_Send_Compose_CMD(sendBuf,CMD_AUDIO_MIXER_CONFIG,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}


/**
 * [SendAudioMixerStreamToServer 发送混音器音频流到服务器，以便TCP模式设备接收]
 * @param rxbuf [description]
 */
void SendAudioMixerStreamToServer(unsigned char *streamBuf,int streamLen)
{
	//printf("SendAudioMixerStreamToServer:streamLen=%d\n",streamLen);
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int data_len = 8+streamLen;
	unsigned char data[MAX_BUF_SIZE]={0};
	memcpy(data,g_mac_addr,6);
	data[6] = DECODE_STANDARD_PCM;	//默认不转码，由服务器判断
	data[7] = g_audio_mixer_signalType;	//信号类型
	memcpy(data+8,streamBuf,streamLen);

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_AUDIO_MIXER_STREAM,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}


/**
 * [SendAudioMixerStreamToMultiCast 发送混音器音频流到组播上]
 * @param rxbuf [description]
 */
void SendAudioMixerStreamToMultiCast(unsigned char *streamBuf,int streamLen)
{
	//printf("SendAudioMixerStreamToMultiCast:streamLen=%d\n",streamLen);
	
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int data_len = 8+streamLen;
	unsigned char data[MAX_BUF_SIZE]={0};
	memcpy(data,g_mac_addr,6);
	data[6] = DECODE_STANDARD_PCM;	//默认不转码
	data[7] = g_audio_mixer_signalType;	//信号类型
	memcpy(data+8,streamBuf,streamLen);

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_AUDIO_MIXER_STREAM,CURRENT_DEVICE_MODEL,data_len,data);

	static int multicast_send_socket=0;
	if(multicast_send_socket == 0)
	{
		multicast_send_socket=socket(AF_INET, SOCK_DGRAM, 0);
	}
	struct sockaddr_in multicast_send_sockAddr;
	memset (&multicast_send_sockAddr, 0, sizeof(multicast_send_sockAddr));
    multicast_send_sockAddr.sin_family = AF_INET;
	multicast_send_sockAddr.sin_addr.s_addr = inet_addr(g_audio_mixer_broadcast_addr);
	multicast_send_sockAddr.sin_port = htons (g_audio_mixer_broadcast_port);

	sendto(multicast_send_socket, sendBuf, sendLen, 0,(struct sockaddr*)&multicast_send_sockAddr, sizeof(multicast_send_sockAddr));
}

#endif
/*************上面指令为混音器专用************************/




void procHost_set_phoneGateway_source(unsigned char *rxbuf)
{
	printf("procHost_set_phoneGateway_source...\n");

	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[128]={0};
	int data_len = 1;
	data[0] = 1;	//1-接受  2-拒绝
	int sendLen = 0;

	unsigned char tmp_phoneGateway_mac[6]={0};
	unsigned char tmp_phoneGateway_eventType;	//事件类型：1为启动，0为停止，2为重试
	unsigned char tmp_phoneGateway_volume=0;
	unsigned int  tmp_phoneGateway_sampleRate=0;
	char tmp_phoneGateway_broadcast_addr[32]={0};
	int  tmp_phoneGateway_broadcast_port=0;
	int  tmp_phoneGateway_codecs=0;

	int pos=PAYLOAD_START;
	memcpy(tmp_phoneGateway_mac,rxbuf+pos,6);
	pos+=6;

	tmp_phoneGateway_eventType = rxbuf[pos++];

	tmp_phoneGateway_volume = rxbuf[pos++];
	tmp_phoneGateway_sampleRate =  (rxbuf[pos]<<16)+(rxbuf[pos+1]<<8)+(rxbuf[pos+2]);   //3个字节
	pos+=3;

	pos+=2;	//跳过采样精度和声道数
	
	int broadcast_addrLen=rxbuf[pos++];
	memcpy(tmp_phoneGateway_broadcast_addr,rxbuf+pos,broadcast_addrLen);
	pos+=broadcast_addrLen;

	tmp_phoneGateway_broadcast_port = (rxbuf[pos]<<8)+rxbuf[pos+1];
	pos+=2;

	tmp_phoneGateway_codecs = rxbuf[pos++];

#if 0
	printf("tmp_phoneGateway_mac:\n");
	for(int i=0;i<6;i++)
	{
		printf("%02x ",tmp_phoneGateway_mac[i]);
	}
	printf("\n");
#endif
	//printf("volume=%d,sampleRate=%d,broadcast_addr=%s,port=%d,codecs=%d\n", \
		tmp_phoneGateway_volume,tmp_phoneGateway_sampleRate,tmp_phoneGateway_broadcast_addr,tmp_phoneGateway_broadcast_port, \
		tmp_phoneGateway_codecs);

	#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
	bool isPriority_ok=false;
	bool curIsPhoneGatewaySource=(get_system_source() == SOURCE_PHONE_GATEWAY);
	if(PriorityIsValid(PRIORITY_PHONE_GATEWAY))
	{
		if(get_system_source() == SOURCE_PHONE_GATEWAY)
		{
			isPriority_ok=false;
		}
		else
		{
			isPriority_ok=true;
		}
	}

	if(tmp_phoneGateway_eventType == 0)	//停止
	{
		//如果当前音源是电话网关音源且之前的mac就是该电话网关设备mac，那么停止，否则不处理
		if(curIsPhoneGatewaySource && memcmp(tmp_phoneGateway_mac,g_phone_gateway_mac,6) == 0)
		{
			Set_zone_idle_status(NULL,  __func__, __LINE__,true);
		}
		//应答
		data[0]=1;		//接受
		sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SET_PHONE_GATEWAY_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
		host_udp_send_data(sendBuf, sendLen);
		return;
	}
	else
	{
		if(!isPriority_ok)
		{
			//不满足优先级的话，退出
			data[0]=2;		//拒绝
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SET_PHONE_GATEWAY_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
			host_udp_send_data(sendBuf, sendLen);
			return;
		}
	}
	
	if(tmp_phoneGateway_eventType == 2)	//重发
	{
		if(!phone_gateway_source_repeat_again_enable)
		{
			//应答
			data[0]=2;		//拒绝
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SET_PHONE_GATEWAY_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
			host_udp_send_data(sendBuf, sendLen);
			return;
		}
	}
	phone_gateway_source_repeat_again_enable=1;

	//停止之前的音源
	//先判断目前是否已经处于混音器音源，是的话只需要变更相关参数，或者先不管这些，全部停止再说
	//清空节目名称
	memset(g_media_name,0,sizeof(g_media_name));
	Clean_All_Audio(1,1);
	#endif
	

	memcpy(g_phone_gateway_mac,tmp_phoneGateway_mac,6);
	g_phone_gateway_volume = tmp_phoneGateway_volume;
	g_phone_gateway_sampleRate = tmp_phoneGateway_sampleRate;
	sprintf(g_phone_gateway_broadcast_addr,"%s",tmp_phoneGateway_broadcast_addr);
	g_phone_gateway_broadcast_port = tmp_phoneGateway_broadcast_port;
	g_phone_gateway_codecs = tmp_phoneGateway_codecs;


	//respond_null_payload_pkg(rxbuf);

	//应答
	data[0]=1;		//接受
	sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SET_PHONE_GATEWAY_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
	
	#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
	{
		//设置音频混音音源
		set_system_source(SOURCE_PHONE_GATEWAY);

		if(g_phone_gateway_volume>=0 && g_phone_gateway_volume<=100)
		{
			if(!curIsPhoneGatewaySource)
			{
				g_pre_system_volume=g_system_volume;
			}
			g_system_volume=g_phone_gateway_volume;
		}
		else
		{
			printf("g_phone_gateway_volume=%d,Follow Device!\n",g_phone_gateway_volume);
		}

		pkg_query_current_status(NULL);

		Open_Audio_Out(g_phone_gateway_sampleRate,16,1);

		Create_Concentrated_Mode_Play_Task();
		
		//开启超时检测线程
		Create_PhoneGateway_Timing_Task();
		if(g_network_mode == NETWORK_MODE_LAN)
		{
			//如果是局域网模式，需要开启组播接收线程,注意要关掉原来的线程
			start_PhoneGateway_recv_pthread();
		}
	}
	#elif(IS_DEVICE_PHONE_GATEWAY)
	{
		//混音器收到指令，应该开始发送数据
		//设置音频混音音源
		//set_system_source(SOURCE_PHONE_GATEWAY);
		g_device_phone_gateway_ready=1;
	}
	#endif

}
/*************下面指令为电话网关专用************************/

#if(IS_DEVICE_PHONE_GATEWAY)

/*********************************************************************
 * @fn      Host_Set_Phone_Gateway_Config
 *
 * @brief   //主机查询/设置电话网关
 *
 * @param   rxbuf - 包数据
 *
 * @return  none
 */
void Host_Set_Phone_Gateway_Config(unsigned char *rxbuf)
{
	printf("Host_Set_Phone_Gateway_Config...\n");
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int dataPos=PAYLOAD_START;

	int event = rxbuf[dataPos++];
	if(event == CMD_SET)
	{
		int nMasterSwitch = rxbuf[dataPos++];
		int nZoneVolume = rxbuf[dataPos++];
		int telWhitelistslen = rxbuf[dataPos++];
		if(telWhitelistslen>MAX_PHONE_GATEWAY_WHITELIST_LENGTH-1)
		{
			telWhitelistslen = MAX_PHONE_GATEWAY_WHITELIST_LENGTH-1;
		}
		char telWhitelist[MAX_PHONE_GATEWAY_WHITELIST_LENGTH]={0}; //电话白名单
		memcpy(telWhitelist,rxbuf+dataPos,telWhitelistslen);
		dataPos+=telWhitelistslen;

		bool isUpdate=false;
		if(phoneGateway_info.m_nMasterSwitch!=nMasterSwitch)
		{
			if(!nMasterSwitch)	//原来主开关是打开状态，现在变成了关闭状态
			{
				//判断信号是不是存在，如果存在，需要将信号置为0，并立即发送send_online_info,然后由服务器通知绑定的终端退出混音器音源
				//短时间内服务器会立即转变状态并再次通知该混音器和绑定终端
				if(g_phone_gateway_signal_valid)
				{
					g_phone_gateway_signal_valid=0;   //电话网关信号无效
					g_device_phone_gateway_ready=0;   //电话网关准备标志重置
					send_online_info();
				}
			}
			phoneGateway_info.m_nMasterSwitch=nMasterSwitch;
			isUpdate=true;
		}
		
		if(phoneGateway_info.m_nVolume!=nZoneVolume)
		{
			phoneGateway_info.m_nVolume=nZoneVolume;
			isUpdate=true;
		}

		if(strcmp(phoneGateway_info.telWhitelist,telWhitelist))
		{
			memset(phoneGateway_info.telWhitelist,0,sizeof(phoneGateway_info.telWhitelist));
			strcpy(phoneGateway_info.telWhitelist,telWhitelist);
			isUpdate=true;
		}

		if(isUpdate)
		{
			save_sysconf(INI_SECTION_PHONE_GATEWAY,NULL);
		}
	}

	data[data_len++]=event;
	data[data_len++]=phoneGateway_info.m_nMasterSwitch;
	data[data_len++]=phoneGateway_info.m_nVolume;
	int TelWhitelistLen=strlen(phoneGateway_info.telWhitelist);
	data[data_len++]=TelWhitelistLen;
	memcpy(data+data_len,phoneGateway_info.telWhitelist,TelWhitelistLen);
	data_len+=TelWhitelistLen;

	sendLen=Network_Send_Compose_CMD(sendBuf,CMD_PHONE_GATEWAY_CONFIG,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}


/**
 * [SendPhoneGatewayStreamToServer 发送电话网关音频流到服务器，以便TCP模式设备接收]
 * @param rxbuf [description]
 */
void SendPhoneGatewayStreamToServer(unsigned char *streamBuf,int streamLen)
{
	//printf("SendPhoneGatewayStreamToServer:streamLen=%d\n",streamLen);
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int data_len = 7+streamLen;
	unsigned char data[MAX_BUF_SIZE]={0};
	memcpy(data,g_mac_addr,6);
	data[6] = DECODE_STANDARD_PCM;	//默认不转码，由服务器判断
	memcpy(data+7,streamBuf,streamLen);

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_PHONE_GATEWAY_STREAM,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}


/**
 * [SendPhoneGatewayStreamToMultiCast 发送电话网关音频流到组播上]
 * @param rxbuf [description]
 */
void SendPhoneGatewayStreamToMultiCast(unsigned char *streamBuf,int streamLen)
{
	//printf("SendPhoneGatewayStreamToMultiCast:streamLen=%d\n",streamLen);
	
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int data_len = 7+streamLen;
	unsigned char data[MAX_BUF_SIZE]={0};
	memcpy(data,g_mac_addr,6);
	data[6] = DECODE_STANDARD_PCM;	//默认不转码
	memcpy(data+7,streamBuf,streamLen);

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_PHONE_GATEWAY_STREAM,CURRENT_DEVICE_MODEL,data_len,data);

	static int multicast_send_socket=0;
	if(multicast_send_socket == 0)
	{
		multicast_send_socket=socket(AF_INET, SOCK_DGRAM, 0);
	}
	struct sockaddr_in multicast_send_sockAddr;
	memset (&multicast_send_sockAddr, 0, sizeof(multicast_send_sockAddr));
    multicast_send_sockAddr.sin_family = AF_INET;
	multicast_send_sockAddr.sin_addr.s_addr = inet_addr(g_phone_gateway_broadcast_addr);
	multicast_send_sockAddr.sin_port = htons (g_phone_gateway_broadcast_port);

	sendto(multicast_send_socket, sendBuf, sendLen, 0,(struct sockaddr*)&multicast_send_sockAddr, sizeof(multicast_send_sockAddr));
}

#endif
/*************上面指令为电话网关专用************************/


/*************下面指令为音频采集器专用************************/
#if(IS_DEVICE_AUDIO_COLLECTOR)

/*********************************************************************
 * @fn      Host_Set_Audio_Collector_Config
 *
 * @brief   //主机查询/设置音频采集器
 *
 * @param   rxbuf - 包数据
 *
 * @return  none
 */
void Host_Set_Audio_Collector_Config(unsigned char *rxbuf)
{
	printf("Host_Set_Audio_Collector_Config...\n");
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int dataPos=PAYLOAD_START;

	int event = rxbuf[dataPos++];
	if(event == CMD_SET)
	{
		int nTriggerSwitch = rxbuf[dataPos++];
		int nTriggerChannelId = rxbuf[dataPos++];
		int nTriggerZoneVolume = rxbuf[dataPos++];

		bool isUpdate=false;
		if(audioCollector_info.m_nTriggerSwitch!=nTriggerSwitch)
		{
			if(!nTriggerSwitch)	//原来主开关是打开状态，现在变成了关闭状态
			{
				//判断信号是不是存在，如果存在，需要将信号置为0，并立即发送send_online_info,然后由服务器通知绑定的终端退出采集音源
				//短时间内服务器会立即转变状态并再次通知该采集器和绑定终端
				if(audioCollector_info.m_nSignalValid)
				{
					audioCollector_info.m_nSignalValid=0;   //音频采集器信号无效
					send_online_info();
				}
			}
			audioCollector_info.m_nTriggerSwitch=nTriggerSwitch;
			isUpdate=true;
		}
		if(audioCollector_info.m_nTriggerChannelId!=nTriggerChannelId)
		{
			audioCollector_info.m_nTriggerChannelId=nTriggerChannelId;
			isUpdate=true;
		}
		if(audioCollector_info.m_nTriggerZoneVolume!=nTriggerZoneVolume)
		{
			audioCollector_info.m_nTriggerZoneVolume=nTriggerZoneVolume;
			isUpdate=true;
		}

		if(isUpdate)
		{
			save_sysconf(INI_SECTION_COLLECTOR,NULL);
		}
	}

	data[data_len++]=event;
	data[data_len++]=audioCollector_info.m_nTriggerSwitch;
	data[data_len++]=audioCollector_info.m_nTriggerChannelId;
	data[data_len++]=audioCollector_info.m_nTriggerZoneVolume;

	sendLen=Network_Send_Compose_CMD(sendBuf,CMD_AUDIO_COLLECTOR_CONFIG,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}


#endif
/*************上面指令为音频采集器专用************************/


/*************下面指令为远程遥控器专用************************/

#if(IS_DEVICE_REMOTE_CONTROLER)

/**
 * [SendRemoteControlerKeyToServer 发送远程遥控器按键到服务器]
 * @param rxbuf [description]
 */
void SendRemoteControlerKeyToServer(unsigned char keyId)
{
	//printf("SendAudioMixerStreamToServer:streamLen=%d\n",streamLen);
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[MAX_BUF_SIZE]={0};
	int data_len = 1;
	data[0] = keyId;

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_REMOTE_CONTROLER_KEY,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}

#endif
/*************上面指令为远程遥控器专用************************/


/*************下面指令为对讲终端专用************************/

#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)

/****************************************************
 * @fn      calling_invation
 *
 * @brief	主叫方邀请对讲 
 *
 * @param	unsigned char *calledMac 被叫方MAC
 *
 * @return	int 应答包长度
 */
void Send_calling_invation(unsigned char *calledMac)
{
	printf("Send_calling_invation...\n");
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=19;

	memcpy(data,g_mac_addr,6);	//主叫方MAC
	memcpy(data+6,calledMac,6);	//被叫方MAC

	unsigned char audioCoding=DECODE_STANDARD_PCM;          //语音编码
	data[12]=audioCoding;	//音频编码
	data[13]=1;	//NAT
	data[14]=0;	//port high
	data[15]=0;	//port low
	data[16]=CURRENT_DEVICE_MODEL;	//主叫方设备型号
	data[17]=CALL_TIMESTAMP_UNIT>>8;	//媒体时间戳单元 high
	data[18]=(unsigned char)CALL_TIMESTAMP_UNIT;		//媒体时间戳单元 low

	memcpy(m_stCallInfo.deviceMac,calledMac,6);
	m_stCallInfo.isCallingParty = 1;	//主叫方

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CALLING_INVITATION,CURRENT_DEVICE_MODEL,data_len,data);
	// 发送数据
	host_udp_send_data(sendBuf, sendLen);
}



/****************************************************
 * @fn      Recv_calling_invation
 *
 * @brief	收到主叫方的对讲邀请 
 *
 * @param	
 *
 * @return	int 应答包长度
 */
bool Recv_calling_invation(unsigned char *Pkg)
{
	if(!IS_DEVICE_SUPPORT_INTERCOM)
	{
		return false;
	}
	printf("Recv_calling_invation1...\n");
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=19;

	int pos=PAYLOAD_START;

	unsigned char callingMac[6]={0};
	unsigned char calledMac[6]={0};
	memcpy(callingMac,Pkg+pos,6);	//保存主叫方MAC
	memcpy(calledMac,Pkg+pos+6,6);	//保存被叫方MAC
	pos+=12;

	//如果主叫就是本机，那么代表被叫终端不存在，立即挂断
	if(	memcmp(callingMac,g_mac_addr,6) == 0 )
	{
		printf("Recv_calling_invation2.\n");
		m_stCallInfo.self_callStatus = CALL_STATUS_FREE;
		EnterCallMode(false);
		return false;
	}

	//如果被叫方MAC与本机不符，不响应
	if(	memcmp(calledMac,g_mac_addr,6) )
	{
		printf("Recv_calling_invation3.\n");
		Send_callStatus_feedback(CALL_STATUS_REJECT);
		return false;
	}

	//todo如果当前不允许进入对讲音源
	if( !PriorityIsValid(PRIORITY_CALL) || m_stCallInfo.self_callStatus != CALL_STATUS_FREE)
	{
		printf("Recv_calling_invation4.\n");
		//应答状态
		Send_callStatus_feedback(CALL_STATUS_BUSY);
		return false;
	}

    unsigned char  audioCoding=Pkg[pos++];          //语音编码
    unsigned char  enableNat=Pkg[pos++];            //开启NAT后由主机转发双方音频流
    unsigned short audioPort=(Pkg[pos]<<8)+(Pkg[pos+1]);      //关闭NAT后有效
	pos+=2;
	//主叫方设备型号
	unsigned char callingModel = Pkg[pos++];
	//媒体时间戳单元
	unsigned short mediaStampUnit = (Pkg[pos]<<8)+(Pkg[pos+1]);
	pos+=2;
	
	//if(m_stPager_Info.self_audioCoding != DECODE_STANDARD_PCM && m_stPager_Info.self_audioCoding != DECODE_G711 && \
		m_stPager_Info.self_audioCoding != DECODE_G722 && m_stPager_Info.self_audioCoding != DECODE_G722_1)
	if(audioCoding != DECODE_STANDARD_PCM && audioCoding != DECODE_G711 && audioCoding != DECODE_G722)
	{
		printf("Recv_calling_invation5.\n");
		Send_callStatus_feedback(CALL_STATUS_CODECES_NOT_SUPPORT);
		return false;
	}
	m_stCallInfo.audioCoding = audioCoding;
	m_stCallInfo.isEnableNat =   enableNat;
	m_stCallInfo.audioPort = audioPort;
	m_stCallInfo.isCallingParty = 0;	//被叫方
	memcpy(m_stCallInfo.deviceMac,callingMac,6);

	printf("Recv_calling_invation2,callingMac:\n");
	
	for(i=0;i<6;i++)
	{
		printf("%x: ",m_stCallInfo.deviceMac[i]);
	}
	printf("\n");


	memcpy(data,callingMac,6);	//主叫方MAC
	memcpy(data+6,calledMac,6);	//被叫方MAC
	data[12]=audioCoding;	//音频编码
	data[13]=enableNat;		//NAT
	data[14]=audioPort>>8;	//port high
	data[15]=audioPort;	//port low
	data[16]=CURRENT_DEVICE_MODEL;	//主叫方设备型号
	data[17]=CALL_TIMESTAMP_UNIT>>8;	//媒体时间戳单元 high
	data[18]=(unsigned char)CALL_TIMESTAMP_UNIT;		//媒体时间戳单元 low


	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CALLED_RESPONSE,CURRENT_DEVICE_MODEL,data_len,data);
	// 发送数据
	host_udp_send_data(sendBuf, sendLen);
	
	//如果不是寻呼站发起的对讲，那么不自动应答，而是播放铃声
	if(callingModel!=DEVICE_MODEL_PAGING)
	{
		m_stCallInfo.self_callStatus = CALL_STATUS_WAIT_CALLED_ANSWER;
		Send_callStatus_feedback(CALL_STATUS_WAIT_CALLED_ANSWER);

		Clean_All_Audio(1,1);
		set_system_source(SOURCE_CALL);
		//开启铃声线程
		Create_AudioCall_Ring_Task();
	}
	else
	{
		m_stCallInfo.self_callStatus = CALL_STATUS_CONNECT;
		Send_callStatus_feedback(CALL_STATUS_CONNECT);
		//开启对讲
		EnterCallMode(true);
	}
	
	return true;
}




/****************************************************
 * @fn      Recv_called_response
 *
 * @brief	收到被叫方的对讲应答 
 *
 * @param	
 *
 * @return	int 应答包长度
 */
bool Recv_called_response(unsigned char *Pkg)
{
	if(!IS_DEVICE_SUPPORT_INTERCOM)
	{
		return false;
	}
	printf("Recv_called_response1...\n");
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	int pos=PAYLOAD_START;

	unsigned char calledMac[6]={0};
	memcpy(calledMac,Pkg+pos+6,6);	//被叫方MAC
	pos+=12;
	
    if( memcmp(calledMac,m_stCallInfo.deviceMac,6) )		//跟发起时的被叫MAC不符，退出
	{
		Send_callStatus_feedback(CALL_STATUS_BUSY);
		return false;
	}

	unsigned char  audioCoding=Pkg[pos++];          //语音编码
    unsigned char  enableNat=Pkg[pos++];            //开启NAT后由主机转发双方音频流
    unsigned short audioPort=(Pkg[pos]<<8)+(Pkg[pos+1]);      //关闭NAT后有效
	pos+=2;
	//主叫方设备型号
	unsigned char callingModel = Pkg[pos++];
	//媒体时间戳单元
	unsigned short mediaStampUnit = (Pkg[pos]<<8)+(Pkg[pos+1]);
	pos+=2;
	
	//if(m_stPager_Info.self_audioCoding != DECODE_STANDARD_PCM && m_stPager_Info.self_audioCoding != DECODE_G711 && \
		m_stPager_Info.self_audioCoding != DECODE_G722 && m_stPager_Info.self_audioCoding != DECODE_G722_1)
	if(audioCoding != DECODE_STANDARD_PCM && audioCoding != DECODE_G711 && audioCoding != DECODE_G722)
	{
		Send_callStatus_feedback(CALL_STATUS_CODECES_NOT_SUPPORT);
		return false;
	}
	m_stCallInfo.audioCoding = audioCoding;
	m_stCallInfo.isEnableNat =   enableNat;
	m_stCallInfo.audioPort = audioPort;
	m_stCallInfo.isCallingParty = 1;	//主叫方

	printf("Recv_called_response ok...\n");
	//
	//m_stCallInfo.self_callStatus = CALL_STATUS_WAIT_CALLED_ANSWER;
	//Send_callStatus_feedback(CALL_STATUS_WAIT_CALLED_ANSWER);

	return true;
}





/****************************************************
 * @fn      Send_callStatus_feedback
 *
 * @brief	对讲设备主动发送对讲状态 
 *
 * @param	
 *
 * @return	int 应答包长度
 */
void Send_callStatus_feedback(int status)
{
	printf("Send_callStatus_feedback,status=%d...\n",status);
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=7;

	memcpy(data,g_mac_addr,6);	//本机MAC
	data[6]=status;	//音频编码

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CALLED_STATUS,CURRENT_DEVICE_MODEL,data_len,data);
	// 发送数据
	host_udp_send_data(sendBuf, sendLen);
}


/****************************************************
 * @fn      Recv_callStatus_feedback
 *
 * @brief	收到对讲方的状态应答 
 *
 * @param	
 *
 * @return	int 应答包长度
 */
void Recv_callStatus_feedback(unsigned char *Pkg)
{
	if(!IS_DEVICE_SUPPORT_INTERCOM)
	{
		return;
	}
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	int pos=PAYLOAD_START;

	unsigned char calledMac[6]={0};
	memcpy(calledMac,Pkg+pos,6);			//MAC
	pos+=6;
	int callStatus=Pkg[pos++];

	
	m_stCallInfo.other_callStatus = callStatus;

	printf("Recv_callStatus_feedback:%d\n",callStatus);

	if(m_stCallInfo.self_callStatus != CALL_STATUS_FREE && ( callStatus == CALL_STATUS_FREE || callStatus == CALL_STATUS_HANGUP ) )
	{
		//如果是挂断，需要立即响应，避免线程轮询后接收多个状态后覆盖
		printf("other hangup!\n");
		m_stCallInfo.self_callStatus = CALL_STATUS_FREE;
		EnterCallMode(false);
	}
	else if(m_stCallInfo.self_callStatus == CALL_STATUS_WAIT_CALLED_ANSWER && callStatus == CALL_STATUS_CONNECT)
	{
		//主叫
		printf("other connect!\n");
		m_stCallInfo.self_callStatus = CALL_STATUS_CONNECT;
		Send_callStatus_feedback(CALL_STATUS_CONNECT);
		EnterCallMode(true);
	}
}




/****************************************************
 * @fn      Send_Call_Audio_Stream
 *
 * @brief   //发送对讲音频流
 *
 * @param
 *
 * @return	int 应答包长度
 */
int Send_Call_Audio_Stream( unsigned char* data,int len )
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int Pkg_count=0;

	  if(len%512 > 0)
		  Pkg_count=len/512 + 1;
	  else
		  Pkg_count=len/512;
	//if(Paging_status != PAGING_START) printf("\nSendToZone_PCM_Data_Cmd,len=%d，Pkg_count=%d\n",len,Pkg_count);
	int sendLen=0;
	int Len_Sended=0;

	int i;
	for(i=0;i<Pkg_count;i++)		//最大
	{
		
		unsigned char dataBuf[MAX_BUF_SIZE]={0};
		int pos=0;

		//本机mac
		memcpy(dataBuf+pos,g_mac_addr,6);			//MAC
		pos+=6;

		int streamLen=(len>=512)?512:len;
		//数据流长度
		dataBuf[pos++]=streamLen>>8;				//stream_len High
		dataBuf[pos++]=streamLen;					//stream_len low
		
		//数据流偏移量
		dataBuf[pos++]=6+2+1+4;					//mac+streamLen+streamOffset+timeStamp
		
		//媒体时间戳
		dataBuf[pos++]=m_stCallInfo.self_mediaTimeStamp>>24;
		dataBuf[pos++]=m_stCallInfo.self_mediaTimeStamp>>16;
		dataBuf[pos++]=m_stCallInfo.self_mediaTimeStamp>>8;
		dataBuf[pos++]=m_stCallInfo.self_mediaTimeStamp;

		memcpy(dataBuf+pos,data+Len_Sended,streamLen);
		pos+=streamLen;

		sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CALLING_AUDIOSTREAM , CURRENT_DEVICE_MODEL,pos,dataBuf);
		host_udp_send_data(sendBuf, sendLen);
		len-=512;
		Len_Sended+=512;
	}
	//每发送一次整包，时间戳自增
	m_stCallInfo.self_mediaTimeStamp += CALL_TIMESTAMP_UNIT;

}



/****************************************************
 * @fn      Recv_Call_Audio_Stream
 *
 * @brief   //接收对讲音频流
 *
 * @param
 *
 * @return	int 应答包长度
 */
void Recv_Call_Audio_Stream(unsigned char *Pkg)
{
	if(!IS_DEVICE_SUPPORT_INTERCOM)
	{
		return;
	}
	if( m_stCallInfo.self_callStatus != CALL_STATUS_CONNECT )
	{
		return;
	}
	int i=0;

	int pos=PAYLOAD_START;

	//MAC
	unsigned char calledMac[6]={0};
	memcpy(calledMac,Pkg+pos,6);			//MAC
	pos+=6;
	//数据流长度
	int streamLen = (Pkg[pos]<<8)+Pkg[pos+1];
	pos+=2;
	//数据流偏移量
	unsigned char streamOffset=Pkg[pos++];
	//媒体时间戳
	unsigned int mediaTimeStamp = (Pkg[pos]<<24) + (Pkg[pos+1]<<16) + (Pkg[pos+2]<<8) + Pkg[pos+3] ;
	pos+=4;

	
	if( memcmp(calledMac,m_stCallInfo.deviceMac,6) )
	{
		return;
	}

	m_stCall_recv_stream.rx_call_timing_count=0;
	
	//pthread_mutex_lock(&call_data_mutex);
	memcpy(m_stCall_recv_stream.rx_call_data[m_stCall_recv_stream.rx_call_read_pos],Pkg+PAYLOAD_START+streamOffset,streamLen);
	m_stCall_recv_stream.rx_call_len[m_stCall_recv_stream.rx_call_read_pos]=streamLen;
	m_stCall_recv_stream.rx_call_valid[m_stCall_recv_stream.rx_call_read_pos]=1;
	//printf("rx_call_stream.rx_call_read_pos=%d,len=%d\n",m_stCall_recv_stream.rx_call_read_pos,streamLen);
	m_stCall_recv_stream.rx_call_read_pos++;
	if(m_stCall_recv_stream.rx_call_read_pos>=RX_CALL_BUF_PKG_MAX)
		m_stCall_recv_stream.rx_call_read_pos=0;
	//pthread_mutex_unlock(&call_data_mutex);
}







/****************************************************
 * @fn      procHost_Query_Set_Intercom_Basic
 *
 * @brief   //处理 主机查询/设置对讲终端基础参数
 *
 * @param
 *
 * @return	void
 */
void procHost_Query_Set_Intercom_Basic(unsigned char *Pkg,int payload_Length)
{
	if(!IS_DEVICE_SUPPORT_INTERCOM)
	{
		return;
	}
	int pos=PAYLOAD_START;
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[128]={0};
	int data_len=0;

	switch (Pkg[pos++])
   {
	   case CMD_QUERY : // 查询
	   {
           printf("procHost_Query_Set_Intercom_Basic:query\n");
		   data[data_len++] = CMD_QUERY;	//查询/设置
		   data[data_len++] = 1;	//参数id
		   data[data_len++] = 12;	//参数长度
		   memcpy(data+data_len,m_stCallDeviceConfig.Key1_mac,6);
		   data_len+=6;
		   memcpy(data+data_len,m_stCallDeviceConfig.Key2_mac,6);
		   data_len+=6;
		   data[data_len++] = 2;	//参数id
		   data[data_len++] = 3;	//参数长度
		   data[data_len++] = m_stCallDeviceConfig.AutoAnswerTime;
		   data[data_len++] = m_stCallDeviceConfig.micVol;
		   data[data_len++] = m_stCallDeviceConfig.farOutVol;

		   int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_INTERCOM_BASIC_CONFIG,CURRENT_DEVICE_MODEL,data_len,data);
		   //发送数据
		   host_udp_send_data(sendBuf, sendLen);
	   }
	   break;
	   case CMD_SET : //设置
	   {
#if 0
			printf("Call Set Buf:payload_Length=%d\n",payload_Length);
			for(int i=PAYLOAD_START;i<PAYLOAD_START+payload_Length;i++)
			{
				printf("%x ",Pkg[i]);
			}
			printf("\n");
#endif
			int parmIndex=0;
			unsigned char key1_mac[6]={0};
			unsigned char key2_mac[6]={0};

			int count=0;
			while( pos < PAYLOAD_START+payload_Length && ++count<MAX_BUF_SIZE )
			{
				int parmId = Pkg[pos++];
				int parmLength=Pkg[pos++];
				
				for(parmIndex=0;parmIndex<parmLength && pos < PAYLOAD_START+payload_Length;)
				{
					if(parmId == 1)
					{
						if(parmIndex == 0)
						{
							memcpy(key1_mac,Pkg+pos,6);
							pos+=6;
							parmIndex+=6;

							memcpy(m_stCallDeviceConfig.Key1_mac,key1_mac,6);
						}
						else if(parmIndex == 6)
						{
							memcpy(key2_mac,Pkg+pos,6);
							pos+=6;
							parmIndex+=6;

							memcpy(m_stCallDeviceConfig.Key2_mac,key2_mac,6);
						}
						else
						{
							pos++;			//如果该参数还有其他字段，暂时不处理
							parmIndex++;	//如果该参数还有其他字段，暂时不处理
						}
					}
					else if(parmId == 2)
					{
						if(parmIndex == 0)
						{
							m_stCallDeviceConfig.AutoAnswerTime = Pkg[pos++];
							parmIndex++;
						}
						else if(parmIndex == 1)
						{
							m_stCallDeviceConfig.micVol = Pkg[pos++];
							parmIndex++;
						}
						else if(parmIndex == 2)
						{
							m_stCallDeviceConfig.farOutVol = Pkg[pos++];
							parmIndex++;
						}
						else
						{
							pos++;			//如果该参数还有其他字段，暂时不处理
							parmIndex++;	//如果该参数还有其他字段，暂时不处理
						}
					}
					else
					{
						pos += parmLength;	//不是预设的参数id,跳过
						break;
					}
				}
			}

			save_sysconf(INI_SECTION_INTERCOM,NULL);


			//应答给服务器
		   data[data_len++] = CMD_SET;	//查询/设置
		   data[data_len++] = 1;	//参数id
		   data[data_len++] = 12;	//参数长度
		   memcpy(data+data_len,m_stCallDeviceConfig.Key1_mac,6);
		   data_len+=6;
		   memcpy(data+data_len,m_stCallDeviceConfig.Key2_mac,6);
		   data_len+=6;
		   data[data_len++] = 2;	//参数id
		   data[data_len++] = 3;	//参数长度
		   data[data_len++] = m_stCallDeviceConfig.AutoAnswerTime;
		   data[data_len++] = m_stCallDeviceConfig.micVol;
		   data[data_len++] = m_stCallDeviceConfig.farOutVol;
		   int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_INTERCOM_BASIC_CONFIG,CURRENT_DEVICE_MODEL,data_len,data);
		   //发送数据
		   host_udp_send_data(sendBuf, sendLen);

	   }
	   break;
   }
}

#endif
/*************上面指令为对讲终端专用************************/




#if 1
/****************************************************
 * @fn      procHost_Query_Set_Trigger
 *
 * @brief   //处理 主机查询/设置终端触发参数
 *
 * @param
 *
 * @return	void
 */
void procHost_Query_Set_Trigger(unsigned char *Pkg,int payload_Length)
{
	int pos=PAYLOAD_START;
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[256]={0};
	int data_len=0;

	switch (Pkg[pos++])
   {
	   case CMD_QUERY : // 查询
	   {
           printf("procHost_Query_Set_Trigger:query\n");
		   data[data_len++] = CMD_QUERY;	//查询/设置
		   data[data_len++] = 1;	//参数id
		   int parm1_pos=data_len++;
		   int parm1_length=0;
		   data[data_len++] = st_triggerSong.trigger_switch;	//触发开关
		   parm1_length++;
		   data[data_len++] = !st_triggerSong.trigger_mode;		//触发模式（服务器定义与板子相反，服务器0代表电平触发，板子0代表短路触发）
		   parm1_length++;
		   data[parm1_pos] = parm1_length;	//参数1长度
		   
		   data[data_len++] = 2;	//参数id
		   int parm2_pos=data_len++;
		   int parm2_length=0;
		   int triggerSongName_len = strlen(st_triggerSong.trigger_song_name);
		   data[data_len++] = triggerSongName_len;	//触发歌曲名称长度
		   parm2_length++;
		   memcpy(data+data_len,st_triggerSong.trigger_song_name,triggerSongName_len);
		   data_len+=triggerSongName_len;
		   parm2_length+=triggerSongName_len;
		   int triggerSongMd5_len = strlen(st_triggerSong.trigger_song_md5);
		   data[data_len++] = triggerSongMd5_len;	//触发歌曲MD5长度
		   parm2_length++;
		   memcpy(data+data_len,st_triggerSong.trigger_song_md5,triggerSongMd5_len);
		   data_len+=triggerSongMd5_len;
		   parm2_length+=triggerSongMd5_len;
		   data[data_len++] = st_triggerSong.trigger_playTimes == 0?1:st_triggerSong.trigger_playTimes;		//触发播放次数(默认1)
		   parm2_length++;
		   data[data_len++] = st_triggerSong.trigger_volume;		//触发播放次数(默认1)
		   parm2_length++;
		   data[parm2_pos] = parm2_length;		//参数2长度

		   int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_TRIGGER_CONFIG,CURRENT_DEVICE_MODEL,data_len,data);
		   //发送数据
		   host_udp_send_data(sendBuf, sendLen);
	   }
	   break;
	   case CMD_SET : //设置
	   {
			int parmIndex=0;

			int curTrigger_switch = st_triggerSong.trigger_switch;

			int count=0;
			while( pos < PAYLOAD_START+payload_Length && ++count<MAX_BUF_SIZE )
			{
				int parmId = Pkg[pos++];
				int parmLength=Pkg[pos++];

				int trigger_name_len = 0;
				int trigger_md5_len = 0;
				for(parmIndex=0;parmIndex<parmLength && pos < PAYLOAD_START+payload_Length;)
				{
					if(parmId == 1)
					{
						if(parmIndex == 0)
						{
							st_triggerSong.trigger_switch = Pkg[pos++];
							parmIndex++;
						}
						else if(parmIndex == 1)
						{
							st_triggerSong.trigger_mode = !Pkg[pos++];	//触发模式（服务器定义与板子相反，服务器0代表电平触发，板子0代表短路触发）
							parmIndex++;
						}
						else
						{
							pos++;			//如果该参数还有其他字段，暂时不处理
							parmIndex++;	//如果该参数还有其他字段，暂时不处理
						}
					}
					else if(parmId == 2)
					{
						if(parmIndex == 0)
						{
							trigger_name_len = Pkg[pos++];
							parmIndex++;
						}
						else if(trigger_name_len>0 && parmIndex == 1)
						{
							memset(st_triggerSong.trigger_song_name,0,sizeof(st_triggerSong.trigger_song_name));
							memcpy(st_triggerSong.trigger_song_name,Pkg+pos,trigger_name_len);
							pos+=trigger_name_len;
							parmIndex+=trigger_name_len;
						}
						else if(parmIndex == 1+trigger_name_len)
						{
							trigger_md5_len = Pkg[pos++];
							parmIndex++;
						}
						else if(trigger_md5_len>0 && parmIndex == 1+trigger_name_len+1)
						{
							memset(st_triggerSong.trigger_song_md5,0,sizeof(st_triggerSong.trigger_song_md5));
							memcpy(st_triggerSong.trigger_song_md5,Pkg+pos,trigger_md5_len);
							pos+=trigger_md5_len;
							parmIndex+=trigger_md5_len;
						}
						else if(parmIndex == 1+trigger_name_len+1+trigger_md5_len)
						{
							st_triggerSong.trigger_playTimes = Pkg[pos++];
							parmIndex++;
						}
						else if(parmIndex == 1+trigger_name_len+1+trigger_md5_len+1)
						{
							st_triggerSong.trigger_volume = Pkg[pos++];
							parmIndex++;
						}
						else
						{
							pos++;			//如果该参数还有其他字段，暂时不处理
							parmIndex++;	//如果该参数还有其他字段，暂时不处理
						}
					}
					else
					{
						pos += parmLength;	//不是预设的参数id,跳过
						break;
					}
				}
			}

		   save_sysconf(INI_SECTION_TRIGGER,NULL);


		   data[data_len++] = CMD_SET;	//查询/设置
		   data[data_len++] = 1;	//参数id
		   int parm1_pos=data_len++;
		   int parm1_length=0;
		   data[data_len++] = st_triggerSong.trigger_switch;	//触发开关
		   parm1_length++;
		   data[data_len++] = !st_triggerSong.trigger_mode;		//触发模式（服务器定义与板子相反，服务器0代表电平触发，板子0代表短路触发）
		   parm1_length++;
		   data[parm1_pos] = parm1_length;	//参数1长度

		   data[data_len++] = 2;	//参数id
		   int parm2_pos=data_len++;
		   int parm2_length=0;
		   int triggerSongName_len = strlen(st_triggerSong.trigger_song_name);
		   data[data_len++] = triggerSongName_len;	//触发歌曲名称长度
		   parm2_length++;
		   memcpy(data+data_len,st_triggerSong.trigger_song_name,triggerSongName_len);
		   data_len+=triggerSongName_len;
		   parm2_length+=triggerSongName_len;
		   int triggerSongMd5_len = strlen(st_triggerSong.trigger_song_md5);
		   data[data_len++] = triggerSongMd5_len;	//触发歌曲MD5长度
		   parm2_length++;
		   memcpy(data+data_len,st_triggerSong.trigger_song_md5,triggerSongMd5_len);
		   data_len+=triggerSongMd5_len;
		   parm2_length+=triggerSongMd5_len;
		   data[data_len++] = st_triggerSong.trigger_playTimes == 0?1:st_triggerSong.trigger_playTimes;		//触发播放次数(默认1)
		   parm2_length++;
		   data[data_len++] = st_triggerSong.trigger_volume;		//触发播放次数(默认1)
		   parm2_length++;
		   data[parm2_pos] = parm2_length;		//参数2长度

		   int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_TRIGGER_CONFIG,CURRENT_DEVICE_MODEL,data_len,data);
		   //发送数据
		   host_udp_send_data(sendBuf, sendLen);


		   if(curTrigger_switch!=st_triggerSong.trigger_switch && st_triggerSong.trigger_switch == 0 && st_triggerSong.trigger_local_play_flag)
		   {
				Set_zone_idle_status(NULL,  __func__, __LINE__,true);
		   }
	   }
	   break;
   }
}


/****************************************************
 * @fn      procHost_Request_Trigger_Play
 *
 * @brief   //请求服务器播放触发歌曲
 *
 * @param
 *
 * @return	void
 */
void procHost_Request_Trigger_Play()
{
	int pos=PAYLOAD_START;
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[256]={0};
	int data_len=0;

	printf("procHost_Request_Trigger_Play\n");
	int triggerSongName_len = strlen(st_triggerSong.trigger_song_name);
	data[data_len++] = triggerSongName_len;	//触发歌曲名称长度
	memcpy(data+data_len,st_triggerSong.trigger_song_name,triggerSongName_len);
	data_len+=triggerSongName_len;
	data[data_len++] = st_triggerSong.trigger_playTimes == 0?1:st_triggerSong.trigger_playTimes;		//触发播放次数(默认1)
	data[data_len++] = st_triggerSong.trigger_volume;		//触发播放音量(默认1)
	
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_REQUEST_HOST_TRIGGER_PLAY,CURRENT_DEVICE_MODEL,data_len,data);
	//发送数据
	host_udp_send_data(sendBuf, sendLen);
}



#endif


#if SUPPORT_HTTP_SERVER
//终端向服务器发送json命令
void SendJsonCommandToServer(unsigned char *jsonBuf)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[1420]={0};
	int jsonLen=strlen(jsonBuf);
	int data_len=2+jsonLen;
	data[0]=data_len>>8;				//stream_len High
	data[1]=data_len;					//stream_len low
	sprintf(data+2,jsonBuf);
	

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_HOST_JSON_COMMAND,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}
#endif


#if SUPPORT_SIP
/*********************************************************************
 * @fn      HOST_CONTROL_SIP_LOGIN
 *
 * @brief   主机向设备请求SIP登录
 *
 * @param   rxbuf - 包数据
 *
 * @return  none
 */
static void HOST_CONTROL_SIP_LOGIN(unsigned char *rxbuf,int payload_Length) {
	printf("HOST_CONTROL_SIP_LOGIN\n");
	char sipServerIP[SERVER_ADDRESS_LEN_MAX]={0};
	unsigned char sipServerIPLen=0;
	char sipUser[SIP_USER_NAME_LEN_MAX]={0};
	unsigned char sipUserLen=0;
	char sipPasswd[SIP_PASSWORD_LEN_MAX]={0};
	unsigned char sipPasswdLen=0;
	unsigned short sipServerPort= 0;
	#if 1
	unsigned char transProtocol = SIP_TRANSFER_PROTOCOL_DEF;
	#else	//测试用
	static char transProtocol = SIP_TRANSFER_PROTOCOL_UDP;
	if(transProtocol == SIP_TRANSFER_PROTOCOL_UDP)
	{
		transProtocol=SIP_TRANSFER_PROTOCOL_TCP;
	}
	else
	{
		transProtocol=SIP_TRANSFER_PROTOCOL_UDP;
	}
	#endif

	unsigned char *pData = rxbuf+PAYLOAD_START;
	int pos=0;

	unsigned char isEnable = pData[pos++];

	int callVolume = pData[pos++];
	int MIC_inputLevel = pData[pos++];

	sipServerIPLen = pData[pos++];
	strncpy(sipServerIP,pData+pos,sipServerIPLen);
	pos+=sipServerIPLen;

	sipServerPort = (pData[pos])*256 + (pData[pos+1]);
	pos+=2;

	sipUserLen = pData[pos++];
	strncpy(sipUser,pData+pos,sipUserLen);
	pos+=sipUserLen;

	sipPasswdLen = pData[pos++];
	strncpy(sipPasswd,pData+pos,sipPasswdLen);
	pos+=sipPasswdLen;

	if(pos<payload_Length)
	{
		//代表有传输协议
		transProtocol=pData[pos++];
	}


	

	printf("SIP Login Info:");
	printf("SIP Server IP:%s",sipServerIP);
	printf("SIP Server Port:%d",sipServerPort);
	printf("SIP User:%s",sipUser);
	printf("SIP Password:%s",sipPasswd);

	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[128]={0};
	int data_len = 1;

	//appData.voiceInfoSettings.callVolume
	//如果目前处于SIP通话状态，不处理
	if(get_system_source() != SOURCE_SIP_CALLING)
	{
		//data[0] = SIP_STATUS_REGISTER_LOADING;	//SIP状态,正在登录
		PJ_THREAD_REGISTER_MACRO(HOST_CONTROL_SIP_LOGIN);
		HostModifyAccountInfo(isEnable,callVolume,sipServerIP,sipServerPort,sipUser,sipPasswd,transProtocol);
	}
	else
	{
		//data[0] = SIP_STATUS_REGISTER_CALLING;	//正在通话
	}

#if 0
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CONTROL_SIP_LOGIN,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
#endif

	return;
}

/*********************************************************************
 * @fn      HOST_QUERY_SIP_INFO
 *
 * @brief   主机向设备查询SIP信息
 *
 * @param   rxbuf - 包数据
 *
 * @return  none
 */
static void HOST_QUERY_SIP_INFO() {
	if(!IS_EXTENSION_HAS_SIP)
	{
		return;
	}
	unsigned char buf[128]={0};
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[128]={0};
	int data_len = getSipInformationToArray(data);	
	if(data_len<0)
	{
		return;
	}

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_QUERY_SIP_INFO,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}

/*********************************************************************
 * @fn      HOST_CONTROL_SIP_Status
 *
 * @brief   主机向设备查询SIP状态
 *
 * @param   rxbuf - 包数据
 *
 * @return  none
 */
void HOST_CONTROL_SIP_Status(unsigned char *rxbuf) {
	if(!IS_EXTENSION_HAS_SIP)
	{
		return;
	}

	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[128]={0};
	int data_len = 1;
	data[0]=getSipCurStatus();	//SIP状态

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_QUERY_SIP_STATUS,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}


/*********************************************************************
 * @fn      SEND_SIP_STATUS_TO_SERVER
 *
 * @brief   设备主动发送SIP状态给服务器
 *
 * @param   rxbuf - 包数据
 *
 * @return  none
 */
void SEND_SIP_STATUS_TO_SERVER(unsigned char status) {
	if(!IS_EXTENSION_HAS_SIP)
	{
		return;
	}
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[128]={0};
	int data_len = 1;
	data[0] = status;	//SIP状态

	printf("SEND_SIP_STATUS_TO_SERVER:%d\n",status);

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_QUERY_SIP_STATUS,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}

#endif






/****************************************************
 * @fn      procTools_PreSet_Extension
 *
 * @brief   //处理 配置工具预设置扩展参数
 *
 * @param
 *
 * @return	void
 */
void procTools_PreSet_Extension(unsigned char *Pkg,int pkgLen)
{
	printf("procTools_PreSet_Extension...\n");
	int pos=PAYLOAD_START;
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[256]={0};
	int data_len=0;

	//判断是否为分区的MAC，否则退出
	int mac_match=1;
	for(int i=0;i<6;i++)
	{
		if( Pkg[PAYLOAD_START+i] != g_mac_addr[i] )
		{
			mac_match=0;
			break;
		}
	}

	if(!mac_match)
	{
		printf("procTools_Set_Extension,mac not match!\n");
		return;
	}
	pos+=6;

	memcpy(data,g_mac_addr,6);	//本机MAC
	data_len+=6;

	int readyExtensionNum=Pkg[pos++];	//预处理的扩展模块数量
	printf("readyExtensionNum=%d\n",readyExtensionNum);
	bool isSetOK=true;

	int readyExtensionSwitch[MAX_AUTH_DEVICE_EXTENSION]={0};
	for(int i=0;i<readyExtensionNum;i++)
	{
		int extensionNameLen = Pkg[pos++];

		char extensionName[32]={0};
		memcpy(extensionName,Pkg+pos,extensionNameLen);
		pos+=extensionNameLen;
		int extensionValid=Pkg[pos++];
		unsigned int key = (Pkg[pos]<<24)+(Pkg[pos+1]<<16)+(Pkg[pos+2]<<8)+Pkg[pos+3];
		pos+=4;
		//判断extensionName是否存在
		if(isPermissionNameCorrect(extensionName))
		{
			//如果存在，那么判断key是否正确
			if(!set_extension_permissions(extensionName,key,extensionValid,true))
			{
				isSetOK=false;
				break;
			}
			else
			{
				int index=get_extension_index(extensionName);
				if(index>=0)
				{
					readyExtensionSwitch[index] = extensionValid;
				}
			}
		}
	}

	if(isSetOK)
	{
		//1、判断有没有互斥的选项，如果有，报错
		if(readyExtensionSwitch[AUTH_DEVICE_FIRE])
		{
			if(readyExtensionSwitch[AUTH_DEVICE_POWER] || readyExtensionSwitch[AUTH_DEVICE_REMOTE])
			{
				isSetOK=false;
			}
		}
		if(readyExtensionSwitch[AUTH_DEVICE_POWER])
		{
			if(readyExtensionSwitch[AUTH_DEVICE_FIRE] || readyExtensionSwitch[AUTH_DEVICE_REMOTE])
			{
				isSetOK=false;
			}
		}
		if(readyExtensionSwitch[AUTH_DEVICE_REMOTE])
		{
			if(readyExtensionSwitch[AUTH_DEVICE_POWER] || readyExtensionSwitch[AUTH_DEVICE_FIRE])
			{
				isSetOK=false;
			}
		}

#if 0
		//2.非P26不允许设置AUTH_DEVICE_FIRE、AUTH_DEVICE_POWER、AUTH_DEVICE_REMOTE
		if(readyExtensionSwitch[AUTH_DEVICE_FIRE] || readyExtensionSwitch[AUTH_DEVICE_POWER] || readyExtensionSwitch[AUTH_DEVICE_REMOTE])
		{
			if(!IS_MULTIFUNCTION_DECODER)
			{
				isSetOK=false;
			}
		}
#endif
		
		//3.非P28不允许设置AUTH_DEVICE_INTERCOM
		if(readyExtensionSwitch[AUTH_DEVICE_INTERCOM])
		{
			if(!IS_MODEL_SIMPLE_AMP)
			{
				isSetOK=false;
			}
		}

		//4.非解码终端不允许设置AUTH_DEVICE_TTS和AUTH_DEVICE_SIP以及AUTH_DEVICE_INFORMATION_PUB
		if(readyExtensionSwitch[AUTH_DEVICE_TTS] || readyExtensionSwitch[AUTH_DEVICE_SIP] ||\
			readyExtensionSwitch[AUTH_DEVICE_INFORMATION_PUB])
		{
			if ( !(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL) )
			{
				isSetOK=false;
			}
		}
#ifndef USE_PC_SIMULATOR
		if(readyExtensionSwitch[AUTH_DEVICE_TTS])
		{
			int isTTSCheckJetExist=CheckTTSJetExist();
			if(!isTTSCheckJetExist)
			{
				isSetOK=false;
			}
		}
#endif
	}
	printf("isSetOK=%d\n",isSetOK);
	data[data_len++] = isSetOK;	//设置状态
	
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_TOOLS_PRE_DEVICE_EXTENSION,CURRENT_DEVICE_MODEL,data_len,data);
	//发送数据
	host_udp_send_data(sendBuf, sendLen);
	//20240928 增加组播查询
	multicast_send_data(sendBuf, sendLen);
}



/****************************************************
 * @fn      procTools_Set_Extension
 *
 * @brief   //处理 配置工具查询/设置扩展参数
 *
 * @param
 *
 * @return	void
 */
void procTools_Set_Extension(unsigned char *Pkg,int pkgLen)
{
	printf("procTools_Set_Extension...\n");
	int pos=PAYLOAD_START;
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[256]={0};
	int data_len=0;

	//判断是否为分区的MAC，否则退出
	int mac_match=1;
	for(int i=0;i<6;i++)
	{
		if( Pkg[PAYLOAD_START+i] != g_mac_addr[i] )
		{
			mac_match=0;
			break;
		}
	}

	if(!mac_match)
	{
		printf("procTools_Set_Extension,mac not match!\n");
		return;
	}
	pos+=6;

	memcpy(data,g_mac_addr,6);	//本机MAC
	data_len+=6;

	switch (Pkg[pos++])
   {
	   case CMD_QUERY : // 查询
	   {
           printf("procTools_Set_Extension:query\n");
		   data[data_len++] = CMD_QUERY;	//查询/设置
		   data[data_len++] = 0;			//设置状态，如果是查询/则忽略
		   data[data_len++] = MAX_AUTH_DEVICE_EXTENSION;	//授权模块数量
		   for(int i=0;i<MAX_AUTH_DEVICE_EXTENSION;i++)
		   {
				int extensionNameLen = strlen(g_extension_permissions[i]);
		   		data[data_len++] = extensionNameLen;		//名称长度
				memcpy(data+data_len,g_extension_permissions[i],extensionNameLen);	//授权名称
				data_len += extensionNameLen;
				data[data_len++] = g_extension_valid[i];	//授权状态
		   }
		   int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_TOOLS_DEVICE_EXTENSION,CURRENT_DEVICE_MODEL,data_len,data);
		   //发送数据
		   host_udp_send_data(sendBuf, sendLen);

			//20240928 增加组播查询
			multicast_send_data(sendBuf, sendLen);
	   }
	   break;
	   case CMD_SET : //设置
	   {
		   int readyExtensionNum=Pkg[pos++];	//预处理的扩展模块数量
		   printf("readyExtensionNum=%d\n",readyExtensionNum);
		   bool isSetOK=true;
		   for(int i=0;i<readyExtensionNum;i++)
		   {
				int extensionNameLen = Pkg[pos++];

				char extensionName[32]={0};
				memcpy(extensionName,Pkg+pos,extensionNameLen);
				pos+=extensionNameLen;
				int extensionValid=Pkg[pos++];
				unsigned int key = (Pkg[pos]<<24)+(Pkg[pos+1]<<16)+(Pkg[pos+2]<<8)+Pkg[pos+3];
				pos+=4;

				//printf("extensionName=%s,valid=%d\n",extensionName,extensionValid);
				//判断extensionName是否存在
				if(isPermissionNameCorrect(extensionName))
				{
					//如果存在，那么判断key是否正确
					if(!set_extension_permissions(extensionName,key,extensionValid,false))
					{
						isSetOK=false;
						break;
					}
				}
		   }

		   data[data_len++] = CMD_SET;	//查询/设置
		   data[data_len++] = isSetOK;	//设置状态，如果是查询/则忽略
		   data[data_len++] = MAX_AUTH_DEVICE_EXTENSION;	//授权模块数量
		   for(int i=0;i<MAX_AUTH_DEVICE_EXTENSION;i++)
		   {
				int extensionNameLen = strlen(g_extension_permissions[i]);
		   		data[data_len++] = extensionNameLen;		//名称长度
				memcpy(data+data_len,g_extension_permissions[i],extensionNameLen);	//授权名称
				data_len += extensionNameLen;
				data[data_len++] = g_extension_valid[i];	//授权状态
		   }
		   int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_TOOLS_DEVICE_EXTENSION,CURRENT_DEVICE_MODEL,data_len,data);
		   //发送数据
		   host_udp_send_data(sendBuf, sendLen);

		   //20240928 增加组播查询
		   multicast_send_data(sendBuf, sendLen);

		   if(isSetOK)
		   {
				System_Reboot();
		   }
	   }
	   break;
   }
}


#if SUPPORT_INFORMATION_PUBLISH
/*********************************************************************
 * @fn      Host_Set_Information_Publish_Config
 *
 * @brief   //主机查询/设置信息发布
 *
 * @param   rxbuf - 包数据
 *
 * @return  none
 */
void Host_Set_Information_Publish_Config(unsigned char *rxbuf)
{
	printf("Host_Set_Information_Publish_Config...\n");
	if(!IS_EXTENSION_HAS_INFORMATION_PUB)
	{
		printf("No Permission...\n");
		return;
	}
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int dataPos=PAYLOAD_START;

	int event = rxbuf[dataPos++];
	if(event == CMD_SET)
	{
		int nEnableDisplay = rxbuf[dataPos++];
		int textLen = rxbuf[dataPos++];
		if(textLen>MAX_INFORMATION_PUBLISH_TEXT_BYTES)
		{
			textLen = MAX_INFORMATION_PUBLISH_TEXT_BYTES;
		}
		char text[MAX_INFORMATION_PUBLISH_TEXT_BYTES+1]={0};
		memcpy(text,rxbuf+dataPos,textLen);
		dataPos+=textLen;

		int nEffects = rxbuf[dataPos++];
		int nMoveSpeed = rxbuf[dataPos++];
		int nStayTime = rxbuf[dataPos++];


		//设置
		informationPub_info.m_bEnableDisplay = nEnableDisplay;
		sprintf(informationPub_info.m_szText,"%s",text);
		informationPub_info.m_nEffects = nEffects;
		informationPub_info.m_nMoveSpeed = nMoveSpeed;
		informationPub_info.m_nStayTime = nStayTime;

		save_sysconf(INI_SECTION_INFORMATIONPUB,NULL);

		InformationPub_Control_ProgramAndRefresh();
	}

	data[data_len++]=event;
	data[data_len++]=informationPub_info.m_bEnableDisplay;
	int textLen = strlen(informationPub_info.m_szText);
	data[data_len++]=textLen;
	memcpy(data+data_len,informationPub_info.m_szText,textLen);
	data_len+=textLen;
	data[data_len++]=informationPub_info.m_nEffects;
	data[data_len++]=informationPub_info.m_nMoveSpeed;
	data[data_len++]=informationPub_info.m_nStayTime;

	sendLen=Network_Send_Compose_CMD(sendBuf,CMD_INFORMATION_PUBLISH_CONFIG,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}

#endif


#if IS_DEVICE_AMP_CONTROLER
//主机查询功放控制器状态
void Host_Query_Set_Amp_Controler_Status(unsigned char *rxbuf)
{
	printf("Host_Query_Amp_Controler_Status...\n");

	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int dataPos=PAYLOAD_START;

	int event = CMD_QUERY;
	if(rxbuf)
	{
		event = rxbuf[dataPos++];
	}
	if(event == CMD_SET)
	{
		//todo
	}

	data[data_len++]=event;
	data[data_len++]=1;	//功能开关
	data[data_len++]=MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM;	//主功放数量
	data[data_len++]=MAX_AMP_CONTROLER_BACKUP_CHANNEL_NUM;	//备用功放数量

	for(int i=0;i<MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM;i++)
	{
		data[data_len++]=g_ampControler_info.masterChannelStatusArray[i];
	}
	data[data_len++]=g_ampControler_info.backupChannelStatus;
	data[data_len++]=g_ampControler_info.backupChannelId;	//备份通道ID:0~4

	sendLen=Network_Send_Compose_CMD(sendBuf,CMD_AMP_CONTROLER_STATUS,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}
#endif



#if IS_DEVICE_NOISE_DETECTOR
//主机查询/设置噪声自适应器参数
void Host_Set_NoiseDetector_Config(unsigned char *rxbuf)
{
	printf("Host_Set_NoiseDetector_Config...\n");

	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int dataPos=PAYLOAD_START;

	int event = CMD_QUERY;
	if(rxbuf)
	{
		event = rxbuf[dataPos++];
	}
	if(event == CMD_SET)
	{
		//先保存到临时变量，然后判断是否相等，不相等，需要保存到配置文件中
		bool isUpdate=false;
		bool isEnable = rxbuf[dataPos++];
		if(g_noiseDetector_info.isEnable != isEnable)
		{
			g_noiseDetector_info.isEnable = isEnable;
			isUpdate=true;
		}
		for(int i=0;i<NOISE_NUM_SEGMENTS;i++)
		{
			unsigned char segVol=rxbuf[dataPos++];
			if(g_noiseDetector_info.segmentVol[i] != segVol)
			{
				g_noiseDetector_info.segmentVol[i] = segVol;
				isUpdate=true;
			}
		}
		
		if(isUpdate)
		{
			save_sysconf(INI_SECTION_NOISE_DETECTOR,NULL);
		}
	}

	data[data_len++]=event;
	data[data_len++]=g_noiseDetector_info.isEnable;	//功能开关
	//8个通道的噪声值（每个通道两个字节）
	for(int i=0;i<NOISE_NUM_CHANNELS;i++)
	{
		data[data_len++]=g_noiseDetector_info.channelVal[i]>>8;
		data[data_len++]=g_noiseDetector_info.channelVal[i];
	}
	//8段音量值
	for(int i=0;i<NOISE_NUM_SEGMENTS;i++)
	{
		data[data_len++]=g_noiseDetector_info.segmentVol[i];
	}

	sendLen=Network_Send_Compose_CMD(sendBuf,CMD_NOISE_DETECTOR_CONFIG,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}
#endif


#if IS_DEVICE_GPS_SYNCHRONIZER
/*********************************************************************
 * @fn      Gps_send_time_sync
 *
 * @brief   //GPS校时器主动同步时间给服务器
 *
 * @param   none
 *
 * @return  none
 */
void Gps_send_time_sync()
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;

	char	szDate[11]  = {0};
    char	szTime[9]	= {0};
    sprintf(szDate, "%04d-%02d-%02d", g_gps_info.utc_time.year, g_gps_info.utc_time.month, g_gps_info.utc_time.day);
    sprintf(szTime, "%02d:%02d:%02d", g_gps_info.utc_time.hour, g_gps_info.utc_time.minute, g_gps_info.utc_time.second);
	
	data[data_len++]=strlen(szDate);
	memcpy(data+data_len,szDate,strlen(szDate));
	data_len+=strlen(szDate);
	data[data_len++]=strlen(szTime);
	memcpy(data+data_len,szTime,strlen(szTime));
	data_len+=strlen(szTime);

	sendLen=Network_Send_Compose_CMD(sendBuf,CMD_GPS_SYNC,CURRENT_DEVICE_MODEL,data_len,data);
	host_udp_send_data(sendBuf, sendLen);
}
#endif

/*********************************************************************
 * @fn      network_pkg_process
 *
 * @brief   网络数据包处理
 *
 * @param   Pkg - 指向接收缓存
 *			Pkg_Length - 接收数据长度
 *
 * @return  none
 */
void network_pkg_process(unsigned char NetPkgType,unsigned char *Pkg, int Pkg_Length)
{
	int cmd_word = 0;
	int i = 0,dataLen=0;
	cmd_word = Pkg[0]*256 + Pkg[1];
	dataLen = Pkg[6]*256 + Pkg[7];
	if(g_paging_status != PAGING_START)
	{
		//printf("RX HOST CMD_WORD = 0x%04x\n", cmd_word);
		//printf("process_host_pkg：IP:%d.%d.%d.%d\n", (Server_Addr)&0xff, (Server_Addr>>8)&0xff,(Server_Addr>>16)&0xff,(Server_Addr>>24)&0xff);
	}

	int device_model = Pkg[4];
	if(g_network_mode == NETWORK_MODE_LAN)
	{
		if( device_model != DEVICE_MODEL_HOST && device_model != DEVICE_MODEL_PAGING && device_model != DEVICE_MODEL_MOBILE
				&& device_model != DEVICE_MODEL_NETWORK_TOOLS)
		{
			printf("process_host_pkg ERROR1:device_model=0x%x\n",device_model);
			return;
		}
	}
	else if(g_network_mode == NETWORK_MODE_WAN)
	{
		if(device_model != DEVICE_MODEL_NETWORK_TOOLS && device_model != DEVICE_MODEL_HOST)
		{
			printf("process_host_pkg ERROR2:device_model=0x%x\n",device_model);
			return;
		}
	}

	if(device_model == DEVICE_MODEL_HOST)
	{
		g_host_device_TimeOut=0;
	}

	switch(cmd_word)
	{
		#if ENABLE_TCP_CLIENT
		case CMD_TCP_CLIENT_CONNECTED:
			kcp_send_heartbeat();	//TODO
			if(g_network_mode == NETWORK_MODE_WAN)
			{
				if(power_on_flag==0)
				{
					power_on_flag = 1;
					power_on_notice_host();
				}
			}
			//如果是电源时序器，发送详细信息
			#if (IS_DEVICE_POWER_SEQUENCE)
			Host_Query_Set_Sequence_Power_Info(NULL);
			#endif
		break;
		#endif
		case CMD_SET_DEVICE_ALIAS://修改设备别名
			Change_Device_Alias(Pkg);
		break;
		case CMD_TIME_SYN:	//时间同步
			pkg_set_local_time(Pkg);
		break;
		case CMD_SET_VOL://设置音量值
			pkg_set_volume(Pkg);
		break;
		case CMD_HOST_SET_SUB_VOLUME://设置子音量
			pkg_set_sub_volume(Pkg,dataLen);
		break;
		case CMD_HOST_SET_VOLUME_ADD_MIN://主机控制音量加/减
			pkg_control_volume_add_min(Pkg);
		break;
		case CMD_GET_ZONE_DETAIL_INFO://获取设备信息
			pkg_query_current_status(Pkg);
		break;
		case CMD_FIRMWARE_VERSION://查询当前版本信息
			pkg_query_firmware_version(Pkg);
		break;
		case CMD_UPDATE_INFO://获取升级信息
			pkg_get_update_info(Pkg);
		break;
		case CMD_PLAY_NETWORK:// 主机点播歌曲
		break;

		case CMD_SET_ZONE_PLAYSTATUS://主机设置终端的播放状态
			play_status_control(Pkg);
		break;
		case CMD_SET_ZONE_MUTE_STATUS://单播设置/查询终端静音状态
		break;
		case CMD_SET_ZONE_CONTROL_MODE://终端控制模式设置
		break;

		case CMD_QUERY_FILE://主机向终端查询存储在本地的文件信息
			response_send_host_xml_file_info(Pkg);
		break;
		
		case CMD_GET_HOST_SYNC_INFO://主机向终端请求更新文件
			//Get_UpdateXML_Info_From_Host(Pkg);
		break;
		case CMD_SEND_HOST_MUSICLIST_SYNC_PROGRESS://向主机发送同步进度
			break;
		case CMD_GET_HOST_STOP_SYNC_MUSICLIST://主机停止同步歌曲
		break;
		case CMD_SET_RING://设置钟声
		break;
		case CMD_PLAY_RING://主机向终端请求播放钟声
		break;
		case CMD_PLAY_LOCAL_MUSIC://主机向终端请求播放本地歌曲
			//host_play_local_music(Pkg);
		break;
		case CMD_SET_PLAYMODE://主机设置终端的播放模式
			//play_mode_control(Pkg);
		break;
		case CMD_SET_ZONE_IDLE_STATUS://主机设置终端空闲模式
			Set_zone_idle_status(Pkg,  __func__, __LINE__,true);
		break;
		case CMD_SET_ZONE_MAC://主机请求重新分配MAC
			//Host_Set_Zone_MAC(Pkg);
		break;
		case CMD_CONTROL_REBOOT://主机向终端发送重启指令
			host_control_reboot(Pkg, 0);
		break;
		case CMD_CONTROL_FORMAT://主机向设备请求清除数据
			host_control_format(Pkg);
		break;
		case CMD_QUERY_FLASH_INFO://主机向设备请求查询FLASH信息
			//HOST_QUERY_FLASH_INFO(Pkg);
		break;
		case CMD_QUERY_MAC_INFO://主机向设备请求查询/设置MAC地址
			//HOST_QUERY_SET_MAC_INFO(Pkg);
		break;
		case CMD_QUERY_TIME_INFO://主机向设备请求查询设备日期时间
			//HOST_QUERY_TIME_INFO(Pkg);
		break;
		case CMD_QUERY_SET_WORK_MODE://工作模式
			HOST_QUERY_SET_WORK_MODE(Pkg);
		break;
		case CMD_CONCENTRATED_PLAY_SOURCE://集中模式下播放
			if(!(g_network_mode == NETWORK_MODE_WAN && NetPkgType == NET_TYPE_UNICAST_SERVER))
			{
				HOST_CONCENTRATED_PLAY_SOURCE(Pkg);
			}
		break;

		case CMD_HOST_AUDIO_STREAM://播放节目源数据流传输（TCP模式适用，UDP模式下直接发送数据到指定的组播地址中，无需命令字）
		case CMD_SEND_PCM_DATA_TCP:
		case CMD_WEB_PAGING_STREAM:
		case CMD_AUDIO_COLLECTOR_STREAM_TCP:
		case CMD_AUDIO_MIXER_STREAM:
		case CMD_PHONE_GATEWAY_STREAM:
			kcp_concentrated_ser_data(Pkg,Pkg_Length);
			break;

		case CMD_SET_ALARM_MUSIC_FILE://主机向终端设置警报声
			//host_set_Alarm_ring(Pkg);
		break;
		case CMD_SET_ALARM_STATUS://主机向终端设置开启/关闭警报
			//host_play_Alarm_ring(Pkg);
		break;
		case CMD_HOST_SET_AUDIO_COLLECOR_SOURCE://主机向音频采集器/终端设置音频采集音源
			HOST_SET_AUDIO_COLLECOR_SOURCE(Pkg);
		break;
		case CMD_HOST_QUERY_SET_NET_WORK_MODE://主机向终端设置网络模式
			ProcessHostSetNetWork(Pkg,Pkg_Length,false);
		break;
		case CMD_HOST_QUERY_SET_IP_INFO://主机向终端设置IP属性
			HOST_QUERY_SET_IP_INFO(Pkg);
		break;
		case CMD_HOST_QUERY_RECORD_LIST://主机获取设备记录文件列表
			//SendRecordListToHost(Pkg);
		break;
		case CMD_SEND_RECORD_FILE_CONTENT: //终端向主机发送记录文件内容
			//SendRecordFileContent(Pkg);
		break;
		case CMD_HOST_NET_DECODE_POWER_OUT_WAY://主机向网络解码播放器查询/设置电源输出模式
		break;
		case CMD_HOST_QUERY_AUDIO_RETURN_STATUS://主机向网络解码播放器查询回路检测状态
		break;
		case CMD_HOST_SET_EQ://主机向解码终端查询/设置高低音
			host_set_eq(Pkg);
		break;
		case CMD_HOST_SET_EMMC_STATUS://主机向终端查询/设置EMMC工作状态
		break;
		case CMD_HOST_QUERY_SET_SOUND_MIXING://主机向终端查询/设置混音模式
			//ProcHostSetReqSoundMixing(Pkg);
		break;
		case CMD_HOST_QUERY_SET_PARTITION_DEVICE_STATUS://查询/设置网络解码分区器输出状态
		break;
        
        case CMD_HOST_SET_BLUETOOTH:                    //主机或配置工具向终端查询/设置蓝牙信息
            Host_Set_BT_Info(Pkg);
        break;

		case CMD_SEND_PAGING_NOTIFY_TCP:				//通知终端开始寻呼(TCP模式) 服务器转发寻呼台
			pkg_paging_notification(Pkg+PAYLOAD_START,dataLen,DEVICE_MODEL_PAGING,0);
		break;

		case CMD_PAGING_AGAIN:
			if(paging_repeat_again_enable)
			{
				#if !CANCEL_PAGER_SOURCE_PRIORITY
				pkg_paging_notification(Pkg+PAYLOAD_START,dataLen,DEVICE_MODEL_PAGING,0);
				#endif
			}
		break;

		case CMD_HOST_NOTIFY_DECODER_READY_TIMING:		//主机通知解码终端即将进入定时音源
			host_notify_decoder_ready_timing(Pkg);
		break;
	
		/***************消防采集器**********************/
#if (IS_DEVICE_FIRE_COLLECTOR)
		case CMD_HOST_QUERY_FIRE_TRIG_STATUS://主机向消防采集器查询通道触发状态
		  	Host_Query_Fire_Collector_Trig_Status();
		break;
		case CMD_HOST_SET_FIRE_TRIG_MODE://主机向消防采集器设置触发模式
			Host_Set_Fire_Collector_Trig_Mode(Pkg);
		break;
		case CMD_SEND_FIRE_TRIG_STATUS:	//通道触发状态发生改变时主动通知主机
		  	fire_collector_info.IsHost_received=1;
		break;
#endif

#if ENABLE_LISTEN_TEST
		case CMD_LISTEN_EVENT:				//监听设备发起监听
			Host_set_listen_event(Pkg);
		break;
		case CMD_LISTEN_STATUS:				//监听设备发送监听状态
			recv_listen_status(Pkg);
		break;
#endif

		case CMD_WEB_PAGING_NOTIFY:			//服务器发起广播寻呼通知
		memcpy(pre_web_pager_cmd_pkg,Pkg,sizeof(pre_web_pager_cmd_pkg));	//上一个APP寻呼的发起包,用于恢复寻呼
		pkg_paging_notification(Pkg+PAYLOAD_START,dataLen,DEVICE_MODEL_PAGING,1);
		break;

#if(IS_DEVICE_AUDIO_COLLECTOR)
		/**************音频采集器专用 Start************/
		case CMD_HOST_SEND_AUDIO_COLLECTOR_SELECTED:	//主机向采集器主动发送终端音源选择状态
        	ProcHost_Send_AudioCollector_Selected(Pkg);
        break;
		/**************音频采集器专用 end************/
#endif

		/**************电源时序器专用 Start************/
#if(IS_DEVICE_POWER_SEQUENCE)
		case CMD_SEQUENCE_POWER_INFO:		//主机通知解码终端即将进入定时音源
			Host_Query_Set_Sequence_Power_Info(Pkg);
		break;
		case CMD_SEQUENCE_POWER_TIMING:		//主机向电源时序器发送定时点信息
			Host_Send_Sequence_Timing_Info(Pkg);
		break;
		/**************电源时序器专用 end************/
#endif

		/**************混音器 Start************/
#if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_AUDIO_MIXER)
		case CMD_HOST_SET_AUDIO_MIXER_SOURCE:		//主机向混音器/终端设置混音音源
			procHost_set_audioMixer_source(Pkg);
		break;
#endif

#if IS_DEVICE_AUDIO_MIXER
		case CMD_AUDIO_MIXER_CONFIG:			   //查询/设置音频协处理器参数
			Host_Set_Audio_Mixer_Config(Pkg);
		break;
#endif
		/**************混音器 end************/

#if IS_DEVICE_AUDIO_COLLECTOR
		case CMD_AUDIO_COLLECTOR_CONFIG:			   //查询/设置音频采集器参数
			Host_Set_Audio_Collector_Config(Pkg);
		break;
#endif


#if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_PHONE_GATEWAY)
		case CMD_SET_PHONE_GATEWAY_SOURCE:			  //主机向电话网关/终端设置混音音源
			procHost_set_phoneGateway_source(Pkg);
		break;
#endif

#if IS_DEVICE_PHONE_GATEWAY
		case CMD_PHONE_GATEWAY_CONFIG:			   	   //查询/设置电话网关参数
			Host_Set_Phone_Gateway_Config(Pkg);
		break;
#endif

#if SUPPORT_INFORMATION_PUBLISH
		case CMD_INFORMATION_PUBLISH_CONFIG:			//查询/设置信息发布参数
			Host_Set_Information_Publish_Config(Pkg);
		break;
#endif

		/**************远程遥控器专用 Start************/
#if(IS_DEVICE_REMOTE_CONTROLER)
		case CMD_HOST_SET_REMOTE_CONTROLER_KEY:		//远程遥控器按键,不需要接收应答
		break;
		/**************远程遥控器专用 end************/
#endif

		/************对讲终端专用 Start**************/
#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
		case CMD_CALLING_INVITATION:
			Recv_calling_invation(Pkg);
		break;
		case CMD_CALLED_RESPONSE:
			Recv_called_response(Pkg);
		break;
		case CMD_CALLED_STATUS:
			Recv_callStatus_feedback(Pkg);
		break;
		case CMD_CALLING_AUDIOSTREAM:
			Recv_Call_Audio_Stream(Pkg);
		break;

		case CMD_HOST_SET_INTERCOM_BASIC_CONFIG:
			procHost_Query_Set_Intercom_Basic(Pkg,dataLen);
		break;
		/************对讲终端专用 End**************/
#endif

		case CMD_HOST_SET_TRIGGER_CONFIG:
			procHost_Query_Set_Trigger(Pkg,dataLen);;
		break;
#if SUPPORT_SIP
		case CMD_QUERY_SIP_STATUS:
		HOST_CONTROL_SIP_Status(Pkg);
		break;
		case CMD_CONTROL_SIP_LOGIN:
		HOST_CONTROL_SIP_LOGIN(Pkg,dataLen);
		break;
		case CMD_QUERY_SIP_INFO:
		HOST_QUERY_SIP_INFO(Pkg);
		break;
#endif

		/**************功放控制器专用 Start************/
#if IS_DEVICE_AMP_CONTROLER		
		case CMD_AMP_CONTROLER_STATUS:
			Host_Query_Set_Amp_Controler_Status(Pkg);
		break;
#endif
		/************功放控制器专用 End**************/

		/**************噪声自适应器专用 Start************/
#if IS_DEVICE_NOISE_DETECTOR
		case CMD_NOISE_DETECTOR_CONFIG:
			Host_Set_NoiseDetector_Config(Pkg);
		break;
#endif
		/************噪声自适应器专用 End**************/

		case CMD_SEND_TOOLS_DEVICE_EXTENSION:
		procTools_Set_Extension(Pkg,Pkg_Length);
		break;
	}
	
}





