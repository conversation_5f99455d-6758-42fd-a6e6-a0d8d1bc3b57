/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> 
 * @Date: 2021-09-06 16:22:30 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-02-24 17:12:32
 */

#include "sysconf.h"
#include "network_protocol.h"
#include "network_process.h"
#include "multicast.h"
#include "recv_stream.h"
#include <sys/time.h>
#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
#include "../intercom/call_ring.h"
#endif

stPager_property pager_property;
/***********Web Paging Start*******/
unsigned int pre_web_pager_strem_timeout;			//上一个APP寻呼流的超时时间
unsigned char pre_web_pager_cmd_pkg[MAX_BUF_SIZE];	//上一个APP寻呼的发起包1
/***********Web Paging End*******/

static unsigned char Centralized_Mode_Timeout_task_flag = 0;//集中模式歌曲超时检测任务创建标志， 1：任务已创建 0：任务没有创建
static pthread_mutex_t mutex_Centralized_Mode_Timeout_task=PTHREAD_MUTEX_INITIALIZER;	//集中模式歌曲超时检测任务锁
static pthread_cond_t cond_Centralized_Mode_Timeout_task = PTHREAD_COND_INITIALIZER;	//集中模式歌曲超时检测任务条件变量
static unsigned char nflag_Centralized_Mode_Timeout_task=0;		//集中模式歌曲超时检测任务条件值

static unsigned char AudioCollector_Timeout_task_flag = 0;//集中模式歌曲超时检测任务创建标志， 1：任务已创建 0：任务没有创建
static pthread_mutex_t mutex_AudioCollector_Timeout_task=PTHREAD_MUTEX_INITIALIZER;	//集中模式歌曲超时检测任务锁
static pthread_cond_t cond_AudioCollector_Timeout_task = PTHREAD_COND_INITIALIZER;	//集中模式歌曲超时检测任务条件变量
static unsigned char nflag_AudioCollector_Timeout_task=0;		//集中模式歌曲超时检测任务条件值


static unsigned char Paging_Timeout_task_flag = 0;//集中模式歌曲超时检测任务创建标志， 1：任务已创建 0：任务没有创建
static pthread_mutex_t mutex_Paging_Timeout_task=PTHREAD_MUTEX_INITIALIZER;	//集中模式歌曲超时检测任务锁
static pthread_cond_t cond_Paging_Timeout_task = PTHREAD_COND_INITIALIZER;	//集中模式歌曲超时检测任务条件变量
static unsigned char nflag_Paging_Timeout_task=0;		//集中模式歌曲超时检测任务条件值

static unsigned char AudioMixer_Timeout_task_flag = 0;
static pthread_mutex_t mutex_audioMixer_Timeout_task=PTHREAD_MUTEX_INITIALIZER;
static pthread_cond_t cond_audioMixer_Timeout_task = PTHREAD_COND_INITIALIZER;
static unsigned char nflag_audioMixer_Timeout_task=0;

static unsigned char PhoneGatewey_Timeout_task_flag = 0;
static pthread_mutex_t mutex_phoneGateway_Timeout_task=PTHREAD_MUTEX_INITIALIZER;
static pthread_cond_t cond_phoneGateway_Timeout_task = PTHREAD_COND_INITIALIZER;
static unsigned char nflag_phoneGateway_Timeout_task=0;

static unsigned char AudioCall_Timeout_task_flag = 0;
static pthread_mutex_t mutex_audioCall_Timeout_task=PTHREAD_MUTEX_INITIALIZER;
static pthread_cond_t cond_audioCall_Timeout_task = PTHREAD_COND_INITIALIZER;
static unsigned char nflag_audioCall_Timeout_task=0;

//20220606
static unsigned char mcast_song_recv_task_flag=0;          //组播歌曲/采集接收数据线程开启标志 1:已经启动 0：未启动
static unsigned char mcast_song_recv_task_need_exit=0;	  //组播歌曲/采集接收数据线程需要退出标志

//20220709
static unsigned char mcast_audioMixer_recv_task_flag=0;          //组播音频混音接收数据线程开启标志 1:已经启动 0：未启动
static unsigned char mcast_audioMixer_recv_task_need_exit=0;	 //组播音频混音接收数据线程需要退出标志

//20240307
static unsigned char mcast_phoneGateway_recv_task_flag=0;        //组播电话网关接收数据线程开启标志 1:已经启动 0：未启动
static unsigned char mcast_phoneGateway_recv_task_need_exit=0;	 //组播电话网关接收数据线程需要退出标志

//20221027
static unsigned char AudioCall_ring_task_flag = 0;		//语音对讲响铃任务线程


pthread_mutex_t mutex_cleanPagerStream=PTHREAD_MUTEX_INITIALIZER;	//清理寻呼音频流互斥锁
pthread_mutex_t mutex_cleanMixedStream=PTHREAD_MUTEX_INITIALIZER;	//清理混音音频流互斥锁
pthread_mutex_t mutex_cleanPhoneGatewayStream=PTHREAD_MUTEX_INITIALIZER;	//清理电话网关音频流互斥锁


bool isMulticastPagerRecvThreadRun=false;	//组播寻呼接收线程运行标志
/*********************************************************************
 * @fn      Clean_Concentrated_Info
 *
 * @brief  	清除集中模式歌曲数据
 *
 * @param   NULL
 *
 * @return  none
*********************************************************************/
void Clean_Concentrated_Info(void)
{
	int i = 0;
	for(i = 0; i < CONCENTRATED_MODE_DATA_BUF_MAX; i++)
	{
		memset(concentrated_pcm_buf[i], 0x00, sizeof(concentrated_pcm_buf[i]));
		concentrated_pcm_buf_valid[i] = 0;
	}
	concentrated_read_pos = 0;
	concentrated_read_pos_total = 0;
	concentrated_write_pos = 0;
	concentrated_write_pos_total = 0;
	concentrated_write_need_wait=0;

	printf("-------------------Clean_Concentrated_Info-------------------\n");
}



/*********************************************************************
 * @fn      Clean_Pager_Pcm_Buf
 *
 * @brief   清除寻呼数据
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Clean_Pager_Pcm_Buf(void)
{
	pthread_mutex_lock(&mutex_cleanPagerStream);
	int i = 0;
	for(i = 0; i < PAGER_PCM_BUF_MAX; i++)
	{
		memset(pager_pcm_buf[i], 0x00, sizeof(pager_pcm_buf[i]));
		pager_pcm_buf_valid[i] = 0;
	}
	pager_read_pos = 0;
	pager_write_pos = 0;
	pager_read_pos_total = 0;
	pager_write_pos_total = 0;
	pager_pcm_write=0;
	pager_pcm_capacity=0;
	pthread_mutex_unlock(&mutex_cleanPagerStream);
}


/*********************************************************************
 * @fn      Clean_Mixed_Info
 *
 * @brief  	清除混音音频数据
 *
 * @param   NULL
 *
 * @return  none
*********************************************************************/
void Clean_Mixed_Info(void)
{
	pthread_mutex_lock(&mutex_cleanMixedStream);
	int i = 0;
	for(i = 0; i < AUDIO_MIXER_STREAM_BUF_MAX; i++)
	{
		memset(audioMixer_stream_buf[i], 0x00, sizeof(audioMixer_stream_buf[i]));
		audioMixer_stream_valid[i] = 0;
	}
	audioMixer_stream_read_pos = 0;
	audioMixer_stream_read_pos_total = 0;
	audioMixer_stream_write_pos = 0;
	audioMixer_stream_write_pos_total=0;
	pthread_mutex_unlock(&mutex_cleanMixedStream);

	//("-------------------Clean_Mixed_Info-------------------\n");
}


/*********************************************************************
 * @fn      Clean_PhoneGateway_Info
 *
 * @brief  	清除电话网关数据
 *
 * @param   NULL
 *
 * @return  none
*********************************************************************/
void Clean_PhoneGateway_Info(void)
{
	pthread_mutex_lock(&mutex_cleanPhoneGatewayStream);
	int i = 0;
	for(i = 0; i < PHONE_GATEWAY_STREAM_BUF_MAX; i++)
	{
		memset(phoneGateway_stream_buf[i], 0x00, sizeof(phoneGateway_stream_buf[i]));
		phoneGateway_stream_valid[i] = 0;
	}
	phoneGateway_stream_read_pos = 0;
	phoneGateway_stream_read_pos_total = 0;
	phoneGateway_stream_write_pos = 0;
	phoneGateway_stream_write_pos_total=0;
	pthread_mutex_unlock(&mutex_cleanPhoneGatewayStream);

	//("-------------------Clean_Mixed_Info-------------------\n");
}


/*********************************************************************
 * @fn      mcast_recv_concentrated_data
 *
 * @brief   集中模式下数据接收线程
 *
 * @param   void
 *
 * @return	void
 */
void *mcast_recv_concentrated_data(void)
{
	printf("enter mcast_recv_concentrated_data\n");
	int rxlen = 0;
	int ret = -1;
	struct sockaddr_in server_addr, client_addr;
	struct ip_mreq mreq;
	fd_set readfd;
	struct timeval timeout;
	int cli_len = sizeof(client_addr);
	int i;
	int payload_length = 0;
	int pkg_length = 0;

	int rxIndex = 0;
	float before_time = 0;
	float current_time = 0;
	struct timeval time;
	unsigned int pcm_capacity = 0;
	unsigned char pcm_buf_start_read_flag = 0;

	int recv_type=0;

	int concentrated_sockfd;

	memset(&server_addr, 0x00, sizeof(server_addr));
	server_addr.sin_family = AF_INET;
	server_addr.sin_addr.s_addr = htonl(INADDR_ANY);
	if(g_concentrated_start)
	{	
		recv_type=1;
		server_addr.sin_port = htons(g_concentrated_multicast_port);
		mreq.imr_multiaddr.s_addr = inet_addr(g_concentrated_multicast_address); // 注册组播地址
	}
	else if(g_collector_run_flag)
	{
		recv_type=2;
		server_addr.sin_port = htons(g_ac_mcast_port);
		mreq.imr_multiaddr.s_addr = inet_addr(g_ac_multicast_address); // 注册组播地址
	}	
	else
	{
		printf("mcast_recv_concentrated_data:enter error!\n");
		pthread_exit(NULL);
	}
	mreq.imr_interface.s_addr = htonl(INADDR_ANY);                 // 允许任何地址访问

	// 创建套接字
	if ( (concentrated_sockfd = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
	{
		perror("Create mcast_recv_paging_data socket error");
		pthread_exit(NULL);
	}

	/*设置地址重用,以解决第二次运行时端口占用的问题(SO_REUSEADDR允许完全相同的地址和端口的重复绑定，但这只用于UDP)*/
	int opt = 1;
	if((setsockopt(concentrated_sockfd,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt)))<0)
	{
		perror("setsockopt:mcast_recv_paging_data concentrated_sockfd SO_REUSEADDR failed");
		close(concentrated_sockfd);
		pthread_exit(NULL);
	}

	// 绑定地址到套接字
	if ( bind(concentrated_sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) <0)
	{
		perror("bind:concentrated_sockfd");
		close(concentrated_sockfd);
		pthread_exit(NULL);
	}

	//接收组播的socket需要先绑定网卡
	if (setsockopt(concentrated_sockfd, SOL_SOCKET,SO_BINDTODEVICE, (char *)&nif_eth0, sizeof(nif_eth0)) < 0)
	{
		perror("concentrated_sockfd bind interface fail");
	}

	// 加入组中，才能接收组播源发出的数据
	if (setsockopt(concentrated_sockfd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq, sizeof(mreq)) < 0)
	{
		perror("setsockopt:concentrated_sockfd");
		close(concentrated_sockfd);
		pthread_exit(NULL);
	}


	int exit_cnt = 0;
	while (1)
	{
		timeout.tv_sec = 0;      // 获取数据超时时间设置
		timeout.tv_usec = 50000;
		FD_ZERO(&readfd);        // 清空读文件描述集合
		FD_SET(concentrated_sockfd, &readfd); // 注册套接字文件描述符

		if(mcast_song_recv_task_need_exit)
		{
			break;
		}
		if(recv_type == 1)			//歌曲播放
		{
			if(!g_concentrated_start || server_addr.sin_port!=htons(g_concentrated_multicast_port))
			{
				exit_cnt++;
				if(exit_cnt>=1)
				{
					break;
				}
			}
		}
		else if(recv_type == 2)		//采集音源
		{
			if(!g_collector_run_flag || server_addr.sin_port!=htons(g_ac_mcast_port))
			{
				exit_cnt++;
				if(exit_cnt>=1)
				{
					break;
				}
			}
		}

		ret = select(concentrated_sockfd+1, &readfd, NULL, NULL, &timeout);
		switch (ret)
		{
			case -1 :
				//memset(rxbuf_macast_pcm, 0x00, sizeof(rxbuf_macast_pcm)); // 清空接收缓存
				perror("mcast_recv_paging_data call select error");
				break;

			case 0 :	//超时
				//perror("mcast_recv_paging_data call select TimeOut");
				break;

			default : // 接收数据
				if (FD_ISSET(concentrated_sockfd, &readfd))
				{
					if(	concentrated_write_pos_total-concentrated_read_pos_total >= CONCENTRATED_MODE_DATA_BUF_MAX)	//超过限制，舍弃掉部分数据
					{
						concentrated_write_pos = 0;
						concentrated_read_pos = 0;
						concentrated_write_pos_total = 0;
						concentrated_read_pos_total = 0;
						//清掉所有缓冲区
						for(i = 0; i < CONCENTRATED_MODE_DATA_BUF_MAX; i++)
						{
							memset(concentrated_pcm_buf[i], 0x00, sizeof(concentrated_pcm_buf[i]));
							concentrated_pcm_buf_valid[i] = 0;
						}
					}

					if(recv_type == 1 && g_IsCentralized_mode_multicast_new_cmd)
					{
						unsigned char buf[1500]={0};
						int pkg_length=recvfrom(concentrated_sockfd, buf, MAX_BUF_SIZE, 0, (struct sockaddr*)&client_addr, &cli_len);

						int cmd_word = buf[0]*256 + buf[1];
						int payload_length=0;
						if(pkg_length<9 || cmd_word == 0)
							break;
						payload_length = buf[PAYLOAD_START-2]*256+buf[PAYLOAD_START-1];
						if(payload_length > MAX_BUF_SIZE || payload_length == 0)
						{
							break;
						}
						if(Calculate_XorDat(&buf[PAYLOAD_START], payload_length) != buf[pkg_length-1])
						{
							break;
						}

						unsigned char *payloadBuf=buf+PAYLOAD_START;
						if(cmd_word != CMD_STREAM_SOURCE_MULTICAST_NEW)
						{
							break;
						}
						memcpy(concentrated_pcm_buf[concentrated_write_pos],payloadBuf,payload_length);
						concentrated_pcm_buf_len[concentrated_write_pos]=payload_length;
						//printf("payload_length=%d\n",payload_length);
					}
					else
					{
						concentrated_pcm_buf_len[concentrated_write_pos] = recvfrom(concentrated_sockfd, &concentrated_pcm_buf[concentrated_write_pos], MAX_BUF_SIZE, 0, (struct sockaddr*)&client_addr, &cli_len);
					}
					
					g_centralized_mode_timeout_count =0;
					g_centralized_mode_timing_repeat = 0;

					if(g_collector_run_flag)
						g_ac_timing_count = 0;

					//printf("write_pos=%d,len=%d\n",concentrated_write_pos,concentrated_pcm_buf_len[concentrated_write_pos]);
					concentrated_pcm_buf_valid[concentrated_write_pos] = 1;
					concentrated_write_pos++;
					concentrated_write_pos_total++;
					if(concentrated_write_pos >= CONCENTRATED_MODE_DATA_BUF_MAX)
					{
						concentrated_write_pos = 0;
					}
				}
			    break;
		}
	}
	close(concentrated_sockfd);

	printf("exit mcast_recv_concentrated_data!\n");
	mcast_song_recv_task_flag=0;
	pthread_exit(NULL);
}

/*********************************************************************
 * @fn      start_concentrated_recv_pthread
 *
 * @brief   启动集中模式下数据接收线程
 *
 * @param   void
 *
 * @return	void
 */
void start_concentrated_recv_pthread(void)
{
	int count=30;
	while(	count-- && mcast_song_recv_task_flag )
	{
		mcast_song_recv_task_need_exit=1;
		usleep(10000);
	}
	mcast_song_recv_task_flag=1;
	mcast_song_recv_task_need_exit=0;

	printf("start_concentrated_recv_pthread:count=%d\n",count);
	int ret = -1;
	pthread_t Mcast_Pthread;
	pthread_attr_t Pthread_Attr;

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Mcast_Pthread, &Pthread_Attr, (void *)mcast_recv_concentrated_data, NULL);
	if (ret < 0)
	{
		printf("start_concentrated_recv_pthread create failed!!!\n");
	}
	else
	{
		printf("start_concentrated_recv_pthread create success!\n");
	}
	pthread_attr_destroy(&Pthread_Attr);
}








/*********************************************************************
 * @fn      mcast_recv_paging_data
 *
 * @brief   寻呼数据（组播）接收线程
 *
 * @param   void
 *
 * @return	void
 */
void *mcast_recv_paging_data(void)
{
	isMulticastPagerRecvThreadRun=true;
	printf("enter mcast_recv_paging_data\n");
	int rxlen = 0;
	int ret = -1;
	struct sockaddr_in server_addr, client_addr;
	struct ip_mreq mreq;
	fd_set readfd;
	struct timeval timeout;
	int cli_len = sizeof(client_addr);
	int i;
	int payload_length = 0;
	int pkg_length = 0;

	int rxIndex = 0;
	float before_time = 0;
	float current_time = 0;
	struct timeval time;
	unsigned int pcm_capacity = 0;
	unsigned char pcm_buf_start_read_flag = 0;

	int paging_sockfd;

	memset(&server_addr, 0x00, sizeof(server_addr));
	server_addr.sin_family = AF_INET;
	server_addr.sin_addr.s_addr = htonl(INADDR_ANY);
	
	server_addr.sin_port = htons(MCAST_PAGING_RECV_PORT);
	mreq.imr_multiaddr.s_addr = inet_addr(MCAST_PAGING_RECV_ADDR); // 注册组播地址
	

	mreq.imr_interface.s_addr = htonl(INADDR_ANY);                 // 允许任何地址访问

	// 创建套接字
	if ( (paging_sockfd = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
	{
		perror("Create mcast_recv_paging_data socket error");
		isMulticastPagerRecvThreadRun=false;
		pthread_exit(NULL);
	}

	/*设置地址重用,以解决第二次运行时端口占用的问题(SO_REUSEADDR允许完全相同的地址和端口的重复绑定，但这只用于UDP)*/
	int opt = 1;
	if((setsockopt(paging_sockfd,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt)))<0)
	{
		perror("setsockopt:mcast_recv_paging_data paging_sockfd SO_REUSEADDR failed");
		close(paging_sockfd);
		isMulticastPagerRecvThreadRun=false;
		pthread_exit(NULL);
	}

	// 绑定地址到套接字
	if ( bind(paging_sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) <0)
	{
		perror("bind:paging_sockfd");
		close(paging_sockfd);
		isMulticastPagerRecvThreadRun=false;
		pthread_exit(NULL);
	}

	//接收组播的socket需要先绑定网卡
	if (setsockopt(paging_sockfd, SOL_SOCKET,SO_BINDTODEVICE, (char *)&nif_eth0, sizeof(nif_eth0)) < 0)
	{
		perror("paging_sockfd bind interface fail");
	}

	// 加入组中，才能接收组播源发出的数据
	if (setsockopt(paging_sockfd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq, sizeof(mreq)) < 0)
	{
		perror("setsockopt:paging_sockfd");
		close(paging_sockfd);
		isMulticastPagerRecvThreadRun=false;
		pthread_exit(NULL);
	}


	int exit_cnt = 0;
	unsigned char paging_multi_buf[MAX_BUF_SIZE]={0};
	while (1)
	{
		timeout.tv_sec = 0;      // 获取数据超时时间设置
		timeout.tv_usec = 50000;
		FD_ZERO(&readfd);        // 清空读文件描述集合
		FD_SET(paging_sockfd, &readfd); // 注册套接字文件描述符


		if(g_paging_status == PAGING_STOP || pager_need_exit_flag)
		{
			printf("mcast_recv_paging_data need exit!\n");
			break;
		}
		ret = select(paging_sockfd+1, &readfd, NULL, NULL, &timeout);
		switch (ret)
		{
			case -1 :
				//memset(rxbuf_macast_pcm, 0x00, sizeof(rxbuf_macast_pcm)); // 清空接收缓存
				perror("mcast_recv_paging_data call select error");
				break;

			case 0 :	//超时
				//perror("mcast_recv_paging_data call select TimeOut");
				break;

			default : // 接收数据
				memset(paging_multi_buf,0,sizeof(paging_multi_buf));
				recvfrom(paging_sockfd, paging_multi_buf, MAX_BUF_SIZE, 0, (struct sockaddr*)&client_addr, &cli_len);

				payload_length = paging_multi_buf[PAYLOAD_START-2]*256+paging_multi_buf[PAYLOAD_START-1];
				pkg_length = payload_length+PACKAGE_MIN_SIZE;
				if(pkg_length > MAX_BUF_SIZE || payload_length == 0)
				{
					break;
				}
				if(Calculate_XorDat(&paging_multi_buf[PAYLOAD_START], payload_length) == paging_multi_buf[pkg_length-1])
				{
				   //判断发送数据的寻呼台MAC是否一致
					int mac_match=1;
					unsigned char pager_mac[6]={0};
					memcpy(pager_mac,paging_multi_buf+PAYLOAD_START,6);
					for(i=0;i<6;i++)
					{
						if(pager_mac[i] != pager_property.pager_mac[i])
						{
							mac_match=0;
							break;
						}
					}
					if(!mac_match)
					{
						printf("[Paging_Multi_Server_Data]mac not match\n");
						continue;
					}
					
					if(payload_length-6 >PAGER_PCM_BASIC_VALUE)
					{
						continue;
					}
					if(g_paging_timing_count<PAGING_TIMING_COUNT_MAX)
						g_paging_timing_count = 0;

					//判断寻呼类型（从保留字可以得出，旧寻呼台不支持，判断依据为发起寻呼通知指令是否带上pagingTYpe类型，且为1或者3）
					if(pager_property.pagingType == PAGING_TYPE_MIC || pager_property.pagingType == PAGING_TYPE_MUSIC)
					{
						int new_pagingType= paging_multi_buf[3];
						if( new_pagingType!= pager_property.pagingType && (new_pagingType == PAGING_TYPE_MIC || new_pagingType == PAGING_TYPE_MUSIC) )
						{
							pager_property.pagingType = new_pagingType;
							Clean_Pager_Pcm_Buf();
							printf("new PagingType=%d\r\n",new_pagingType);
						}
					}

					for(i = 0; i < payload_length-6; i++)
					{
						if(pager_pcm_write>=sizeof(pager_pcm_buf[0]))
						{
							printf("Error:pager_pcm_write>=sizeof(pcm_buf[0]\r\n");
							break;
						}
						pager_pcm_buf[pager_write_pos][pager_pcm_write++] = paging_multi_buf[PAYLOAD_START+6+i];
					}
					pager_pcm_capacity++;
		
					if(pager_property.decodeType == DECODE_STANDARD_PCM && pager_pcm_capacity >= PAGER_PCM_READ_VALVE)
					{
						pager_pcm_buf_len[pager_write_pos] = pager_pcm_write;
						pager_pcm_buf_valid[pager_write_pos] = 1;
						pager_write_pos++;
						if(pager_write_pos >= PAGER_PCM_BUF_MAX)
							pager_write_pos = 0;
						pager_write_pos_total++;	
						pager_pcm_capacity = 0;
						pager_pcm_write = 0;
					}
					#if SUPPORT_CODEC_G722
					else if(pager_property.decodeType == DECODE_G722 && pager_pcm_capacity >= PAGER_PCM_READ_VALVE)
					{
						if(pager_pcm_write>512)
						{
							printf("Error:pager_pcm_write g722>512\r\n");
							pager_pcm_capacity = 0;
							pager_pcm_write = 0;
							continue;
						}
						pager_pcm_buf_len[pager_write_pos] = pager_pcm_write;
						pager_pcm_buf_valid[pager_write_pos] = 1;
						pager_write_pos++;
						if(pager_write_pos >= PAGER_PCM_BUF_MAX)
							pager_write_pos = 0;
						pager_write_pos_total++;	
						pager_pcm_capacity = 0;
						pager_pcm_write = 0;
					}
					#endif
					#if SUPPORT_CODEC_G7221
					else if(pager_property.decodeType == DECODE_G722_1 && pager_pcm_capacity >= 1)
					{
						pager_pcm_buf_len[pager_write_pos] = pager_pcm_write;
						pager_pcm_buf_valid[pager_write_pos] = 1;
						pager_write_pos++;
						if(pager_write_pos >= PAGER_PCM_BUF_MAX)
							pager_write_pos = 0;
						pager_write_pos_total++;
						pager_pcm_capacity = 0;
						pager_pcm_write = 0;
					}
					#endif
				}
			    break;
		}
	}
	close(paging_sockfd);

	isMulticastPagerRecvThreadRun=false;

	printf("exit mcast_recv_paging_data!\n");
		pthread_exit(NULL);
}

/*********************************************************************
 * @fn      start_mcast_paging_recv_pthread
 *
 * @brief   启动寻呼数据（组播）接收线程
 *
 * @param   void
 *
 * @return	void
 */
void start_mcast_paging_recv_pthread(void)
{
	int ret = -1;
	pthread_t Mcast_Pthread;
	pthread_attr_t Pthread_Attr;

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Mcast_Pthread, &Pthread_Attr, (void *)mcast_recv_paging_data, NULL);
	if (ret < 0)
	{
		printf("start_mcast_paging_recv_pthread create failed!!!\n");
	}
	else
	{
		printf("start_mcast_paging_recv_pthread create success!\n");
	}
	pthread_attr_destroy(&Pthread_Attr);
}





void suspend_Centralized_Mode_Timing_Task()
{
    pthread_mutex_lock(&mutex_Centralized_Mode_Timeout_task);
    nflag_Centralized_Mode_Timeout_task=0;
    pthread_mutex_unlock(&mutex_Centralized_Mode_Timeout_task);
}


void resume_Centralized_Mode_Timing_Task()
{
    pthread_mutex_lock(&mutex_Centralized_Mode_Timeout_task);
	if(!nflag_Centralized_Mode_Timeout_task)
	{
		nflag_Centralized_Mode_Timeout_task=1;
		printf("Resume：Centralized_Mode_Timing_Task\n");
		pthread_cond_signal(&cond_Centralized_Mode_Timeout_task);
	}
    pthread_mutex_unlock(&mutex_Centralized_Mode_Timeout_task);
}


/*********************************************************************
 * @fn      Create_Centralized_Mode_Timing
 *
 * @brief  	集中模式歌曲数据接收超时检测任务回调函数
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_Centralized_Mode_TimeOut_Count(void *p_arg)
{
	g_centralized_mode_timeout_count = 0;
	nflag_Centralized_Mode_Timeout_task= 1;
	while(1)
	{
		pthread_mutex_lock(&mutex_Centralized_Mode_Timeout_task);
		while(!nflag_Centralized_Mode_Timeout_task)
		{
			printf("suspend:Centralized_Mode_TimeOut Task\n");
			pthread_cond_wait(&cond_Centralized_Mode_Timeout_task, &mutex_Centralized_Mode_Timeout_task);
		}
		pthread_mutex_unlock(&mutex_Centralized_Mode_Timeout_task);

		if(g_centralized_mode_timeout_pause)
		{
			usleep(100000);
			continue;
		}
		if(g_centralized_mode_timeout_count == CENTRALIZED_MODE_TIMING_COUNT_MAX-1)
		{
			g_centralized_mode_timing_repeat++;
			if(g_centralized_mode_timing_repeat >= 5)
			{
				g_centralized_mode_timing_repeat = 0;
				#if !LOCAL_SOURCE_PRIORITY_HIGHEST
				printf("concentrate_repeat_paly_enable=0\n");
				concentrate_repeat_paly_enable = 0;
				#endif
			}
		}

		if(g_centralized_mode_timeout_count >= CENTRALIZED_MODE_TIMING_COUNT_MAX)
		{               
			printf("g_centralized_mode_timeout_count:%d\n", g_centralized_mode_timeout_count);
	
			if(g_media_source == SOURCE_FIRE_ALARM || g_media_source == SOURCE_LOCAL_PLAY || g_media_source == SOURCE_TIMING || g_media_source == SOURCE_MONITOR_EV)
			{
				Set_zone_idle_status(NULL,  __func__, __LINE__,true);
			}
			if(g_centralized_mode_timeout_count != 0)     //可能其他地方已经将此任务休眠，此时不能再度休眠,否则任务一唤醒马上再度休眠
			{
				g_centralized_mode_timeout_count=0;
				//挂起任务
				suspend_Centralized_Mode_Timing_Task();
				continue;
			}
		}

		if( !( g_media_source == SOURCE_TIMING && strlen(g_media_name) == 0 ) )   //为定时且歌名长度为0时代表处于定时音源但没有歌曲播放，此时不进行超时计数
        g_centralized_mode_timeout_count++;


		//printf("c_timeout=%d...\n",g_centralized_mode_timeout_count);
		usleep(200000);
	}
}

/*********************************************************************
 * @fn      Create_Centralized_Mode_Timing_Task
 *
 * @brief  	创建集中模式歌曲数据接收超时检测任务
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_Centralized_Mode_Timing_Task(void)
{
	if(Centralized_Mode_Timeout_task_flag)
	{
		g_centralized_mode_timeout_count = 0;
		resume_Centralized_Mode_Timing_Task();
		return;
	}
	Centralized_Mode_Timeout_task_flag = 1;

    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Create_Centralized_Mode_TimeOut_Count, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}












void suspend_AudioCollector_Timing_Task()
{
    pthread_mutex_lock(&mutex_AudioCollector_Timeout_task);
    nflag_AudioCollector_Timeout_task=0;
    pthread_mutex_unlock(&mutex_AudioCollector_Timeout_task);
}


void resume_AudioCollector_Timing_Task()
{
    pthread_mutex_lock(&mutex_AudioCollector_Timeout_task);
	if(!nflag_AudioCollector_Timeout_task)
	{
		nflag_AudioCollector_Timeout_task=1;
		printf("Resume：AudioCollector_Timing_Task\n");
		pthread_cond_signal(&cond_AudioCollector_Timeout_task);
	}
    pthread_mutex_unlock(&mutex_AudioCollector_Timeout_task);
}


/*********************************************************************
 * @fn      Create_AudioCollector_TimeOut
 *
 * @brief  	集中模式歌曲数据接收超时检测任务回调函数
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_AudioCollector_TimeOut(void *p_arg)
{
	g_ac_timing_count = 0;
	nflag_AudioCollector_Timeout_task= 1;
	while(1)
	{
		pthread_mutex_lock(&mutex_AudioCollector_Timeout_task);
		while(!nflag_AudioCollector_Timeout_task)
		{
			printf("suspend:AudioCollector_Timeout Task\n");
			pthread_cond_wait(&cond_AudioCollector_Timeout_task, &mutex_AudioCollector_Timeout_task);
		}
		pthread_mutex_unlock(&mutex_AudioCollector_Timeout_task);

		if(g_ac_timing_wait)
		{
		  	g_ac_timing_count=0;
			usleep(100000);//延时
			continue;
		}
		
		if(g_ac_timing_count >= AUDIO_COLLECT_TIMING_COUNT_MAX)
		{
			if(g_media_source >= SOURCE_AUDIO_COLLECTOR_BASE && g_media_source <= SOURCE_AUDIO_COLLECTOR_MAX)
			{
				Set_zone_idle_status(NULL,  __func__, __LINE__,true);
			}
			if(g_ac_timing_count != 0)		//可能其他地方已经将此任务休眠，此时不能再度休眠,否则任务一唤醒马上再度休眠
			{
				g_ac_timing_count=0;
				suspend_AudioCollector_Timing_Task();//挂起任务
			}
		}
		g_ac_timing_count++;
		sleep(1);
	}
}

/*********************************************************************
 * @fn      Create_AudioCollector_Timing_Task
 *
 * @brief  	创建音频采集数据接收超时检测任务
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_AudioCollector_Timing_Task(void)
{
	if(AudioCollector_Timeout_task_flag)
	{
		g_ac_timing_count = 0;
		resume_AudioCollector_Timing_Task();
		return;
	}
	AudioCollector_Timeout_task_flag = 1;

    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Create_AudioCollector_TimeOut, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}







void suspend_Paging_Timing_Task()
{
    pthread_mutex_lock(&mutex_Paging_Timeout_task);
    nflag_Paging_Timeout_task=0;
    pthread_mutex_unlock(&mutex_Paging_Timeout_task);
}


void resume_Paging_Timing_Task()
{
    pthread_mutex_lock(&mutex_Paging_Timeout_task);
	if(!nflag_Paging_Timeout_task)
	{
		nflag_Paging_Timeout_task=1;
		printf("Resume：Paging_Timing_Task\n");
		pthread_cond_signal(&cond_Paging_Timeout_task);
	}
    pthread_mutex_unlock(&mutex_Paging_Timeout_task);
}


/*********************************************************************
 * @fn      Create_Paging_TimeOut
 *
 * @brief  	寻呼数据接收超时检测任务回调函数
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_Paging_TimeOut(void *p_arg)
{
	g_paging_timing_count = 0;
	nflag_Paging_Timeout_task= 1;
	while(1)
	{
		pthread_mutex_lock(&mutex_Paging_Timeout_task);
		while(!nflag_Paging_Timeout_task)
		{
			printf("suspend:Paging_Timeout Task\n");
			pthread_cond_wait(&cond_Paging_Timeout_task, &mutex_Paging_Timeout_task);
		}
		pthread_mutex_unlock(&mutex_Paging_Timeout_task);


		if(g_paging_timing_count >= PAGING_TIMING_COUNT_MAX)//时间超过5s，关闭寻呼
		{
			g_paging_status = PAGING_STOP;

			printf("ERROR:Ppaging_notification_timing:paging_timing_count>=50\n");
			//当音源改变时，组播发送分区信息
            pager_need_exit_flag = 1;
			int count = 300;
			while(count-- && pager_need_exit_flag)
			{
				usleep(10000);
			}
			pager_need_exit_flag = 0;
			g_paging_timing_count = 0;

			if(g_paging_status == PAGING_START)     //刚好又进来寻呼
			{
				continue;
			}
			//当音源改变时，组播发送分区信息
			if(g_media_source == SOURCE_NET_PAGING)
			{
				Set_zone_idle_status(NULL,  __func__, __LINE__,true);
			}
			
			memset(&pager_property,0,sizeof(pager_property));
			suspend_Paging_Timing_Task();
		}
		
		if(g_media_source != SOURCE_NET_PAGING)
		{
			g_paging_timing_count=0;
		}
		else
			g_paging_timing_count++;

		usleep(100000);
	}
}

/*********************************************************************
 * @fn      Create_Paging_Timing_Task
 *
 * @brief  	创建寻呼数据接收超时检测任务
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_Paging_Timing_Task(void)
{
	if(Paging_Timeout_task_flag)
	{
		g_paging_timing_count = 0;
		resume_Paging_Timing_Task();
		return;
	}
	Paging_Timeout_task_flag = 1;

    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Create_Paging_TimeOut, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}



#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
void suspend_AudioCall_Timing_Task()
{
    pthread_mutex_lock(&mutex_audioCall_Timeout_task);
    nflag_audioCall_Timeout_task=0;
    pthread_mutex_unlock(&mutex_audioCall_Timeout_task);
}


void resume_AudioCall_Timing_Task()
{
    pthread_mutex_lock(&mutex_audioCall_Timeout_task);
	if(!nflag_audioCall_Timeout_task)
	{
		nflag_audioCall_Timeout_task=1;
		printf("Resume：AudioCall_Timing_Task\n");
		pthread_cond_signal(&cond_audioCall_Timeout_task);
	}
    pthread_mutex_unlock(&mutex_audioCall_Timeout_task);
}


/*********************************************************************
 * @fn      Create_AudioCall_TimeOut
 *
 * @brief  	音频对讲数据接收超时检测任务回调函数
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_AudioCall_TimeOut(void *p_arg)
{
	m_stCall_recv_stream.rx_call_timing_count = 0;
	nflag_audioCall_Timeout_task= 1;
	while(1)
	{
		pthread_mutex_lock(&mutex_audioCall_Timeout_task);
		while(!nflag_audioCall_Timeout_task)
		{
			printf("suspend:AudioCall_Timeout Task\n");
			pthread_cond_wait(&cond_audioCall_Timeout_task, &mutex_audioCall_Timeout_task);
		}
		pthread_mutex_unlock(&mutex_audioCall_Timeout_task);

		if(g_media_source != SOURCE_CALL || m_stCall_recv_stream.rx_call_timing_count >= AUDIO_CALL_TIMING_COUNT_MAX)//时间超过5s，关闭对讲
		{
			m_stCall_recv_stream.rx_call_timing_count=0;
			EnterCallMode(false);
			suspend_AudioCall_Timing_Task();
		}
		else
		{
			m_stCall_recv_stream.rx_call_timing_count++;
		}

		usleep(10000);
	}
}

/*********************************************************************
 * @fn      Create_AudioCall_Timing_Task
 *
 * @brief  	创建音频对讲数据接收超时检测任务
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_AudioCall_Timing_Task(void)
{
	if(AudioCall_Timeout_task_flag)
	{
		m_stCall_recv_stream.rx_call_timing_count = 0;
		resume_AudioCall_Timing_Task();
		return;
	}
	AudioCall_Timeout_task_flag = 1;

    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Create_AudioCall_TimeOut, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}






/*********************************************************************
 * @fn      Create_AudioCall_Ring
 *
 * @brief  	音频对讲数据接收超时检测任务回调函数
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_AudioCall_Ring(void *p_arg)
{
	if(m_stCallInfo.self_callStatus != CALL_STATUS_WAIT_CALLED_ANSWER)
	{
		printf("Create_AudioCall_Ring error1\n");
		AudioCall_ring_task_flag=0;
		return;
	}

	//打开audioOut,播放ring
	Open_Audio_Out(16000,16,1);

	//暂时只处理主叫等待响铃，被叫主动应答
	int callingParty = m_stCallInfo.isCallingParty;
    const unsigned char *callRingBuf = callingParty?_acCalling_ring:_acCalled_Incoming_ring;
    int callRing_bytes = callingParty?sizeof(_acCalling_ring):sizeof(_acCalled_Incoming_ring);
	int fileReadTotal=0;
	int fileReadOnce=1024*4;

	struct timeval tv_before, tv_after;
	unsigned long long before_us, after_us;
	gettimeofday(&tv_before, NULL);
	before_us = tv_before.tv_sec * 1000000 + tv_before.tv_usec;
	
	while(m_stCallInfo.self_callStatus == CALL_STATUS_WAIT_CALLED_ANSWER)
	{
		//等待振铃,如果音源不是SOURCE_CALL
		if(g_media_source != SOURCE_CALL)
		{
			EnterCallMode(false);
			break;
		}

		//播放响铃
		if(fileReadTotal<callRing_bytes)
		{
			int readBytes=(callRing_bytes-fileReadTotal>=fileReadOnce)?fileReadOnce:callRing_bytes-fileReadTotal;
			#ifndef USE_PC_SIMULATOR
			mi_audio_write(callRingBuf+fileReadTotal,readBytes);
			#endif
			fileReadTotal+=readBytes;
		}
		else
		{
			fileReadTotal=0;
		}

		gettimeofday(&tv_after, NULL);
		after_us = tv_after.tv_sec * 1000000 + tv_after.tv_usec;
		if (after_us - before_us > 40*1000*1000)	//40秒
		{
			printf("Call Ring Timeout...\n");
			EnterCallMode(false);
			break;
		}
		
		usleep(10000);
	}
	AudioCall_ring_task_flag=0;
	printf("Exit Create_AudioCall_Ring!\n");
}

/*********************************************************************
 * @fn      Create_AudioCall_Ring_Task
 *
 * @brief  	创建铃声线程(主动呼时响铃，被动时自动应答)
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_AudioCall_Ring_Task(void)
{
	printf("Create_AudioCall_Ring_Task1\n");
	if(AudioCall_ring_task_flag || m_stCallInfo.self_callStatus != CALL_STATUS_WAIT_CALLED_ANSWER)
	{
		printf("Create_AudioCall_Ring_Task error!\n");
		return;
	}
	AudioCall_ring_task_flag=1;
    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Create_AudioCall_Ring, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}

#endif




void suspend_AudioMixer_Timing_Task()
{
    pthread_mutex_lock(&mutex_audioMixer_Timeout_task);
    nflag_audioMixer_Timeout_task=0;
	printf("suspend_AudioMixer_Timing_Task...\n");
    pthread_mutex_unlock(&mutex_audioMixer_Timeout_task);
}


void resume_AudioMixer_Timing_Task()
{
    pthread_mutex_lock(&mutex_audioMixer_Timeout_task);
	if(!nflag_audioMixer_Timeout_task)
	{
		nflag_audioMixer_Timeout_task=1;
		printf("Resume:audioMixer_Timing_Task\n");
		pthread_cond_signal(&cond_audioMixer_Timeout_task);
	}
    pthread_mutex_unlock(&mutex_audioMixer_Timeout_task);
}


/*********************************************************************
 * @fn      Create_AudioMixer_TimeOut
 *
 * @brief  	
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_AudioMixer_TimeOut(void *p_arg)
{
	g_audio_mixer_stream_timing_count = 0;
	nflag_audioMixer_Timeout_task= 1;
	printf("Create_AudioMixer_TimeOut...\n");
	while(1)
	{
		pthread_mutex_lock(&mutex_audioMixer_Timeout_task);
		while(!nflag_audioMixer_Timeout_task)
		{
			printf("suspend:AudioMixer_Timeout Task\n");
			pthread_cond_wait(&cond_audioMixer_Timeout_task, &mutex_audioMixer_Timeout_task);
		}
		pthread_mutex_unlock(&mutex_audioMixer_Timeout_task);


		if(g_audio_mixer_stream_timing_count++ >= AUDIO_MIXER_TIMING_COUNT_MAX)//时间超过5s，关闭
		{
			g_audio_mixer_stream_timing_count = 0;

			memset(audioMixer_stream_valid,0,sizeof(audioMixer_stream_valid));
			audioMixer_stream_read_pos=0;
			audioMixer_stream_write_pos=0;
			audioMixer_stream_read_pos_total=0;
			audioMixer_stream_write_pos_total=0;

			printf("g_audio_mixer_stream_timing_count>=AUDIO_MIXER_TIMING_COUNT_MAX\n");

			//当音源改变时，组播发送分区信息
			if(g_media_source == SOURCE_AUDIO_MIXED)
			{
				Set_zone_idle_status(NULL,  __func__, __LINE__,true);
			}
			
			suspend_AudioMixer_Timing_Task();
		}

		usleep(100000);
	}
}

/*********************************************************************
 * @fn      Create_AudioMixer_Timing_Task
 *
 * @brief  	创建混音数据接收超时检测任务
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_AudioMixer_Timing_Task(void)
{
	if(AudioMixer_Timeout_task_flag)
	{
		g_audio_mixer_stream_timing_count = 0;
		resume_AudioMixer_Timing_Task();
		return;
	}
	AudioMixer_Timeout_task_flag = 1;

    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Create_AudioMixer_TimeOut, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}




/*********************************************************************
 * @fn      mcast_audioMixer_recv_concentrated_data
 *
 * @brief   混音器组播数据接收线程
 *
 * @param   void
 *
 * @return	void
 */
void *mcast_audioMixer_recv_concentrated_data(void)
{
	printf("enter mcast_audioMixer_recv_concentrated_data\n");
	int rxlen = 0;
	int ret = -1;
	struct sockaddr_in server_addr, client_addr;
	struct ip_mreq mreq;
	fd_set readfd;
	struct timeval timeout;
	int cli_len = sizeof(client_addr);
	int i;
	int payload_length = 0;
	int pkg_length = 0;

	int rxIndex = 0;
	float before_time = 0;
	float current_time = 0;
	struct timeval time;
	unsigned int pcm_capacity = 0;
	unsigned char pcm_buf_start_read_flag = 0;

	int recv_type=0;

	int audioMixer_sockfd;

	memset(&server_addr, 0x00, sizeof(server_addr));
	server_addr.sin_family = AF_INET;
	server_addr.sin_addr.s_addr = htonl(INADDR_ANY);

	server_addr.sin_port = htons(g_audio_mixer_broadcast_port);
	mreq.imr_multiaddr.s_addr = inet_addr(g_audio_mixer_broadcast_addr); // 注册组播地址

	mreq.imr_interface.s_addr = htonl(INADDR_ANY);                 // 允许任何地址访问

	// 创建套接字
	if ( (audioMixer_sockfd = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
	{
		perror("Create mcast_recv_paging_data socket error");
		pthread_exit(NULL);
	}

	/*设置地址重用,以解决第二次运行时端口占用的问题(SO_REUSEADDR允许完全相同的地址和端口的重复绑定，但这只用于UDP)*/
	int opt = 1;
	if((setsockopt(audioMixer_sockfd,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt)))<0)
	{
		perror("setsockopt:mcast_recv_paging_data audioMixer_sockfd SO_REUSEADDR failed");
		close(audioMixer_sockfd);
		pthread_exit(NULL);
	}

	// 绑定地址到套接字
	if ( bind(audioMixer_sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) <0)
	{
		perror("bind:audioMixer_sockfd");
		close(audioMixer_sockfd);
		pthread_exit(NULL);
	}

	//接收组播的socket需要先绑定网卡
	if (setsockopt(audioMixer_sockfd, SOL_SOCKET,SO_BINDTODEVICE, (char *)&nif_eth0, sizeof(nif_eth0)) < 0)
	{
		perror("audioMixer_sockfd bind interface fail");
	}

	// 加入组中，才能接收组播源发出的数据
	if (setsockopt(audioMixer_sockfd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq, sizeof(mreq)) < 0)
	{
		perror("setsockopt:audioMixer_sockfd");
		close(audioMixer_sockfd);
		pthread_exit(NULL);
	}

	unsigned char temp_recv_buf[AUDIO_MIXER_STREAM_BUF_SIZE]={0};

	int exit_cnt = 0;
	while (1)
	{
		timeout.tv_sec = 0;      // 获取数据超时时间设置
		timeout.tv_usec = 50000;
		FD_ZERO(&readfd);        // 清空读文件描述集合
		FD_SET(audioMixer_sockfd, &readfd); // 注册套接字文件描述符

		if(mcast_audioMixer_recv_task_need_exit)
		{
			break;
		}
	
		if(get_system_source()!=SOURCE_AUDIO_MIXED)
		{
			break;
		}
		

		ret = select(audioMixer_sockfd+1, &readfd, NULL, NULL, &timeout);
		switch (ret)
		{
			case -1 :
				//memset(rxbuf_macast_pcm, 0x00, sizeof(rxbuf_macast_pcm)); // 清空接收缓存
				perror("mcast_audioMixer_recv_concentrated_data call select error");
				break;

			case 0 :	//超时
				//perror("mcast_recv_paging_data call select TimeOut");
				break;

			default : // 接收数据
				if (FD_ISSET(audioMixer_sockfd, &readfd))
				{
					memset(temp_recv_buf,0,sizeof(temp_recv_buf));
					recvfrom(audioMixer_sockfd, temp_recv_buf, MAX_BUF_SIZE, 0, (struct sockaddr*)&client_addr, &cli_len);

					payload_length = temp_recv_buf[PAYLOAD_START-2]*256+temp_recv_buf[PAYLOAD_START-1];
					pkg_length = payload_length+PACKAGE_MIN_SIZE;
					if(pkg_length > MAX_BUF_SIZE || payload_length == 0)
					{
						break;
					}
					if(Calculate_XorDat(&temp_recv_buf[PAYLOAD_START], payload_length) == temp_recv_buf[pkg_length-1])
					{
						//判断发送数据的MAC是否一致
						int mac_match=1;
						unsigned char mixer_mac[6]={0};
						memcpy(mixer_mac,temp_recv_buf+PAYLOAD_START,6);
						for(i=0;i<6;i++)
						{
							if(mixer_mac[i] != g_audio_mixer_mac[i])
							{
								mac_match=0;
								break;
							}
						}
						if(!mac_match)
						{
							continue;
						}
						//保存音频编码
						g_audio_mixer_codecs = temp_recv_buf[PAYLOAD_START+6];
						
						//判断混音信号类型
						int new_signalType= temp_recv_buf[PAYLOAD_START+7];
						if(new_signalType!= g_audio_mixer_signalType)
						{
							//如果原来不是MIC信号，现在有MIC信号了，那么需要清除缓冲区，确保最低时延
							if(new_signalType == MIXER_SIGNAL_TYPE_MIC)
							{
								Clean_Mixed_Info();
							}
							g_audio_mixer_signalType = new_signalType;
							printf("g_audio_mixer_signalType=%d\n",new_signalType);
						}
						
						audioMixer_stream_len[audioMixer_stream_write_pos] = payload_length-8;
						memcpy(audioMixer_stream_buf[audioMixer_stream_write_pos],temp_recv_buf+PAYLOAD_START+8,audioMixer_stream_len[audioMixer_stream_write_pos]);
						g_audio_mixer_stream_timing_count = 0;
						//printf("write_pos=%d,len=%d\n",audioMixer_stream_write_pos,audioMixer_stream_len[concentrated_write_pos]);
						audioMixer_stream_valid[audioMixer_stream_write_pos] = 1;
						audioMixer_stream_write_pos++;
						audioMixer_stream_write_pos_total++;
						if(audioMixer_stream_write_pos >= AUDIO_MIXER_STREAM_BUF_MAX)
						{
							audioMixer_stream_write_pos = 0;
						}
					}

				}
			    break;
		}
	}
	close(audioMixer_sockfd);

	printf("exit mcast_audioMixer_recv_concentrated_data!\n");
	mcast_audioMixer_recv_task_flag=0;
	pthread_exit(NULL);
}

/*********************************************************************
 * @fn      start_AudioMixer_recv_pthread
 *
 * @brief   启动集中模式下音频混音数据接收线程
 *
 * @param   void
 *
 * @return	void
 */
void start_AudioMixer_recv_pthread(void)
{
	int count=30;
	while(	count-- && mcast_audioMixer_recv_task_flag )
	{
		mcast_audioMixer_recv_task_need_exit=1;
		usleep(10000);
	}
	mcast_audioMixer_recv_task_flag=1;
	mcast_audioMixer_recv_task_need_exit=0;

	printf("start_AudioMixer_recv_pthread:count=%d\n",count);
	int ret = -1;
	pthread_t Mcast_Pthread;
	pthread_attr_t Pthread_Attr;

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Mcast_Pthread, &Pthread_Attr, (void *)mcast_audioMixer_recv_concentrated_data, NULL);
	if (ret < 0)
	{
		printf("start_AudioMixer_recv_pthread create failed!!!\n");
	}
	else
	{
		printf("start_AudioMixer_recv_pthread create success!\n");
	}
	pthread_attr_destroy(&Pthread_Attr);
}





void suspend_PhoneGateway_Timing_Task()
{
    pthread_mutex_lock(&mutex_phoneGateway_Timeout_task);
    nflag_phoneGateway_Timeout_task=0;
	printf("suspend_PhoneGateway_Timing_Task...\n");
    pthread_mutex_unlock(&mutex_phoneGateway_Timeout_task);
}


void resume_PhoneGateway_Timing_Task()
{
    pthread_mutex_lock(&mutex_phoneGateway_Timeout_task);
	if(!nflag_phoneGateway_Timeout_task)
	{
		nflag_phoneGateway_Timeout_task=1;
		printf("Resume:phoneGateway_Timing_Task\n");
		pthread_cond_signal(&cond_phoneGateway_Timeout_task);
	}
    pthread_mutex_unlock(&mutex_phoneGateway_Timeout_task);
}


/*********************************************************************
 * @fn      Create_PhoneGateway_TimeOut
 *
 * @brief  	
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_PhoneGateway_TimeOut(void *p_arg)
{
	g_phone_gateway_stream_timing_count = 0;
	nflag_phoneGateway_Timeout_task= 1;
	printf("Create_PhoneGateway_TimeOut...\n");
	while(1)
	{
		pthread_mutex_lock(&mutex_phoneGateway_Timeout_task);
		while(!nflag_phoneGateway_Timeout_task)
		{
			printf("suspend:PhoneGateway_Timeout Task\n");
			pthread_cond_wait(&cond_phoneGateway_Timeout_task, &mutex_phoneGateway_Timeout_task);
		}
		pthread_mutex_unlock(&mutex_phoneGateway_Timeout_task);


		if(g_phone_gateway_stream_timing_count++ >= PHONE_GATEWAY_TIMING_COUNT_MAX)//时间超过5s，关闭
		{
			g_phone_gateway_stream_timing_count = 0;

			memset(phoneGateway_stream_valid,0,sizeof(phoneGateway_stream_valid));
			phoneGateway_stream_read_pos=0;
			phoneGateway_stream_write_pos=0;
			phoneGateway_stream_read_pos_total=0;
			phoneGateway_stream_write_pos_total=0;

			printf("g_phone_gateway_stream_timing_count>=PHONE_GATEWAY_TIMING_COUNT_MAX\n");

			//当音源改变时，组播发送分区信息
			if(g_media_source == SOURCE_PHONE_GATEWAY)
			{
				Set_zone_idle_status(NULL,  __func__, __LINE__,true);
			}
			
			suspend_PhoneGateway_Timing_Task();
		}

		usleep(100000);
	}
}

/*********************************************************************
 * @fn      Create_PhoneGateway_Timing_Task
 *
 * @brief  	创建电话网关数据接收超时检测任务
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_PhoneGateway_Timing_Task(void)
{
	if(PhoneGatewey_Timeout_task_flag)
	{
		g_phone_gateway_stream_timing_count = 0;
		resume_AudioMixer_Timing_Task();
		return;
	}
	AudioMixer_Timeout_task_flag = 1;

    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Create_PhoneGateway_TimeOut, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}




/*********************************************************************
 * @fn      mcast_phoneGateway_recv_concentrated_data
 *
 * @brief   电话网关组播数据接收线程
 *
 * @param   void
 *
 * @return	void
 */
void *mcast_phoneGateway_recv_concentrated_data(void)
{
	printf("enter mcast_phoneGateway_recv_concentrated_data\n");
	int rxlen = 0;
	int ret = -1;
	struct sockaddr_in server_addr, client_addr;
	struct ip_mreq mreq;
	fd_set readfd;
	struct timeval timeout;
	int cli_len = sizeof(client_addr);
	int i;
	int payload_length = 0;
	int pkg_length = 0;

	int rxIndex = 0;
	float before_time = 0;
	float current_time = 0;
	struct timeval time;
	unsigned int pcm_capacity = 0;
	unsigned char pcm_buf_start_read_flag = 0;

	int recv_type=0;

	int phoneGateway_sockfd;

	memset(&server_addr, 0x00, sizeof(server_addr));
	server_addr.sin_family = AF_INET;
	server_addr.sin_addr.s_addr = htonl(INADDR_ANY);

	server_addr.sin_port = htons(g_phone_gateway_broadcast_port);
	mreq.imr_multiaddr.s_addr = inet_addr(g_phone_gateway_broadcast_addr); // 注册组播地址

	mreq.imr_interface.s_addr = htonl(INADDR_ANY);                 // 允许任何地址访问

	// 创建套接字
	if ( (phoneGateway_sockfd = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
	{
		perror("Create mcast_recv_paging_data socket error");
		pthread_exit(NULL);
	}

	/*设置地址重用,以解决第二次运行时端口占用的问题(SO_REUSEADDR允许完全相同的地址和端口的重复绑定，但这只用于UDP)*/
	int opt = 1;
	if((setsockopt(phoneGateway_sockfd,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt)))<0)
	{
		perror("setsockopt:mcast_recv_paging_data phoneGateway_sockfd SO_REUSEADDR failed");
		close(phoneGateway_sockfd);
		pthread_exit(NULL);
	}

	// 绑定地址到套接字
	if ( bind(phoneGateway_sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) <0)
	{
		perror("bind:phoneGateway_sockfd");
		close(phoneGateway_sockfd);
		pthread_exit(NULL);
	}

	//接收组播的socket需要先绑定网卡
	if (setsockopt(phoneGateway_sockfd, SOL_SOCKET,SO_BINDTODEVICE, (char *)&nif_eth0, sizeof(nif_eth0)) < 0)
	{
		perror("phoneGateway_sockfd bind interface fail");
	}

	// 加入组中，才能接收组播源发出的数据
	if (setsockopt(phoneGateway_sockfd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq, sizeof(mreq)) < 0)
	{
		perror("setsockopt:phoneGateway_sockfd");
		close(phoneGateway_sockfd);
		pthread_exit(NULL);
	}

	unsigned char temp_recv_buf[AUDIO_MIXER_STREAM_BUF_SIZE]={0};

	int exit_cnt = 0;
	while (1)
	{
		timeout.tv_sec = 0;      // 获取数据超时时间设置
		timeout.tv_usec = 50000;
		FD_ZERO(&readfd);        // 清空读文件描述集合
		FD_SET(phoneGateway_sockfd, &readfd); // 注册套接字文件描述符

		if(mcast_phoneGateway_recv_task_need_exit)
		{
			break;
		}
	
		if(get_system_source()!=SOURCE_PHONE_GATEWAY)
		{
			break;
		}
		

		ret = select(phoneGateway_sockfd+1, &readfd, NULL, NULL, &timeout);
		switch (ret)
		{
			case -1 :
				//memset(rxbuf_macast_pcm, 0x00, sizeof(rxbuf_macast_pcm)); // 清空接收缓存
				perror("mcast_phoneGateway_recv_concentrated_data call select error");
				break;

			case 0 :	//超时
				//perror("mcast_recv_paging_data call select TimeOut");
				break;

			default : // 接收数据
				if (FD_ISSET(phoneGateway_sockfd, &readfd))
				{
					memset(temp_recv_buf,0,sizeof(temp_recv_buf));
					recvfrom(phoneGateway_sockfd, temp_recv_buf, MAX_BUF_SIZE, 0, (struct sockaddr*)&client_addr, &cli_len);

					payload_length = temp_recv_buf[PAYLOAD_START-2]*256+temp_recv_buf[PAYLOAD_START-1];
					pkg_length = payload_length+PACKAGE_MIN_SIZE;
					if(pkg_length > MAX_BUF_SIZE || payload_length == 0)
					{
						break;
					}
					if(Calculate_XorDat(&temp_recv_buf[PAYLOAD_START], payload_length) == temp_recv_buf[pkg_length-1])
					{
						//判断发送数据的MAC是否一致
						int mac_match=1;
						unsigned char phone_gateway_mac[6]={0};
						memcpy(phone_gateway_mac,temp_recv_buf+PAYLOAD_START,6);
						for(i=0;i<6;i++)
						{
							if(phone_gateway_mac[i] != g_phone_gateway_mac[i])
							{
								mac_match=0;
								break;
							}
						}
						if(!mac_match)
						{
							continue;
						}
						//保存音频编码
						g_phone_gateway_codecs = temp_recv_buf[PAYLOAD_START+6];
						
						phoneGateway_stream_len[phoneGateway_stream_write_pos] = payload_length-7;
						memcpy(phoneGateway_stream_buf[phoneGateway_stream_write_pos],temp_recv_buf+PAYLOAD_START+7,phoneGateway_stream_len[phoneGateway_stream_write_pos]);
						g_phone_gateway_stream_timing_count = 0;
						//printf("write_pos=%d,len=%d\n",phoneGateway_stream_write_pos,phoneGateway_stream_len[concentrated_write_pos]);
						phoneGateway_stream_valid[phoneGateway_stream_write_pos] = 1;
						phoneGateway_stream_write_pos++;
						phoneGateway_stream_write_pos_total++;
						if(phoneGateway_stream_write_pos >= PHONE_GATEWAY_STREAM_BUF_MAX)
						{
							phoneGateway_stream_write_pos = 0;
						}
					}

				}
			    break;
		}
	}
	close(phoneGateway_sockfd);

	printf("exit mcast_phoneGateway_recv_concentrated_data!\n");
	mcast_phoneGateway_recv_task_flag=0;
	pthread_exit(NULL);
}

/*********************************************************************
 * @fn      start_PhoneGateway_recv_pthread
 *
 * @brief   启动集中模式下电话网关数据接收线程
 *
 * @param   void
 *
 * @return	void
 */
void start_PhoneGateway_recv_pthread(void)
{
	int count=30;
	while(	count-- && mcast_phoneGateway_recv_task_flag )
	{
		mcast_phoneGateway_recv_task_need_exit=1;
		usleep(10000);
	}
	mcast_phoneGateway_recv_task_flag=1;
	mcast_phoneGateway_recv_task_need_exit=0;

	printf("start_PhoneGateway_recv_pthread:count=%d\n",count);
	int ret = -1;
	pthread_t Mcast_Pthread;
	pthread_attr_t Pthread_Attr;

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Mcast_Pthread, &Pthread_Attr, (void *)mcast_phoneGateway_recv_concentrated_data, NULL);
	if (ret < 0)
	{
		printf("start_PhoneGateway_recv_pthread create failed!!!\n");
	}
	else
	{
		printf("start_PhoneGateway_recv_pthread create success!\n");
	}
	pthread_attr_destroy(&Pthread_Attr);
}




void Kcp_Pager_Stream_Recv(unsigned char *buf,int len)
{
	int i;
	unsigned char *paging_multi_buf=buf;

	//判断发送数据的寻呼台MAC是否一致
	int mac_match=1;
	unsigned char pager_mac[6]={0};
	memcpy(pager_mac,paging_multi_buf,6);
	for(i=0;i<6;i++)
	{
		if(pager_mac[i] != pager_property.pager_mac[i])
		{
			mac_match=0;
			break;
		}
	}
	if(!mac_match)
	{
		printf("[Paging_Multi_Server_Data]mac not match\n");
		return;
	}
	
	if( (g_paging_status != PAGING_START) )
	{
		printf("Pager:Exit recv multicastData!!!\n");
		return;
	}
	if(g_paging_timing_count<PAGING_TIMING_COUNT_MAX)
		g_paging_timing_count = 0;
	for(i = 0; i < len-6; i++)
	{
		if(pager_pcm_write>=sizeof(pager_pcm_buf[0]))
		{
			printf("Error:pager_pcm_write>=sizeof(pcm_buf[0]\n");
			break;
		}
		pager_pcm_buf[pager_write_pos][pager_pcm_write++] = paging_multi_buf[6+i];
	}
	pager_pcm_capacity++;

	if(pager_property.decodeType == DECODE_STANDARD_PCM && pager_pcm_capacity >= PAGER_PCM_READ_VALVE)
	{
		pager_pcm_buf_len[pager_write_pos] = pager_pcm_write;
		pager_pcm_buf_valid[pager_write_pos] = 1;
		pager_write_pos++;
		if(pager_write_pos >= PAGER_PCM_BUF_MAX)
			pager_write_pos = 0;
		pager_write_pos_total++;	
		pager_pcm_capacity = 0;
		pager_pcm_write = 0;
	}
	#if SUPPORT_CODEC_G722
	else if(pager_property.decodeType == DECODE_G722 && pager_pcm_capacity >= PAGER_PCM_READ_VALVE)
	{
		if(pager_pcm_write>512)
		{
			printf("Error:pager_pcm_write2 g722>512\r\n");
			pager_pcm_capacity = 0;
			pager_pcm_write = 0;
			return;
		}
		pager_pcm_buf_len[pager_write_pos] = pager_pcm_write;
		pager_pcm_buf_valid[pager_write_pos] = 1;
		pager_write_pos++;
		if(pager_write_pos >= PAGER_PCM_BUF_MAX)
			pager_write_pos = 0;
		pager_write_pos_total++;
		pager_pcm_capacity = 0;
		pager_pcm_write = 0;
	}
	#endif
	#if SUPPORT_CODEC_G7221
	else if(pager_property.decodeType == DECODE_G722_1 && pager_pcm_capacity >= 1)
	{
		pager_pcm_buf_len[pager_write_pos] = pager_pcm_write;
		pager_pcm_buf_valid[pager_write_pos] = 1;
		pager_write_pos++;
		if(pager_write_pos >= PAGER_PCM_BUF_MAX)
			pager_write_pos = 0;
		pager_write_pos_total++;
		pager_pcm_capacity = 0;
		pager_pcm_write = 0;
	}
	#endif
}



void kcp_concentrated_ser_data(unsigned char *buf,int len)
{
	int i=0;
    int cmd_word = buf[0]*256 + buf[1];
	//printf("kcp_concentrated_ser_data:cmd_word=0x%x\n",cmd_word);
	int payload_length=0;

	if(len<9 || cmd_word == 0)
		return;
	payload_length = buf[PAYLOAD_START-2]*256+buf[PAYLOAD_START-1];
	if(payload_length > MAX_BUF_SIZE || payload_length == 0)
	{
		//printf("TTTT11234...\r\n");
		return;
	}
	if(Calculate_XorDat(&buf[PAYLOAD_START], payload_length) != buf[len-1])
	{
		//printf("TTTT34JKJK4323...\r\n");
		return;
	}

	unsigned char *payloadBuf=buf+PAYLOAD_START;

	if(	get_system_source() == SOURCE_LOCAL_PLAY || get_system_source() == SOURCE_TIMING || get_system_source() == SOURCE_FIRE_ALARM ||
		(get_system_source() >= SOURCE_AUDIO_COLLECTOR_BASE && get_system_source() <= SOURCE_AUDIO_COLLECTOR_MAX) )
	{
		if(cmd_word != CMD_HOST_AUDIO_STREAM && cmd_word != CMD_AUDIO_COLLECTOR_STREAM_TCP)
		{
			printf("cmd_word:0x%04x != CMD_HOST_AUDIO_STREAM or CMD_AUDIO_COLLECTOR_STREAM_TCP...\r\n",cmd_word);
			return;
		}

		if(cmd_word == CMD_HOST_AUDIO_STREAM && !(get_system_source() == SOURCE_LOCAL_PLAY || get_system_source() == SOURCE_TIMING || get_system_source() == SOURCE_FIRE_ALARM))
		{
			return;
		}
		else if(cmd_word == CMD_AUDIO_COLLECTOR_STREAM_TCP && !((get_system_source() >= SOURCE_AUDIO_COLLECTOR_BASE && get_system_source() <= SOURCE_AUDIO_COLLECTOR_MAX)) )
		{
			return;
		}

		if(cmd_word == CMD_AUDIO_COLLECTOR_STREAM_TCP && !g_collector_run_flag)
			return;

		if(	concentrated_write_pos_total-concentrated_read_pos_total >= CONCENTRATED_MODE_DATA_BUF_MAX)	//超过限制，舍弃掉部分数据
		{
            printf("write_pos-read_pos>=%d\r\n",CONCENTRATED_MODE_DATA_BUF_MAX);
			concentrated_write_pos = 0;
			concentrated_read_pos = 0;
			concentrated_write_pos_total = 0;
			concentrated_read_pos_total = 0;
			//清掉所有缓冲区
			for(i = 0; i < CONCENTRATED_MODE_DATA_BUF_MAX; i++)
			{
				memset(concentrated_pcm_buf[i], 0x00, sizeof(concentrated_pcm_buf[i]));
				concentrated_pcm_buf_valid[i] = 0;
			}
		}

		if(cmd_word == CMD_HOST_AUDIO_STREAM)
		{
			int currentFramesNum= (buf[2] <<8) + buf[3];
			//printf("currentFramesNum:%d\n",currentFramesNum);
			online_saver_put_frame(currentFramesNum,payloadBuf,payload_length); 
		}

		int threshold_value = 0;
		g_centralized_mode_timeout_count =0;
		g_centralized_mode_timing_repeat = 0;

		if(cmd_word == CMD_AUDIO_COLLECTOR_STREAM_TCP)
		{
			g_ac_timing_count = 0;
			memset(concentrated_pcm_buf[concentrated_write_pos], 0x00, sizeof(concentrated_pcm_buf[concentrated_write_pos]));
			memcpy(&concentrated_pcm_buf[concentrated_write_pos][0], payloadBuf+2, payload_length-2);
			concentrated_pcm_buf_len[concentrated_write_pos] = payload_length-2;
		}
		else
		{
			memset(concentrated_pcm_buf[concentrated_write_pos], 0x00, sizeof(concentrated_pcm_buf[concentrated_write_pos]));
			memcpy(&concentrated_pcm_buf[concentrated_write_pos][0], payloadBuf, payload_length);
			concentrated_pcm_buf_len[concentrated_write_pos] = payload_length;
		}
		concentrated_pcm_buf_valid[concentrated_write_pos] = 1;
		concentrated_write_pos++;
		concentrated_write_pos_total++;
		if(concentrated_write_pos >= CONCENTRATED_MODE_DATA_BUF_MAX)
		{
			concentrated_write_pos = 0;
		}
        //printf("write_pos_total=%d,read_pos_total=%d\r\n",concentrated_write_pos_total,concentrated_read_pos_total);
	}
	else if(cmd_word == CMD_SEND_PCM_DATA_TCP || cmd_word == CMD_WEB_PAGING_STREAM)
	{
		if( cmd_word == CMD_WEB_PAGING_STREAM )
		{
			if( g_paging_status != PAGING_START )
			{
				//计时，超过3秒重新如果还在发，代表非手动停止，那么重新开始寻呼
				pre_web_pager_strem_timeout++;
				if(pre_web_pager_strem_timeout >= 50)
				{
					int pre_cmd_word = pre_web_pager_cmd_pkg[0]*256 + pre_web_pager_cmd_pkg[1];
					int pre_dataLen = pre_web_pager_cmd_pkg[6]*256 + pre_web_pager_cmd_pkg[7];
					if(pre_cmd_word == CMD_WEB_PAGING_NOTIFY)
					{
						//开始
						printf("ready resume web pager!\n");
						pkg_paging_notification(pre_web_pager_cmd_pkg+PAYLOAD_START,pre_dataLen,DEVICE_MODEL_PAGING,1);
					}
				}
			}
			else
			{
				if(pre_web_pager_strem_timeout >= 50)
				{
					pre_web_pager_strem_timeout++;
					if(pre_web_pager_strem_timeout>=100)
					{
						pre_web_pager_strem_timeout=0;
						printf("ready resume recv!\n");
					}
					else
					{
						return;
					}
				}
				else
				{
					pre_web_pager_strem_timeout = 0;
				}
			}
		}
		if(get_system_source() == SOURCE_NET_PAGING)
		{
			//判断寻呼类型（从保留字可以得出，旧寻呼台不支持，判断依据为发起寻呼通知指令是否带上pagingTYpe类型，且为1或者3）
			if(cmd_word == CMD_SEND_PCM_DATA_TCP)
			{
				if(pager_property.pagingType == PAGING_TYPE_MIC || pager_property.pagingType == PAGING_TYPE_MUSIC)
				{
					int new_pagingType= buf[3];
					if( new_pagingType!= pager_property.pagingType && (new_pagingType == PAGING_TYPE_MIC || new_pagingType == PAGING_TYPE_MUSIC) )
					{
						pager_property.pagingType = new_pagingType;

						Clean_Pager_Pcm_Buf();
						printf("new PagingType=%d\r\n",new_pagingType);
					}
				}
			}
			Kcp_Pager_Stream_Recv(payloadBuf,payload_length);
		}
	}
	//混音器音频流
	else if(cmd_word == CMD_AUDIO_MIXER_STREAM)
	{
		//printf("KCP:CMD_AUDIO_MIXER_STREAM...\n");
		if(get_system_source() != SOURCE_AUDIO_MIXED)
		{
			return;
		}
		//判断发送数据的MAC是否一致
		int mac_match=1;
		unsigned char mixer_mac[6]={0};
		memcpy(mixer_mac,buf+PAYLOAD_START,6);
		for(i=0;i<6;i++)
		{
			if(mixer_mac[i] != g_audio_mixer_mac[i])
			{
				mac_match=0;
				break;
			}
		}
		if(!mac_match)
		{
			return;
		}

		//保存音频编码
		g_audio_mixer_codecs = buf[PAYLOAD_START+6];

		//判断混音信号类型
		int new_signalType= buf[PAYLOAD_START+7];
		if(new_signalType!= g_audio_mixer_signalType)
		{
			//如果原来不是MIC信号，现在有MIC信号了，那么需要清除缓冲区，确保最低时延
			if(new_signalType == MIXER_SIGNAL_TYPE_MIC)
			{
				Clean_Mixed_Info();
			}
			g_audio_mixer_signalType = new_signalType;
			printf("g_audio_mixer_signalType=%d\n",new_signalType);
		}

		audioMixer_stream_len[audioMixer_stream_write_pos] = payload_length-8;
		memcpy(audioMixer_stream_buf[audioMixer_stream_write_pos],buf+PAYLOAD_START+8,audioMixer_stream_len[audioMixer_stream_write_pos]);
		g_audio_mixer_stream_timing_count = 0;
		//printf("write_pos=%d,len=%d\n",audioMixer_stream_write_pos,concentrated_pcm_buf_len[concentrated_write_pos]);
		audioMixer_stream_valid[audioMixer_stream_write_pos] = 1;
		audioMixer_stream_write_pos++;
		audioMixer_stream_write_pos_total++;
		if(audioMixer_stream_write_pos >= AUDIO_MIXER_STREAM_BUF_MAX)
		{
			audioMixer_stream_write_pos = 0;
		}
	}
	//电话网关音频流
	else if(cmd_word == CMD_PHONE_GATEWAY_STREAM)
	{
		//printf("KCP:CMD_PHONE_GATEWAY_STREAM...\n");
		if(get_system_source() != SOURCE_PHONE_GATEWAY)
		{
			return;
		}
		//判断发送数据的MAC是否一致
		int mac_match=1;
		unsigned char phone_mac[6]={0};
		memcpy(phone_mac,buf+PAYLOAD_START,6);
		for(i=0;i<6;i++)
		{
			if(phone_mac[i] != g_phone_gateway_mac[i])
			{
				mac_match=0;
				break;
			}
		}
		if(!mac_match)
		{
			return;
		}

		//保存音频编码
		g_phone_gateway_codecs = buf[PAYLOAD_START+6];

		phoneGateway_stream_len[phoneGateway_stream_write_pos] = payload_length-8;
		memcpy(phoneGateway_stream_buf[phoneGateway_stream_write_pos],buf+PAYLOAD_START+8,phoneGateway_stream_len[phoneGateway_stream_write_pos]);
		g_phone_gateway_stream_timing_count = 0;
		//printf("write_pos=%d,len=%d\n",phoneGateway_stream_write_pos,phoneGateway_stream_len[phoneGateway_stream_write_pos]);
		phoneGateway_stream_valid[phoneGateway_stream_write_pos] = 1;
		phoneGateway_stream_write_pos++;
		phoneGateway_stream_write_pos_total++;
		if(phoneGateway_stream_write_pos >= PHONE_GATEWAY_STREAM_BUF_MAX)
		{
			phoneGateway_stream_write_pos = 0;
		}
	}
}