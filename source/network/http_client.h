#ifndef _HTTP_CLIENT_H_
#define _HTTP_CLIENT_H_

#include "../thirdParty/my_md5.h"

/*********************************************************************
 * MACROS
 */

#define UPDATE_SAVE_PATH           "/customer/App/Update.temp"
#define UPDATE_REAL_PATH           "/customer/App/Update.tar.gz"

enum
{
    UPDATE_TYPE_APP=0,
    UPDATE_TYPE_BP1048,
    UPDATE_TYPE_DISP
};


#define	HTTP_DEBUG            0
#define MAX_RECV_SIZE         32*1024                             /*下载缓存*/
#define TIMEOUT               10                                  /*获取服务器端数据等待超时时间设置，单位为妙*/

#define START_DOWNLOAD        0x01                                /*开始下载*/
#define FIRMWARE_NEWEST       0x02                                /*本地固件已是最新版本*/
#define CONNECT_TIME_OUT      0x03                                /*连接服务器超时*/
#define UPDATE_FAIL           0x04                                /*升级失败*/
#define UPDATE_SUCCEED        0x05                                /*升级成功*/



extern unsigned char g_download_path[256];      /*固件下载到本地的路径*/
extern unsigned char g_url_buf[256];            /*固件在服务器上的路径*/
extern unsigned char g_buf_send[1024];        /*发送数据暂存区*/
extern int g_update_flag;                   /*0-没升级，1-正在升级*/
extern int g_update_type;			//0-app 1:BP1048 2:Disp Module
extern int g_download_progress;             /*下载进度*/
extern unsigned char g_web_server_ip[64];
extern unsigned int g_web_server_port;
extern char upgrade_file_md5_str[MD5_STR_LEN+1];


extern unsigned char *firmware_download_buffer; //下载BP1048数据缓存
extern unsigned int firmware_download_length;		//下载大小
extern unsigned char firmaware_versionNoV[16];

/*********************************************************************
 * LOCAL FUNCTIONS
 */
static void Http_Send(int sockfd, char *send_buf, int len, int flags);
static unsigned long Get_ContentLength(char *rev_buf);
void *Http_Load_File(void);
static void Comb_Request_Package(char *url);
static unsigned long Get_File_Size(const char *path);
void start_download_file_pthread(void);

#endif /*_HTTP_CLIENT_H_*/

