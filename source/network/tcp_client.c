/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-16 12:54:42 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-09-16 18:04:18
 */
#include "sysconf.h"
#include "network_protocol.h"
#include "network_process.h"
#include <errno.h>
#include "tcp_client.h"
#include "network/multicast.h"
#include "kcp/mkcp.h"

#define TCP_RECV_TIMEOUT 50000

/*****************    本地变量声明    ******************/
static _st_tcp_info st_master_tcp_info;		//主服务器TCP信息
static _st_tcp_info st_slave_tcp_info;		//备用服务器TCP信息

int tcp_connect_init()
{
	/*********主服务器信息*******/
	st_master_tcp_info.tcp_connect_status = 0;			//与主机的TCP连接状态，1-连接成功，0-连接失败
	st_master_tcp_info.tcp_exit_flag = 0xff;			//退出TCP标志:0代表TCP线程正常工作，1-需要关闭，0xFF-没有启动
	st_master_tcp_info.tcp_reconnect_flag=0;			//TCP重连标志,1-需要重连，0-不需要重连
	memset(&st_master_tcp_info.tcp_client_addr,0,sizeof(st_master_tcp_info.tcp_client_addr));	//tcp客户端连接地址
	st_master_tcp_info.tcp_sockfd=-1;					//tcp客户端句柄
	memset(&st_master_tcp_info.tcp_pkg_info,0,sizeof(st_master_tcp_info.tcp_pkg_info));	//tcp包信息
	st_master_tcp_info.tcp_clinet_fr=0;    				//tcp first response

#if IS_DEVICE_AUDIO_MIXER
	/*********备用服务器信息*******/
	st_slave_tcp_info.tcp_connect_status = 0;			//与主机的TCP连接状态，1-连接成功，0-连接失败
	st_slave_tcp_info.tcp_exit_flag = 0xff;			//退出TCP标志:0代表TCP线程正常工作，1-需要关闭，0xFF-没有启动
	st_slave_tcp_info.tcp_reconnect_flag=0;			//TCP重连标志,1-需要重连，0-不需要重连
	memset(&st_slave_tcp_info.tcp_client_addr,0,sizeof(st_slave_tcp_info.tcp_client_addr));	//tcp客户端连接地址
	st_slave_tcp_info.tcp_sockfd=-1;					//tcp客户端句柄
	memset(&st_slave_tcp_info.tcp_pkg_info,0,sizeof(st_slave_tcp_info.tcp_pkg_info));	//tcp包信息
	st_slave_tcp_info.tcp_clinet_fr=0;    				//tcp first response
#endif
}


int tcp_get_master_connect_status()
{
	return st_master_tcp_info.tcp_connect_status;
}

int tcp_get_master_client_fr_flag()
{
	return st_master_tcp_info.tcp_clinet_fr;
}

void tcp_set_master_client_fr_flag(int fr_flag)
{
	st_master_tcp_info.tcp_clinet_fr=fr_flag;
}

#if IS_DEVICE_AUDIO_MIXER
int tcp_get_slave_connect_status()
{
	return st_slave_tcp_info.tcp_connect_status;
}
int tcp_get_slave_client_fr_flag()
{
	return st_slave_tcp_info.tcp_clinet_fr;
}
void tcp_set_slave_client_fr_flag(int fr_flag)
{
	st_slave_tcp_info.tcp_clinet_fr=fr_flag;
}
#endif

void tcp_client_exit()
{
	if(st_master_tcp_info.tcp_exit_flag!=0xff)
	{
		st_master_tcp_info.tcp_exit_flag=1;
	}
	#if IS_DEVICE_AUDIO_MIXER
	if(st_slave_tcp_info.tcp_exit_flag!=0xff)
	{
		st_slave_tcp_info.tcp_exit_flag=1;
	}
	#endif
}

void tcp_client_reconnect()
{
	if(st_master_tcp_info.tcp_exit_flag == 0)
	{
		exit_kcp_recv_thread();
		//重连标志一定要放到下面，否则可能导致死锁（因为连接上会立即再次调用exit_kcp_recv_thread函数）
		st_master_tcp_info.tcp_reconnect_flag=1;
	}
	#if IS_DEVICE_AUDIO_MIXER
	if(st_slave_tcp_info.tcp_exit_flag == 0)
	{
		//重连标志一定要放到下面，否则可能导致死锁（因为连接上会立即再次调用exit_kcp_recv_thread函数）
		st_slave_tcp_info.tcp_reconnect_flag=1;
	}
	else if(st_slave_tcp_info.tcp_exit_flag == 0xff)
	{
		TCP_Client_Slave_Start();
	}
	#endif
}

void host_tcp_send_data(unsigned char *send_buf, int send_len)
{
	if(send_len < PACKAGE_MIN_SIZE)
		return;
	if(g_network_mode == NETWORK_MODE_WAN)
	{
		if(st_master_tcp_info.tcp_connect_status || st_slave_tcp_info.tcp_connect_status)
		{
			// 获取两个字节的命令字
			int cmd_word=(send_buf[0]<<8) + send_buf[1];
			//printf("Send TCP CMD_WORD = 0x%04x\n", cmd_word);
			if(st_master_tcp_info.tcp_connect_status)
			{
				sendto(st_master_tcp_info.tcp_sockfd, send_buf, send_len, 0, (struct sockaddr *)&st_master_tcp_info.tcp_client_addr,
					sizeof(st_master_tcp_info.tcp_client_addr));
			}
			if(st_slave_tcp_info.tcp_connect_status)
			{
				sendto(st_slave_tcp_info.tcp_sockfd, send_buf, send_len, 0, (struct sockaddr *)&st_slave_tcp_info.tcp_client_addr,
					sizeof(st_slave_tcp_info.tcp_client_addr));
			}
			//如果是音频混音器，通讯数据发送双份(编码+解码，以便服务器更新)
			#if IS_DEVICE_AUDIO_MIXER
					//上线和离线信息因为处理了发送函数，函数内手动发送两份，所以此处不需要再次转换型号后再次发送
					//发送混音器码流只能从编码器发送，不要转换型号
					if(cmd_word!=CMD_TCP_CLIENT_CONNECTED && cmd_word!=CMD_AUDIO_MIXER_STREAM && cmd_word!=CMD_ONLINE && cmd_word!=CMD_OFFLINE 
						&& cmd_word!=CMD_CLIENT_POWER_ON_NOTICE_HOST && cmd_word!=CMD_AUDIO_MIXER_CONFIG && cmd_word!=CMD_HOST_SET_AUDIO_MIXER_SOURCE)
					{
						unsigned char deviceType=send_buf[4];
						if(send_buf[4] == MODEL_AUDIO_MIXER_DECODER)
						{
							send_buf[4] = MODEL_AUDIO_MIXER_ENCODER;
						}
						else if(send_buf[4] == MODEL_AUDIO_MIXER_ENCODER)
						{
							send_buf[4] = MODEL_AUDIO_MIXER_DECODER;
						}

						if(send_buf[4] == MODEL_AUDIO_MIXER_DECODER_C)
						{
							send_buf[4] = MODEL_AUDIO_MIXER_ENCODER_C;
						}
						else if(send_buf[4] == MODEL_AUDIO_MIXER_ENCODER_C)
						{
							send_buf[4] = MODEL_AUDIO_MIXER_DECODER_C;
						}

						printf("host_tcp_send_data:CMD=0x%x\n",cmd_word);
						if(st_master_tcp_info.tcp_connect_status)
						{
							sendto(st_master_tcp_info.tcp_sockfd, send_buf, send_len, 0, (struct sockaddr *)&st_master_tcp_info.tcp_client_addr,
								sizeof(st_master_tcp_info.tcp_client_addr));
						}
						if(st_slave_tcp_info.tcp_connect_status)
						{
							sendto(st_slave_tcp_info.tcp_sockfd, send_buf, send_len, 0, (struct sockaddr *)&st_slave_tcp_info.tcp_client_addr,
								sizeof(st_slave_tcp_info.tcp_client_addr));
						}
						
						send_buf[4] = deviceType;
					}
			#endif
#if 0
			if (txlen <= 0)
			{
				perror("host_tcp_send_data");
				printf("ERROR:cmd_word = 0x%04x\n", cmd_word);
			}
#endif
		}
	}
}


static void reset_tcp_client_pkg(_st_tcp_info *tcp_info)
{
	memset(&tcp_info->tcp_pkg_info,0,sizeof(tcp_info->tcp_pkg_info));
}


static void tcp_pkg_handle(_st_tcp_info *tcp_info,int recvLen,unsigned char *recvBuf)
{
	bool isMasterTcp= (tcp_info == &st_master_tcp_info)?true:false;
    int	pos = 0;
    int iLength = recvLen;
    unsigned char *pData=recvBuf;
    //printf("tcp_recv_len1=%d\n",iLength);
    while(pos < iLength && !tcp_info->tcp_exit_flag && !tcp_info->tcp_reconnect_flag)
    {
        if(tcp_info->tcp_pkg_info.pkg_len + (iLength - pos) <= 8) // 原有的数据+收到的数据都不够包头长度
        {
            memcpy(tcp_info->tcp_pkg_info.pkg_data + tcp_info->tcp_pkg_info.pkg_len, pData + pos, iLength - pos);
            tcp_info->tcp_pkg_info.pkg_len += (iLength - pos);
            pos += iLength - pos;
        }
        else
        {
            if(tcp_info->tcp_pkg_info.pkg_len < 8) // 原有的数据不够包头长，所以要构成包头
            {
                memcpy(tcp_info->tcp_pkg_info.pkg_data + tcp_info->tcp_pkg_info.pkg_len, pData + pos, 8-tcp_info->tcp_pkg_info.pkg_len);
                pos += 8-tcp_info->tcp_pkg_info.pkg_len;
                tcp_info->tcp_pkg_info.pkg_len = 8; // 包头
            }
            unsigned int dataLen = (*(tcp_info->tcp_pkg_info.pkg_data + 6))*256 + (*(tcp_info->tcp_pkg_info.pkg_data + 7)); // 数据长度（+6位置是固定的）
            unsigned int idleLen	= dataLen - (tcp_info->tcp_pkg_info.pkg_len - 8) + 1;	// 一个包剩余的长度,+1是检验位

            if(iLength - pos < idleLen) // 收到的数据部分凑不够完整的包
            {
                memcpy(tcp_info->tcp_pkg_info.pkg_data+tcp_info->tcp_pkg_info.pkg_len, pData + pos, iLength - pos);
                tcp_info->tcp_pkg_info.pkg_len += iLength - pos;
                pos += iLength - pos;
            }
            else	// 可以构成完整的包
            {
                memcpy(tcp_info->tcp_pkg_info.pkg_data+tcp_info->tcp_pkg_info.pkg_len, pData + pos, idleLen);
                tcp_info->tcp_pkg_info.pkg_len += idleLen;
                pos += idleLen;

                unsigned int command = (tcp_info->tcp_pkg_info.pkg_data[0]<<8)+tcp_info->tcp_pkg_info.pkg_data[1];
                //printf("tcp:recv command=0x%04x\n",command);
                //network_pkg_process(tcp_info->tcp_pkg_info.pkg_data,tcp_info->tcp_pkg_info.pkg_len);
				#if IS_DEVICE_AUDIO_MIXER
				//20230906如果不是主连接且当前主TCP连接的状态为已连接，那么只响应备用服务器特定的指令；如果需要同时受控，则可以取消此限制。
				if(!isMasterTcp && st_master_tcp_info.tcp_connect_status)
				{
					if(! (command == CMD_AUDIO_MIXER_CONFIG || command == CMD_CONTROL_REBOOT || command == CMD_HOST_QUERY_SET_NET_WORK_MODE || 
						command == CMD_HOST_QUERY_SET_IP_INFO || command == CMD_UPDATE_INFO || command == CMD_RATE_PROGRESS ||
						command == CMD_SET_DEVICE_ALIAS || command == CMD_CONTROL_FORMAT) )
					{
						printf("Tcp Command=0x%04x:Not Master Server and Master Server connected!\n",command);
						tcp_info->tcp_pkg_info.pkg_len = 0; // 构成一个包后，重置为0，准备下一个包
						continue;
					}
				}
				#endif
				NetPkg_Add(NET_TYPE_TCP_SERVER,tcp_info->tcp_pkg_info.pkg_data,tcp_info->tcp_pkg_info.pkg_len);
                tcp_info->tcp_pkg_info.pkg_len = 0; // 构成一个包后，重置为0，准备下一个包
            }
        }
    }
}


/*
Linux下常见的socket错误码：
EACCES, EPERM：用户试图在套接字广播标志没有设置的情况下连接广播地址或由于防火墙策略导致连接失败。
EADDRINUSE 98：Address already in use（本地地址处于使用状态）
EAFNOSUPPORT 97：Address family not supported by protocol（参数serv_add中的地址非合法地址）
EAGAIN：没有足够空闲的本地端口。
EALREADY 114：Operation already in progress（套接字为非阻塞套接字，并且原来的连接请求还未完成）
EBADF 77：File descriptor in bad state（非法的文件描述符）
ECONNREFUSED 111：Connection refused（远程地址并没有处于监听状态）
EFAULT：指向套接字结构体的地址非法。
EINPROGRESS 115：Operation now in progress（套接字为非阻塞套接字，且连接请求没有立即完成）
EINTR：系统调用的执行由于捕获中断而中止。
EISCONN 106：Transport endpoint is already connected（已经连接到该套接字）
ENETUNREACH 101：Network is unreachable（网络不可到达）
ENOTSOCK 88：Socket operation on non-socket（文件描述符不与套接字相关）
ETIMEDOUT 110：Connection timed out（连接超时）
*/

void * tcp_client_communication(void * arg)
{
	_st_tcp_info *tcp_info = (_st_tcp_info *)arg;
	bool isMasterTcp= (tcp_info == &st_master_tcp_info)?true:false;
	printf("tcp_client_communication:isMasterTcp=%d\n",isMasterTcp);
	
	int i;
	int ret = -1;
	fd_set readfd;
	int pkg_count = 0;
	struct timeval timeout;
	unsigned long count = 0;

	tcp_info->tcp_exit_flag=0;
	tcp_info->tcp_reconnect_flag=0;
	tcp_info->tcp_connect_status=0;
		/*套接字信息赋值*/
	memset(&tcp_info->tcp_client_addr,0,sizeof(struct sockaddr_in));
	tcp_info->tcp_client_addr.sin_family = AF_INET; // IPV4

	//默认第一个tcp地址
	g_current_connect_tcp_id=1;
	bool isSlaveTcpAddrValid = false;
	if((if_a_string_is_a_valid_ipv4_address(g_host_tcp_addr_domain2) || is_valid_domain(g_host_tcp_addr_domain2)) && g_host_tcp_port2>0)
	{
		isSlaveTcpAddrValid = true;
	}
	while( g_network_mode == NETWORK_MODE_WAN && !tcp_info->tcp_exit_flag )
	{
		#if IS_DEVICE_AUDIO_MIXER
			char *tcp_addr_domian=(isMasterTcp)?g_host_tcp_addr_domain:g_host_tcp_addr_domain2;
			int tcp_port=(isMasterTcp)?g_host_tcp_port:g_host_tcp_port2;

			struct hostent *host = gethostbyname(tcp_addr_domian);
			if(host == NULL) {
				printf("DNS resolve failed for %s\n", tcp_addr_domian);
				sleep(3);
				continue;
			}
			memset(&tcp_info->tcp_client_addr,0,sizeof(struct sockaddr_in));
			tcp_info->tcp_client_addr.sin_family = AF_INET;
			memcpy(&tcp_info->tcp_client_addr.sin_addr, host->h_addr_list[0], host->h_length);
			tcp_info->tcp_client_addr.sin_port = htons(tcp_port);
		#else
			char *tcp_addr_domian=(g_current_connect_tcp_id == 1)?g_host_tcp_addr_domain:g_host_tcp_addr_domain2;
			int tcp_port=(g_current_connect_tcp_id == 1)?g_host_tcp_port:g_host_tcp_port2;

			struct hostent *host = gethostbyname(tcp_addr_domian);
			if(host == NULL) {
				if(isSlaveTcpAddrValid)
				{
					if(g_current_connect_tcp_id == 1)
					{
						g_current_connect_tcp_id=2;
						printf("DNS resolve failed for %s,try to connect TCP server2!\n", tcp_addr_domian);
					}
					else if(g_current_connect_tcp_id == 2)
					{
						g_current_connect_tcp_id=1;
						printf("DNS resolve failed for %s,try to connect TCP server1!\n", tcp_addr_domian);
					}
				}
				else
				{
					printf("DNS resolve failed for %s\n", tcp_addr_domian);
				}
				sleep(3);
				continue;
			}
			//将解析后的地址给到tcp_info->tcp_client_addr
			memset(&tcp_info->tcp_client_addr,0,sizeof(struct sockaddr_in));
			tcp_info->tcp_client_addr.sin_family = AF_INET;
			memcpy(&tcp_info->tcp_client_addr.sin_addr, host->h_addr_list[0], host->h_length);
			tcp_info->tcp_client_addr.sin_port = htons(tcp_port);
			//打印解析后的地址
			char ipAddress[INET_ADDRSTRLEN];
			inet_ntop(AF_INET, &tcp_info->tcp_client_addr.sin_addr,
				ipAddress, INET_ADDRSTRLEN);
			printf("tcp_client_communication:DNS resolve success for %s,ipAddress=%s,port=%d\n", tcp_addr_domian,ipAddress,tcp_port);
		#endif

		//将解析后的地址赋值给g_host_tcp_prase_ipAddress
		inet_ntop(AF_INET, &tcp_info->tcp_client_addr.sin_addr, 
			g_host_tcp_prase_ipAddress, INET_ADDRSTRLEN);

		tcp_info->tcp_connect_status=0;
		tcp_info->tcp_exit_flag=0;
		reset_tcp_client_pkg(tcp_info);
		/*创建套接字，SERVER_IPV4，TCP*/
		tcp_info->tcp_sockfd = socket(AF_INET, SOCK_STREAM, 0);
		if (tcp_info->tcp_sockfd >= 0)
		{
			printf("tcp_client_communication:Creat Socket Succeed!\n");
		}
		else
		{
			perror("tcp_client_communication:socket failed,exit thread!\n");
			close(tcp_info->tcp_sockfd);
			tcp_info->tcp_exit_flag=0xff;
			pthread_exit(NULL);
		}


		//设置为非阻塞
		unsigned long ul = 1;
        ioctl(tcp_info->tcp_sockfd, FIONBIO, &ul); //设置为非阻塞模式
#if 0
		int nRecvBuf=128*1024;//设置为128KB
		setsockopt(tcp_info->tcp_sockfd,SOL_SOCKET,SO_RCVBUF,(const char*)&nRecvBuf,sizeof(int));
#endif
		//connect会立即返回，可能返回成功，也可能返回失败。如果连接的服务器在同一台主机上，那么在调用connect 建立连接时，连接通常会立即建立成功。
		int conn = connect(tcp_info->tcp_sockfd, (struct sockaddr *)&tcp_info->tcp_client_addr, sizeof(struct sockaddr_in));
		if (conn == 0) {
			printf("Socket Connect Success Immediately.\n");
		}
		else 
		{
			printf("Get The Connect Result by select().\n");
			int connect_flag=0;
			if (errno == EINPROGRESS)
			{
				fd_set rfds, wfds;
				struct timeval tv;

				FD_ZERO(&rfds);
				FD_ZERO(&wfds);
				FD_SET(tcp_info->tcp_sockfd, &rfds);
				FD_SET(tcp_info->tcp_sockfd, &wfds);

				/* set select() time out */
				tv.tv_sec = 4;
				tv.tv_usec = 0;
				int selres = select(tcp_info->tcp_sockfd + 1, &rfds, &wfds, NULL, &tv);
				switch (selres)
				{
					case -1:
						printf("select error\n");
						ret = -1;
						break;
					case 0:
						printf("select time out\n");
						ret = -1;
						break;
					default:
					if (FD_ISSET(tcp_info->tcp_sockfd, &rfds) || FD_ISSET(tcp_info->tcp_sockfd, &wfds))
					{
						if( connect(tcp_info->tcp_sockfd, (struct sockaddr *)&tcp_info->tcp_client_addr, sizeof(struct sockaddr_in)) !=0 )
						{
							int err = errno;
							if (err == EISCONN)
							{
								printf("TCP:Connect Succeed1!\n");
								ret = 0;
							}
							else
							{
								printf("TCP:connect failed,errno = %d\n", errno);
								ret = errno;
							}
						}
						else
						{
							printf("TCP:Connect Succeed2:isMasterTcp=%d!\n",isMasterTcp);
							ret = 0;
						}
					}
					else
					{
						printf("haha\n");
					}
					break;
				}
				if ( ret != 0)
				{
					if(ret == ECONNREFUSED)
					{
						int count=25;		//5s
						while( count-- && !tcp_info->tcp_exit_flag )
						{
							usleep(200000);
						}
					}
				}
				else
				{
					connect_flag=1;
				}
			}

			if(!connect_flag)
			{
				printf("tcp_client_communication:Connect failed,retry...\n");
				if(tcp_info->tcp_exit_flag)
				{
					printf("need exit_tcp,close tcp thread.\n");
				}
				else
				{
					close(tcp_info->tcp_sockfd);
				}

				#if !IS_DEVICE_AUDIO_MIXER
				//切换TCP主备连接
				if(g_current_connect_tcp_id == 1)
				{
					//备用服务器的IP是合法的IPV4地址，且端口号有效，那么切换到备用服务器
					if(isSlaveTcpAddrValid)
					{
						g_current_connect_tcp_id=2;
						printf("Try tcp_id=2!\n");
					}
				}
				else if(g_current_connect_tcp_id == 2)
				{
					g_current_connect_tcp_id=1;
					printf("Try tcp_id=1!\n");
				}
				#endif

				sleep(1);
				continue;
			}
		}

		if( tcp_info->tcp_exit_flag || g_network_mode == NETWORK_MODE_LAN) //模式改变或者需要退出
		{
			printf("tcp_info->tcp_exit_flag || g_network_mode == NETWORK_MODE_LAN,exit\n");
			continue;
		}

		if(strncmp(g_host_tcp_prase_ipAddress,"192",3) && strncmp(g_host_tcp_prase_ipAddress,"169",3) && strncmp(g_host_tcp_prase_ipAddress,"127",3) && strncmp(g_host_tcp_prase_ipAddress,"10.",3) && strncmp(g_host_tcp_prase_ipAddress,"29.",3) && strncmp(g_host_tcp_prase_ipAddress,"172",3) && strncmp(g_host_tcp_prase_ipAddress,"148.12",6) )	//既不是192开头也不是169、127、10、29开头，代表是公网IP
		{
			g_Is_tcp_real_internet=1;
		}
		else
		{
			g_Is_tcp_real_internet=0;
		}

		//已经连接上，重新设置为阻塞模式
		ul = 0;
        ioctl(tcp_info->tcp_sockfd, FIONBIO, &ul);

		tcp_info->tcp_reconnect_flag=0;
		while(g_network_mode == NETWORK_MODE_WAN && !tcp_info->tcp_exit_flag)
		{
			if(tcp_info->tcp_reconnect_flag)
			{
				g_current_connect_tcp_id=1;	//默认第一个tcp地址
				tcp_info->tcp_connect_status=0;
				close(tcp_info->tcp_sockfd);
				break;
			}

			//开始读数据
			timeout.tv_sec = 0; //获取数据超时时间设置
			timeout.tv_usec = TCP_RECV_TIMEOUT;

			FD_ZERO(&readfd); //清空读文件描述集合
			FD_SET(tcp_info->tcp_sockfd, &readfd); //注册套接字文件描述符


			ret = select(tcp_info->tcp_sockfd+1, &readfd, NULL, NULL, &timeout);

			int connect_error=0;
			switch(ret)
			{
				case -1 : //调用出错
					perror("tcp:select error,reconnect");
					tcp_info->tcp_reconnect_flag=1;
					tcp_info->tcp_connect_status=0;
					connect_error=1;
					close(tcp_info->tcp_sockfd);
					break;

				case 0 : //超时
					//perror("TCP select TimeOut");
					//reset_tcp_client_pkg();
					break;

				default : //有数据可读 - 需要考虑粘包情况
					if (FD_ISSET(tcp_info->tcp_sockfd, &readfd))
					{
						/*读取数据*/
						unsigned char recvBuf[MAX_BUF_SIZE]={0};
						int recv_len = recv(tcp_info->tcp_sockfd, recvBuf, MAX_BUF_SIZE, 0);
						//printf("TCP recv_len=%d\n",recv_len);
						if( recv_len == 0 || recv_len == -1 )
						{
							//断开连接，重连
							printf("tcp_recv_error,reconnect\n");
							connect_error=1;
							tcp_info->tcp_reconnect_flag=1;
							tcp_info->tcp_connect_status=0;

							//如果设备正在播放歌曲的时候TCP断开了，应该立即停止播放
							#if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_AUDIO_MIXER)
							if(isMasterTcp)
							{
								if( g_media_source != SOURCE_NULL && g_media_source != SOURCE_AUX && g_media_source != SOURCE_100V_INPUT )
								{
									printf("tcp disconnect,terminal play!\r\n");
									Set_zone_idle_status(NULL,  __func__, __LINE__,true);
								}
							}
							#endif

							int count=30;		//3s
							while( count-- && !tcp_info->tcp_exit_flag )
							{
								usleep(100000);
							}

							break;
						}
						else
							tcp_pkg_handle(tcp_info,recv_len,recvBuf);
					}
					break;
			}
			if(connect_error == 0 && tcp_info->tcp_connect_status == 0)
			{
				tcp_info->tcp_connect_status=1;
				tcp_info->tcp_clinet_fr=0;
				#if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_AUDIO_MIXER)
				if(isMasterTcp)
					mkcp_init();
				#endif
				usleep(100000);
				send_online_info();
			}
		}
	}
	close(tcp_info->tcp_sockfd);
	tcp_info->tcp_connect_status=0;
	tcp_info->tcp_exit_flag=0xff;

	exit_kcp_recv_thread();

	printf("TCP thread closed...\n");
	pthread_exit(NULL);
}



void TCP_Client_Start()
{
	int ret=-1;
	pthread_t tcp_master_pthread;
	pthread_attr_t tcp_pthread_attr;
	pthread_attr_init(&tcp_pthread_attr);
	pthread_attr_setdetachstate(&tcp_pthread_attr, PTHREAD_CREATE_DETACHED);

	// Create Thread for master connection
	ret = pthread_create(&tcp_master_pthread, &tcp_pthread_attr, (void *)tcp_client_communication, (void *)&st_master_tcp_info);
	if (ret < 0)
	{
		perror("create tcp_master_pthread");
	}
	else
	{
		 printf("create tcp_master_pthread success!\n");
	}
	pthread_attr_destroy(&tcp_pthread_attr);

	#if IS_DEVICE_AUDIO_MIXER
	TCP_Client_Slave_Start();
	#endif
}


#if IS_DEVICE_AUDIO_MIXER

void TCP_Client_Slave_Start()
{
	int ret=-1;
	pthread_t tcp_slave_pthread;
	pthread_attr_t tcp_pthread_attr;
	pthread_attr_init(&tcp_pthread_attr);
	pthread_attr_setdetachstate(&tcp_pthread_attr, PTHREAD_CREATE_DETACHED);

	//判断备用服务器是否设置有效，且备用服务器和主服务器不相同的话，才去连接备用服务器
	if( (g_host_tcp_port2>0 && (if_a_string_is_a_valid_ipv4_address(g_host_tcp_addr_domain2) || is_valid_domain(g_host_tcp_addr_domain2)) ) &&\
		(strcmp(g_host_tcp_addr_domain,g_host_tcp_addr_domain2) || g_host_tcp_port!=g_host_tcp_port2) )	//IP不相同或者端口不相同才认为有效，否则认定是同一服务器
	{
		// Create Thread for slave connection
		ret = pthread_create(&tcp_slave_pthread, &tcp_pthread_attr, tcp_client_communication, (void *)&st_slave_tcp_info);
		if (ret < 0)
		{
			perror("create tcp_slave_pthread");
		}
		else
		{
			printf("create tcp_slave_pthread success!\n");
		}
	}
	else
	{
		printf("Slave Server not specify,don't create slave tcp_client_communication!\n");
	}

	pthread_attr_destroy(&tcp_pthread_attr);
}


#endif