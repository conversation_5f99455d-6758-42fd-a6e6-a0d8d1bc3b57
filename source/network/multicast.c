/*
 * @Author: <PERSON><PERSON>.<PERSON> 
 * @Date: 2021-09-06 14:19:32 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-09-16 17:52:10
 */

#include "sysconf.h"
#include "network_protocol.h"
#include "network_process.h"
#include "udp_client.h"
#include "../kcp/mkcp.h"
#include "multicast.h"
#if SUPPORT_SIP
#include "../pjsip/appConfig.h"
#endif

static int multicast_send_socket=-1;
static int multicast_send_netTools_socket=-1;	//新增，原来和multicast_send_socket是一样的
static struct sockaddr_in multi_send_sockAddr;
static struct sockaddr_in multi_send_netTools_sockAddr;	//新增，原来和multi_send_sockAddr是一样的

struct sockaddr_in multi_host_addr;

static int multicast_sockfd = -1;

/*********************************************************************
 * @fn      multicast_send_data
 *
 * @brief  	发送组播数据
 *
 * @param   data--数据缓冲区
 * 			len--数据长度
 *
 * @return  none
*********************************************************************/
void multicast_send_data(unsigned char * data,unsigned short len)  
{
   if(multicast_send_socket == -1)
   {
       multicast_send_socket = socket(AF_INET, SOCK_DGRAM, 0);
	   	//发送组播的socket需要先绑定网卡（如果存在4G网卡，那么此处必须）
		if (setsockopt(multicast_send_socket, SOL_SOCKET,SO_BINDTODEVICE, (char *)&nif_eth0, sizeof(nif_eth0)) < 0)
		{
			perror("multicast_send_socket bind interface fail");
		}
       memset (&multi_send_sockAddr, 0, sizeof(multi_send_sockAddr));
       multi_send_sockAddr.sin_family = AF_INET;
       multi_send_sockAddr.sin_port = htons (MCAST_CMD_SEND_PORT);
       inet_pton(AF_INET, MCAST_CMD_SEND_ADDR, &multi_send_sockAddr.sin_addr);
   }
   if(multicast_send_netTools_socket == -1)
   {
       multicast_send_netTools_socket = socket(AF_INET, SOCK_DGRAM, 0);
	   	//发送组播的socket需要先绑定网卡（如果存在4G网卡，那么此处必须）
		if (setsockopt(multicast_send_netTools_socket, SOL_SOCKET,SO_BINDTODEVICE, (char *)&nif_eth0, sizeof(nif_eth0)) < 0)
		{
			perror("multicast_send_netTools_socket bind interface fail");
		}
       memset (&multi_send_netTools_sockAddr, 0, sizeof(multi_send_netTools_sockAddr));
       multi_send_netTools_sockAddr.sin_family = AF_INET;
       multi_send_netTools_sockAddr.sin_port = htons (MCAST_NETTOOLS_CMD_SEND_PORT);
       inet_pton(AF_INET, MCAST_NETTOOLS_CMD_SEND_ADDR, &multi_send_netTools_sockAddr.sin_addr);
   }

   int sendLen=sendto(multicast_send_socket, data, len, 0,(struct sockaddr*)&multi_send_sockAddr, sizeof(multi_send_sockAddr));
   sendLen=sendto(multicast_send_netTools_socket, data, len, 0,(struct sockaddr*)&multi_send_netTools_sockAddr, sizeof(multi_send_netTools_sockAddr));
   //如果是音频混音器，通讯数据发送双份(编码+解码，以便服务器更新)
   #if IS_DEVICE_AUDIO_MIXER
   		if(len >= PACKAGE_MIN_SIZE)
		{
			int cmd_word = (data[0]<<8) + data[1];
			if(cmd_word!=CMD_TCP_CLIENT_CONNECTED && cmd_word!=CMD_AUDIO_MIXER_STREAM && cmd_word!=CMD_ONLINE && cmd_word!=CMD_OFFLINE 
				&& cmd_word!=CMD_CLIENT_POWER_ON_NOTICE_HOST && cmd_word!=CMD_AUDIO_MIXER_CONFIG)
			{
				unsigned char deviceType=data[4];
				if(data[4] == MODEL_AUDIO_MIXER_DECODER)
				{
					data[4] = MODEL_AUDIO_MIXER_ENCODER;
				}
				else if(data[4] == MODEL_AUDIO_MIXER_ENCODER)
				{
					data[4] = MODEL_AUDIO_MIXER_DECODER;
				}

				if(data[4] == MODEL_AUDIO_MIXER_DECODER_C)
				{
					data[4] = MODEL_AUDIO_MIXER_ENCODER_C;
				}
				else if(data[4] == MODEL_AUDIO_MIXER_ENCODER_C)
				{
					data[4] = MODEL_AUDIO_MIXER_DECODER_C;
				}

				printf("multicast_send_data:CMD=0x%x\n",cmd_word);
				sendLen=sendto(multicast_send_socket, data, len, 0,(struct sockaddr*)&multi_send_sockAddr, sizeof(multi_send_sockAddr));
				sendLen=sendto(multicast_send_netTools_socket, data, len, 0,(struct sockaddr*)&multi_send_netTools_sockAddr, sizeof(multi_send_netTools_sockAddr));
				data[4]=deviceType;
			}
		}
   #endif

   if(sendLen == -1)
   {
       perror("multicast_send_data\n");
   }
}


/*********************************************************************
 * @fn      power_on_notice_host
 *
 * @brief   开机首次发送在线通知
 *
 * @param   void
 *
 * @return	void
 *********************************************************************/
void power_on_notice_host(void)
{
	int i;
	int payloadSize = 0;
	unsigned char data_buf[MAX_BUF_SIZE];
	unsigned char send_buf[128];

	memset(send_buf, 0x00, sizeof(send_buf)); // 清空缓存
	memset(data_buf, 0x00, sizeof(data_buf));
	// 添加命令字
	send_buf[0] = (unsigned char)(CMD_CLIENT_POWER_ON_NOTICE_HOST>>8);
	send_buf[1] = (unsigned char)CMD_CLIENT_POWER_ON_NOTICE_HOST;
	// 添加包序号
	send_buf[2] = 0;
	// 保留位

	send_buf[3] = g_system_work_mode << 2;
	send_buf[3] = send_buf[3] | g_network_mode << 4;
	send_buf[3] = send_buf[3] | 0x01 << 6;
	// 产品型号
	send_buf[4] = CURRENT_DEVICE_MODEL;
	// 包属性及编码格式
	send_buf[5] = 0x00;

	//20230602 启动类型
	send_buf[PAYLOAD_START+payloadSize] = g_sysBootType;
	payloadSize++;

	// 添加负载数据长度
	send_buf[6] = payloadSize/256;
	send_buf[7] = payloadSize%256;

	// 计算校验和
	send_buf[payloadSize+PAYLOAD_START] = Calculate_XorDat(&send_buf[PAYLOAD_START], payloadSize);
	// 发送数据
	if(g_network_mode == NETWORK_MODE_LAN)
	{
		multicast_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
               // CMD_CLIENT_POWER_ON_NOTICE_HOST
	}
	else
	{
		#if ENABLE_TCP_CLIENT
		host_tcp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
		#endif
	}
    printf("[power_on_notice_host]\n");
}


/*********************************************************************
 * @fn      Send_Unonline_Info
 *
 * @brief   发送离线通知
 *
 * @param   void
 *
 * @return	void
 *********************************************************************/
void Send_Unonline_Info(void)
{
	int i = 0;
	int pos = 0;
	unsigned char data_buf[MAX_BUF_SIZE];
	unsigned char send_buf[128];
	memset(send_buf, 0x00, sizeof(send_buf)); // 清空缓存
	memset(data_buf, 0x00, sizeof(data_buf));
	// 添加命令字
	send_buf[pos++] = (unsigned char)(CMD_OFFLINE>>8);
	send_buf[pos++] = (unsigned char)CMD_OFFLINE;
	// 添加包序号
	send_buf[pos++] = 0;
	// 保留位

	send_buf[pos++] = RESERVE;

	// 产品型号
	send_buf[pos++] = CURRENT_DEVICE_MODEL;
	// 包属性及编码格式
	send_buf[pos++] = 0x00;
	send_buf[pos++] = 0x00;
	send_buf[pos++] = 0x06;
	for (i=0; i<6; i++)
	{
		send_buf[pos++] = g_mac_addr[i];
	}
	send_buf[pos++] = Calculate_XorDat(&send_buf[PAYLOAD_START], 6);
	if(g_network_mode == NETWORK_MODE_LAN)
	{
		multicast_send_data(send_buf, pos);
	}
	else
	{
		#if ENABLE_TCP_CLIENT
		host_tcp_send_data(send_buf, pos);
		#endif
		host_udp_send_data(send_buf, pos);
	}
}


/*********************************************************************
 * @fn      send_online_info
 *
 * @brief   发送在线通知
 *
 * @param   void
 *
 * @return	void
 *********************************************************************/
void send_online_info(void)
{
	int i;
	int mac_byte_pos=0;
	int payloadSize = 0;
	unsigned char data_buf[MAX_BUF_SIZE];
	unsigned char send_buf[128];

	memset(send_buf, 0x00, sizeof(send_buf));
	memset(data_buf, 0x00, sizeof(data_buf));
	// 添加命令字
	send_buf[0] = (unsigned char)(CMD_ONLINE>>8);
	send_buf[1] = (unsigned char)CMD_ONLINE;
	// 添加包序号
	send_buf[2] = 0;
	// 保留位

	send_buf[3] = g_system_work_mode << 2;
	send_buf[3] = send_buf[3] | g_network_mode << 4;
	send_buf[3] = send_buf[3] | 0x01 << 6;
	// 产品型号
	send_buf[4] = CURRENT_DEVICE_MODEL;
	// 包属性及编码格式
	send_buf[5] = 0x00;

	// 添加设备别名
	if (strlen(g_device_alias) > 0)
	{
		send_buf[PAYLOAD_START] = strlen(g_device_alias);
		payloadSize += 1;
		for (i=0; i<strlen(g_device_alias); i++)
		{
			send_buf[PAYLOAD_START+payloadSize+i] = g_device_alias[i];
		}
		payloadSize += strlen(g_device_alias);
	}
	else
	{
		send_buf[PAYLOAD_START] = 0x00;
		payloadSize += 1;
	}

	// 添加终端ID（即MAC地址）
	//get_eth_macaddr(data_buf);
	send_buf[PAYLOAD_START+payloadSize] = 6;
	payloadSize += 1;
	mac_byte_pos = PAYLOAD_START+payloadSize;
	for (i=0; i<6; i++)
	{
		send_buf[PAYLOAD_START+payloadSize+i] = g_mac_addr[i];
	}
	payloadSize += 6;

	// 添加音量
	if(g_paging_status == PAGING_START)
	{
		send_buf[PAYLOAD_START+payloadSize] = pager_property.volume;
	}
	#if SUPPORT_SIP
	else if(g_media_source == SOURCE_SIP_CALLING)
	{
		send_buf[PAYLOAD_START+payloadSize] = appData.voiceInfoSettings.callVolumeTemp;
	}
	#endif
	else if(g_media_source == SOURCE_API_TTS_MUSIC)
	{
		send_buf[PAYLOAD_START+payloadSize] = curTTSParm.m_nVolume;
	}
	else
	{
		send_buf[PAYLOAD_START+payloadSize] = g_system_volume;
	}
	payloadSize += 1;

	// 添加节目源
	send_buf[PAYLOAD_START+payloadSize] = g_media_source;
	payloadSize += 1;
	//节目名称长度

	int media_len=strlen(g_media_name)>64?64:strlen(g_media_name);
	send_buf[PAYLOAD_START+payloadSize] = media_len;
	payloadSize += 1;
	//节目名称
	for (i=0; i<media_len; i++)
	{
		send_buf[PAYLOAD_START+payloadSize+i] = g_media_name[i];
	}
	payloadSize += media_len;

	//播放状态
	send_buf[PAYLOAD_START+payloadSize] = g_media_status;
	payloadSize += 1;
	//终端控制模式
	send_buf[PAYLOAD_START+payloadSize] = g_terminal_control_mode;
	payloadSize += 1;


	// 添加版本信息
	memset(data_buf, 0x00, sizeof(data_buf));
	//sprintf(data_buf, "%s", LOCAL_FIRMWARE_VERSION);
	
	sprintf(data_buf, "%s", LOCAL_FIRMWARE_VERSION);
	#if LZY_COMMERCIAL_VERSION
	strcat(data_buf,"L");
	#endif
	#if AIPU_VERSION
	strcat(data_buf,"AP");
	#endif
	#if YIHUI_VERSION
	if(isDisplayValid())
	{
		if(APP_THEME_COLOR == APP_AISP_GENERAL)
		{
			strcat(data_buf,"C1A0");
		}
		else if(APP_THEME_COLOR == APP_JUSBE_GENERAL)
		{
			strcat(data_buf,"C3A0");
		}
	}
	else
	{
		strcat(data_buf,"ND");
	}
	#endif

	if(g_device_moduleId>0)
	{
		char tmp_moduleId[10]={0};
		sprintf(tmp_moduleId,"%d",g_device_moduleId);
		strcat(data_buf,"-");
		strcat(data_buf,tmp_moduleId);
	}

	if(g_CurExistDsipModule && strlen(g_DispVersion)>0)
	{
		strcat(data_buf,"/");
		strcat(data_buf,g_DispVersion);
	}

	#if DECODER_LZY_VERESION_FM_C4A1
	strcat(data_buf,"(C4A1)");
	#endif
	#if DECODER_LZY_VERESION_FM_C4A2
	strcat(data_buf,"(C4A2)");
	#endif

	#if NETWORK_VPN_INTERNET
	strcat(data_buf,"(VPN)");
	#endif

	#if LOCAL_SOURCE_PRIORITY_HIGHEST
	strcat(data_buf,"(Aux)");
	#endif

	#if CANCEL_PAGER_SOURCE_PRIORITY
	strcat(data_buf,"(CPa)");
	#endif

	#if SUPPORT_VOLUME_CONTROL_UART
	if(g_exist_volumeControl_device)
		strcat(data_buf,"(VC-1)");
	else
		strcat(data_buf,"(VC-0)");
	strcat(data_buf,"(Lo)");
	#endif

	#if CANCEL_PAGER_SOURCE_PRIORITY
	strcat(data_buf,"(Cp)");
	#endif
#if 0
	#if SUPPORT_HTTP_SERVER
	strcat(data_buf,"(HS)");
	#endif
#endif

	if(is_Enable_UART_LED_PLAYER)
	{
		strcat(data_buf,"(Led)");
	}
	else if(SUPPORT_TM1640_DISPLAY)
	{
		strcat(data_buf,"(Seg)");
	}

	#if LZY_CUSTOMER_MQTT
	strcat(data_buf,"(MQ1)");
	#endif
	
	send_buf[PAYLOAD_START+payloadSize] = strlen(data_buf);
	payloadSize += 1;
	for (i = 0; i<strlen(data_buf); i++)
	{
		send_buf[PAYLOAD_START+payloadSize+i] = data_buf[i];
	}
	payloadSize += strlen(data_buf);
    
	//设备特性
	unsigned char device_feature=0;
	if(IS_MODEL_WOODEN && g_exist_bluetooth_module)
		device_feature |= DF_BLUETOOTH;
	if(IS_DEVICE_SUPPORT_INTERCOM)
		device_feature |= DF_CALL;

	#if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
		#if SUPPORT_SIP
		if(IS_EXTENSION_HAS_SIP)
		{
			device_feature |= DF_SIP;
		}
		#endif
		#if SUPPORT_INFORMATION_PUBLISH
		if(IS_EXTENSION_HAS_INFORMATION_PUB)
		{
			device_feature |= DF_INFORMATION_PUBLISH;
		}
		#endif
	#endif

	send_buf[PAYLOAD_START+payloadSize] = device_feature;
    payloadSize++;

	//音频混音器信号状态
	#if IS_DEVICE_AUDIO_MIXER
	send_buf[PAYLOAD_START+payloadSize] = g_audio_mixer_signal_valid;
	#else
	send_buf[PAYLOAD_START+payloadSize] = 0;
	#endif
	payloadSize++;


	//4G模块信号质量
	send_buf[PAYLOAD_START+payloadSize] = eth_link_status?0:stModule4GInfo.csq_rssi;		//0或者99都代表未知，使用的是非4G模块或者是4G模块但使用了有线网卡
	payloadSize++;

	//4G模块卡号长度
	int iccid_length=strlen(stModule4GInfo.iccid);
	send_buf[PAYLOAD_START+payloadSize] = iccid_length;		//0或者99都代表未知，使用非4G模块或者，或者4G模块使用了有线通讯
	payloadSize++;
	//4G模块卡号
	memcpy(send_buf+PAYLOAD_START+payloadSize,stModule4GInfo.iccid,iccid_length);
	payloadSize+=iccid_length;

	//用户名(目前仅寻呼台用到)
	int userLen=0;
	char userName[33]={0};
	send_buf[PAYLOAD_START+payloadSize] = userLen;
	payloadSize++;
	memcpy(send_buf+PAYLOAD_START+payloadSize,userName,userLen);
	payloadSize+=userLen;

	//音频采集器信号状态
	#if IS_DEVICE_AUDIO_COLLECTOR
	send_buf[PAYLOAD_START+payloadSize] = audioCollector_info.m_nSignalValid;
	#else
	send_buf[PAYLOAD_START+payloadSize] = 0;
	#endif
	payloadSize++;

	//电话网关信号状态
	#if IS_DEVICE_PHONE_GATEWAY
	send_buf[PAYLOAD_START+payloadSize] = g_phone_gateway_signal_valid;
	#else
	send_buf[PAYLOAD_START+payloadSize] = 0;
	#endif
	payloadSize++;

	//新增字段，支持新的播放指令(加入命令字)
	bool hasExtraFeature=false;
	switch(CURRENT_DEVICE_MODEL)
	{
		case MODEL_FIRE_COLLECTOR_C:
		case MODEL_FIRE_COLLECTOR_F:
			if(IS_EXTENSION_HAS_FIRE)
			{
				hasExtraFeature=true;
				send_buf[PAYLOAD_START+payloadSize] = DF_EXTRA_FIRE_COLLECTOR;	
			}
		break;
		case MODEL_AUDIO_COLLECTOR_C:	//默认就有采集器
		case MODEL_AUDIO_COLLECTOR_F:
			hasExtraFeature=true;
			send_buf[PAYLOAD_START+payloadSize] = DF_EXTRA_AUDIO_COLLECTOR;
		break;
		case MODEL_SEQUENCE_POWER_C:
		case MODEL_SEQUENCE_POWER_F:
			if(IS_EXTENSION_HAS_POWER)
			{
				hasExtraFeature=true;
				send_buf[PAYLOAD_START+payloadSize] = DF_EXTRA_POWER_SEQUENCE;
			}
		break;
		case MODEL_REMOTE_CONTROLER_C:
		case MODEL_REMOTE_CONTROLER_F:
			if(IS_EXTENSION_HAS_REMOTE)
			{
				hasExtraFeature=true;
				send_buf[PAYLOAD_START+payloadSize] = DF_EXTRA_REMOTE_CONTROLER;
			}
		break;
		case MODEL_GPS_SYNCHRONIZER:
			if(IS_EXTENSION_HAS_GPS)
			{
				hasExtraFeature=true;
				send_buf[PAYLOAD_START+payloadSize] = DF_EXTRA_GPS_SYNCHRONIZER;
			}
		break;
	}
	if(!hasExtraFeature)
	{
		//printf("has No ExtraFeature!!!\n");
		send_buf[PAYLOAD_START+payloadSize] = 0;	//额外的特性，0表示没有额外的特性
	}
	payloadSize++;
	

	// 添加负载数据长度
	send_buf[6] = payloadSize/256;
	send_buf[7] = payloadSize%256;

	// 计算校验和
	send_buf[payloadSize+PAYLOAD_START] = Calculate_XorDat(&send_buf[PAYLOAD_START], payloadSize);
	// 发送数据
	if(g_network_mode == NETWORK_MODE_LAN)
	{
		multicast_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}
	else
	{
		#if ENABLE_TCP_CLIENT

		if(sysRunTime>=10)    				//开机前10s不发送组播信息
        {
            multicast_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
        }

		if(tcp_get_master_connect_status() == 1)
		{
			#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
			{
				if(!tcp_get_master_client_fr_flag())    //第一次还未应答
				{
					send_buf[0] = (unsigned char)(CMD_TCP_CLIENT_CONNECTED>>8);
					send_buf[1] = (unsigned char)CMD_TCP_CLIENT_CONNECTED;
					tcp_set_master_client_fr_flag(1);
				}
			}
			#endif
			host_tcp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);

			#if !IS_DEVICE_AUDIO_MIXER
			kcp_send_heartbeat();
			#endif
		}
        #endif
	}



	#if IS_DEVICE_AUDIO_MIXER
		#if(CURRENT_DEVICE_MODEL == MODEL_AUDIO_MIXER_ENCODER)
		send_buf[4] = MODEL_AUDIO_MIXER_DECODER;
		#elif(CURRENT_DEVICE_MODEL == MODEL_AUDIO_MIXER_ENCODER_C)
		send_buf[4] = MODEL_AUDIO_MIXER_DECODER_C;
		#endif
		// MAC地址
		for (i=0; i<6; i++)
		{
			send_buf[mac_byte_pos+i] = g_mixer_mac_addr[i];
		}
		// 计算校验和
		send_buf[payloadSize+PAYLOAD_START] = Calculate_XorDat(&send_buf[PAYLOAD_START], payloadSize);

		// 发送数据
		if(g_network_mode == NETWORK_MODE_LAN)
		{
			multicast_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
		}
		else
		{
			#if ENABLE_TCP_CLIENT

			if(sysRunTime>=10)    				//开机前10s不发送组播信息
			{
				multicast_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
			}

			if(tcp_get_master_connect_status() == 1)
			{
				if(!tcp_get_master_client_fr_flag())    //第一次还未应答
				{
					send_buf[0] = (unsigned char)(CMD_TCP_CLIENT_CONNECTED>>8);
					send_buf[1] = (unsigned char)CMD_TCP_CLIENT_CONNECTED;
					tcp_set_master_client_fr_flag(1);
				}
				host_tcp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);

				kcp_send_heartbeat();
			}
			else if(tcp_get_slave_connect_status() == 1)
			{
				if(!tcp_get_slave_client_fr_flag())    //第一次还未应答
				{
					send_buf[0] = (unsigned char)(CMD_TCP_CLIENT_CONNECTED>>8);
					send_buf[1] = (unsigned char)CMD_TCP_CLIENT_CONNECTED;
					tcp_set_slave_client_fr_flag(1);
				}
				host_tcp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);

				kcp_send_heartbeat();
			}
			#endif
		}

	#endif

    printf("[send_online_info]\n");
}


/*********************************************************************
 * @fn      Handle_Multicast_CMD
 *
 * @brief  	处理组播命令字
 *
 * @param   NULL
 *
 * @return  none
*********************************************************************/
void Handle_Multicast_CMD(unsigned char *rxbuf)
{       
	//判断自己是否处于MAC列表中
	int pos=PAYLOAD_START;
	int zone_num=rxbuf[pos++];
	unsigned char *mac_buf = &rxbuf[pos];

	int i=0,t=0;
	int mac_ok=0;
	for(i=0;i<zone_num;i++)
	{
		if(memcmp(g_mac_addr,mac_buf+i*6,6) == 0)
		{
			mac_ok=1;
			break;
		}
		#if IS_DEVICE_AUDIO_MIXER
		if(memcmp(g_mixer_mac_addr,mac_buf+i*6,6) == 0)
		{
			mac_ok=1;
			break;
		}
		#endif
	}
	if(!mac_ok)
	{
		return;
	}

	pos+=zone_num*6;

	//MAC匹配，校验通过
	//获取两个字节的命令字
	int cmd_word = rxbuf[pos]*256 + rxbuf[pos+1];
	pos+=2;
	int payload_length = rxbuf[pos]*256 + rxbuf[pos+1];
	pos+=2;
	unsigned char *cmd_buf=&rxbuf[pos];
	
	printf("Handle_Multicast_CMD:0x%04x\n",cmd_word);
	
	//判断是主机还是寻呼台发过来的组播指令,
	switch(cmd_word)
	{
		case CMD_PAGING_NOTIFY:			//寻呼通知
			pkg_paging_notification(cmd_buf,payload_length,DEVICE_MODEL_PAGING,0);
			break;
	}
	
}


/*********************************************************************
 * @fn      Multicast_Pkg_Process
 *
 * @brief   命令处理
 *
 * @param   pkg_buf   - 包缓存
 *          pkg_len -  包长度
 *
 * @return	void
 *********************************************************************/
void Multicast_Pkg_Process(unsigned char *pkg_buf, int pkg_len)
{
	int cmd_word = 0;
	// 获取两个字节的命令字
	cmd_word = (pkg_buf[0])*256 + pkg_buf[1];
	//Dbg("multicast cmd_word:0x%04x\n", cmd_word);

	int device_model = pkg_buf[4];
	if(g_network_mode == NETWORK_MODE_LAN)
	{
		if( device_model != DEVICE_MODEL_HOST && device_model != DEVICE_MODEL_MOBILE
		   && device_model != DEVICE_MODEL_PAGING && device_model != DEVICE_MODEL_NETWORK_TOOLS)
		{
			if(g_paging_status != PAGING_START) printf("Multicast_Pkg_Process ERROR1:device_model=0x%x\n",device_model);
			return;
		}

		if(device_model == DEVICE_MODEL_HOST)
		{
			g_host_device_TimeOut=0;
		}
	}
	else if(g_network_mode == NETWORK_MODE_WAN)
	{
		if(device_model != DEVICE_MODEL_NETWORK_TOOLS && cmd_word!=CMD_HOST_QUERY_SET_IP_INFO)
		{
			if(g_paging_status != PAGING_START) printf("Multicast_Pkg_Process ERROR2:device_model=0x%x\n",device_model);
			return;
		}
	}

	switch(cmd_word)
	{
		case CMD_SEARCH_DEVICE://搜索在线终端设备
			send_online_info();
			if(device_model == DEVICE_MODEL_HOST)
			{
				if(g_network_mode == NETWORK_MODE_LAN)
				{
					if(power_on_flag==0)
					{
						power_on_flag = 1;
						power_on_notice_host();
						//如果是电源时序器，发送详细信息
						#if (IS_DEVICE_POWER_SEQUENCE)
						Host_Query_Set_Sequence_Power_Info(NULL);
						#endif
					}
				}
			}
		break;
		case CMD_TIME_SYN://时间同步
			pkg_set_local_time(pkg_buf);
		break;
		case CMD_HOST_QUERY_SET_IP_INFO://主机向终端设置IP属性
			HOST_QUERY_SET_IP_INFO(pkg_buf);
		break;
		case CMD_SEND_PAGING_NOTIFY_MULTICAST://通知终端开始寻呼(组播)
		break;

		case CMD_CONTROL_REBOOT_MULTICAST://主机向终端发送重启指令(组播)
			host_control_reboot(pkg_buf, 1);
		break;

		case CMD_SEND_MULTICAST_CMD:			//控制设备（包括主机）组播命令给终端(包括寻呼台发起寻呼通知）
			Handle_Multicast_CMD(pkg_buf);
		break;
	
		case CMD_HOST_SET_DSP_FIRMWARE_FEATURE:	//主机或配置工具向终端查询/设置DSP固件功能特性（组播）
		#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
			Host_Set_Dsp_Firmware_Feature(pkg_buf);
		#endif
		break;
		
		case CMD_HOST_SET_SERIAL_NUMBER:		//主机或配置工具向终端获取设备序列号（组播)
			Host_Set_SerialNumber(pkg_buf);
		break;

		case CMD_HOST_QUERY_SET_NET_WORK_MODE:	//主机向终端设置网络模式
		case CMD_NETWORK_MODE_MULTICAST:
			ProcessHostSetNetWork(pkg_buf,pkg_len,true);
		break;

		case CMD_SEND_TOOLS_DEVICE_EXTENSION:
			procTools_Set_Extension(pkg_buf,pkg_len);
		break;

		case CMD_SEND_TOOLS_PRE_DEVICE_EXTENSION:
			procTools_PreSet_Extension(pkg_buf,pkg_len);
		break;
	}
}


/*****多网卡时，网络接口变化后（特定条件：开机时是4G，后面插入了有线），一定要将当前网络接口加入组播，否则无法接收。
 * 		这里只需要将接收组播命令的socket加入即可，其他的音频socket每次进入都会重新创建并加入，无需理会
******/
void socket_join_multicast_membership()
{
	struct ip_mreq mreq;
	mreq.imr_multiaddr.s_addr = inet_addr(MCAST_CMD_RECV_ADDR);
	mreq.imr_interface.s_addr = htonl(INADDR_ANY);
	/* 把本机加入组播地址，即本机网卡作为组播成员，只有加入组才能收到组播消息 */
	if(multicast_sockfd !=-1 )
	{
		if (setsockopt(multicast_sockfd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq,sizeof (struct ip_mreq)) == -1)
		{
			perror("set multicast_sockfd IP_ADD_MEMBERSHIP\n");
		}
	}
}

/*********************************************************************
 * @fn      host_udp_client
 *
 * @brief   创建一个组播线程来处理主机的请求
 *
 * @param   void
 *
 * @return  none
 */
void *multicast_cmd_client(void)
{
	int ret, i;
	int Rxlen;
	int Index = 0;
    int Pkg_Length = 0;
    int Payload_Length = 0;
	fd_set readfd;
	int Read_Size = MAX_BUF_SIZE;
	unsigned char RxBuf[MAX_BUF_SIZE];
	struct sockaddr_in Host_Addr;
	int g_sockaddr_len = sizeof(struct sockaddr_in);
    struct ip_mreq mreq;     /* 创建 socket 用于UDP通讯 */
	
	/*填充地址信息*/
	memset(&Host_Addr, 0, sizeof(Host_Addr));         // 清空地址信息
	Host_Addr.sin_family = AF_INET;                   // 选用TCP/IP协议
	Host_Addr.sin_port = htons(MCAST_CMD_RECV_PORT);  // 注册端口
	
	/*创建套接字*/
	if ((multicast_sockfd = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
	{
		perror("multicast_sockfd create error");
		exit(0);
	}
	else
	{
		//printf("multicast_sockfd create success!\n");
	}
	
	//接收组播的socket需要先绑定网卡
	if (setsockopt(multicast_sockfd, SOL_SOCKET,SO_BINDTODEVICE, (char *)&nif_eth0, sizeof(nif_eth0)) < 0)
	{
		perror("multicast_sockfd bind interface fail");
	}

     /* 设置要加入组播的地址 */
	 memset(&mreq, 0,sizeof (struct ip_mreq));
	 inet_pton(AF_INET,MCAST_CMD_RECV_ADDR,&Host_Addr.sin_addr);
	 /* 设置组地址 */
	 memcpy (&mreq.imr_multiaddr.s_addr,&Host_Addr.sin_addr.s_addr, sizeof (struct in_addr));
	 /* 设置发送组播消息的源主机的地址信息 */
	 mreq.imr_interface.s_addr = htonl (INADDR_ANY);
	 /* 把本机加入组播地址，即本机网卡作为组播成员，只有加入组才能收到组播消息 */
	 if (setsockopt(multicast_sockfd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq,sizeof (struct ip_mreq)) == -1)
	 {
		perror ("multicast_sockfd SOCKET:");
		exit (-1);
	 }
     
	/*设置地址重用,以解决第二次运行时端口占用的问题(SO_REUSEADDR允许完全相同的地址和端口的重复绑定，但这只用于UDP)*/
	int opt = 1;
	if((setsockopt(multicast_sockfd,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt)))<0)
	{
		perror("setsockopt SO_REUSEADDR failed");
		close(multicast_sockfd);
		exit(0);
	}
	/*绑定地址到套接字*/
	ret = bind(multicast_sockfd, (struct sockaddr *)&Host_Addr, sizeof(Host_Addr));
	if (ret < 0)
	{
		perror("multicast_sockfd call bind error");
		close(multicast_sockfd);
		exit(0);
	}
	else
	{
		printf("multicast_sockfd bind socket success!\n");
	}

	int nSndBuf=128*1024;//设置为128KB
	setsockopt(multicast_sockfd,SOL_SOCKET,SO_SNDBUF,(const char*)&nSndBuf,sizeof(int));

	
	while(1)
	{	

		FD_ZERO(&readfd);                // 清空读文件描述集合
		FD_SET(multicast_sockfd, &readfd); // 注册套接字文件描述符
		int fdmax=multicast_sockfd;
		Rxlen=0;

		ret = select(fdmax+1, &readfd, NULL, NULL, NULL);
		switch(ret)
		{
			case -1 : // 调用出错
				perror("multicast pthread call select error");
				break;
			
			case 0 : // 超时
				//printf("multicast pthread select timeout!!!\n");
				break;
				
			default : // 有数据可读
				if (FD_ISSET(multicast_sockfd, &readfd))
				{
					Rxlen = recvfrom(multicast_sockfd, &RxBuf[Index], Read_Size, 0, (struct sockaddr *)&Host_Addr, &g_sockaddr_len);
				}
				if (Rxlen < PACKAGE_MIN_SIZE)
				{
					perror("pager udp recvfrom");
					memset(RxBuf, 0, MAX_BUF_SIZE);
					continue;
				}
				else
				{
						Payload_Length = (int)RxBuf[PAYLOAD_START-2]*256 + (int)RxBuf[PAYLOAD_START-1]; // 负载数据长度
						if(Payload_Length>MAX_BUF_SIZE-PACKAGE_MIN_SIZE)
						{
							memset(RxBuf, 0, MAX_BUF_SIZE);
							continue;
						}
						Pkg_Length = Payload_Length + PACKAGE_MIN_SIZE;     // 整个包长
						if(Pkg_Length>MAX_BUF_SIZE)
						{
							memset(RxBuf, 0, MAX_BUF_SIZE);
							continue;
						}

					/*判断接收长度是否等于包长*/
					if (Rxlen != Pkg_Length)
					{
						memset(RxBuf, 0, MAX_BUF_SIZE);
						continue;
					}
					else
					{
						// 计算校验和
						if (Calculate_XorDat(&RxBuf[PAYLOAD_START], Payload_Length) == RxBuf[Pkg_Length-1])
						{
							//printf("multicast payload check succeed,start process command!\n");
							//Multicast_Pkg_Process(RxBuf, Pkg_Length);
							if(RxBuf[4] == DEVICE_MODEL_HOST)
							{
								multi_host_addr=Host_Addr;
								//printf("multi_host_addr1:%d.%d.%d.%d\n", (multi_host_addr.sin_addr.s_addr)&0xff, (multi_host_addr.sin_addr.s_addr>>8)&0xff,(multi_host_addr.sin_addr.s_addr>>16)&0xff,(multi_host_addr.sin_addr.s_addr>>24)&0xff);
							}
							NetPkg_Add(NET_TYPE_MULTICAST_SERVER,RxBuf,Pkg_Length);
						}
						else
						{
							printf("multicast payload check error!!!\n");
						}
						memset(RxBuf, 0, MAX_BUF_SIZE);
					}
				}
				break;
		}
	}

	close(multicast_sockfd);
	pthread_exit(NULL);
}

/*********************************************************************
 * @fn      start_multicast_cmd_client
 *
 * @brief   启动组播命令接收客户端
 *
 * @param
 *
 *
 * @return
 */
void start_multicast_cmd_client(void)
{
	int ret = -1;
    pthread_t host_multicast_pthread;
	pthread_attr_t host_multicast_pthread_attr;
	
	pthread_attr_init(&host_multicast_pthread_attr);
	pthread_attr_setdetachstate(&host_multicast_pthread_attr, PTHREAD_CREATE_DETACHED);
	ret = pthread_create(&host_multicast_pthread, &host_multicast_pthread_attr, (void *)multicast_cmd_client, NULL);
	if (ret < 0)
	{
		perror("start_multicast_cmd_client create");
	}
	else
	{
		printf("start_multicast_cmd_client succeed!\n");
	}
	pthread_attr_destroy(&host_multicast_pthread_attr);
}