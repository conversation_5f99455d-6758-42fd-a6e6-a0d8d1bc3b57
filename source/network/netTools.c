/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-04 16:02:08 
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-09-16 15:06:56
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>


#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netdb.h>
#include <net/if.h>
#include <ifaddrs.h>
#include <linux/sockios.h>
#include <linux/ethtool.h>

#include <unistd.h>
#include <sys/ioctl.h>

#include "netTools.h"
#include "../system/sysMethod.h"
#include "../sigmastar/mi_functions.h"
#include "sysconf.h"


struct ifreq nif_eth0,nif_4g;

void nif_init()
{
    memset(&nif_eth0, 0, sizeof(nif_eth0));
    memset(&nif_4g, 0, sizeof(nif_4g));
    strcpy(nif_eth0.ifr_name,"eth0");
    strcpy(nif_4g.ifr_name,"eth1");
}



// -1 -- error , details can check errno
// 1 -- interface link up
// 0 -- interface link down.
int get_netlink_status(const char *if_name)
{
    #ifdef USE_PC_SIMULATOR
    return 1;
    #endif
    static int skfd = -1;
    struct ifreq ifr;
    struct ethtool_value edata;
    edata.cmd = ETHTOOL_GLINK;
    edata.data = 0;
    memset(&ifr, 0, sizeof(ifr));
    strncpy(ifr.ifr_name, if_name, sizeof(ifr.ifr_name) - 1);
    ifr.ifr_data = (char *) &edata;
    if(skfd <0)
    {
        if (( skfd = socket( AF_INET, SOCK_DGRAM, 0 )) < 0)
        {
            printf("get_netlink_status error1.\n");
            return -1;
        }
    }

    if(ioctl( skfd, SIOCETHTOOL, &ifr ) == -1)
    {
        printf("get_netlink_status error2.\n");
        close(skfd);
        skfd=-1;
        return -1;
    }
    //close(skfd);
    return edata.data;
}

/*********************************************************************
* @fn      set_mac_address
*
* @brief    初始化MAC地址
*
* @param   void
*
* @return  NULL
*/
void set_mac_address(char *macAddr)
{
    unsigned char mac_address[32];
   unsigned char t_mac_address[64];
   sprintf(mac_address,"%02x:%02x:%02x:%02x:%02x:%02x",macAddr[0],macAddr[1],macAddr[2],macAddr[3],macAddr[4],macAddr[5]);
   printf("set_mac_address=%s\n", mac_address);
   sprintf(t_mac_address, "ifconfig eth0 hw ether %s", mac_address);

   pox_system("ifconfig eth0 down");
   pox_system(t_mac_address);
   pox_system("ifconfig eth0 up");
}




/*********************************************************************
 * @fn      GetLocalIp
 *
 * @brief   获取本地网卡IP地址，"eth0/1"需根据实际网卡号进行填写
 *
 * @param   sockfd - 套接字文件描述符
 *			Localip - 获取的IP地址
 *
 * @return  1 - 获取成功
 *			-1 - 获取失败
 */
int GetLocalIp(char *localIP)
{
    char ifName[64]={0};
    char ifName2[64]={0};
    struct ifaddrs * ifAddrStruct=NULL;
    void * tmpAddrPtr=NULL;
    getifaddrs(&ifAddrStruct);
    struct ifaddrs * ifAddrStruct_first=ifAddrStruct;

    #ifdef USE_PC_SIMULATOR
    sprintf(ifName,"%s","ens32");
    sprintf(ifName2,"%s","wifi0");
    #else
    sprintf(ifName,"%s","eth0");
    if(!eth_link_status)
    {
        if(g_module_4G_status <= MODULE_4G_OFF)
        {
            memset(localIP,0,sizeof(localIP));
        }
        else
        {
            sprintf(ifName,"%s","eth1");
        }
    }

    #endif


    while (ifAddrStruct!=NULL)
    {
        if ( strcmp(ifAddrStruct->ifa_name,ifName) == 0 && ifAddrStruct->ifa_addr->sa_family==AF_INET)
        {   // check it is IP4 is a valid IP4 Address
            tmpAddrPtr = &((struct sockaddr_in *)ifAddrStruct->ifa_addr)->sin_addr;
            char addressBuffer[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, tmpAddrPtr, addressBuffer, INET_ADDRSTRLEN);
            //printf("%s IPV4 Address %s\n", ifAddrStruct->ifa_name, addressBuffer);
            if(strcmp(addressBuffer,"127.0.0.1") != 0)
            {
                printf("GetLocalIp:%s\n",addressBuffer);
                memset(localIP,0,sizeof(localIP));
                strcpy(localIP, addressBuffer);

                if(ifAddrStruct_first!=NULL)
                    freeifaddrs(ifAddrStruct_first);
                return 0;
            }
        }
        #ifdef USE_PC_SIMULATOR
        if ( strcmp(ifAddrStruct->ifa_name,ifName2) == 0 && ifAddrStruct->ifa_addr->sa_family==AF_INET)
        {   // check it is IP4 is a valid IP4 Address
            tmpAddrPtr = &((struct sockaddr_in *)ifAddrStruct->ifa_addr)->sin_addr;
            char addressBuffer[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, tmpAddrPtr, addressBuffer, INET_ADDRSTRLEN);
            //printf("%s IPV4 Address %s\n", ifAddrStruct->ifa_name, addressBuffer);
            if(strcmp(addressBuffer,"127.0.0.1") != 0)
            {
                printf("GetLocalIp:%s\n",addressBuffer);
                memset(localIP,0,sizeof(localIP));
                strcpy(localIP, addressBuffer);

                if(ifAddrStruct_first!=NULL)
                    freeifaddrs(ifAddrStruct_first);
                return 0;
            }
        }
        #endif

        ifAddrStruct = ifAddrStruct->ifa_next;
    }

    if(ifAddrStruct_first!=NULL)
        freeifaddrs(ifAddrStruct_first);
    return -1;
}


/*********************************************************************
 * @fn      GetLocalMAC
 *
 * @brief   获取本地网卡MAC地址，"eth0/1"需根据实际网卡号进行填写
 *
 * @param   mac_addr - 获取的MAC地址
 *
 * @return  1 - 获取成功
 *			-1 - 获取失败
 */
void GetLocalMAC(unsigned char *mac_addr,bool bSet)
{
#ifdef USE_PC_SIMULATOR
	struct ifreq ifr_ip;
	char temp_buf[10]={0};
    char temp_buf2[10]={0};
	strcpy(temp_buf,"ens32");
    strcpy(temp_buf2,"wifi0");
    memset(&ifr_ip, 0, sizeof(ifr_ip));
	strncpy(ifr_ip.ifr_name, temp_buf, sizeof(ifr_ip.ifr_name)-1);

    int sockfd;
    if ((sockfd = socket(AF_INET, SOCK_DGRAM, 0)) == -1)
        return;
	if (ioctl(sockfd, SIOCGIFHWADDR, &ifr_ip) < 0)
	{
        memset(&ifr_ip, 0, sizeof(ifr_ip));
        strncpy(ifr_ip.ifr_name, temp_buf2, sizeof(ifr_ip.ifr_name)-1);
		if (ioctl(sockfd, SIOCGIFHWADDR, &ifr_ip) < 0)
            return;
	}
	int i;
	for(i=0;i<6;i++)
		mac_addr[i]=  ifr_ip.ifr_hwaddr.sa_data[i];
    #if 1
    int appCnt=0;
    char readline[32]={0};
    FILE* fp = popen("ps -ef | grep demo | wc -l", "r" );
    if ( NULL == fp )
    {
        printf("popen ps error!");
    }
    else
    {
        memset( readline, 0, sizeof( readline ) );
        fgets( readline,sizeof( readline ),fp );
        pclose(fp);

        appCnt = atoi(readline);
        printf("appCnt=%d\n",appCnt);
    }

    mac_addr[4] = appCnt>>8;
    mac_addr[5] = appCnt;
    #endif
	printf("GetLocalMAC=%02x:%02x:%02x:%02x:%02x:%02x\n",mac_addr[0],mac_addr[1],mac_addr[2],mac_addr[3],mac_addr[4],mac_addr[5]);
#else
    //由uuid取得MAC,然后设置MAC
    unsigned long long  u64Uuid=sstar_get_chip_uuid();
    if(u64Uuid>0)
    {
        mac_addr[0]=0x2C;
        mac_addr[1]=((u64Uuid)>>40) & 0xFF;
        mac_addr[2]=((u64Uuid)>>32) & 0xFF;
        mac_addr[3]=((u64Uuid)>>24) & 0xFF;
        mac_addr[4]=((u64Uuid)>>16) & 0xFF;
        mac_addr[5]=((u64Uuid)>>8) & 0xFF;

        #if IS_DEVICE_AUDIO_MIXER
        g_mixer_mac_addr[0]=0x2B;
        g_mixer_mac_addr[1]=((u64Uuid)>>40) & 0xFF;
        g_mixer_mac_addr[2]=((u64Uuid)>>32) & 0xFF;
        g_mixer_mac_addr[3]=((u64Uuid)>>24) & 0xFF;
        g_mixer_mac_addr[4]=((u64Uuid)>>16) & 0xFF;
        g_mixer_mac_addr[5]=((u64Uuid)>>8) & 0xFF;
        #endif

        printf("GetLocalMAC=%02x:%02x:%02x:%02x:%02x:%02x\n",mac_addr[0],mac_addr[1],mac_addr[2],mac_addr[3],mac_addr[4],mac_addr[5]);
        if(bSet)
            set_mac_address(mac_addr);
    }
#endif
}



void Get_Network_Info(char *ipaddress,char *netmask,char *gateway)
{
	if(g_IP_Assign == IP_ASSIGN_STATIC)
	{
		sprintf(ipaddress,"%s",g_Static_ip_address);
		sprintf(netmask,"%s",g_Subnet_Mask);
		sprintf(gateway,"%s",g_GateWay);
	}
	else
	{
		if( strlen(g_ipAddress)>7 && strcmp(g_ipAddress,"127.0.0.1")!=0 )
		{
			sprintf(ipaddress,"%s",g_ipAddress);

			//获取子网掩码
			char Cmd[100]={0};
			char readline[32]={0};
			memset( Cmd, 0, sizeof( Cmd ) );
			sprintf( Cmd,"ifconfig |grep inet| sed -n \'1p\'|awk \'{print $4}\'|awk -F \':\' \'{print $2}\'");
			FILE* fp = popen( Cmd, "r" );
			if ( NULL == fp )
			{
				sprintf(netmask,"%s","0.0.0.0");
			}
			else
			{
				memset( readline, 0, sizeof( readline ) );
				fgets( readline,sizeof( readline ),fp );
				pclose(fp);

				sprintf(netmask,"%s",readline);
			}

			//获取网关
			memset( Cmd, 0, sizeof( Cmd ) );
			sprintf( Cmd,"route -n | grep eth0 | grep UG | awk \'{print $2}\'");
			fp = popen( Cmd, "r" );
			if ( NULL == fp )
			{
				sprintf(gateway,"%s","0.0.0.0");
			}
			else
			{
				memset( readline, 0, sizeof( readline ) );
				fgets( readline,sizeof( readline ),fp );
				pclose(fp);

				sprintf(gateway,"%s",readline);
			}

		}
		else
		{
			sprintf(ipaddress,"%s","0.0.0.0");
			sprintf(netmask,"%s","0.0.0.0");
			sprintf(gateway,"%s","0.0.0.0");
		}
	}
	printf("Get_Network_Info:ip=%s,netmask=%s,gateWay=%s\n",ipaddress,netmask,gateway);
}


void init_network()
{
	printf("Network_init...\n");
    pox_system("ifconfig eth0 up");
	pox_system("ifconfig lo 127.0.0.1 up");
    usleep(10000);
    eth_link_status=get_netlink_status("eth0");
    printf("init_network:eth_link_status=%d\n",eth_link_status);

	if(g_IP_Assign == IP_ASSIGN_STATIC)
	{
        char cmd[128]={0};
		//设置静态IP、子网掩码
        if((strlen(g_Static_ip_address) == 0) || (strlen(g_Subnet_Mask) == 0) || (strlen(g_GateWay) == 0) )
			return;
		sprintf(cmd,"ifconfig %s %s netmask %s","eth0",g_Static_ip_address,g_Subnet_Mask);
		pox_system(cmd);

		//设置网关
		sprintf(cmd,"route add default gw %s",g_GateWay);
		pox_system(cmd);
		//设置主DNS、备用DNS服务器
		if(strlen(g_Primary_DNS) >=7)
		{
			sprintf(cmd,"echo \"nameserver %s\" > /etc/resolv.conf",g_Primary_DNS);
			pox_system(cmd);
			if(strlen(g_Alternative_DNS) >=7)
			{
				sprintf(cmd,"echo \"nameserver %s\" >> /etc/resolv.conf",g_Alternative_DNS);
				pox_system(cmd);
			}
		}
	}
    else
    {
        pox_system("ifconfig eth0 127.0.0.1");
    }
}





/*added by xyang for ip check 2011-08-13*/
int if_a_string_is_a_valid_ipv4_address(const char *str)
{
    struct in_addr addr;
    int ret;
    if( strcmp(str,"0.0.0.0") == 0 )
    {
        return 0;
    }
    ret = inet_pton(AF_INET, str, &addr);
    if (ret > 0);
    else if (ret < 0)
    {
        //printf("inet_pton error");
    }
    else
    {
        //printf("\"%s\" is not a valid IPv4 address\n", str);
    }
    return ret;
}

/**
 * [isValidSubnetMask 判断子网掩码是否正确]
 * @param  mask [description]
 * @return      [description]
 */
int isValidSubnetMask(const char *mask){
    if(if_a_string_is_a_valid_ipv4_address (mask))
    {
        unsigned int b = 0, i, n[4];
        sscanf(mask, "%u.%u.%u.%u", &n[3], &n[2], &n[1], &n[0]);
        for(i = 0; i < 4; ++i) //将子网掩码存入32位无符号整型
            b += n[i] << (i * 8);
        b = ~b + 1;
        if((b & (b - 1)) == 0)   //判断是否为2^n
            return 1;
    }
    return 0;
}

/**
 * [isGatewayByNetmask_Error 判断ip、子网掩码、网关是否正确]
 * @param  ip   [description]
 * @param  mask [description]
 * @param  gw   [description]
 * @return      [0:正确 -1：ip错误  -2：子网掩码错误 -3：网关错误 -4：ip和网关不在同一网段内 ]
 */
int isGatewayByNetmask_Error(const char *ip,const char *mask,const char *gw)
{
    if(!if_a_string_is_a_valid_ipv4_address (ip)){
        return -1;
    } 
    if(!isValidSubnetMask(mask)){
        return -2;
    }
    if(!if_a_string_is_a_valid_ipv4_address (gw)){
        return -3;
    }

    unsigned long addr=0; 
    unsigned long gwi=0; //网关
    unsigned long netMask=0;
    inet_pton(AF_INET, ip, &addr);
    inet_pton(AF_INET, gw, &gwi);
    inet_pton(AF_INET, mask, &netMask);

    if ((addr & netMask) != (gwi & netMask))
    {
        return -4;
    }

	//找到最后一个/
	char *lastDotPos=strrchr(gw,'.');
	if(lastDotPos)
	{
        if( strcmp(lastDotPos+1,"0") == 0 || strcmp(lastDotPos+1,"255") == 0 )
        {
            printf("isGatewayByNetmask_Error:gw error!\n");
            return -5;
        }
	}
    return 0;
}




// 常见顶级域名（TLD）列表
static const char *common_tlds[] = {
    ".com", ".org", ".net", ".io", ".co", ".ai", ".gov", ".edu", ".mil", ".int",
    ".biz", ".info", ".mobi", ".name", ".tv", ".cc", ".me", ".us", ".uk", ".ca",
    ".au", ".de", ".jp", ".cn", ".in", ".br", ".fr", ".ru", ".es", ".it", ".nl",
    ".se", ".ch", ".no", ".mx", ".ar", ".pl", ".id", ".kr", ".tr", ".th", ".vn",
    ".gr", ".be", ".at", ".dk", ".fi", ".nz", ".pt", ".sg", ".my", ".hk", ".tw",
    ".il", ".ir", ".pk", ".ph", ".ro", ".ua", ".za", ".eg", ".sa", ".ae", ".ng",
    ".ke", ".ma", ".cl", ".pe", ".co.uk", ".org.uk", ".gov.uk", ".ac.uk", ".edu.au",
    ".co.jp", ".ne.jp", ".or.jp", ".go.jp", ".ac.jp", ".com.cn", ".net.cn", ".org.cn",
    ".gov.cn", ".edu.cn", ".com.br", ".net.br", ".org.br", ".gov.br", ".edu.br",
    NULL  // 结束标志
};

// 检查字符串是否以某个 TLD 结尾
bool ends_with_tld(const char *domain) {
    if (domain == NULL || strlen(domain) < 3 || strlen(domain) > 63) {
        return false;  // 最短的 TLD 是 2 字符（如 .co），但通常至少 3（.com）
    }

    for (int i = 0; common_tlds[i] != NULL; i++) {
        const char *tld = common_tlds[i];
        size_t tld_len = strlen(tld);
        size_t domain_len = strlen(domain);

        if (domain_len >= tld_len) {
            if (strcmp(domain + domain_len - tld_len, tld) == 0) {
                return true;
            }
        }
    }

    return false;
}

// 改进后的域名校验（增加 TLD 检查）
bool is_valid_domain(const char *domain) {
    if (domain == NULL || strlen(domain) > 63 || strlen(domain) == 0) {
        return false;
    }

    // 不能以点或连字符开头或结尾
    if (domain[0] == '.' || domain[0] == '-' || 
        domain[strlen(domain) - 1] == '.' || domain[strlen(domain) - 1] == '-') {
        return false;
    }

    const char *ptr = domain;
    int label_len = 0;

    while (*ptr) {
        if (*ptr == '.') {
            if (label_len == 0 || label_len > 63) {
                return false;
            }
            label_len = 0;
            ptr++;
            continue;
        }
        if (!isalnum(*ptr)) {
            if (*ptr != '-') {
                return false;
            }
        }
        label_len++;
        ptr++;
    }

    // 最后一个标签不能为空或过长
    if (label_len == 0 || label_len > 63) {
        return false;
    }

    // 检查是否以常见 TLD 结尾
    return ends_with_tld(domain);
}