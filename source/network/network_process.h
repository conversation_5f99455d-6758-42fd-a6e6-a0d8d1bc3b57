/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-06 14:30:46 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-09-16 11:34:21
 */

#ifndef _NETWORK_PROCESS_H_
#define _NETWORK_PROCESS_H_

#include "stdbool.h"

enum
{
	NET_TYPE_UNICAST_SERVER=1,
    NET_TYPE_UNICAST_PAGER=2,
	NET_TYPE_MULTICAST_SERVER=3,
	NET_TYPE_TCP_SERVER=4,
};

typedef struct
{
    unsigned char buf[MAX_BUF_SIZE];
    int len;
    unsigned char type; //1表示udp-sever,2表示udp-multicast,3表示tcp
}_stNetworkBuf;

typedef struct
{
    int write_pos;
    int read_pos;
    _stNetworkBuf stNetworkBuf[MAX_BUF_NUM];
}_stNetworkRecv;

extern _stNetworkRecv stNetworkRecv;

#define PP_HTONL(x) ((((x) & (unsigned long)0x000000ffUL) << 24) | \
                     (((x) & (unsigned long)0x0000ff00UL) <<  8) | \
                     (((x) & (unsigned long)0x00ff0000UL) >>  8) | \
                     (((x) & (unsigned long)0xff000000UL) >> 24) )
#define ip_addr_ismulticast(addr1) (((addr1)->s_addr & PP_HTONL(0xf0000000UL)) == PP_HTONL(0xe0000000UL))


void start_network_data_process_pthread(void);

void NetPkg_Add(int NetType,unsigned char *buf,unsigned int len);

unsigned char Calculate_XorDat(unsigned char *Data, int Length);    //异或和
void network_pkg_process(unsigned char NetPkgType,unsigned char *Pkg, int Pkg_Length);       //网络数据包处理

void pkg_query_current_status(unsigned char *rxbuf);
void Set_zone_idle_status(unsigned char *rxbuf, const char *function, int line, bool canRepeat_paly);
void HOST_QUERY_SET_IP_INFO(unsigned char *pkg_data);
void host_control_reboot(char *rxbuf,unsigned char netType);
void pkg_paging_notification(unsigned char *payload_Buf,int payload_Length,unsigned char ctl_Model,unsigned char IsWebPagering);
void Host_Set_Dsp_Firmware_Feature(unsigned char *pkg_data);
void Host_Set_SerialNumber(unsigned char *pkg_data);

//主机向终端设置网络模式
void ProcessHostSetNetWork(unsigned char * pkg,int pkg_len,bool isMulticast);

int respond_pkg_update_status_code(unsigned char code);
int send_download_rate_of_progress(unsigned char progress);

void pkg_set_local_time(unsigned char *rxbuf);
void requset_Host_Synchroniztion_Time(void);

//发送采集音频流到服务器，以便TCP模式设备接收
void SendAudioCollectorStreamToServer(unsigned char *streamBuf,int streamLen,int channelId);
//发送采集音频流到组播上
void SendAudioCollectorStreamToMultiCast(unsigned char *streamBuf,int streamLen,int channelId);
//服务器查询/设置电源时序器信息
void Host_Query_Set_Sequence_Power_Info(unsigned char *rxbuf);
//服务器发送电源时序器定时点信息
void Host_Send_Sequence_Timing_Info(unsigned char *rxbuf);

//消防采集器通道状态发生改变时主动通知主机
void Fire_Collector_Send_Trig_Status(unsigned int triggerStatus);

/********************************************************************************************/
//主机查询/设置音频混音器
void Host_Set_Audio_Mixer_Config(unsigned char *rxbuf);
//发送混音器音频流到服务器，以便TCP模式设备接收
void SendAudioMixerStreamToServer(unsigned char *streamBuf,int streamLen);
//发送混音器音频流到组播上
void SendAudioMixerStreamToMultiCast(unsigned char *streamBuf,int streamLen);
/********************************************************************************************/

/********************************************************************************************/
//主机查询/设置电话网关
void Host_Set_Phone_Gateway_Config(unsigned char *rxbuf);
//发送电话网关音频流到服务器，以便TCP模式设备接收
void SendPhoneGatewayStreamToServer(unsigned char *streamBuf,int streamLen);
//发送电话网关音频流到组播上
void SendPhoneGatewayStreamToMultiCast(unsigned char *streamBuf,int streamLen);
/********************************************************************************************/

//主机查询/设置音频采集器
void Host_Set_Audio_Collector_Config(unsigned char *rxbuf);

//送远程遥控器按键到服务器
void SendRemoteControlerKeyToServer(unsigned char keyId);


/*************下面指令为对讲终端专用************************/
void Send_calling_invation(unsigned char *calledMac);
bool Recv_calling_invation(unsigned char *Pkg);
bool Recv_called_response(unsigned char *Pkg);
void Send_callStatus_feedback(int status);
void Recv_callStatus_feedback(unsigned char *Pkg);
int Send_Call_Audio_Stream( unsigned char* data,int len );
void Recv_Call_Audio_Stream(unsigned char *Pkg);
/*************上面指令为对讲终端专用************************/

void procHost_Request_Trigger_Play();

//终端向服务器发送json命令
void SendJsonCommandToServer(unsigned char *jsonBuf);


void SEND_SIP_STATUS_TO_SERVER(unsigned char status);

static void HOST_CONTROL_SIP_Status(unsigned char *rxbuf);
static void HOST_QUERY_SIP_INFO();
static void HOST_CONTROL_SIP_LOGIN(unsigned char *rxbuf,int payload_Length);

void procTools_PreSet_Extension(unsigned char *Pkg,int pkgLen);
void procTools_Set_Extension(unsigned char *Pkg,int pkgLen);


//主机查询/设置信息发布参数
void Host_Set_Information_Publish_Config(unsigned char *rxbuf);

//主机查询功放控制器状态
void Host_Query_Set_Amp_Controler_Status(unsigned char *rxbuf);

//主机查询/设置噪声自适应器参数
void Host_Set_NoiseDetector_Config(unsigned char *rxbuf);

//GPS校时器主动同步时间给服务器
void Gps_send_time_sync();
#endif