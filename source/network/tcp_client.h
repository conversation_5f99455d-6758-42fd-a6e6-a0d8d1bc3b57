/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-16 12:54:53 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-09-16 16:34:27
 */
#ifndef _TCP_CLIENT_H_
#define _TCP_CLIENT_H_

#include "const.h"

struct tcp_client_pkg
{
    unsigned char pkg_data[MAX_BUF_SIZE];
    unsigned short pkg_len;
};

typedef struct
{
    int tcp_connect_status;		    //与主机的TCP连接状态，1-连接成功，0-连接失败
    int tcp_exit_flag;			    //退出TCP标志:0代表TCP线程正常工作，1-需要关闭，0xFF-没有启动
    int tcp_reconnect_flag;			//TCP重连标志,1-需要重连，0-不需要重连
    struct sockaddr_in tcp_client_addr;	//tcp客户端连接地址
    int tcp_sockfd;						//tcp客户端句柄
    struct tcp_client_pkg tcp_pkg_info;	//tcp包信息
    int tcp_clinet_fr;    				//tcp first response
}_st_tcp_info;

int tcp_connect_init();
int tcp_get_master_connect_status();

int tcp_get_master_client_fr_flag();
void tcp_set_master_client_fr_flag(int fr_flag);
void TCP_Client_Start();
void host_tcp_send_data(unsigned char *send_buf, int send_len);
void tcp_client_exit();
void tcp_client_reconnect();
#if IS_DEVICE_AUDIO_MIXER
void TCP_Client_Slave_Start();
int tcp_get_slave_connect_status();
int tcp_get_slave_client_fr_flag();
void tcp_set_slave_client_fr_flag(int fr_flag);
#endif

#endif