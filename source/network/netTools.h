/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-04 16:01:46 
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-09-04 16:52:14
 */

#ifndef _NETTOOLS_H_
#define _NETTOOLS_H_

#include <stdbool.h>
#include <net/if.h>

extern struct ifreq nif_eth0,nif_4g;

//判断网络信息数据长度
#define NET_INFO_ERROR(TempStatic_ip_address, TempSubnet_Mask, TempGateWay)	\
								( (strlen(TempStatic_ip_address) > 16 || strlen(TempStatic_ip_address) < 7) || \
								(strlen(TempSubnet_Mask) > 16 || strlen(TempSubnet_Mask) < 7) || \
								(strlen(TempGateWay) > 16 || strlen(TempGateWay) < 7) )		//***************	

int get_netlink_status(const char *if_name);
void set_mac_address(char *macAddr);
int GetLocalIp(char *localIP);
void GetLocalMAC(unsigned char *mac_addr,bool bSet);
void init_network();

int if_a_string_is_a_valid_ipv4_address(const char *str);
int isValidSubnetMask(const char *mask);
int isGatewayByNetmask_Error(const char *ip,const char *mask,const char *gw);
void Get_Network_Info(char *ipaddress,char *netmask,char *gateway);
void nif_init();
bool is_valid_domain(const char *domain);
#endif