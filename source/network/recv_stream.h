/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-06 16:22:43 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-02-24 16:30:14
 */

#ifndef _PAGING_STREAM_H_
#define _PAGING_STREAM_H_

#include "pthread.h"

#define CENTRALIZED_MODE_TIMING_COUNT_MAX 	25//(5s)
#define AUDIO_COLLECT_TIMING_COUNT_MAX      6//(6s)
#define PAGING_TIMING_COUNT_MAX             50//(5s)

#define AUDIO_CALL_TIMING_COUNT_MAX         500//(5s)
#define AUDIO_MIXER_TIMING_COUNT_MAX        50//(5s)
#define PHONE_GATEWAY_TIMING_COUNT_MAX      50//(5s)

typedef struct
{
    unsigned char authority;
    unsigned char volume;
    unsigned char pager_mac[6];
    int audioRate;
    unsigned char audioFMT;
    unsigned char audioChannels;
    unsigned char decodeType;
    unsigned char multicastIP[16];
	int multicastPort;
	unsigned char pagingType;	//呼叫类型，0-旧寻呼台 1-寻呼台MIC寻呼	2-WEB广播寻呼	3-寻呼台音乐广播
}stPager_property;

extern stPager_property pager_property;

/***********Web Paging Start*******/
extern unsigned int pre_web_pager_strem_timeout;			//上一个APP寻呼流的超时时间
extern unsigned char pre_web_pager_cmd_pkg[MAX_BUF_SIZE];	//上一个APP寻呼的发起包1
/***********Web Paging End*******/


extern pthread_mutex_t mutex_cleanPagerStream;	//清理寻呼音频流互斥锁
extern pthread_mutex_t mutex_cleanMixedStream;	//清理混音音频流互斥锁
extern pthread_mutex_t mutex_cleanPhoneGatewayStream;	//清理电话网关音频流互斥锁

extern bool isMulticastPagerRecvThreadRun;	//组播寻呼接收线程运行标志

void start_concentrated_recv_pthread(void);
void Clean_Concentrated_Info(void);

void suspend_Centralized_Mode_Timing_Task(void);
void resume_Centralized_Mode_Timing_Task(void);
void Create_Centralized_Mode_Timing_Task(void);

void suspend_AudioCollector_Timing_Task(void);
void resume_AudioCollector_Timing_Task(void);
void Create_AudioCollector_Timing_Task(void);

void start_mcast_paging_recv_pthread(void);
void Create_Paging_Timing_Task(void);

void Clean_Pager_Pcm_Buf(void);
void Kcp_Pager_Stream_Recv(unsigned char *buf,int len);
void kcp_concentrated_ser_data(unsigned char *buf,int len);

void suspend_AudioMixer_Timing_Task(void);
void Clean_Mixed_Info(void);
void Create_AudioMixer_Timing_Task(void);
void start_AudioMixer_recv_pthread(void);

void suspend_PhoneGateway_Timing_Task(void);
void Clean_PhoneGateway_Info(void);
void Create_PhoneGateway_Timing_Task(void);
void start_PhoneGateway_recv_pthread(void);

void Create_AudioCall_Timing_Task(void);
void Create_AudioCall_Ring_Task(void);

#endif