#include "../../lvgl.h"

/*******************************************************************************
 * Size: 20 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 20 --font <PERSON>-Medium.ttf -r 0x20-0x7F,0xB0,0x2022 --font <PERSON>ont<PERSON>wesome5-Solid+Brands+Regular.woff -r 61441,61448,61451,61452,61452,61453,61457,61459,61461,61465,61468,61473,61478,61479,61480,61502,61512,61515,61516,61517,61521,61522,61523,61524,61543,61544,61550,61552,61553,61556,61559,61560,61561,61563,61587,61589,61636,61637,61639,61671,61674,61683,61724,61732,61787,61931,62016,62017,62018,62019,62020,62087,62099,62212,62189,62810,63426,63650 --format lvgl -o lv_font_montserrat_20.c --force-fast-kern-format
 ******************************************************************************/

#ifndef LV_FONT_MONTSERRAT_20
#define LV_FONT_MONTSERRAT_20 1
#endif

#if LV_FONT_MONTSERRAT_20

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t gylph_bitmap[] = {
    /* U+20 " " */

    /* U+21 "!" */
    0x6f, 0xc6, 0xfc, 0x5f, 0xb4, 0xfa, 0x4f, 0xa3,
    0xf9, 0x3f, 0x92, 0xf8, 0x2f, 0x71, 0xd6, 0x0,
    0x1, 0x94, 0x9f, 0xe4, 0xf9,

    /* U+22 "\"" */
    0xbe, 0x1, 0xf8, 0xbe, 0x1, 0xf8, 0xad, 0x1,
    0xf7, 0xad, 0x0, 0xf7, 0xad, 0x0, 0xf7, 0x57,
    0x0, 0x83,

    /* U+23 "#" */
    0x0, 0x0, 0x7f, 0x0, 0x4, 0xf2, 0x0, 0x0,
    0x0, 0xac, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x0,
    0xca, 0x0, 0x9, 0xd0, 0x0, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x6, 0x88, 0xfb, 0x88,
    0x8e, 0xc8, 0x84, 0x0, 0x2, 0xf4, 0x0, 0xf,
    0x70, 0x0, 0x0, 0x3, 0xf2, 0x0, 0xf, 0x50,
    0x0, 0x0, 0x5, 0xf1, 0x0, 0x2f, 0x40, 0x0,
    0x0, 0x7, 0xf0, 0x0, 0x4f, 0x20, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x38, 0x8d,
    0xd8, 0x88, 0xcf, 0x88, 0x70, 0x0, 0xd, 0x90,
    0x0, 0xac, 0x0, 0x0, 0x0, 0xf, 0x70, 0x0,
    0xca, 0x0, 0x0, 0x0, 0x1f, 0x50, 0x0, 0xe8,
    0x0, 0x0,

    /* U+24 "$" */
    0x0, 0x0, 0x5, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf0,
    0x0, 0x0, 0x0, 0x7, 0xcf, 0xff, 0xc8, 0x10,
    0x1, 0xdf, 0xfe, 0xfd, 0xff, 0xd0, 0x8, 0xfc,
    0x15, 0xf0, 0x6, 0x60, 0xc, 0xf3, 0x5, 0xf0,
    0x0, 0x0, 0xc, 0xf5, 0x5, 0xf0, 0x0, 0x0,
    0x6, 0xfe, 0x76, 0xf0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xf9, 0x40, 0x0, 0x0, 0x2, 0x7c, 0xff,
    0xfe, 0x40, 0x0, 0x0, 0x5, 0xf4, 0xaf, 0xf2,
    0x0, 0x0, 0x5, 0xf0, 0x9, 0xf7, 0x1, 0x0,
    0x5, 0xf0, 0x6, 0xf8, 0xc, 0x92, 0x5, 0xf0,
    0x2d, 0xf4, 0xc, 0xff, 0xed, 0xfd, 0xff, 0xa0,
    0x0, 0x4a, 0xef, 0xff, 0xc6, 0x0, 0x0, 0x0,
    0x5, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x70, 0x0, 0x0,

    /* U+25 "%" */
    0x0, 0x9e, 0xe9, 0x0, 0x0, 0x0, 0xda, 0x0,
    0x0, 0xad, 0x44, 0xda, 0x0, 0x0, 0x9e, 0x10,
    0x0, 0x1f, 0x40, 0x4, 0xf1, 0x0, 0x4f, 0x40,
    0x0, 0x3, 0xf1, 0x0, 0x1f, 0x30, 0x1e, 0x90,
    0x0, 0x0, 0x2f, 0x30, 0x3, 0xf1, 0xa, 0xd0,
    0x0, 0x0, 0x0, 0xcb, 0x11, 0xbb, 0x5, 0xf3,
    0x0, 0x0, 0x0, 0x1, 0xcf, 0xfc, 0x11, 0xe8,
    0x1a, 0xfe, 0x70, 0x0, 0x0, 0x11, 0x0, 0xad,
    0xa, 0xd4, 0x5f, 0x60, 0x0, 0x0, 0x0, 0x5f,
    0x32, 0xf3, 0x0, 0x7e, 0x0, 0x0, 0x0, 0x1f,
    0x70, 0x4f, 0x0, 0x4, 0xf0, 0x0, 0x0, 0xb,
    0xc0, 0x4, 0xf0, 0x0, 0x3f, 0x0, 0x0, 0x6,
    0xf2, 0x0, 0x2f, 0x20, 0x6, 0xe0, 0x0, 0x2,
    0xf7, 0x0, 0x0, 0xbb, 0x23, 0xe6, 0x0, 0x0,
    0xcc, 0x0, 0x0, 0x1, 0xaf, 0xe8, 0x0,

    /* U+26 "&" */
    0x0, 0x1, 0x9d, 0xfd, 0x70, 0x0, 0x0, 0x0,
    0xd, 0xf9, 0x7b, 0xf7, 0x0, 0x0, 0x0, 0x4f,
    0x90, 0x0, 0xdc, 0x0, 0x0, 0x0, 0x5f, 0x70,
    0x0, 0xec, 0x0, 0x0, 0x0, 0x1f, 0xe1, 0x1b,
    0xf4, 0x0, 0x0, 0x0, 0x5, 0xfd, 0xef, 0x50,
    0x0, 0x0, 0x0, 0x4, 0xef, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xc7, 0xfd, 0x20, 0x8, 0x30,
    0x7, 0xf9, 0x0, 0x5f, 0xe2, 0x1f, 0x80, 0xe,
    0xe0, 0x0, 0x5, 0xfe, 0xaf, 0x30, 0xf, 0xd0,
    0x0, 0x0, 0x4f, 0xfc, 0x0, 0xd, 0xf7, 0x0,
    0x0, 0x5e, 0xff, 0x30, 0x4, 0xff, 0xeb, 0xbe,
    0xfe, 0x6f, 0xf2, 0x0, 0x29, 0xdf, 0xfc, 0x70,
    0x3, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+27 "'" */
    0xbe, 0xbe, 0xad, 0xad, 0xad, 0x57,

    /* U+28 "(" */
    0x0, 0xe, 0xd0, 0x0, 0x7f, 0x60, 0x0, 0xef,
    0x0, 0x3, 0xfa, 0x0, 0x8, 0xf5, 0x0, 0xb,
    0xf2, 0x0, 0xe, 0xf0, 0x0, 0xf, 0xe0, 0x0,
    0xf, 0xd0, 0x0, 0x1f, 0xc0, 0x0, 0xf, 0xd0,
    0x0, 0xf, 0xe0, 0x0, 0xe, 0xf0, 0x0, 0xb,
    0xf2, 0x0, 0x8, 0xf5, 0x0, 0x3, 0xfa, 0x0,
    0x0, 0xee, 0x0, 0x0, 0x7f, 0x60, 0x0, 0xe,
    0xd0,

    /* U+29 ")" */
    0x2f, 0xb0, 0x0, 0xaf, 0x30, 0x3, 0xfa, 0x0,
    0xe, 0xf0, 0x0, 0x9f, 0x40, 0x6, 0xf7, 0x0,
    0x3f, 0xa0, 0x2, 0xfb, 0x0, 0x1f, 0xc0, 0x0,
    0xfd, 0x0, 0x1f, 0xc0, 0x2, 0xfb, 0x0, 0x3f,
    0xa0, 0x6, 0xf7, 0x0, 0x9f, 0x40, 0xe, 0xf0,
    0x3, 0xfa, 0x0, 0xaf, 0x30, 0x2f, 0xb0, 0x0,

    /* U+2A "*" */
    0x0, 0x9, 0x90, 0x0, 0x26, 0x9, 0x90, 0x62,
    0x5f, 0xcb, 0xbc, 0xf5, 0x2, 0xbf, 0xfb, 0x20,
    0x7, 0xef, 0xfe, 0x70, 0x6f, 0x69, 0x96, 0xf6,
    0x1, 0x9, 0x90, 0x10, 0x0, 0x6, 0x60, 0x0,

    /* U+2B "+" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xfa, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xf4, 0x6a, 0xaa,
    0xfd, 0xaa, 0xa2, 0x0, 0x0, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xfa,
    0x0, 0x0,

    /* U+2C "," */
    0x6, 0xa1, 0xf, 0xf8, 0xa, 0xf7, 0x5, 0xf2,
    0x9, 0xc0, 0xd, 0x70,

    /* U+2D "-" */
    0x9b, 0xbb, 0xb5, 0xdf, 0xff, 0xf8,

    /* U+2E "." */
    0x7, 0xb2, 0xf, 0xf8, 0xa, 0xe4,

    /* U+2F "/" */
    0x0, 0x0, 0x0, 0x7, 0x50, 0x0, 0x0, 0x4,
    0xf7, 0x0, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x0,
    0xf, 0xc0, 0x0, 0x0, 0x4, 0xf6, 0x0, 0x0,
    0x0, 0xaf, 0x10, 0x0, 0x0, 0xf, 0xc0, 0x0,
    0x0, 0x5, 0xf6, 0x0, 0x0, 0x0, 0xaf, 0x10,
    0x0, 0x0, 0xf, 0xb0, 0x0, 0x0, 0x5, 0xf6,
    0x0, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x0, 0xf,
    0xb0, 0x0, 0x0, 0x5, 0xf6, 0x0, 0x0, 0x0,
    0xbf, 0x10, 0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0,
    0x6, 0xf5, 0x0, 0x0, 0x0, 0xbf, 0x0, 0x0,
    0x0, 0x1f, 0xb0, 0x0, 0x0, 0x6, 0xf5, 0x0,
    0x0, 0x0,

    /* U+30 "0" */
    0x0, 0x1, 0x8d, 0xfe, 0xa3, 0x0, 0x0, 0x2,
    0xef, 0xfd, 0xef, 0xf6, 0x0, 0x0, 0xdf, 0xa1,
    0x0, 0x6f, 0xf2, 0x0, 0x6f, 0xc0, 0x0, 0x0,
    0x7f, 0xb0, 0xb, 0xf4, 0x0, 0x0, 0x0, 0xff,
    0x0, 0xef, 0x10, 0x0, 0x0, 0xc, 0xf3, 0xf,
    0xf0, 0x0, 0x0, 0x0, 0xaf, 0x50, 0xff, 0x0,
    0x0, 0x0, 0xa, 0xf5, 0xe, 0xf1, 0x0, 0x0,
    0x0, 0xcf, 0x30, 0xbf, 0x40, 0x0, 0x0, 0xf,
    0xf0, 0x6, 0xfc, 0x0, 0x0, 0x7, 0xfb, 0x0,
    0xd, 0xfa, 0x10, 0x6, 0xff, 0x20, 0x0, 0x2e,
    0xff, 0xdf, 0xff, 0x60, 0x0, 0x0, 0x18, 0xdf,
    0xea, 0x30, 0x0,

    /* U+31 "1" */
    0xdf, 0xff, 0xf4, 0xac, 0xce, 0xf4, 0x0, 0xb,
    0xf4, 0x0, 0xb, 0xf4, 0x0, 0xb, 0xf4, 0x0,
    0xb, 0xf4, 0x0, 0xb, 0xf4, 0x0, 0xb, 0xf4,
    0x0, 0xb, 0xf4, 0x0, 0xb, 0xf4, 0x0, 0xb,
    0xf4, 0x0, 0xb, 0xf4, 0x0, 0xb, 0xf4, 0x0,
    0xb, 0xf4,

    /* U+32 "2" */
    0x0, 0x6c, 0xef, 0xea, 0x30, 0x2, 0xdf, 0xfe,
    0xdf, 0xff, 0x50, 0x5f, 0x91, 0x0, 0x9, 0xfe,
    0x0, 0x10, 0x0, 0x0, 0xe, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x20, 0x0, 0x0, 0x0, 0x2f,
    0xd0, 0x0, 0x0, 0x0, 0x1d, 0xf5, 0x0, 0x0,
    0x0, 0x1c, 0xf8, 0x0, 0x0, 0x0, 0x1d, 0xf8,
    0x0, 0x0, 0x0, 0x1d, 0xf8, 0x0, 0x0, 0x0,
    0x2e, 0xf7, 0x0, 0x0, 0x0, 0x2e, 0xf6, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xcc, 0xcc, 0xcc, 0x94,
    0xff, 0xff, 0xff, 0xff, 0xfc,

    /* U+33 "3" */
    0x4f, 0xff, 0xff, 0xff, 0xff, 0x3, 0xcc, 0xcc,
    0xcc, 0xef, 0xd0, 0x0, 0x0, 0x0, 0x2f, 0xe2,
    0x0, 0x0, 0x0, 0x1d, 0xf4, 0x0, 0x0, 0x0,
    0xc, 0xf6, 0x0, 0x0, 0x0, 0x9, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xfe, 0x80, 0x0, 0x0,
    0x6, 0x68, 0xef, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x50, 0x0, 0x0, 0x0, 0x8, 0xf8, 0x1,
    0x0, 0x0, 0x0, 0xaf, 0x77, 0xe6, 0x10, 0x0,
    0x6f, 0xf2, 0x7f, 0xff, 0xee, 0xff, 0xf6, 0x0,
    0x28, 0xcf, 0xfe, 0xa3, 0x0,

    /* U+34 "4" */
    0x0, 0x0, 0x0, 0x7, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0x30, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfd, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x2e, 0xf2, 0x0, 0x6f, 0x70,
    0x0, 0x0, 0xcf, 0x50, 0x0, 0x6f, 0x70, 0x0,
    0x9, 0xf9, 0x0, 0x0, 0x6f, 0x70, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x2c, 0xcc,
    0xcc, 0xcc, 0xdf, 0xec, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0x70, 0x0,

    /* U+35 "5" */
    0x0, 0xff, 0xff, 0xff, 0xff, 0x0, 0x1f, 0xfc,
    0xcc, 0xcc, 0xc0, 0x2, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0x90, 0x0, 0x0, 0x0, 0x6, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xec, 0xca, 0x72,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x2, 0x8f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xa0, 0x0, 0x0, 0x0, 0x4, 0xfc, 0x2,
    0x0, 0x0, 0x0, 0x6f, 0xa3, 0xf8, 0x20, 0x0,
    0x5f, 0xf4, 0x4f, 0xff, 0xed, 0xff, 0xf9, 0x0,
    0x17, 0xce, 0xfe, 0xb5, 0x0,

    /* U+36 "6" */
    0x0, 0x0, 0x5b, 0xef, 0xeb, 0x60, 0x0, 0xb,
    0xff, 0xec, 0xdf, 0xb0, 0x0, 0xaf, 0xb2, 0x0,
    0x1, 0x10, 0x4, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf0,
    0x6c, 0xff, 0xc6, 0x0, 0xf, 0xfa, 0xfd, 0xbc,
    0xff, 0xa0, 0xf, 0xff, 0x60, 0x0, 0x2e, 0xf5,
    0xf, 0xfa, 0x0, 0x0, 0x5, 0xfa, 0xc, 0xf7,
    0x0, 0x0, 0x3, 0xfc, 0x7, 0xfa, 0x0, 0x0,
    0x5, 0xfa, 0x1, 0xef, 0x60, 0x0, 0x2e, 0xf4,
    0x0, 0x4f, 0xfe, 0xbc, 0xff, 0x80, 0x0, 0x1,
    0x9d, 0xfe, 0xb4, 0x0,

    /* U+37 "7" */
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x6f, 0xec,
    0xcc, 0xcc, 0xdf, 0xf1, 0x6f, 0x80, 0x0, 0x0,
    0x6f, 0xa0, 0x6f, 0x80, 0x0, 0x0, 0xdf, 0x30,
    0x14, 0x20, 0x0, 0x4, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x60, 0x0,
    0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x20, 0x0, 0x0, 0x0, 0x6,
    0xfb, 0x0, 0x0, 0x0,

    /* U+38 "8" */
    0x0, 0x6, 0xce, 0xfe, 0xb5, 0x0, 0x0, 0xcf,
    0xfc, 0xac, 0xff, 0xb0, 0x6, 0xfc, 0x10, 0x0,
    0x2d, 0xf4, 0x9, 0xf6, 0x0, 0x0, 0x8, 0xf7,
    0x6, 0xfb, 0x0, 0x0, 0x1d, 0xf4, 0x0, 0xaf,
    0xea, 0x9a, 0xff, 0x80, 0x0, 0x5e, 0xff, 0xff,
    0xfe, 0x40, 0x6, 0xfe, 0x61, 0x2, 0x7f, 0xf4,
    0xe, 0xf3, 0x0, 0x0, 0x5, 0xfc, 0x1f, 0xe0,
    0x0, 0x0, 0x0, 0xff, 0xf, 0xf1, 0x0, 0x0,
    0x3, 0xfe, 0xa, 0xfb, 0x10, 0x0, 0x2d, 0xf8,
    0x1, 0xdf, 0xfc, 0xbc, 0xff, 0xc0, 0x0, 0x7,
    0xce, 0xfe, 0xb6, 0x0,

    /* U+39 "9" */
    0x0, 0x3a, 0xef, 0xeb, 0x40, 0x0, 0x6, 0xff,
    0xda, 0xcf, 0xf8, 0x0, 0x1f, 0xf4, 0x0, 0x1,
    0xcf, 0x50, 0x5f, 0x90, 0x0, 0x0, 0x2f, 0xd0,
    0x6f, 0x90, 0x0, 0x0, 0x3f, 0xf1, 0x2f, 0xf4,
    0x0, 0x1, 0xcf, 0xf4, 0x8, 0xff, 0xda, 0xbf,
    0xec, 0xf5, 0x0, 0x4b, 0xef, 0xd9, 0x1a, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x90, 0x0, 0x40, 0x0, 0x1a, 0xfe, 0x10,
    0x6, 0xfe, 0xdd, 0xff, 0xe3, 0x0, 0x3, 0xad,
    0xfe, 0xc7, 0x10, 0x0,

    /* U+3A ":" */
    0xa, 0xe4, 0xf, 0xf8, 0x7, 0xb2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xb2, 0xf, 0xf8, 0xa, 0xe4,

    /* U+3B ";" */
    0xa, 0xe4, 0xf, 0xf8, 0x7, 0xb2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xa1, 0xf, 0xf8, 0xa, 0xf7, 0x5, 0xf2,
    0x9, 0xc0, 0xd, 0x70,

    /* U+3C "<" */
    0x0, 0x0, 0x0, 0x0, 0x63, 0x0, 0x0, 0x2,
    0x9f, 0xf4, 0x0, 0x5, 0xcf, 0xfb, 0x40, 0x28,
    0xef, 0xe8, 0x10, 0x0, 0xaf, 0xc4, 0x0, 0x0,
    0x0, 0x8f, 0xfa, 0x40, 0x0, 0x0, 0x2, 0x9e,
    0xfd, 0x71, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xb2,
    0x0, 0x0, 0x0, 0x39, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x1,

    /* U+3D "=" */
    0xaf, 0xff, 0xff, 0xff, 0xf4, 0x6a, 0xaa, 0xaa,
    0xaa, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xf4, 0x6a, 0xaa,
    0xaa, 0xaa, 0xa2,

    /* U+3E ">" */
    0x63, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xd6, 0x10,
    0x0, 0x0, 0x17, 0xdf, 0xf9, 0x30, 0x0, 0x0,
    0x3, 0xaf, 0xfc, 0x60, 0x0, 0x0, 0x1, 0x6e,
    0xf4, 0x0, 0x0, 0x6, 0xcf, 0xf3, 0x0, 0x39,
    0xff, 0xd6, 0x0, 0x5d, 0xff, 0xa3, 0x0, 0x0,
    0xad, 0x71, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0,

    /* U+3F "?" */
    0x0, 0x7c, 0xef, 0xda, 0x30, 0x2, 0xef, 0xfc,
    0xce, 0xff, 0x60, 0x6f, 0x80, 0x0, 0x8, 0xfe,
    0x0, 0x10, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x60, 0x0, 0x0, 0x1, 0xcf, 0x80, 0x0, 0x0,
    0x0, 0xcf, 0x80, 0x0, 0x0, 0x0, 0x4f, 0xc0,
    0x0, 0x0, 0x0, 0x3, 0x74, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x93,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xc0, 0x0, 0x0,
    0x0, 0x6, 0xf8, 0x0, 0x0,

    /* U+40 "@" */
    0x0, 0x0, 0x0, 0x49, 0xdf, 0xfe, 0xc9, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xfd, 0x97, 0x67,
    0x9e, 0xfb, 0x10, 0x0, 0x0, 0x5, 0xfd, 0x30,
    0x0, 0x0, 0x0, 0x4d, 0xe3, 0x0, 0x0, 0x3f,
    0xa0, 0x0, 0x58, 0x85, 0x3, 0x94, 0xbe, 0x10,
    0x0, 0xec, 0x0, 0x2d, 0xff, 0xff, 0xd7, 0xf5,
    0x1d, 0xb0, 0x6, 0xf3, 0x1, 0xef, 0x71, 0x4,
    0xdf, 0xf5, 0x5, 0xf2, 0xb, 0xd0, 0x8, 0xf7,
    0x0, 0x0, 0x1e, 0xf5, 0x0, 0xe7, 0xe, 0x90,
    0xc, 0xf0, 0x0, 0x0, 0x8, 0xf5, 0x0, 0xca,
    0xf, 0x70, 0xe, 0xe0, 0x0, 0x0, 0x6, 0xf5,
    0x0, 0xab, 0xf, 0x70, 0xd, 0xf0, 0x0, 0x0,
    0x6, 0xf5, 0x0, 0xba, 0xe, 0x90, 0xa, 0xf3,
    0x0, 0x0, 0xb, 0xf5, 0x0, 0xd8, 0xb, 0xd0,
    0x3, 0xfd, 0x10, 0x0, 0x7f, 0xf7, 0x3, 0xf4,
    0x6, 0xf3, 0x0, 0x7f, 0xfa, 0x9d, 0xf7, 0xfe,
    0xae, 0xc0, 0x0, 0xec, 0x0, 0x4, 0xcf, 0xfb,
    0x40, 0x5e, 0xfa, 0x10, 0x0, 0x3f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xfd, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3d, 0xfd, 0x97, 0x68, 0xaf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xdf, 0xfd,
    0xb7, 0x10, 0x0, 0x0,

    /* U+41 "A" */
    0x0, 0x0, 0x0, 0xe, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xaf, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfa, 0x1f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xf3, 0xa, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xc0, 0x3, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x50, 0x0, 0xcf, 0x30, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x5f, 0xb0, 0x0,
    0x0, 0x8, 0xf7, 0x0, 0x0, 0xe, 0xf2, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x6f, 0xda, 0xaa, 0xaa, 0xaa, 0xff, 0x10,
    0x0, 0xdf, 0x30, 0x0, 0x0, 0x0, 0x9f, 0x80,
    0x5, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xe0,
    0xc, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf6,

    /* U+42 "B" */
    0xef, 0xff, 0xff, 0xfe, 0xb5, 0x0, 0xe, 0xfb,
    0xaa, 0xab, 0xdf, 0xfa, 0x0, 0xef, 0x10, 0x0,
    0x0, 0x4f, 0xf3, 0xe, 0xf1, 0x0, 0x0, 0x0,
    0xaf, 0x60, 0xef, 0x10, 0x0, 0x0, 0xc, 0xf4,
    0xe, 0xf1, 0x0, 0x0, 0x29, 0xfc, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0xe, 0xfb, 0xaa,
    0xaa, 0xce, 0xfd, 0x10, 0xef, 0x10, 0x0, 0x0,
    0x8, 0xfb, 0xe, 0xf1, 0x0, 0x0, 0x0, 0xf,
    0xf0, 0xef, 0x10, 0x0, 0x0, 0x0, 0xff, 0x1e,
    0xf1, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0xef, 0xba,
    0xaa, 0xab, 0xef, 0xf4, 0xe, 0xff, 0xff, 0xff,
    0xfd, 0x92, 0x0,

    /* U+43 "C" */
    0x0, 0x0, 0x17, 0xce, 0xfe, 0xb5, 0x0, 0x0,
    0x5, 0xef, 0xff, 0xde, 0xff, 0xc1, 0x0, 0x5f,
    0xf9, 0x20, 0x0, 0x3b, 0xf5, 0x2, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x30, 0x8, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x30, 0x0, 0x5f, 0xfa,
    0x30, 0x0, 0x3b, 0xf5, 0x0, 0x5, 0xef, 0xff,
    0xdf, 0xff, 0xc1, 0x0, 0x0, 0x17, 0xce, 0xfe,
    0xb5, 0x0,

    /* U+44 "D" */
    0xef, 0xff, 0xff, 0xfd, 0xa4, 0x0, 0x0, 0xef,
    0xdc, 0xcc, 0xdf, 0xff, 0xb1, 0x0, 0xef, 0x10,
    0x0, 0x0, 0x5d, 0xfd, 0x0, 0xef, 0x10, 0x0,
    0x0, 0x0, 0xcf, 0xa0, 0xef, 0x10, 0x0, 0x0,
    0x0, 0x1f, 0xf1, 0xef, 0x10, 0x0, 0x0, 0x0,
    0xa, 0xf6, 0xef, 0x10, 0x0, 0x0, 0x0, 0x8,
    0xf8, 0xef, 0x10, 0x0, 0x0, 0x0, 0x7, 0xf8,
    0xef, 0x10, 0x0, 0x0, 0x0, 0xa, 0xf6, 0xef,
    0x10, 0x0, 0x0, 0x0, 0x1f, 0xf1, 0xef, 0x10,
    0x0, 0x0, 0x0, 0xbf, 0xa0, 0xef, 0x10, 0x0,
    0x0, 0x5d, 0xfd, 0x10, 0xef, 0xcc, 0xcc, 0xdf,
    0xff, 0xb1, 0x0, 0xef, 0xff, 0xff, 0xfd, 0xa4,
    0x0, 0x0,

    /* U+45 "E" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xe, 0xfd, 0xcc,
    0xcc, 0xcc, 0xc0, 0xef, 0x10, 0x0, 0x0, 0x0,
    0xe, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xef, 0x10,
    0x0, 0x0, 0x0, 0xe, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xf2, 0xe, 0xfc,
    0xcc, 0xcc, 0xcc, 0x10, 0xef, 0x10, 0x0, 0x0,
    0x0, 0xe, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x10, 0x0, 0x0, 0x0, 0xe, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xcc, 0xcc, 0xcc, 0xcc, 0x3e,
    0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+46 "F" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xef, 0xdc, 0xcc,
    0xcc, 0xcc, 0xef, 0x10, 0x0, 0x0, 0x0, 0xef,
    0x10, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0,
    0x0, 0xef, 0x10, 0x0, 0x0, 0x0, 0xef, 0xcc,
    0xcc, 0xcc, 0xc1, 0xef, 0xff, 0xff, 0xff, 0xf2,
    0xef, 0x10, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0,
    0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x0, 0xef,
    0x10, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0,
    0x0, 0xef, 0x10, 0x0, 0x0, 0x0,

    /* U+47 "G" */
    0x0, 0x0, 0x16, 0xce, 0xfe, 0xb6, 0x0, 0x0,
    0x5, 0xef, 0xff, 0xde, 0xff, 0xd2, 0x0, 0x5f,
    0xf9, 0x20, 0x0, 0x29, 0xf7, 0x2, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x20, 0x8, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x11, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x3, 0xfb,
    0xd, 0xf3, 0x0, 0x0, 0x0, 0x3, 0xfb, 0x8,
    0xfa, 0x0, 0x0, 0x0, 0x3, 0xfb, 0x1, 0xff,
    0x60, 0x0, 0x0, 0x3, 0xfb, 0x0, 0x5f, 0xfa,
    0x30, 0x0, 0x2a, 0xfb, 0x0, 0x4, 0xef, 0xff,
    0xdf, 0xff, 0xe4, 0x0, 0x0, 0x17, 0xce, 0xfe,
    0xb6, 0x0,

    /* U+48 "H" */
    0xef, 0x10, 0x0, 0x0, 0x0, 0xdf, 0x2e, 0xf1,
    0x0, 0x0, 0x0, 0xd, 0xf2, 0xef, 0x10, 0x0,
    0x0, 0x0, 0xdf, 0x2e, 0xf1, 0x0, 0x0, 0x0,
    0xd, 0xf2, 0xef, 0x10, 0x0, 0x0, 0x0, 0xdf,
    0x2e, 0xf1, 0x0, 0x0, 0x0, 0xd, 0xf2, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x2e, 0xfd, 0xcc,
    0xcc, 0xcc, 0xcf, 0xf2, 0xef, 0x10, 0x0, 0x0,
    0x0, 0xdf, 0x2e, 0xf1, 0x0, 0x0, 0x0, 0xd,
    0xf2, 0xef, 0x10, 0x0, 0x0, 0x0, 0xdf, 0x2e,
    0xf1, 0x0, 0x0, 0x0, 0xd, 0xf2, 0xef, 0x10,
    0x0, 0x0, 0x0, 0xdf, 0x2e, 0xf1, 0x0, 0x0,
    0x0, 0xd, 0xf2,

    /* U+49 "I" */
    0xef, 0x1e, 0xf1, 0xef, 0x1e, 0xf1, 0xef, 0x1e,
    0xf1, 0xef, 0x1e, 0xf1, 0xef, 0x1e, 0xf1, 0xef,
    0x1e, 0xf1, 0xef, 0x1e, 0xf1,

    /* U+4A "J" */
    0x0, 0xcf, 0xff, 0xff, 0xf4, 0x0, 0x9c, 0xcc,
    0xcf, 0xf4, 0x0, 0x0, 0x0, 0xb, 0xf4, 0x0,
    0x0, 0x0, 0xb, 0xf4, 0x0, 0x0, 0x0, 0xb,
    0xf4, 0x0, 0x0, 0x0, 0xb, 0xf4, 0x0, 0x0,
    0x0, 0xb, 0xf4, 0x0, 0x0, 0x0, 0xb, 0xf4,
    0x0, 0x0, 0x0, 0xb, 0xf4, 0x0, 0x0, 0x0,
    0xb, 0xf4, 0x1, 0x0, 0x0, 0xd, 0xf2, 0xc,
    0xc2, 0x0, 0x6f, 0xe0, 0xa, 0xff, 0xde, 0xff,
    0x60, 0x0, 0x6c, 0xff, 0xc5, 0x0,

    /* U+4B "K" */
    0xef, 0x10, 0x0, 0x0, 0xb, 0xf8, 0xe, 0xf1,
    0x0, 0x0, 0xb, 0xf8, 0x0, 0xef, 0x10, 0x0,
    0xb, 0xf9, 0x0, 0xe, 0xf1, 0x0, 0xb, 0xfa,
    0x0, 0x0, 0xef, 0x10, 0xa, 0xfb, 0x0, 0x0,
    0xe, 0xf1, 0xa, 0xfb, 0x0, 0x0, 0x0, 0xef,
    0x19, 0xff, 0x30, 0x0, 0x0, 0xe, 0xfa, 0xfe,
    0xfe, 0x10, 0x0, 0x0, 0xef, 0xfd, 0x1a, 0xfc,
    0x0, 0x0, 0xe, 0xfd, 0x10, 0xc, 0xfa, 0x0,
    0x0, 0xef, 0x20, 0x0, 0x1e, 0xf7, 0x0, 0xe,
    0xf1, 0x0, 0x0, 0x2f, 0xf4, 0x0, 0xef, 0x10,
    0x0, 0x0, 0x4f, 0xf2, 0xe, 0xf1, 0x0, 0x0,
    0x0, 0x6f, 0xd1,

    /* U+4C "L" */
    0xef, 0x10, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0,
    0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x0, 0xef,
    0x10, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0,
    0x0, 0xef, 0x10, 0x0, 0x0, 0x0, 0xef, 0x10,
    0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x0,
    0xef, 0x10, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0,
    0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x0, 0xef,
    0x10, 0x0, 0x0, 0x0, 0xef, 0xcc, 0xcc, 0xcc,
    0xc8, 0xef, 0xff, 0xff, 0xff, 0xfb,

    /* U+4D "M" */
    0xef, 0x10, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfe,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xef,
    0xf3, 0x0, 0x0, 0x0, 0x2, 0xff, 0xfe, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xef, 0x9f,
    0x60, 0x0, 0x0, 0x4f, 0xaf, 0xfe, 0xf1, 0xee,
    0x10, 0x0, 0xd, 0xf1, 0xff, 0xef, 0x6, 0xf9,
    0x0, 0x7, 0xf7, 0xe, 0xfe, 0xf0, 0xc, 0xf3,
    0x1, 0xfd, 0x0, 0xef, 0xef, 0x0, 0x3f, 0xc0,
    0xaf, 0x40, 0xe, 0xfe, 0xf0, 0x0, 0x9f, 0x9f,
    0xa0, 0x0, 0xef, 0xef, 0x0, 0x1, 0xef, 0xf1,
    0x0, 0xe, 0xfe, 0xf0, 0x0, 0x6, 0xf7, 0x0,
    0x0, 0xef, 0xef, 0x0, 0x0, 0x4, 0x0, 0x0,
    0xe, 0xfe, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef,

    /* U+4E "N" */
    0xef, 0x20, 0x0, 0x0, 0x0, 0xdf, 0x2e, 0xfd,
    0x10, 0x0, 0x0, 0xd, 0xf2, 0xef, 0xfb, 0x0,
    0x0, 0x0, 0xdf, 0x2e, 0xfe, 0xf8, 0x0, 0x0,
    0xd, 0xf2, 0xef, 0x4f, 0xf5, 0x0, 0x0, 0xdf,
    0x2e, 0xf1, 0x5f, 0xf3, 0x0, 0xd, 0xf2, 0xef,
    0x10, 0x8f, 0xe1, 0x0, 0xdf, 0x2e, 0xf1, 0x0,
    0xbf, 0xc0, 0xd, 0xf2, 0xef, 0x10, 0x1, 0xdf,
    0x90, 0xdf, 0x2e, 0xf1, 0x0, 0x2, 0xff, 0x6d,
    0xf2, 0xef, 0x10, 0x0, 0x5, 0xff, 0xff, 0x2e,
    0xf1, 0x0, 0x0, 0x8, 0xff, 0xf2, 0xef, 0x10,
    0x0, 0x0, 0xb, 0xff, 0x2e, 0xf1, 0x0, 0x0,
    0x0, 0xd, 0xf2,

    /* U+4F "O" */
    0x0, 0x0, 0x16, 0xce, 0xfe, 0xb5, 0x0, 0x0,
    0x0, 0x4, 0xef, 0xfe, 0xdf, 0xff, 0xd3, 0x0,
    0x0, 0x5f, 0xf9, 0x20, 0x0, 0x3b, 0xff, 0x20,
    0x1, 0xff, 0x60, 0x0, 0x0, 0x0, 0x9f, 0xd0,
    0x8, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf5,
    0xd, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfa,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfc,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfc,
    0xd, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfa,
    0x8, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf5,
    0x1, 0xff, 0x60, 0x0, 0x0, 0x0, 0x9f, 0xd0,
    0x0, 0x5f, 0xfa, 0x20, 0x0, 0x3b, 0xff, 0x20,
    0x0, 0x4, 0xef, 0xff, 0xdf, 0xff, 0xd3, 0x0,
    0x0, 0x0, 0x17, 0xce, 0xfe, 0xb5, 0x0, 0x0,

    /* U+50 "P" */
    0xef, 0xff, 0xff, 0xec, 0x70, 0x0, 0xef, 0xdc,
    0xcd, 0xef, 0xfd, 0x20, 0xef, 0x10, 0x0, 0x2,
    0xbf, 0xc0, 0xef, 0x10, 0x0, 0x0, 0xe, 0xf3,
    0xef, 0x10, 0x0, 0x0, 0xa, 0xf5, 0xef, 0x10,
    0x0, 0x0, 0xb, 0xf5, 0xef, 0x10, 0x0, 0x0,
    0x2f, 0xf2, 0xef, 0x10, 0x0, 0x15, 0xdf, 0xa0,
    0xef, 0xff, 0xff, 0xff, 0xfb, 0x0, 0xef, 0xcc,
    0xcc, 0xb9, 0x40, 0x0, 0xef, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x10, 0x0, 0x0, 0x0, 0x0, 0xef, 0x10,
    0x0, 0x0, 0x0, 0x0,

    /* U+51 "Q" */
    0x0, 0x0, 0x16, 0xce, 0xfe, 0xb5, 0x0, 0x0,
    0x0, 0x0, 0x4e, 0xff, 0xfd, 0xff, 0xfd, 0x30,
    0x0, 0x0, 0x5f, 0xfa, 0x20, 0x0, 0x4b, 0xff,
    0x20, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x0, 0x9,
    0xfd, 0x0, 0x8, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf5, 0x0, 0xdf, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xa0, 0xf, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xfc, 0x0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xc0, 0xd, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xfa, 0x0, 0x9f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x50, 0x2, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x8f, 0xd0, 0x0, 0x6, 0xff,
    0x92, 0x0, 0x3, 0xbf, 0xf3, 0x0, 0x0, 0x6,
    0xff, 0xfe, 0xce, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0x2, 0x8d, 0xff, 0xfd, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xe2, 0x0, 0x1a, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfb, 0xbf, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4b, 0xef, 0xb3,
    0x0,

    /* U+52 "R" */
    0xef, 0xff, 0xff, 0xec, 0x70, 0x0, 0xef, 0xdc,
    0xcd, 0xef, 0xfd, 0x20, 0xef, 0x10, 0x0, 0x2,
    0xbf, 0xc0, 0xef, 0x10, 0x0, 0x0, 0xe, 0xf3,
    0xef, 0x10, 0x0, 0x0, 0xa, 0xf5, 0xef, 0x10,
    0x0, 0x0, 0xb, 0xf5, 0xef, 0x10, 0x0, 0x0,
    0x2f, 0xf2, 0xef, 0x10, 0x0, 0x15, 0xdf, 0xa0,
    0xef, 0xff, 0xff, 0xff, 0xfa, 0x0, 0xef, 0xcc,
    0xcb, 0xdf, 0x90, 0x0, 0xef, 0x10, 0x0, 0x1e,
    0xf2, 0x0, 0xef, 0x10, 0x0, 0x4, 0xfd, 0x0,
    0xef, 0x10, 0x0, 0x0, 0x9f, 0x90, 0xef, 0x10,
    0x0, 0x0, 0xd, 0xf4,

    /* U+53 "S" */
    0x0, 0x6, 0xce, 0xfe, 0xc7, 0x10, 0x0, 0xcf,
    0xfd, 0xcd, 0xff, 0xd0, 0x8, 0xfc, 0x20, 0x0,
    0x17, 0x60, 0xc, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x71, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xd9,
    0x40, 0x0, 0x0, 0x1, 0x6a, 0xef, 0xfe, 0x40,
    0x0, 0x0, 0x0, 0x3, 0xaf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xf7, 0x1, 0x0, 0x0, 0x0,
    0x7, 0xf8, 0xd, 0xb3, 0x0, 0x0, 0x3e, 0xf4,
    0xa, 0xff, 0xfc, 0xce, 0xff, 0x90, 0x0, 0x39,
    0xdf, 0xfe, 0xa4, 0x0,

    /* U+54 "T" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xbc, 0xcc,
    0xdf, 0xfc, 0xcc, 0xc8, 0x0, 0x0, 0x2f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xd0, 0x0, 0x0,

    /* U+55 "U" */
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x2f, 0xd0, 0xff,
    0x0, 0x0, 0x0, 0x2, 0xfd, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x2f, 0xd0, 0xff, 0x0, 0x0, 0x0,
    0x2, 0xfd, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x2f,
    0xd0, 0xff, 0x0, 0x0, 0x0, 0x2, 0xfd, 0xf,
    0xf0, 0x0, 0x0, 0x0, 0x2f, 0xd0, 0xff, 0x0,
    0x0, 0x0, 0x2, 0xfc, 0xf, 0xf0, 0x0, 0x0,
    0x0, 0x2f, 0xc0, 0xdf, 0x30, 0x0, 0x0, 0x5,
    0xfa, 0x9, 0xf9, 0x0, 0x0, 0x0, 0xcf, 0x60,
    0x2f, 0xf8, 0x0, 0x1, 0xaf, 0xe0, 0x0, 0x5f,
    0xff, 0xde, 0xff, 0xe3, 0x0, 0x0, 0x29, 0xdf,
    0xfd, 0x81, 0x0,

    /* U+56 "V" */
    0xc, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf1,
    0x5, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x90,
    0x0, 0xef, 0x30, 0x0, 0x0, 0x0, 0xdf, 0x20,
    0x0, 0x7f, 0xa0, 0x0, 0x0, 0x4, 0xfb, 0x0,
    0x0, 0x1f, 0xf2, 0x0, 0x0, 0xb, 0xf4, 0x0,
    0x0, 0x9, 0xf8, 0x0, 0x0, 0x2f, 0xd0, 0x0,
    0x0, 0x2, 0xff, 0x0, 0x0, 0x9f, 0x60, 0x0,
    0x0, 0x0, 0xbf, 0x60, 0x1, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xd0, 0x7, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf4, 0xe, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xfb, 0x5f, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xef, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf5, 0x0, 0x0, 0x0,

    /* U+57 "W" */
    0x3f, 0xd0, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0,
    0x0, 0x2, 0xfb, 0xd, 0xf3, 0x0, 0x0, 0x1,
    0xff, 0xc0, 0x0, 0x0, 0x8, 0xf5, 0x8, 0xf8,
    0x0, 0x0, 0x7, 0xff, 0xf1, 0x0, 0x0, 0xd,
    0xf1, 0x3, 0xfd, 0x0, 0x0, 0xc, 0xf8, 0xf7,
    0x0, 0x0, 0x3f, 0xb0, 0x0, 0xdf, 0x30, 0x0,
    0x2f, 0xb2, 0xfc, 0x0, 0x0, 0x8f, 0x50, 0x0,
    0x8f, 0x80, 0x0, 0x7f, 0x50, 0xcf, 0x10, 0x0,
    0xdf, 0x0, 0x0, 0x3f, 0xd0, 0x0, 0xdf, 0x0,
    0x7f, 0x70, 0x3, 0xfb, 0x0, 0x0, 0xd, 0xf3,
    0x2, 0xfa, 0x0, 0x2f, 0xc0, 0x8, 0xf5, 0x0,
    0x0, 0x8, 0xf8, 0x8, 0xf5, 0x0, 0xc, 0xf2,
    0xe, 0xf0, 0x0, 0x0, 0x3, 0xfd, 0xd, 0xf0,
    0x0, 0x7, 0xf7, 0x3f, 0xb0, 0x0, 0x0, 0x0,
    0xdf, 0x6f, 0xa0, 0x0, 0x1, 0xfc, 0x8f, 0x50,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0x40, 0x0, 0x0,
    0xcf, 0xef, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x0, 0x0, 0x0, 0x6f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf9, 0x0, 0x0, 0x0, 0x1f, 0xf5,
    0x0, 0x0,

    /* U+58 "X" */
    0x1f, 0xf3, 0x0, 0x0, 0x0, 0xbf, 0x70, 0x5,
    0xfe, 0x10, 0x0, 0x6, 0xfb, 0x0, 0x0, 0x9f,
    0xa0, 0x0, 0x2f, 0xe1, 0x0, 0x0, 0xd, 0xf6,
    0x0, 0xdf, 0x40, 0x0, 0x0, 0x3, 0xff, 0x29,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xef, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x9f, 0xf3, 0x0, 0x0, 0x0,
    0x7, 0xfc, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x3f,
    0xf2, 0x0, 0xaf, 0xa0, 0x0, 0x0, 0xdf, 0x50,
    0x0, 0xd, 0xf5, 0x0, 0xa, 0xfa, 0x0, 0x0,
    0x3, 0xff, 0x20, 0x5f, 0xd0, 0x0, 0x0, 0x0,
    0x7f, 0xc0,

    /* U+59 "Y" */
    0xc, 0xf5, 0x0, 0x0, 0x0, 0x4, 0xfb, 0x0,
    0x2f, 0xe1, 0x0, 0x0, 0x0, 0xdf, 0x20, 0x0,
    0x8f, 0x90, 0x0, 0x0, 0x7f, 0x70, 0x0, 0x0,
    0xef, 0x30, 0x0, 0x2f, 0xd0, 0x0, 0x0, 0x5,
    0xfc, 0x0, 0xb, 0xf4, 0x0, 0x0, 0x0, 0xb,
    0xf6, 0x5, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xe1, 0xef, 0x10, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xef, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xf7, 0x0, 0x0,
    0x0,

    /* U+5A "Z" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0xcc,
    0xcc, 0xcc, 0xcc, 0xef, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x30, 0x0, 0x0, 0x0, 0x1,
    0xef, 0x50, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xfe,
    0xcc, 0xcc, 0xcc, 0xcc, 0x72, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9,

    /* U+5B "[" */
    0xef, 0xff, 0x4e, 0xfa, 0xa2, 0xef, 0x0, 0xe,
    0xf0, 0x0, 0xef, 0x0, 0xe, 0xf0, 0x0, 0xef,
    0x0, 0xe, 0xf0, 0x0, 0xef, 0x0, 0xe, 0xf0,
    0x0, 0xef, 0x0, 0xe, 0xf0, 0x0, 0xef, 0x0,
    0xe, 0xf0, 0x0, 0xef, 0x0, 0xe, 0xf0, 0x0,
    0xef, 0x0, 0xe, 0xfa, 0xa2, 0xef, 0xff, 0x40,

    /* U+5C "\\" */
    0x57, 0x0, 0x0, 0x0, 0x6, 0xf5, 0x0, 0x0,
    0x0, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0xcf, 0x0,
    0x0, 0x0, 0x6, 0xf5, 0x0, 0x0, 0x0, 0x1f,
    0xa0, 0x0, 0x0, 0x0, 0xbf, 0x0, 0x0, 0x0,
    0x6, 0xf5, 0x0, 0x0, 0x0, 0x1f, 0xb0, 0x0,
    0x0, 0x0, 0xbf, 0x0, 0x0, 0x0, 0x6, 0xf5,
    0x0, 0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0, 0x0,
    0xbf, 0x10, 0x0, 0x0, 0x5, 0xf6, 0x0, 0x0,
    0x0, 0xf, 0xb0, 0x0, 0x0, 0x0, 0xaf, 0x10,
    0x0, 0x0, 0x5, 0xf6, 0x0, 0x0, 0x0, 0xf,
    0xb0, 0x0, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x0,
    0x5, 0xf6,

    /* U+5D "]" */
    0xaf, 0xff, 0x96, 0xac, 0xf9, 0x0, 0x5f, 0x90,
    0x5, 0xf9, 0x0, 0x5f, 0x90, 0x5, 0xf9, 0x0,
    0x5f, 0x90, 0x5, 0xf9, 0x0, 0x5f, 0x90, 0x5,
    0xf9, 0x0, 0x5f, 0x90, 0x5, 0xf9, 0x0, 0x5f,
    0x90, 0x5, 0xf9, 0x0, 0x5f, 0x90, 0x5, 0xf9,
    0x0, 0x5f, 0x96, 0xac, 0xf9, 0xaf, 0xff, 0x90,

    /* U+5E "^" */
    0x0, 0x0, 0x75, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x10, 0x0, 0x0, 0xc, 0xbf, 0x70, 0x0, 0x0,
    0x3f, 0x49, 0xd0, 0x0, 0x0, 0x9d, 0x3, 0xf4,
    0x0, 0x1, 0xf7, 0x0, 0xcb, 0x0, 0x7, 0xf1,
    0x0, 0x6f, 0x20, 0xd, 0xa0, 0x0, 0xf, 0x80,
    0x4f, 0x30, 0x0, 0x9, 0xe0,

    /* U+5F "_" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x33, 0x33, 0x33, 0x33, 0x33,

    /* U+60 "`" */
    0x27, 0x70, 0x0, 0x5, 0xfc, 0x10, 0x0, 0x2d,
    0xd1,

    /* U+61 "a" */
    0x5, 0xbe, 0xfe, 0xb4, 0x0, 0x7f, 0xfd, 0xbd,
    0xff, 0x50, 0x2a, 0x10, 0x0, 0x7f, 0xe0, 0x0,
    0x0, 0x0, 0xd, 0xf2, 0x0, 0x1, 0x11, 0x1c,
    0xf3, 0x8, 0xef, 0xff, 0xff, 0xf3, 0x9f, 0xc6,
    0x44, 0x4c, 0xf3, 0xff, 0x0, 0x0, 0xb, 0xf3,
    0xef, 0x10, 0x0, 0x3f, 0xf3, 0x8f, 0xd7, 0x69,
    0xfe, 0xf3, 0x6, 0xcf, 0xfc, 0x59, 0xf3,

    /* U+62 "b" */
    0x3f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xb1, 0x9e, 0xfd, 0x92, 0x0, 0x3f, 0xde,
    0xfd, 0xce, 0xfe, 0x40, 0x3f, 0xfe, 0x30, 0x0,
    0x8f, 0xe1, 0x3f, 0xf3, 0x0, 0x0, 0xa, 0xf7,
    0x3f, 0xd0, 0x0, 0x0, 0x4, 0xfa, 0x3f, 0xb0,
    0x0, 0x0, 0x2, 0xfc, 0x3f, 0xd0, 0x0, 0x0,
    0x4, 0xfa, 0x3f, 0xf3, 0x0, 0x0, 0xa, 0xf7,
    0x3f, 0xfe, 0x30, 0x0, 0x8f, 0xe1, 0x3f, 0xce,
    0xfd, 0xce, 0xff, 0x40, 0x3f, 0xa1, 0x9e, 0xfe,
    0x92, 0x0,

    /* U+63 "c" */
    0x0, 0x3, 0xae, 0xfe, 0x91, 0x0, 0x7, 0xff,
    0xdc, 0xef, 0xe2, 0x4, 0xfe, 0x40, 0x0, 0x7f,
    0x60, 0xcf, 0x40, 0x0, 0x0, 0x10, 0xf, 0xe0,
    0x0, 0x0, 0x0, 0x2, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x40, 0x0, 0x0, 0x10, 0x4, 0xfe, 0x40, 0x0,
    0x7f, 0x60, 0x7, 0xff, 0xdc, 0xef, 0xe2, 0x0,
    0x3, 0xae, 0xfe, 0x91, 0x0,

    /* U+64 "d" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfd,
    0x0, 0x4, 0xbe, 0xfc, 0x61, 0xfd, 0x0, 0x8f,
    0xfd, 0xce, 0xfb, 0xfd, 0x5, 0xfe, 0x40, 0x0,
    0x7f, 0xfd, 0xc, 0xf5, 0x0, 0x0, 0x9, 0xfd,
    0xf, 0xe0, 0x0, 0x0, 0x3, 0xfd, 0x2f, 0xc0,
    0x0, 0x0, 0x1, 0xfd, 0xf, 0xe0, 0x0, 0x0,
    0x3, 0xfd, 0xc, 0xf4, 0x0, 0x0, 0x8, 0xfd,
    0x5, 0xfe, 0x20, 0x0, 0x5f, 0xfd, 0x0, 0x8f,
    0xfb, 0xad, 0xfb, 0xfd, 0x0, 0x4, 0xbe, 0xfd,
    0x70, 0xfd,

    /* U+65 "e" */
    0x0, 0x4, 0xbe, 0xfc, 0x60, 0x0, 0x0, 0x8f,
    0xfc, 0xbe, 0xfc, 0x0, 0x5, 0xfd, 0x20, 0x0,
    0xaf, 0x80, 0xc, 0xf3, 0x0, 0x0, 0xd, 0xf0,
    0xf, 0xe1, 0x11, 0x11, 0x19, 0xf4, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xf, 0xe4, 0x44, 0x44,
    0x44, 0x41, 0xc, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xfe, 0x40, 0x0, 0x2b, 0x20, 0x0, 0x7f,
    0xfe, 0xcd, 0xff, 0x60, 0x0, 0x3, 0xae, 0xfe,
    0xa3, 0x0,

    /* U+66 "f" */
    0x0, 0x6, 0xdf, 0xd6, 0x0, 0x6f, 0xea, 0xc6,
    0x0, 0xcf, 0x20, 0x0, 0x0, 0xef, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xf1, 0x7a, 0xff, 0xaa, 0xa0,
    0x0, 0xef, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0,
    0x0, 0xef, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0,
    0x0, 0xef, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0,
    0x0, 0xef, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0,
    0x0, 0xef, 0x0, 0x0,

    /* U+67 "g" */
    0x0, 0x4, 0xbe, 0xfd, 0x70, 0xdf, 0x0, 0x8f,
    0xfd, 0xce, 0xfc, 0xef, 0x5, 0xfe, 0x40, 0x0,
    0x5f, 0xff, 0xc, 0xf4, 0x0, 0x0, 0x6, 0xff,
    0xf, 0xe0, 0x0, 0x0, 0x0, 0xff, 0x2f, 0xc0,
    0x0, 0x0, 0x0, 0xff, 0xf, 0xe0, 0x0, 0x0,
    0x1, 0xff, 0xc, 0xf5, 0x0, 0x0, 0x7, 0xff,
    0x5, 0xfe, 0x40, 0x0, 0x5f, 0xff, 0x0, 0x8f,
    0xfd, 0xbe, 0xfc, 0xff, 0x0, 0x4, 0xbe, 0xfd,
    0x71, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfb,
    0x1, 0xa4, 0x0, 0x0, 0x2d, 0xf5, 0x4, 0xff,
    0xfc, 0xbd, 0xff, 0xa0, 0x0, 0x28, 0xce, 0xfe,
    0xb5, 0x0,

    /* U+68 "h" */
    0x3f, 0xb0, 0x0, 0x0, 0x0, 0x3, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xb0, 0x0, 0x0, 0x0,
    0x3, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xb1,
    0x9e, 0xfe, 0x91, 0x3, 0xfd, 0xef, 0xdd, 0xff,
    0xd0, 0x3f, 0xfd, 0x20, 0x2, 0xdf, 0x73, 0xff,
    0x20, 0x0, 0x4, 0xfc, 0x3f, 0xd0, 0x0, 0x0,
    0x1f, 0xd3, 0xfb, 0x0, 0x0, 0x0, 0xfe, 0x3f,
    0xb0, 0x0, 0x0, 0xf, 0xe3, 0xfb, 0x0, 0x0,
    0x0, 0xfe, 0x3f, 0xb0, 0x0, 0x0, 0xf, 0xe3,
    0xfb, 0x0, 0x0, 0x0, 0xfe, 0x3f, 0xb0, 0x0,
    0x0, 0xf, 0xe0,

    /* U+69 "i" */
    0x3e, 0xb0, 0x7f, 0xf0, 0x8, 0x40, 0x0, 0x0,
    0x3f, 0xb0, 0x3f, 0xb0, 0x3f, 0xb0, 0x3f, 0xb0,
    0x3f, 0xb0, 0x3f, 0xb0, 0x3f, 0xb0, 0x3f, 0xb0,
    0x3f, 0xb0, 0x3f, 0xb0, 0x3f, 0xb0,

    /* U+6A "j" */
    0x0, 0x2, 0xec, 0x0, 0x0, 0x5f, 0xf1, 0x0,
    0x0, 0x75, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfd, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x1, 0xfd,
    0x0, 0x0, 0x1f, 0xd0, 0x0, 0x1, 0xfd, 0x0,
    0x0, 0x1f, 0xd0, 0x0, 0x1, 0xfd, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x1f,
    0xd0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x1f, 0xd0,
    0x0, 0x5, 0xfa, 0x7, 0xdb, 0xff, 0x40, 0x7e,
    0xfd, 0x50, 0x0,

    /* U+6B "k" */
    0x3f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xb0, 0x0, 0x2, 0xdf, 0x50, 0x3f, 0xb0,
    0x0, 0x2e, 0xf6, 0x0, 0x3f, 0xb0, 0x3, 0xef,
    0x60, 0x0, 0x3f, 0xb0, 0x3f, 0xf6, 0x0, 0x0,
    0x3f, 0xb4, 0xff, 0x90, 0x0, 0x0, 0x3f, 0xef,
    0xff, 0xf2, 0x0, 0x0, 0x3f, 0xff, 0x59, 0xfd,
    0x0, 0x0, 0x3f, 0xf4, 0x0, 0xcf, 0x90, 0x0,
    0x3f, 0xb0, 0x0, 0x1e, 0xf6, 0x0, 0x3f, 0xb0,
    0x0, 0x4, 0xff, 0x20, 0x3f, 0xb0, 0x0, 0x0,
    0x7f, 0xd0,

    /* U+6C "l" */
    0x3f, 0xb3, 0xfb, 0x3f, 0xb3, 0xfb, 0x3f, 0xb3,
    0xfb, 0x3f, 0xb3, 0xfb, 0x3f, 0xb3, 0xfb, 0x3f,
    0xb3, 0xfb, 0x3f, 0xb3, 0xfb, 0x3f, 0xb0,

    /* U+6D "m" */
    0x3f, 0xa3, 0xae, 0xfd, 0x70, 0x5, 0xcf, 0xfc,
    0x50, 0x3, 0xfd, 0xfe, 0xbc, 0xff, 0xaa, 0xfe,
    0xbc, 0xff, 0x70, 0x3f, 0xfb, 0x10, 0x3, 0xff,
    0xf9, 0x0, 0x4, 0xff, 0x13, 0xff, 0x10, 0x0,
    0x9, 0xfe, 0x0, 0x0, 0xb, 0xf4, 0x3f, 0xd0,
    0x0, 0x0, 0x6f, 0xb0, 0x0, 0x0, 0x8f, 0x63,
    0xfb, 0x0, 0x0, 0x5, 0xf9, 0x0, 0x0, 0x8,
    0xf6, 0x3f, 0xb0, 0x0, 0x0, 0x5f, 0x90, 0x0,
    0x0, 0x8f, 0x63, 0xfb, 0x0, 0x0, 0x5, 0xf9,
    0x0, 0x0, 0x8, 0xf6, 0x3f, 0xb0, 0x0, 0x0,
    0x5f, 0x90, 0x0, 0x0, 0x8f, 0x63, 0xfb, 0x0,
    0x0, 0x5, 0xf9, 0x0, 0x0, 0x8, 0xf6, 0x3f,
    0xb0, 0x0, 0x0, 0x5f, 0x90, 0x0, 0x0, 0x8f,
    0x60,

    /* U+6E "n" */
    0x3f, 0xa2, 0xae, 0xfe, 0x91, 0x3, 0xfd, 0xff,
    0xcb, 0xef, 0xd0, 0x3f, 0xfc, 0x10, 0x1, 0xcf,
    0x73, 0xff, 0x20, 0x0, 0x4, 0xfc, 0x3f, 0xd0,
    0x0, 0x0, 0x1f, 0xd3, 0xfb, 0x0, 0x0, 0x0,
    0xfe, 0x3f, 0xb0, 0x0, 0x0, 0xf, 0xe3, 0xfb,
    0x0, 0x0, 0x0, 0xfe, 0x3f, 0xb0, 0x0, 0x0,
    0xf, 0xe3, 0xfb, 0x0, 0x0, 0x0, 0xfe, 0x3f,
    0xb0, 0x0, 0x0, 0xf, 0xe0,

    /* U+6F "o" */
    0x0, 0x3, 0xae, 0xfd, 0x91, 0x0, 0x0, 0x7f,
    0xfd, 0xce, 0xfe, 0x30, 0x5, 0xfe, 0x40, 0x0,
    0x7f, 0xe1, 0xc, 0xf4, 0x0, 0x0, 0x9, 0xf7,
    0xf, 0xe0, 0x0, 0x0, 0x3, 0xfb, 0x2f, 0xc0,
    0x0, 0x0, 0x1, 0xfd, 0xf, 0xe0, 0x0, 0x0,
    0x3, 0xfb, 0xc, 0xf4, 0x0, 0x0, 0x9, 0xf7,
    0x4, 0xfe, 0x40, 0x0, 0x7f, 0xe1, 0x0, 0x7f,
    0xfd, 0xce, 0xfe, 0x30, 0x0, 0x3, 0xae, 0xfd,
    0x91, 0x0,

    /* U+70 "p" */
    0x3f, 0xa2, 0x9e, 0xfd, 0x92, 0x0, 0x3f, 0xce,
    0xfb, 0xad, 0xfe, 0x40, 0x3f, 0xfd, 0x20, 0x0,
    0x6f, 0xe1, 0x3f, 0xf3, 0x0, 0x0, 0x9, 0xf7,
    0x3f, 0xd0, 0x0, 0x0, 0x4, 0xfa, 0x3f, 0xb0,
    0x0, 0x0, 0x2, 0xfc, 0x3f, 0xd0, 0x0, 0x0,
    0x4, 0xfa, 0x3f, 0xf3, 0x0, 0x0, 0xa, 0xf7,
    0x3f, 0xfe, 0x30, 0x0, 0x8f, 0xe1, 0x3f, 0xde,
    0xfd, 0xce, 0xff, 0x40, 0x3f, 0xb1, 0x9e, 0xfe,
    0x92, 0x0, 0x3f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xb0, 0x0, 0x0,
    0x0, 0x0,

    /* U+71 "q" */
    0x0, 0x4, 0xbe, 0xfc, 0x60, 0xfd, 0x0, 0x8f,
    0xfd, 0xce, 0xfa, 0xfd, 0x5, 0xfe, 0x40, 0x0,
    0x7f, 0xfd, 0xc, 0xf4, 0x0, 0x0, 0x9, 0xfd,
    0xf, 0xe0, 0x0, 0x0, 0x3, 0xfd, 0x2f, 0xc0,
    0x0, 0x0, 0x1, 0xfd, 0xf, 0xe0, 0x0, 0x0,
    0x3, 0xfd, 0xc, 0xf4, 0x0, 0x0, 0x9, 0xfd,
    0x5, 0xfe, 0x40, 0x0, 0x7f, 0xfd, 0x0, 0x8f,
    0xfd, 0xce, 0xfb, 0xfd, 0x0, 0x4, 0xbe, 0xfc,
    0x61, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfd,

    /* U+72 "r" */
    0x3f, 0xa1, 0x9e, 0x83, 0xfc, 0xef, 0xf7, 0x3f,
    0xfe, 0x40, 0x3, 0xff, 0x40, 0x0, 0x3f, 0xe0,
    0x0, 0x3, 0xfc, 0x0, 0x0, 0x3f, 0xb0, 0x0,
    0x3, 0xfb, 0x0, 0x0, 0x3f, 0xb0, 0x0, 0x3,
    0xfb, 0x0, 0x0, 0x3f, 0xb0, 0x0, 0x0,

    /* U+73 "s" */
    0x0, 0x5c, 0xef, 0xea, 0x50, 0x9, 0xff, 0xcb,
    0xdf, 0xd0, 0x1f, 0xe1, 0x0, 0x2, 0x30, 0x2f,
    0xd0, 0x0, 0x0, 0x0, 0xd, 0xfc, 0x63, 0x0,
    0x0, 0x2, 0xcf, 0xff, 0xfb, 0x30, 0x0, 0x1,
    0x47, 0xbf, 0xf2, 0x0, 0x0, 0x0, 0x9, 0xf6,
    0x9, 0x30, 0x0, 0xb, 0xf5, 0x5f, 0xfe, 0xbb,
    0xef, 0xc0, 0x5, 0xae, 0xfe, 0xc7, 0x0,

    /* U+74 "t" */
    0x0, 0x78, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0,
    0x0, 0xef, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xf1,
    0x7a, 0xff, 0xaa, 0xa0, 0x0, 0xef, 0x0, 0x0,
    0x0, 0xef, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0,
    0x0, 0xef, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0,
    0x0, 0xef, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0,
    0x0, 0x6f, 0xfb, 0xd7, 0x0, 0x7, 0xdf, 0xd5,

    /* U+75 "u" */
    0x4f, 0xa0, 0x0, 0x0, 0x3f, 0xb4, 0xfa, 0x0,
    0x0, 0x3, 0xfb, 0x4f, 0xa0, 0x0, 0x0, 0x3f,
    0xb4, 0xfa, 0x0, 0x0, 0x3, 0xfb, 0x4f, 0xa0,
    0x0, 0x0, 0x3f, 0xb4, 0xfa, 0x0, 0x0, 0x3,
    0xfb, 0x4f, 0xb0, 0x0, 0x0, 0x5f, 0xb2, 0xfd,
    0x0, 0x0, 0x9, 0xfb, 0xd, 0xf7, 0x0, 0x5,
    0xff, 0xb0, 0x4f, 0xfd, 0xad, 0xfc, 0xfb, 0x0,
    0x3b, 0xef, 0xd7, 0x2f, 0xb0,

    /* U+76 "v" */
    0xd, 0xf2, 0x0, 0x0, 0x0, 0xef, 0x0, 0x6f,
    0x90, 0x0, 0x0, 0x5f, 0x90, 0x0, 0xff, 0x0,
    0x0, 0xb, 0xf2, 0x0, 0x9, 0xf6, 0x0, 0x2,
    0xfb, 0x0, 0x0, 0x2f, 0xc0, 0x0, 0x9f, 0x40,
    0x0, 0x0, 0xbf, 0x30, 0xf, 0xd0, 0x0, 0x0,
    0x4, 0xfa, 0x6, 0xf7, 0x0, 0x0, 0x0, 0xd,
    0xf1, 0xdf, 0x10, 0x0, 0x0, 0x0, 0x7f, 0xbf,
    0x90, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x0,

    /* U+77 "w" */
    0xbf, 0x10, 0x0, 0x0, 0xef, 0x0, 0x0, 0x1,
    0xfa, 0x5f, 0x70, 0x0, 0x5, 0xff, 0x60, 0x0,
    0x6, 0xf5, 0xf, 0xd0, 0x0, 0xb, 0xff, 0xb0,
    0x0, 0xc, 0xe0, 0xa, 0xf2, 0x0, 0x1f, 0xab,
    0xf1, 0x0, 0x1f, 0x90, 0x4, 0xf8, 0x0, 0x6f,
    0x55, 0xf7, 0x0, 0x7f, 0x30, 0x0, 0xed, 0x0,
    0xce, 0x0, 0xec, 0x0, 0xde, 0x0, 0x0, 0x8f,
    0x32, 0xf9, 0x0, 0x9f, 0x23, 0xf8, 0x0, 0x0,
    0x3f, 0x98, 0xf3, 0x0, 0x3f, 0x88, 0xf2, 0x0,
    0x0, 0xd, 0xee, 0xd0, 0x0, 0xd, 0xde, 0xc0,
    0x0, 0x0, 0x7, 0xff, 0x70, 0x0, 0x7, 0xff,
    0x70, 0x0, 0x0, 0x1, 0xff, 0x10, 0x0, 0x2,
    0xff, 0x10, 0x0,

    /* U+78 "x" */
    0x2f, 0xe1, 0x0, 0x0, 0xdf, 0x30, 0x6f, 0xb0,
    0x0, 0xaf, 0x60, 0x0, 0xaf, 0x70, 0x6f, 0xa0,
    0x0, 0x0, 0xdf, 0x5f, 0xd1, 0x0, 0x0, 0x3,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0xb, 0xfb, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xf6, 0x0, 0x0, 0x2,
    0xfe, 0x2e, 0xf2, 0x0, 0x0, 0xdf, 0x40, 0x3f,
    0xd0, 0x0, 0x9f, 0x80, 0x0, 0x8f, 0xa0, 0x5f,
    0xc0, 0x0, 0x0, 0xcf, 0x60,

    /* U+79 "y" */
    0xd, 0xf2, 0x0, 0x0, 0x0, 0xef, 0x0, 0x6f,
    0x90, 0x0, 0x0, 0x5f, 0x80, 0x0, 0xef, 0x0,
    0x0, 0xb, 0xf2, 0x0, 0x8, 0xf7, 0x0, 0x2,
    0xfb, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x9f, 0x40,
    0x0, 0x0, 0xaf, 0x40, 0xf, 0xd0, 0x0, 0x0,
    0x3, 0xfb, 0x6, 0xf6, 0x0, 0x0, 0x0, 0xd,
    0xf2, 0xdf, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xcf,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x40, 0x0, 0x0, 0x3, 0x0,
    0x3f, 0xc0, 0x0, 0x0, 0x2, 0xfd, 0xbf, 0xf3,
    0x0, 0x0, 0x0, 0x8, 0xef, 0xc4, 0x0, 0x0,
    0x0, 0x0,

    /* U+7A "z" */
    0x1f, 0xff, 0xff, 0xff, 0xf8, 0xa, 0xaa, 0xaa,
    0xaf, 0xf5, 0x0, 0x0, 0x0, 0x9f, 0x90, 0x0,
    0x0, 0x6, 0xfc, 0x0, 0x0, 0x0, 0x3f, 0xe1,
    0x0, 0x0, 0x1, 0xdf, 0x40, 0x0, 0x0, 0xb,
    0xf7, 0x0, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0x0,
    0x4, 0xfd, 0x10, 0x0, 0x0, 0x1e, 0xfc, 0xaa,
    0xaa, 0xa6, 0x3f, 0xff, 0xff, 0xff, 0xfb,

    /* U+7B "{" */
    0x0, 0x3c, 0xfa, 0x0, 0xef, 0xc6, 0x3, 0xfc,
    0x0, 0x4, 0xfa, 0x0, 0x4, 0xfa, 0x0, 0x4,
    0xfa, 0x0, 0x4, 0xfa, 0x0, 0x5, 0xfa, 0x0,
    0x8e, 0xf6, 0x0, 0xdf, 0xe2, 0x0, 0x7, 0xf9,
    0x0, 0x4, 0xfa, 0x0, 0x4, 0xfa, 0x0, 0x4,
    0xfa, 0x0, 0x4, 0xfa, 0x0, 0x4, 0xfa, 0x0,
    0x2, 0xfd, 0x0, 0x0, 0xef, 0xc6, 0x0, 0x3c,
    0xfa,

    /* U+7C "|" */
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee,

    /* U+7D "}" */
    0xaf, 0xc3, 0x0, 0x6c, 0xfe, 0x0, 0x0, 0xcf,
    0x30, 0x0, 0xaf, 0x40, 0x0, 0xaf, 0x40, 0x0,
    0xaf, 0x40, 0x0, 0xaf, 0x40, 0x0, 0x9f, 0x50,
    0x0, 0x5f, 0xe8, 0x0, 0x2e, 0xfd, 0x0, 0x9f,
    0x70, 0x0, 0x9f, 0x40, 0x0, 0xaf, 0x40, 0x0,
    0xaf, 0x40, 0x0, 0xaf, 0x40, 0x0, 0xaf, 0x40,
    0x0, 0xcf, 0x30, 0x6c, 0xfe, 0x0, 0xaf, 0xc3,
    0x0,

    /* U+7E "~" */
    0x9, 0xee, 0x60, 0x0, 0xd6, 0x7f, 0xab, 0xfb,
    0x26, 0xf3, 0xb9, 0x0, 0x5e, 0xff, 0x90, 0x31,
    0x0, 0x0, 0x32, 0x0,

    /* U+B0 "°" */
    0x0, 0x6d, 0xea, 0x10, 0x7, 0xe5, 0x3b, 0xc0,
    0xe, 0x40, 0x0, 0xe4, 0x1f, 0x0, 0x0, 0xb7,
    0xf, 0x30, 0x0, 0xd5, 0x8, 0xc2, 0x8, 0xe0,
    0x0, 0x9f, 0xfc, 0x20, 0x0, 0x0, 0x10, 0x0,

    /* U+2022 "•" */
    0x9, 0xa2, 0x8f, 0xfc, 0x9f, 0xfd, 0x2d, 0xe5,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x15, 0xae, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x7c, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x49, 0xef, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x4, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x69, 0xff, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xfe, 0x95, 0x0, 0x8, 0xff,
    0x0, 0x0, 0xf, 0xff, 0xc7, 0x30, 0x0, 0x0,
    0x8, 0xff, 0x0, 0x0, 0xf, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x0, 0x0, 0xf, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x0, 0x0,
    0xf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x0, 0x0, 0xf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x0, 0x0, 0xf, 0xf8, 0x0, 0x0,
    0x1, 0x7b, 0xbd, 0xff, 0x0, 0x0, 0xf, 0xf8,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0x0, 0x13,
    0x3f, 0xf8, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0x2b, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xfc, 0xdf, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x6, 0xef, 0xff, 0xa1, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x7f, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x8a, 0xa6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F008 "" */
    0xc4, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x4c, 0xfd, 0xcd, 0xff, 0xee, 0xee, 0xee,
    0xee, 0xff, 0xdc, 0xdf, 0xfa, 0x8a, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xb8, 0xaf, 0xf4, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x40, 0x4f,
    0xf4, 0x4, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x50, 0x4f, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xf7, 0x47, 0xfd, 0x77,
    0x77, 0x77, 0x77, 0xdf, 0x84, 0x7f, 0xf4, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x4f,
    0xf7, 0x47, 0xfd, 0x77, 0x77, 0x77, 0x77, 0xdf,
    0x84, 0x7f, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xf4, 0x4, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x50, 0x4f, 0xf4, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x40, 0x4f,
    0xfa, 0x8a, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb8, 0xaf, 0xfd, 0xcd, 0xff, 0xee, 0xee, 0xee,
    0xee, 0xff, 0xdc, 0xdf, 0xc4, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x4c,

    /* U+F00B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xfa, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xfd, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcf, 0xff, 0xf9, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xf9, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xfd, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff,
    0xfa, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xf9, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xfd, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xfa, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0x60, 0x6, 0xe4, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff, 0x40, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0x60, 0x0, 0xff, 0xff,
    0xf4, 0x0, 0x4, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0x40, 0x4f, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5e,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x6, 0x70, 0x0, 0x0, 0x0, 0x18, 0x40, 0x8f,
    0xfb, 0x0, 0x0, 0x1, 0xdf, 0xf4, 0xff, 0xff,
    0xb0, 0x0, 0x1d, 0xff, 0xfb, 0x7f, 0xff, 0xfb,
    0x1, 0xdf, 0xff, 0xf4, 0x8, 0xff, 0xff, 0xbd,
    0xff, 0xff, 0x40, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x1, 0xef, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x1d, 0xff,
    0xff, 0x48, 0xff, 0xff, 0xb0, 0xcf, 0xff, 0xf4,
    0x0, 0x8f, 0xff, 0xf9, 0xdf, 0xff, 0x40, 0x0,
    0x8, 0xff, 0xf9, 0x2e, 0xf4, 0x0, 0x0, 0x0,
    0x8f, 0xc0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x1,
    0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x26, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x64, 0x0,
    0xdf, 0xf4, 0x0, 0x72, 0x0, 0x0, 0x0, 0xb,
    0xfe, 0x10, 0xdf, 0xf4, 0x9, 0xfe, 0x30, 0x0,
    0x0, 0xaf, 0xff, 0x50, 0xdf, 0xf4, 0xe, 0xff,
    0xe1, 0x0, 0x5, 0xff, 0xfb, 0x0, 0xdf, 0xf4,
    0x5, 0xff, 0xfb, 0x0, 0xd, 0xff, 0xb0, 0x0,
    0xdf, 0xf4, 0x0, 0x5f, 0xff, 0x40, 0x4f, 0xff,
    0x20, 0x0, 0xdf, 0xf4, 0x0, 0xb, 0xff, 0xa0,
    0x8f, 0xfb, 0x0, 0x0, 0xdf, 0xf4, 0x0, 0x4,
    0xff, 0xf0, 0xbf, 0xf7, 0x0, 0x0, 0xdf, 0xf4,
    0x0, 0x1, 0xff, 0xf1, 0xbf, 0xf6, 0x0, 0x0,
    0xdf, 0xf4, 0x0, 0x0, 0xff, 0xf2, 0xbf, 0xf7,
    0x0, 0x0, 0x8d, 0xc1, 0x0, 0x0, 0xff, 0xf1,
    0x8f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf0, 0x4f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xb0, 0xe, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x50, 0x6, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x4, 0xff, 0xfc, 0x0,
    0x0, 0xaf, 0xff, 0xd5, 0x10, 0x3, 0x9f, 0xff,
    0xf2, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x9e, 0xff, 0xff, 0xb6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x12, 0x10, 0x0, 0x0,
    0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x2b, 0xff, 0xff, 0xb2, 0x0, 0x10, 0x0,
    0x0, 0x8f, 0x87, 0xff, 0xff, 0xff, 0xff, 0x79,
    0xf8, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x2f, 0xff,
    0xff, 0xff, 0xc7, 0x7c, 0xff, 0xff, 0xff, 0xf2,
    0x7, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x70, 0x0, 0x6f, 0xff, 0xf1, 0x0, 0x0,
    0x1f, 0xff, 0xf6, 0x0, 0x0, 0x7f, 0xff, 0xe0,
    0x0, 0x0, 0xe, 0xff, 0xf7, 0x0, 0x0, 0x6f,
    0xff, 0xf1, 0x0, 0x0, 0x1f, 0xff, 0xf6, 0x0,
    0x7, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x70, 0x2f, 0xff, 0xff, 0xff, 0xc7, 0x7c,
    0xff, 0xff, 0xff, 0xf2, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x8f, 0x97, 0xff, 0xff, 0xff, 0xff, 0x78,
    0xf8, 0x0, 0x0, 0x1, 0x0, 0x1b, 0xff, 0xff,
    0xb1, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x0,
    0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x46, 0x10, 0x0,
    0x67, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfe, 0x20, 0xf, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0x51, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xfc,
    0x8f, 0xff, 0x8f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xfa, 0x0, 0x4e, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf7, 0x8, 0xd3,
    0x2d, 0xff, 0xff, 0x10, 0x0, 0x0, 0xa, 0xff,
    0xf5, 0x1b, 0xff, 0xf5, 0xb, 0xff, 0xf4, 0x0,
    0x0, 0x1c, 0xff, 0xe2, 0x2d, 0xff, 0xff, 0xf7,
    0x8, 0xff, 0xf6, 0x0, 0x3e, 0xff, 0xc1, 0x4e,
    0xff, 0xff, 0xff, 0xfa, 0x5, 0xff, 0xf9, 0xe,
    0xff, 0x90, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x13, 0xef, 0xf6, 0x4f, 0x70, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x31, 0xcc, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0x80, 0x1, 0xef, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xf6, 0x0, 0xe,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0x60, 0x0, 0xef, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xf6, 0x0, 0xe, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0x40, 0x0, 0xcf, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x56, 0x65, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xee, 0xef, 0xff, 0xff, 0xfe, 0xee,
    0x70, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x7a, 0xaa, 0xaa, 0x91,
    0x4f, 0xf4, 0x19, 0xaa, 0xaa, 0xa7, 0xff, 0xff,
    0xff, 0xfd, 0x23, 0x32, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xf, 0x48, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xef, 0xff,
    0x14, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x41,

    /* U+F01C "" */
    0x0, 0x0, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x98, 0x88, 0x88, 0x88, 0x88, 0xdf,
    0xf3, 0x0, 0x0, 0x5, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x1, 0xef,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x80, 0x0, 0xaf, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x20, 0x5f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfc,
    0xd, 0xff, 0x98, 0x88, 0x70, 0x0, 0x0, 0x3,
    0x88, 0x88, 0xdf, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0x7f,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,
    0x7, 0xba, 0x0, 0x0, 0x1, 0x7c, 0xff, 0xff,
    0xb5, 0x0, 0xb, 0xff, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xe5, 0xb, 0xff, 0x0, 0xa,
    0xff, 0xff, 0xdb, 0xbe, 0xff, 0xff, 0x9a, 0xff,
    0x0, 0xaf, 0xff, 0xa2, 0x0, 0x0, 0x3b, 0xff,
    0xff, 0xff, 0x5, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xd, 0xff, 0x60, 0x0,
    0x0, 0x7, 0xba, 0x9c, 0xff, 0xff, 0x3f, 0xfc,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0x7f, 0xf6, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x6f, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0xcf, 0xf3, 0xff, 0xff, 0xc9, 0xaa,
    0x70, 0x0, 0x0, 0x7, 0xff, 0xd0, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x50,
    0xff, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x3b, 0xff,
    0xf9, 0x0, 0xff, 0xa9, 0xff, 0xff, 0xeb, 0xbd,
    0xff, 0xff, 0xa0, 0x0, 0xff, 0xb0, 0x5d, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff, 0xb0,
    0x0, 0x5b, 0xff, 0xff, 0xc8, 0x10, 0x0, 0x0,
    0xab, 0x70, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,
    0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x2, 0xee, 0x0, 0x0, 0x0, 0x2e, 0xff, 0x0,
    0x0, 0x2, 0xef, 0xff, 0x47, 0x77, 0x7e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x9, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x0, 0x0, 0x0, 0x0, 0x89,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x89, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xf0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x73, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x1f, 0xf4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x5f, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x4f, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x1f, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x85, 0x4, 0x77, 0x77, 0xef, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xee, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7b, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x9, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xee, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf0,
    0x0, 0x8, 0x70, 0x8, 0xfd, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xff, 0x0, 0x0, 0xef, 0xb0, 0xc,
    0xf6, 0x4, 0x77, 0x77, 0xef, 0xff, 0xf0, 0x0,
    0x2, 0xdf, 0x80, 0x3f, 0xd0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x74, 0x1, 0xff, 0x10, 0xcf,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xf4,
    0x8, 0xf7, 0x7, 0xf6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x5f, 0xc0, 0x3f, 0xa0, 0x4f, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xff, 0x2,
    0xfb, 0x4, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x5f, 0xc0, 0x3f, 0xa0, 0x5f, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x1f, 0xf4, 0x8, 0xf7,
    0x7, 0xf6, 0xdf, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x74, 0x2, 0xff, 0x10, 0xcf, 0x30, 0x0, 0x0,
    0x9f, 0xff, 0xf0, 0x0, 0x2, 0xef, 0x80, 0x3f,
    0xd0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x0, 0x0,
    0xef, 0xb0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xf0, 0x0, 0x8, 0x70, 0x8, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x89, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7b,
    0x20, 0x0, 0x0,

    /* U+F03E "" */
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe5, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xf6, 0x38, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x7f, 0xff, 0xff, 0xfb, 0xef,
    0xff, 0xff, 0xff, 0x80, 0x0, 0xcf, 0xff, 0xff,
    0x90, 0x3e, 0xff, 0xff, 0xff, 0xfa, 0x7c, 0xff,
    0xff, 0xf9, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0x90, 0x0, 0x0, 0x3e, 0xff,
    0xff, 0xff, 0x90, 0x6f, 0xf9, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xf9, 0x0, 0x6, 0x90, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xc8, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x8c, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x5e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,

    /* U+F048 "" */
    0x47, 0x60, 0x0, 0x0, 0x0, 0x16, 0x1b, 0xff,
    0x10, 0x0, 0x0, 0x2d, 0xfb, 0xbf, 0xf1, 0x0,
    0x0, 0x2e, 0xff, 0xcb, 0xff, 0x10, 0x0, 0x3e,
    0xff, 0xfc, 0xbf, 0xf1, 0x0, 0x4f, 0xff, 0xff,
    0xcb, 0xff, 0x10, 0x5f, 0xff, 0xff, 0xfc, 0xbf,
    0xf1, 0x6f, 0xff, 0xff, 0xff, 0xcb, 0xff, 0x9f,
    0xff, 0xff, 0xff, 0xfc, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcb,
    0xff, 0x4e, 0xff, 0xff, 0xff, 0xfc, 0xbf, 0xf1,
    0x2d, 0xff, 0xff, 0xff, 0xcb, 0xff, 0x10, 0x1c,
    0xff, 0xff, 0xfc, 0xbf, 0xf1, 0x0, 0xc, 0xff,
    0xff, 0xcb, 0xff, 0x10, 0x0, 0xb, 0xff, 0xfc,
    0xbf, 0xf1, 0x0, 0x0, 0xa, 0xff, 0xca, 0xff,
    0x10, 0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x6, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfd, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xd4, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x50, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x20, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x50, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfd, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x6, 0x77, 0x77, 0x30, 0x0, 0x6, 0x77, 0x77,
    0x30, 0xbf, 0xff, 0xff, 0xf3, 0x0, 0xbf, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0xff, 0xf7, 0x0, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8,
    0xef, 0xff, 0xff, 0xf6, 0x0, 0xef, 0xff, 0xff,
    0xf6, 0x6f, 0xff, 0xff, 0xc1, 0x0, 0x6f, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F04D "" */
    0x5, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x30, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F051 "" */
    0x5, 0x20, 0x0, 0x0, 0x0, 0x57, 0x66, 0xff,
    0x40, 0x0, 0x0, 0xc, 0xff, 0x8f, 0xff, 0x60,
    0x0, 0x0, 0xdf, 0xf8, 0xff, 0xff, 0x70, 0x0,
    0xd, 0xff, 0x8f, 0xff, 0xff, 0x80, 0x0, 0xdf,
    0xf8, 0xff, 0xff, 0xff, 0x90, 0xd, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xb0, 0xdf, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xcd, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0x5d, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0x40, 0xdf, 0xf8, 0xff, 0xff, 0xfe,
    0x30, 0xd, 0xff, 0x8f, 0xff, 0xfe, 0x20, 0x0,
    0xdf, 0xf8, 0xff, 0xfd, 0x20, 0x0, 0xd, 0xff,
    0x7f, 0xfd, 0x10, 0x0, 0x0, 0xdf, 0xf3, 0xfb,
    0x10, 0x0, 0x0, 0xc, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe2, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0x80, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x40, 0x0, 0x0, 0x5f, 0xff, 0xe2, 0x0, 0x0,
    0x5f, 0xff, 0xe2, 0x0, 0x0, 0x5f, 0xff, 0xe3,
    0x0, 0x0, 0x5f, 0xff, 0xe3, 0x0, 0x0, 0x5f,
    0xff, 0xe3, 0x0, 0x0, 0x5f, 0xff, 0xe3, 0x0,
    0x0, 0xd, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xe3, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xe3,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xe3, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x5f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdb, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x9, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf6, 0x0,
    0x0, 0x9, 0xff, 0xfc, 0x0, 0x0, 0x9, 0xff,
    0xfc, 0x0, 0x0, 0x9, 0xff, 0xfc, 0x0, 0x0,
    0x9, 0xff, 0xfc, 0x0, 0x0, 0x9, 0xff, 0xfc,
    0x0, 0x0, 0x9, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0x0, 0x2, 0xdb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x2, 0x55, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x37,
    0x77, 0x77, 0x8f, 0xff, 0xc7, 0x77, 0x77, 0x60,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x14, 0x44, 0x44, 0x5f, 0xff,
    0xb4, 0x44, 0x44, 0x30, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x21, 0x0,
    0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x49, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x2, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x10,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x6, 0xad, 0xff, 0xec, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xef, 0xff, 0xfc, 0x40, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0x92, 0x0, 0x5, 0xdf, 0xff,
    0x90, 0x0, 0x0, 0x4, 0xff, 0xff, 0x50, 0x2,
    0x52, 0x1, 0xcf, 0xff, 0xb0, 0x0, 0x4, 0xff,
    0xff, 0x80, 0x0, 0x7f, 0xf9, 0x1, 0xef, 0xff,
    0xb0, 0x1, 0xef, 0xff, 0xf0, 0x0, 0x8, 0xff,
    0xf7, 0x8, 0xff, 0xff, 0x80, 0xaf, 0xff, 0xfb,
    0x2, 0x25, 0xff, 0xff, 0xe0, 0x3f, 0xff, 0xff,
    0x2f, 0xff, 0xff, 0xa0, 0x7f, 0xff, 0xff, 0xff,
    0x2, 0xff, 0xff, 0xf7, 0x9f, 0xff, 0xfb, 0x5,
    0xff, 0xff, 0xff, 0xc0, 0x3f, 0xff, 0xff, 0x21,
    0xef, 0xff, 0xf0, 0xc, 0xff, 0xff, 0xf5, 0x7,
    0xff, 0xff, 0x80, 0x3, 0xff, 0xff, 0x80, 0x1a,
    0xff, 0xe5, 0x1, 0xef, 0xff, 0xb0, 0x0, 0x4,
    0xff, 0xff, 0x50, 0x0, 0x10, 0x1, 0xcf, 0xff,
    0xb0, 0x0, 0x0, 0x3, 0xdf, 0xff, 0x92, 0x0,
    0x5, 0xdf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xef, 0xff, 0xfc, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xad, 0xef, 0xec,
    0x83, 0x0, 0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0x70, 0x4, 0x8c, 0xef, 0xed, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfb,
    0xef, 0xff, 0xfe, 0xff, 0xff, 0xe6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xc4, 0x0,
    0x4, 0xcf, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0x60, 0x3, 0x10, 0x9, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf9, 0x4f, 0xfa, 0x0, 0xcf, 0xff, 0xe1, 0x0,
    0x0, 0xb, 0xb0, 0x0, 0x4e, 0xff, 0xef, 0xff,
    0xa0, 0x4f, 0xff, 0xfb, 0x0, 0x0, 0x6f, 0xfd,
    0x30, 0x1, 0xcf, 0xff, 0xff, 0xf1, 0xf, 0xff,
    0xff, 0x50, 0x0, 0xbf, 0xff, 0xf6, 0x0, 0x8,
    0xff, 0xff, 0xf3, 0xe, 0xff, 0xff, 0xa0, 0x0,
    0x6f, 0xff, 0xff, 0x0, 0x0, 0x5f, 0xff, 0xf2,
    0xf, 0xff, 0xff, 0x50, 0x0, 0xc, 0xff, 0xff,
    0x40, 0x0, 0x2, 0xdf, 0xfe, 0x8f, 0xff, 0xfa,
    0x0, 0x0, 0x1, 0xef, 0xff, 0xc0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xff,
    0xc4, 0x0, 0x0, 0x3, 0xef, 0xfd, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xef, 0xff, 0xfe, 0xe3,
    0x0, 0x1b, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x9d, 0xef, 0xec, 0x20, 0x0, 0x8f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2c, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x60,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xa3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xa2, 0x24, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0x90, 0x1, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xa0, 0x2, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xb0, 0x3, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xc0, 0x4, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xd0, 0x5, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xf9, 0x9c, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xf5, 0x2b, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0x90, 0x1, 0xff, 0xff, 0xff, 0xfe, 0x10,
    0x1, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xff,
    0xff, 0xff, 0xff, 0x90, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x1a, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x1, 0x68, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x40,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x50, 0xef, 0xff,
    0xf6, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0x50, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xf7, 0x22, 0x23, 0xdf, 0xf8,
    0x9, 0xff, 0xf7, 0x2f, 0xff, 0x80, 0x0, 0x0,
    0x2e, 0xb0, 0x7f, 0xff, 0x90, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x3, 0x6, 0xff, 0xfa, 0x0, 0x7,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xfd, 0x3, 0x0, 0x7, 0x60, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xe1, 0x3f, 0x90, 0xf, 0xf8, 0x0,
    0x22, 0x23, 0xdf, 0xfe, 0x22, 0xef, 0xf7, 0x2f,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xf3, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0x50,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xfe, 0xef, 0xff,
    0xf6, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x30, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x94, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xae, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xf9, 0x2, 0xef, 0xff, 0x50, 0x0, 0x0, 0xcf,
    0xff, 0x90, 0x0, 0x2e, 0xff, 0xf5, 0x0, 0xc,
    0xff, 0xf9, 0x0, 0x0, 0x2, 0xef, 0xff, 0x50,
    0xaf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xf2, 0x7f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xe1, 0x6, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x29, 0x20,

    /* U+F078 "" */
    0x6, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29,
    0x20, 0x7f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xe1, 0x9f, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xf2, 0xb, 0xff, 0xf9, 0x0, 0x0,
    0x3, 0xef, 0xff, 0x50, 0x0, 0xbf, 0xff, 0x90,
    0x0, 0x3e, 0xff, 0xf5, 0x0, 0x0, 0xb, 0xff,
    0xf9, 0x3, 0xef, 0xff, 0x50, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xae, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x94, 0x0,
    0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x9c, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xd1, 0x0, 0x58, 0x88, 0x88, 0x88, 0x88, 0x81,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xfd, 0x20, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xe2, 0x4e, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x8f, 0xfc, 0xff, 0xcf,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf6, 0x0,
    0x0, 0x7f, 0xc2, 0xff, 0x67, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x3, 0x1,
    0xff, 0x60, 0x30, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf6, 0x0, 0x0, 0x0, 0x1, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf6, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x3, 0xd7, 0x1f, 0xf6,
    0x3d, 0x70, 0x0, 0x1, 0xff, 0x60, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x7f, 0xf9, 0xef, 0xf0, 0x0,
    0x1, 0xff, 0xb8, 0x88, 0x88, 0x88, 0x32, 0xef,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x2e, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xe2, 0x2, 0xef, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x30, 0x0, 0x0,

    /* U+F07B "" */
    0x5e, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb8, 0x88, 0x88, 0x88, 0x60, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x5e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x11, 0x1b, 0xff, 0xff, 0x51, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x7a, 0xaa, 0xaa, 0x2b,
    0xff, 0xff, 0x42, 0xaa, 0xaa, 0xa7, 0xff, 0xff,
    0xff, 0x82, 0x67, 0x76, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x77, 0x77, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xf, 0x48, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xef, 0xff,
    0x14, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x41,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xfe, 0xb5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x1, 0x30, 0x0, 0x0, 0x1d, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x29, 0xff, 0x70, 0x0, 0x3e, 0xff,
    0xff, 0x30, 0x0, 0x4, 0xbf, 0xff, 0xff, 0x40,
    0x7f, 0xff, 0xff, 0x50, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xfe, 0xef, 0xff, 0xff, 0x50, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x10, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xea, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x57, 0x64, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x25, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x4,
    0xaa, 0x50, 0x7f, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x8f, 0xff, 0xf5, 0xef, 0xd3, 0x7f, 0xf6, 0x0,
    0x8, 0xff, 0xff, 0xb0, 0xff, 0x80, 0xf, 0xf7,
    0x0, 0x8f, 0xff, 0xfb, 0x0, 0xdf, 0xe7, 0xaf,
    0xf5, 0x8, 0xff, 0xff, 0xb0, 0x0, 0x5f, 0xff,
    0xff, 0xfd, 0x9f, 0xff, 0xfb, 0x0, 0x0, 0x5,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x1, 0x5f, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x25, 0x9f, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xfb,
    0x6f, 0xff, 0xfd, 0x10, 0x0, 0xef, 0xd3, 0x7f,
    0xf5, 0x5, 0xff, 0xff, 0xd1, 0x0, 0xff, 0x80,
    0xf, 0xf7, 0x0, 0x5f, 0xff, 0xfd, 0x10, 0xdf,
    0xe7, 0xaf, 0xf5, 0x0, 0x5, 0xff, 0xff, 0xd1,
    0x5f, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x4f, 0xff,
    0xf4, 0x5, 0xef, 0xfb, 0x10, 0x0, 0x0, 0x1,
    0x66, 0x20, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x8, 0xbb, 0xbb, 0xbb, 0x50, 0x90,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0x81,
    0xfb, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x81, 0xff, 0xb0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0x81, 0xff, 0xf8, 0x8c, 0xc9, 0xf, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0xff, 0xfc, 0xf,
    0xff, 0xff, 0xff, 0xd5, 0x44, 0x43, 0xff, 0xfc,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xfc, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xfc, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xfc, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xfc, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xfc, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xfc,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xfc, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xfe, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x58, 0x88, 0x88, 0x88,
    0x88, 0x87, 0x10, 0x0, 0x0,

    /* U+F0C7 "" */
    0x6, 0x77, 0x77, 0x77, 0x77, 0x77, 0x60, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x0, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xfc, 0x10, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xc0, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xf3, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xf4, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xf4,
    0xff, 0xd8, 0x88, 0x88, 0x88, 0x88, 0xef, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xe4, 0x2,
    0xcf, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x2f, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff,
    0x40, 0x0, 0xf, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x6f, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xfc, 0x8a, 0xff, 0xff, 0xff, 0xf4,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F0E7 "" */
    0x0, 0x14, 0x44, 0x44, 0x41, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x40,
    0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x6, 0x71, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x14, 0x44, 0xbf, 0xfe, 0x44, 0x43, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xf9, 0x4f, 0xff, 0xff,
    0x40, 0x0, 0x0, 0xff, 0xff, 0xf8, 0x3f, 0xff,
    0xff, 0x50, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xa8, 0x88, 0x88, 0x20, 0x0, 0x0, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xf0, 0xcf, 0xff, 0xff, 0x51, 0xe2, 0x0,
    0xff, 0xff, 0xf0, 0xef, 0xff, 0xff, 0x51, 0xfe,
    0x20, 0xff, 0xff, 0xf0, 0xef, 0xff, 0xff, 0x51,
    0xff, 0xe2, 0xff, 0xff, 0xf0, 0xef, 0xff, 0xff,
    0x50, 0xbb, 0xb7, 0xff, 0xff, 0xf0, 0xef, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xff, 0xff, 0xf0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xf0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff,
    0xf0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xff, 0xf0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xcf, 0xff, 0xf0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x7b,
    0xbb, 0xbb, 0xbb, 0xbb, 0xb4,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xfb, 0x20, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x75, 0x0, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff,
    0xc8, 0x8f, 0xa8, 0xaf, 0x88, 0xbf, 0x88, 0xfb,
    0x88, 0xff, 0x8f, 0xf8, 0x0, 0xf4, 0x4, 0xf0,
    0x5, 0xe0, 0xe, 0x50, 0xf, 0xf8, 0xff, 0x80,
    0xf, 0x40, 0x4f, 0x0, 0x6f, 0x0, 0xf6, 0x0,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0x94,
    0x6f, 0x64, 0x8f, 0x44, 0xbb, 0x44, 0xff, 0xff,
    0x8f, 0xff, 0xf6, 0x2, 0xf2, 0x5, 0xf0, 0x8,
    0x80, 0xe, 0xff, 0xf8, 0xff, 0xff, 0x94, 0x6f,
    0x64, 0x8f, 0x44, 0xbb, 0x44, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0x80, 0xf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xf6, 0x0, 0xff, 0x8f, 0xf8,
    0x0, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xe, 0x50,
    0xf, 0xf8, 0xff, 0xc8, 0x8f, 0xa8, 0x88, 0x88,
    0x88, 0x88, 0xfb, 0x88, 0xff, 0x8e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x56, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x7e, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x8f, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x5, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x6, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x18, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x2, 0xac, 0xcc, 0xcc, 0xcd, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x27, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x24, 0x44, 0x44, 0x44, 0x30, 0x30, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xfc, 0xf, 0x60, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0xff, 0x60, 0xf, 0xff,
    0xff, 0xff, 0xfc, 0xf, 0xff, 0x60, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xff, 0xfc, 0xb, 0xbb, 0xbb, 0xff, 0xff, 0xff,
    0xff, 0xe1, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8b, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x80,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x23, 0x43, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x8c, 0xff, 0xff, 0xff, 0xfc, 0x83, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x60, 0x0, 0x0, 0x3, 0xdf,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xd3, 0x0, 0x8, 0xff, 0xff, 0xfb, 0x72, 0x0,
    0x0, 0x2, 0x7b, 0xff, 0xff, 0xf8, 0xa, 0xff,
    0xff, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xaf, 0xff, 0xfa, 0xbf, 0xfd, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0xff, 0xb0,
    0xba, 0x10, 0x0, 0x5, 0x9d, 0xef, 0xed, 0x95,
    0x0, 0x0, 0x1a, 0xb0, 0x0, 0x0, 0x0, 0x6d,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xfa, 0x53, 0x23, 0x5a, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xb1, 0x0, 0x0,
    0x0, 0x1, 0xbf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x9d, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x84, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc, 0xff, 0xff, 0xf8,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x5b, 0xff, 0xff, 0x84, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x8f, 0xff,
    0xf8, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8, 0xff, 0xff, 0x84, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F241 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x81, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x81, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x81, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F242 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x81, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x81, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x1f, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x81, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F243 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x81, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x81, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x1f, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x81, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F244 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7c,
    0xdf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfb, 0xbf, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0x70, 0xa, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x32, 0x0, 0x0, 0x9e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0x90, 0x1, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0x30, 0x0, 0xcf, 0xff, 0xf6, 0x3c, 0xf3,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x5f, 0xf9, 0x10,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0xcf, 0xff, 0xf6,
    0x33, 0x34, 0xed, 0x33, 0x33, 0x33, 0x33, 0x5f,
    0xfa, 0x10, 0x2d, 0xff, 0x90, 0x0, 0x0, 0x5f,
    0x30, 0x0, 0x0, 0x0, 0x1c, 0x30, 0x0, 0x0,
    0x32, 0x0, 0x0, 0x0, 0xd, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xf3, 0xa, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xce,
    0xae, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xbe, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x22, 0x20, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x34, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xbf, 0xff, 0xff, 0xe7, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xfa, 0xff, 0xff, 0xb0, 0x0,
    0x4, 0xff, 0xff, 0xf1, 0x9f, 0xff, 0xf8, 0x0,
    0xd, 0xff, 0xff, 0xf1, 0xa, 0xff, 0xff, 0x10,
    0x3f, 0xff, 0xff, 0xf1, 0x0, 0xbf, 0xff, 0x60,
    0x7f, 0xfd, 0x8f, 0xf1, 0x66, 0xc, 0xff, 0xa0,
    0xaf, 0xf8, 0x7, 0xf1, 0x6f, 0x13, 0xff, 0xd0,
    0xcf, 0xff, 0x70, 0x71, 0x53, 0x1e, 0xff, 0xf0,
    0xdf, 0xff, 0xf7, 0x0, 0x1, 0xdf, 0xff, 0xf0,
    0xef, 0xff, 0xff, 0x60, 0xc, 0xff, 0xff, 0xf0,
    0xef, 0xff, 0xff, 0x30, 0x7, 0xff, 0xff, 0xf0,
    0xdf, 0xff, 0xf3, 0x0, 0x10, 0x8f, 0xff, 0xf0,
    0xcf, 0xff, 0x30, 0xb1, 0x67, 0x9, 0xff, 0xf0,
    0x9f, 0xf6, 0xb, 0xf2, 0x6e, 0x2, 0xff, 0xd0,
    0x6f, 0xff, 0xcf, 0xf2, 0x52, 0x2e, 0xff, 0xa0,
    0x1f, 0xff, 0xff, 0xf2, 0x2, 0xef, 0xff, 0x50,
    0x9, 0xff, 0xff, 0xf2, 0x2e, 0xff, 0xfe, 0x0,
    0x0, 0xdf, 0xff, 0xf4, 0xef, 0xff, 0xf5, 0x0,
    0x0, 0x1a, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x27, 0xab, 0xb9, 0x50, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x2, 0xab, 0xbb, 0xb7, 0x0, 0x0,
    0x0, 0x57, 0x77, 0x7c, 0xff, 0xff, 0xff, 0x77,
    0x77, 0x72, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x20, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0xc, 0xff, 0x77, 0xff, 0x3b, 0xfe, 0x1e, 0xff,
    0x40, 0xc, 0xff, 0x66, 0xff, 0x2a, 0xfe, 0xe,
    0xff, 0x40, 0xc, 0xff, 0x66, 0xff, 0x2a, 0xfe,
    0xe, 0xff, 0x40, 0xc, 0xff, 0x66, 0xff, 0x2a,
    0xfe, 0xe, 0xff, 0x40, 0xc, 0xff, 0x66, 0xff,
    0x2a, 0xfe, 0xe, 0xff, 0x40, 0xc, 0xff, 0x66,
    0xff, 0x2a, 0xfe, 0xe, 0xff, 0x40, 0xc, 0xff,
    0x66, 0xff, 0x2a, 0xfe, 0xe, 0xff, 0x40, 0xc,
    0xff, 0x66, 0xff, 0x2a, 0xfe, 0xe, 0xff, 0x40,
    0xc, 0xff, 0x66, 0xff, 0x2a, 0xfe, 0xe, 0xff,
    0x40, 0xc, 0xff, 0x77, 0xff, 0x3b, 0xfe, 0x1e,
    0xff, 0x40, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x57, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x72, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x64, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xdf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x90, 0x8f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xb0, 0x8f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xb0, 0x8f,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xb0, 0x8e, 0x20, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0xff, 0xb0, 0x10, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0x75, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x20, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0x85, 0xff, 0xff, 0x58, 0xff,
    0xff, 0xff, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xb0,
    0x4, 0xff, 0x40, 0xb, 0xff, 0xff, 0xf0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x4, 0x40, 0x4,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0xef, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x40, 0x4,
    0x40, 0x4, 0xff, 0xff, 0xff, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xb0, 0x4, 0xff, 0x40, 0xb, 0xff,
    0xff, 0xf0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0x85,
    0xff, 0xff, 0x58, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x20,

    /* U+F7C2 "" */
    0x0, 0x0, 0x28, 0x88, 0x88, 0x88, 0x73, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x1d,
    0xf6, 0xe, 0x50, 0xd6, 0x8, 0xff, 0x1d, 0xff,
    0x60, 0xe5, 0xd, 0x60, 0x8f, 0xfc, 0xff, 0xf6,
    0xe, 0x50, 0xd6, 0x8, 0xff, 0xff, 0xff, 0x60,
    0xe5, 0xd, 0x60, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x7, 0xab,
    0xbb, 0xbb, 0xbb, 0xbb, 0xa6, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x10,
    0x0, 0xa, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf1, 0x0, 0xb, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x10, 0xc, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf1, 0x1d,
    0xff, 0xff, 0xcb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf,
    0xff, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x9f,
    0xff, 0xf9, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x40, 0x0, 0x8f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0
};

/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 86, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 86, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 21, .adv_w = 125, .box_w = 6, .box_h = 6, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 39, .adv_w = 225, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 137, .adv_w = 199, .box_w = 12, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 257, .adv_w = 270, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 376, .adv_w = 220, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 481, .adv_w = 67, .box_w = 2, .box_h = 6, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 487, .adv_w = 108, .box_w = 6, .box_h = 19, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 544, .adv_w = 108, .box_w = 5, .box_h = 19, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 592, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 624, .adv_w = 186, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 674, .adv_w = 73, .box_w = 4, .box_h = 6, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 686, .adv_w = 123, .box_w = 6, .box_h = 2, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 692, .adv_w = 73, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 698, .adv_w = 113, .box_w = 9, .box_h = 20, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 788, .adv_w = 213, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 879, .adv_w = 118, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 921, .adv_w = 184, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 998, .adv_w = 183, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1075, .adv_w = 214, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1173, .adv_w = 184, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1250, .adv_w = 197, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1334, .adv_w = 191, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1418, .adv_w = 206, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1502, .adv_w = 197, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1586, .adv_w = 73, .box_w = 4, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1608, .adv_w = 73, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1636, .adv_w = 186, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 1686, .adv_w = 186, .box_w = 10, .box_h = 7, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 1721, .adv_w = 186, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 1771, .adv_w = 183, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1848, .adv_w = 331, .box_w = 20, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2028, .adv_w = 234, .box_w = 16, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2140, .adv_w = 242, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2231, .adv_w = 231, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2329, .adv_w = 264, .box_w = 14, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2427, .adv_w = 214, .box_w = 11, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2504, .adv_w = 203, .box_w = 10, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2574, .adv_w = 247, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2672, .adv_w = 260, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2763, .adv_w = 99, .box_w = 3, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2784, .adv_w = 164, .box_w = 10, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2854, .adv_w = 230, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2945, .adv_w = 190, .box_w = 10, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3015, .adv_w = 306, .box_w = 15, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3120, .adv_w = 260, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3211, .adv_w = 269, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3323, .adv_w = 231, .box_w = 12, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3407, .adv_w = 269, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3552, .adv_w = 233, .box_w = 12, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3636, .adv_w = 199, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3720, .adv_w = 188, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3804, .adv_w = 253, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3895, .adv_w = 228, .box_w = 16, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4007, .adv_w = 360, .box_w = 22, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4161, .adv_w = 215, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4259, .adv_w = 207, .box_w = 15, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4364, .adv_w = 210, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4455, .adv_w = 107, .box_w = 5, .box_h = 19, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 4503, .adv_w = 113, .box_w = 9, .box_h = 20, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4593, .adv_w = 107, .box_w = 5, .box_h = 19, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4641, .adv_w = 187, .box_w = 10, .box_h = 9, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 4686, .adv_w = 160, .box_w = 10, .box_h = 3, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4701, .adv_w = 192, .box_w = 6, .box_h = 3, .ofs_x = 2, .ofs_y = 12},
    {.bitmap_index = 4710, .adv_w = 191, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4765, .adv_w = 218, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4855, .adv_w = 183, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4916, .adv_w = 218, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5006, .adv_w = 196, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5072, .adv_w = 113, .box_w = 8, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5132, .adv_w = 221, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5222, .adv_w = 218, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5305, .adv_w = 89, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5335, .adv_w = 91, .box_w = 7, .box_h = 19, .ofs_x = -2, .ofs_y = -4},
    {.bitmap_index = 5402, .adv_w = 197, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5492, .adv_w = 89, .box_w = 3, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5515, .adv_w = 338, .box_w = 19, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5620, .adv_w = 218, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5681, .adv_w = 203, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5747, .adv_w = 218, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 5837, .adv_w = 218, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5927, .adv_w = 131, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5966, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6021, .adv_w = 132, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6077, .adv_w = 217, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6138, .adv_w = 179, .box_w = 13, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 6210, .adv_w = 288, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6309, .adv_w = 177, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6370, .adv_w = 179, .box_w = 13, .box_h = 15, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 6468, .adv_w = 167, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6523, .adv_w = 112, .box_w = 6, .box_h = 19, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 6580, .adv_w = 96, .box_w = 2, .box_h = 19, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 6599, .adv_w = 112, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 6656, .adv_w = 186, .box_w = 10, .box_h = 4, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 6676, .adv_w = 134, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 6708, .adv_w = 100, .box_w = 4, .box_h = 4, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 6716, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6926, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7076, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7266, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7416, .adv_w = 220, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7521, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7731, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7941, .adv_w = 360, .box_w = 23, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8160, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8370, .adv_w = 360, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8543, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8753, .adv_w = 160, .box_w = 10, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8833, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8953, .adv_w = 360, .box_w = 23, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9172, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9322, .adv_w = 280, .box_w = 13, .box_h = 19, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 9446, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9635, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9806, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9977, .adv_w = 280, .box_w = 13, .box_h = 19, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 10101, .adv_w = 280, .box_w = 19, .box_h = 19, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 10282, .adv_w = 200, .box_w = 11, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 10387, .adv_w = 200, .box_w = 11, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 10492, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10663, .adv_w = 280, .box_w = 18, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 10708, .adv_w = 360, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10881, .adv_w = 400, .box_w = 26, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 11154, .adv_w = 360, .box_w = 24, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 11406, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11596, .adv_w = 280, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 11695, .adv_w = 280, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 11794, .adv_w = 400, .box_w = 26, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 12002, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12152, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12362, .adv_w = 320, .box_w = 21, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 12583, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12754, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12943, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13114, .adv_w = 200, .box_w = 14, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 13261, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13450, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13639, .adv_w = 360, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13812, .adv_w = 320, .box_w = 22, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 14043, .adv_w = 240, .box_w = 15, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14201, .adv_w = 400, .box_w = 25, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14439, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 14602, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 14765, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 14928, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 15091, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 15254, .adv_w = 400, .box_w = 26, .box_h = 17, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 15475, .adv_w = 280, .box_w = 16, .box_h = 21, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 15643, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 15832, .adv_w = 320, .box_w = 21, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 16053, .adv_w = 400, .box_w = 25, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16241, .adv_w = 240, .box_w = 15, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 16399, .adv_w = 322, .box_w = 21, .box_h = 13, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1f72, 0xef51, 0xef58, 0xef5b, 0xef5c, 0xef5d, 0xef61,
    0xef63, 0xef65, 0xef69, 0xef6c, 0xef71, 0xef76, 0xef77, 0xef78,
    0xef8e, 0xef98, 0xef9b, 0xef9c, 0xef9d, 0xefa1, 0xefa2, 0xefa3,
    0xefa4, 0xefb7, 0xefb8, 0xefbe, 0xefc0, 0xefc1, 0xefc4, 0xefc7,
    0xefc8, 0xefc9, 0xefcb, 0xefe3, 0xefe5, 0xf014, 0xf015, 0xf017,
    0xf037, 0xf03a, 0xf043, 0xf06c, 0xf074, 0xf0ab, 0xf13b, 0xf190,
    0xf191, 0xf192, 0xf193, 0xf194, 0xf1d7, 0xf1e3, 0xf23d, 0xf254,
    0xf4aa, 0xf712, 0xf7f2
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 63475, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 59, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/

/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    61, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    49, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 3, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 14, 0, 9, -7, 0, 0,
    0, 0, -18, -19, 2, 15, 7, 5,
    -13, 2, 16, 1, 13, 3, 10, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 19, 3, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 6, 0, -10, 0, 0, 0, 0,
    0, -6, 5, 6, 0, 0, -3, 0,
    -2, 3, 0, -3, 0, -3, -2, -6,
    0, 0, 0, 0, -3, 0, 0, -4,
    -5, 0, 0, -3, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    -3, 0, -5, 0, -9, 0, -39, 0,
    0, -6, 0, 6, 10, 0, 0, -6,
    3, 3, 11, 6, -5, 6, 0, 0,
    -18, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -12, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -9, -4, -16, 0, -13,
    -2, 0, 0, 0, 0, 1, 12, 0,
    -10, -3, -1, 1, 0, -5, 0, 0,
    -2, -24, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -26, -3, 12,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -13, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 11,
    0, 3, 0, 0, -6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 12, 3,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -12, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 2,
    6, 3, 10, -3, 0, 0, 6, -3,
    -11, -44, 2, 9, 6, 1, -4, 0,
    12, 0, 10, 0, 10, 0, -30, 0,
    -4, 10, 0, 11, -3, 6, 3, 0,
    0, 1, -3, 0, 0, -5, 26, 0,
    26, 0, 10, 0, 13, 4, 5, 10,
    0, 0, 0, -12, 0, 0, 0, 0,
    1, -2, 0, 2, -6, -4, -6, 2,
    0, -3, 0, 0, 0, -13, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -21, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, -18, 0, -20, 0, 0, 0,
    0, -2, 0, 32, -4, -4, 3, 3,
    -3, 0, -4, 3, 0, 0, -17, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -31, 0, 3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -20, 0, 19, 0, 0, -12, 0,
    11, 0, -22, -31, -22, -6, 10, 0,
    0, -21, 0, 4, -7, 0, -5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 8, 10, -39, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 15, 0, 2, 0, 0, 0,
    0, 0, 2, 2, -4, -6, 0, -1,
    -1, -3, 0, 0, -2, 0, 0, 0,
    -6, 0, -3, 0, -7, -6, 0, -8,
    -11, -11, -6, 0, -6, 0, -6, 0,
    0, 0, 0, -3, 0, 0, 3, 0,
    2, -3, 0, 1, 0, 0, 0, 3,
    -2, 0, 0, 0, -2, 3, 3, -1,
    0, 0, 0, -6, 0, -1, 0, 0,
    0, 0, 0, 1, 0, 4, -2, 0,
    -4, 0, -5, 0, 0, -2, 0, 10,
    0, 0, -3, 0, 0, 0, 0, 0,
    -1, 1, -2, -2, 0, 0, -3, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, -2, 0, -3, -4, 0,
    0, 0, 0, 0, 1, 0, 0, -2,
    0, -3, -3, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, -2, -4, 0, -5, 0, -10,
    -2, -10, 6, 0, 0, -6, 3, 6,
    9, 0, -8, -1, -4, 0, -1, -15,
    3, -2, 2, -17, 3, 0, 0, 1,
    -17, 0, -17, -3, -28, -2, 0, -16,
    0, 6, 9, 0, 4, 0, 0, 0,
    0, 1, 0, -6, -4, 0, -10, 0,
    0, 0, -3, 0, 0, 0, -3, 0,
    0, 0, 0, 0, -2, -2, 0, -2,
    -4, 0, 0, 0, 0, 0, 0, 0,
    -3, -3, 0, -2, -4, -3, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, -3, 0, -4,
    0, -2, 0, -6, 3, 0, 0, -4,
    2, 3, 3, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 2,
    0, 0, -3, 0, -3, -2, -4, 0,
    0, 0, 0, 0, 0, 0, 3, 0,
    -3, 0, 0, 0, 0, -4, -5, 0,
    -6, 0, 10, -2, 1, -10, 0, 0,
    9, -16, -17, -13, -6, 3, 0, -3,
    -21, -6, 0, -6, 0, -6, 5, -6,
    -20, 0, -9, 0, 0, 2, -1, 3,
    -2, 0, 3, 0, -10, -12, 0, -16,
    -8, -7, -8, -10, -4, -9, -1, -6,
    -9, 2, 0, 1, 0, -3, 0, 0,
    0, 2, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, -2, 0, -1, -3, 0, -5, -7,
    -7, -1, 0, -10, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 1,
    -2, 0, 0, 0, 3, 0, 0, 0,
    0, 0, 0, 0, 0, 15, 0, 0,
    0, 0, 0, 0, 2, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    -6, 0, 0, 0, 0, -16, -10, 0,
    0, 0, -5, -16, 0, 0, -3, 3,
    0, -9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, 0, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 3, 0, -6, 0,
    0, 0, 0, 4, 0, 2, -6, -6,
    0, -3, -3, -4, 0, 0, 0, 0,
    0, 0, -10, 0, -3, 0, -5, -3,
    0, -7, -8, -10, -3, 0, -6, 0,
    -10, 0, 0, 0, 0, 26, 0, 0,
    2, 0, 0, -4, 0, 3, 0, -14,
    0, 0, 0, 0, 0, -30, -6, 11,
    10, -3, -13, 0, 3, -5, 0, -16,
    -2, -4, 3, -22, -3, 4, 0, 5,
    -11, -5, -12, -11, -13, 0, 0, -19,
    0, 18, 0, 0, -2, 0, 0, 0,
    -2, -2, -3, -9, -11, -1, -30, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, -2, -3, -5, 0, 0,
    -6, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -6, 0, 0, 6,
    -1, 4, 0, -7, 3, -2, -1, -8,
    -3, 0, -4, -3, -2, 0, -5, -5,
    0, 0, -3, -1, -2, -5, -4, 0,
    0, -3, 0, 3, -2, 0, -7, 0,
    0, 0, -6, 0, -5, 0, -5, -5,
    3, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 3, 0, -4, 0, -2, -4,
    -10, -2, -2, -2, -1, -2, -4, -1,
    0, 0, 0, 0, 0, -3, -3, -3,
    0, 0, 0, 0, 4, -2, 0, -2,
    0, 0, 0, -2, -4, -2, -3, -4,
    -3, 0, 3, 13, -1, 0, -9, 0,
    -2, 6, 0, -3, -13, -4, 5, 0,
    0, -15, -5, 3, -5, 2, 0, -2,
    -3, -10, 0, -5, 2, 0, 0, -5,
    0, 0, 0, 3, 3, -6, -6, 0,
    -5, -3, -5, -3, -3, 0, -5, 2,
    -6, -5, 10, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, 0, -4,
    0, 0, -3, -3, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    -5, 0, -6, 0, 0, 0, -11, 0,
    2, -7, 6, 1, -2, -15, 0, 0,
    -7, -3, 0, -13, -8, -9, 0, 0,
    -14, -3, -13, -12, -15, 0, -8, 0,
    3, 21, -4, 0, -7, -3, -1, -3,
    -5, -9, -6, -12, -13, -7, -3, 0,
    0, -2, 0, 1, 0, 0, -22, -3,
    10, 7, -7, -12, 0, 1, -10, 0,
    -16, -2, -3, 6, -29, -4, 1, 0,
    0, -21, -4, -17, -3, -23, 0, 0,
    -22, 0, 19, 1, 0, -2, 0, 0,
    0, 0, -2, -2, -12, -2, 0, -21,
    0, 0, 0, 0, -10, 0, -3, 0,
    -1, -9, -15, 0, 0, -2, -5, -10,
    -3, 0, -2, 0, 0, 0, 0, -14,
    -3, -11, -10, -3, -5, -8, -3, -5,
    0, -6, -3, -11, -5, 0, -4, -6,
    -3, -6, 0, 2, 0, -2, -11, 0,
    6, 0, -6, 0, 0, 0, 0, 4,
    0, 2, -6, 13, 0, -3, -3, -4,
    0, 0, 0, 0, 0, 0, -10, 0,
    -3, 0, -5, -3, 0, -7, -8, -10,
    -3, 0, -6, 3, 13, 0, 0, 0,
    0, 26, 0, 0, 2, 0, 0, -4,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    -2, -6, 0, 0, 0, 0, 0, -2,
    0, 0, 0, -3, -3, 0, 0, -6,
    -3, 0, 0, -6, 0, 5, -2, 0,
    0, 0, 0, 0, 0, 2, 0, 0,
    0, 0, 5, 6, 3, -3, 0, -10,
    -5, 0, 10, -11, -10, -6, -6, 13,
    6, 3, -28, -2, 6, -3, 0, -3,
    4, -3, -11, 0, -3, 3, -4, -3,
    -10, -3, 0, 0, 10, 6, 0, -9,
    0, -18, -4, 9, -4, -12, 1, -4,
    -11, -11, -3, 13, 3, 0, -5, 0,
    -9, 0, 3, 11, -7, -12, -13, -8,
    10, 0, 1, -23, -3, 3, -5, -2,
    -7, 0, -7, -12, -5, -5, -3, 0,
    0, -7, -7, -3, 0, 10, 7, -3,
    -18, 0, -18, -4, 0, -11, -19, -1,
    -10, -5, -11, -9, 9, 0, 0, -4,
    0, -6, -3, 0, -3, -6, 0, 5,
    -11, 3, 0, 0, -17, 0, -3, -7,
    -5, -2, -10, -8, -11, -7, 0, -10,
    -3, -7, -6, -10, -3, 0, 0, 1,
    15, -5, 0, -10, -3, 0, -3, -6,
    -7, -9, -9, -12, -4, -6, 6, 0,
    -5, 0, -16, -4, 2, 6, -10, -12,
    -6, -11, 11, -3, 2, -30, -6, 6,
    -7, -5, -12, 0, -10, -13, -4, -3,
    -3, -3, -7, -10, -1, 0, 0, 10,
    9, -2, -21, 0, -19, -7, 8, -12,
    -22, -6, -11, -13, -16, -11, 6, 0,
    0, 0, 0, -4, 0, 0, 3, -4,
    6, 2, -6, 6, 0, 0, -10, -1,
    0, -1, 0, 1, 1, -3, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 3, 10, 1, 0, -4, 0, 0,
    0, 0, -2, -2, -4, 0, 0, 0,
    1, 3, 0, 0, 0, 0, 3, 0,
    -3, 0, 12, 0, 6, 1, 1, -4,
    0, 6, 0, 0, 0, 3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 10, 0, 9, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -19, 0, -3, 5, 0, 10,
    0, 0, 32, 4, -6, -6, 3, 3,
    -2, 1, -16, 0, 0, 15, -19, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -22, 12, 45, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -19, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, 0, -6,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, -9, 0,
    0, 1, 0, 0, 3, 41, -6, -3,
    10, 9, -9, 3, 0, 0, 3, 3,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -42, 9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -9,
    0, 0, 0, -9, 0, 0, 0, 0,
    -7, -2, 0, 0, 0, -7, 0, -4,
    0, -15, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -21, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, -3, 0, 0, -6, 0, -5, 0,
    -9, 0, 0, 0, -5, 3, -4, 0,
    0, -9, -3, -7, 0, 0, -9, 0,
    -3, 0, -15, 0, -4, 0, 0, -26,
    -6, -13, -4, -12, 0, 0, -21, 0,
    -9, -2, 0, 0, 0, 0, 0, 0,
    0, 0, -5, -6, -3, -5, 0, 0,
    0, 0, -7, 0, -7, 4, -4, 6,
    0, -2, -7, -2, -5, -6, 0, -4,
    -2, -2, 2, -9, -1, 0, 0, 0,
    -28, -3, -4, 0, -7, 0, -2, -15,
    -3, 0, 0, -2, -3, 0, 0, 0,
    0, 2, 0, -2, -5, -2, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 4, 0, 0, 0, 0, 0,
    0, -7, 0, -2, 0, 0, 0, -6,
    3, 0, 0, 0, -9, -3, -6, 0,
    0, -9, 0, -3, 0, -15, 0, 0,
    0, 0, -31, 0, -6, -12, -16, 0,
    0, -21, 0, -2, -5, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -5, -2,
    -5, 1, 0, 0, 5, -4, 0, 10,
    16, -3, -3, -10, 4, 16, 5, 7,
    -9, 4, 13, 4, 9, 7, 9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 20, 15, -6, -3, 0, -3,
    26, 14, 26, 0, 0, 0, 3, 0,
    0, 12, 0, 0, -5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    0, 0, 0, 0, -27, -4, -3, -13,
    -16, 0, 0, -21, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    4, 0, 0, 0, 0, -27, -4, -3,
    -13, -16, 0, 0, -13, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, -7, 3, 0, -3,
    3, 6, 3, -10, 0, -1, -3, 3,
    0, 3, 0, 0, 0, 0, -8, 0,
    -3, -2, -6, 0, -3, -13, 0, 20,
    -3, 0, -7, -2, 0, -2, -5, 0,
    -3, -9, -6, -4, 0, 0, 0, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, 4, 0, 0, 0, 0, -27,
    -4, -3, -13, -16, 0, 0, -21, 0,
    0, 0, 0, 0, 0, 16, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, 0, -10, -4, -3, 10, -3, -3,
    -13, 1, -2, 1, -2, -9, 1, 7,
    1, 3, 1, 3, -8, -13, -4, 0,
    -12, -6, -9, -13, -12, 0, -5, -6,
    -4, -4, -3, -2, -4, -2, 0, -2,
    -1, 5, 0, 5, -2, 0, 10, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, -3, -3, 0, 0,
    -9, 0, -2, 0, -5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -19, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, -3, 0, -4,
    0, 0, 0, 0, -3, 0, 0, -5,
    -3, 3, 0, -5, -6, -2, 0, -9,
    -2, -7, -2, -4, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -21, 0, 10, 0, 0, -6, 0,
    0, 0, 0, -4, 0, -3, 0, 0,
    -2, 0, 0, -2, 0, -7, 0, 0,
    13, -4, -11, -10, 2, 4, 4, -1,
    -9, 2, 5, 2, 10, 2, 11, -2,
    -9, 0, 0, -13, 0, 0, -10, -9,
    0, 0, -6, 0, -4, -5, 0, -5,
    0, -5, 0, -2, 5, 0, -3, -10,
    -3, 12, 0, 0, -3, 0, -6, 0,
    0, 4, -7, 0, 3, -3, 3, 0,
    0, -11, 0, -2, -1, 0, -3, 4,
    -3, 0, 0, 0, -13, -4, -7, 0,
    -10, 0, 0, -15, 0, 12, -3, 0,
    -6, 0, 2, 0, -3, 0, -3, -10,
    0, -3, 3, 0, 0, 0, 0, -2,
    0, 0, 3, -4, 1, 0, 0, -4,
    -2, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -20, 0, 7, 0,
    0, -3, 0, 0, 0, 0, 1, 0,
    -3, -3, 0, 0, 0, 6, 0, 7,
    0, 0, 0, 0, 0, -20, -18, 1,
    14, 10, 5, -13, 2, 13, 0, 12,
    0, 6, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 17, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};

/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 61,
    .right_class_cnt     = 49,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

/*Store all the custom data of the font*/
static lv_font_fmt_txt_dsc_t font_dsc = {
    .glyph_bitmap = gylph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0
};

/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
lv_font_t lv_font_montserrat_20 = {
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 22,          /*The maximum line height required by the font*/
    .base_line = 4,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0)
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};

#endif /*#if LV_FONT_MONTSERRAT_20*/
