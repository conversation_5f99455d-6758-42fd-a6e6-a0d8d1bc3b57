/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> 
 * @Date: 2022-06-23 14:09:55 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-06-23 14:09:55 
 */

#ifndef _TM1640_H
#define _TM1640_H

#include "../sigmastar/mi_gpio.h"
#include "const.h"
 
/*显示控制命令,设置亮度打开显示屏,共8级亮度，也就是设置不同的脉冲宽度*/
#define BRIGHTNESS_LEVEL_1  0x88
#define BRIGHTNESS_LEVEL_2  0x89
#define BRIGHTNESS_LEVEL_3  0x8a
#define BRIGHTNESS_LEVEL_4  0x8b
#define BRIGHTNESS_LEVEL_5  0x8c
#define BRIGHTNESS_LEVEL_6  0x8d
#define BRIGHTNESS_LEVEL_7  0x8e
#define BRIGHTNESS_LEVEL_8  0x8f
#define CLOSE_DISPLAY       0x80   ///*关显示屏
/*数据设置命令*/
#define CMD_DATA_1          0X40 //地址自动加1，普通模式
#define CMD_DATA_2          0X44 //地址固定，普通模式
 
#define CMD_DATA_3          0X48 //地址自动加1，内部测试模式
#define CMD_DATA_4          0X4C //地址固定，内部测试模式
/*地址命令设置 显存地址为0xC0-0xCF,其对应关系参考手册第6页*/
#define START_ADDRESS_1       0XC0 //第一个数码管
#define START_ADDRESS_2       0XC1 
#define START_ADDRESS_3       0XC2 
#define START_ADDRESS_4       0XC3 
#define START_ADDRESS_5       0XC4 
#define START_ADDRESS_6       0XC5 
#define START_ADDRESS_7       0XC6 
#define START_ADDRESS_8       0XC7 
#define START_ADDRESS_9       0XC8 
#define START_ADDRESS_10      0XC9 
#define START_ADDRESS_11      0XCa 
#define START_ADDRESS_12      0XCb 
#define START_ADDRESS_13      0XCc 
#define START_ADDRESS_14      0XCd 
#define START_ADDRESS_15      0XCe 
#define START_ADDRESS_16      0XCf 

void TM1640_Init(void);
void TM1640_Display_Time(unsigned char* disData,unsigned int flag,unsigned char disNum);
void TM1640_Display_switch(unsigned char display_on);

#define TM_SCL_H() \
    do { \
        if (IS_MODEL_DECODER) { \
            Set_Gpio_High(PAD_SD_D2); \
        } else if (IS_MODEL_P26_V10) { \
            Set_Gpio_High(PAD_KEY7); \
        } \
    } while(0)

#define TM_SCL_L() \
    do { \
        if (IS_MODEL_DECODER) { \
            Set_Gpio_Low(PAD_SD_D2); \
        } else if (IS_MODEL_P26_V10) { \
            Set_Gpio_Low(PAD_KEY7); \
        } \
    } while(0)

#define TM_SDA_H() \
    do { \
        if (IS_MODEL_DECODER) { \
            Set_Gpio_High(PAD_SD_D3); \
        } else if (IS_MODEL_P26_V10) { \
            Set_Gpio_High(PAD_KEY6); \
        } \
    } while(0)

#define TM_SDA_L() \
    do { \
        if (IS_MODEL_DECODER) { \
            Set_Gpio_Low(PAD_SD_D3); \
        } else if (IS_MODEL_P26_V10) { \
            Set_Gpio_Low(PAD_KEY6); \
        } \
    } while(0)

#endif
