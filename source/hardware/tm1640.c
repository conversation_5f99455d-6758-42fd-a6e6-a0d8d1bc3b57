#include <stdint.h>
#include <stdio.h>
#include <unistd.h>
#include "const.h"
#include "tm1640.h"

#if SUPPORT_TM1640_DISPLAY


void delay_100ns(void) {
    usleep(1);
}
void delay_1us(void) {
    usleep(1);
}

/********************Start函数*************************/
static void I2CStart()
{
  TM_SDA_H();  //数据建立和保持时间最小时间为100ns，而33.1776M晶振执行一条指令赋值操作大约360ns，故无需延时
  TM_SCL_H();  //时钟脉冲宽度为400ns
  delay_1us();
  TM_SDA_L();
  delay_1us();
}
/********************Stop函数*************************/
static void I2CStop()
{
	TM_SCL_L();
    delay_100ns();
	TM_SDA_L();
    delay_1us();
    TM_SCL_H();
    delay_100ns();
	TM_SDA_H();
    delay_1us();
}
 
/***************发送8bit数据，从低位开始**************/
static void I2CWritebyte(unsigned char Byte)
{
  unsigned char i;
  for(i=0;i<8;i++)
  {
    TM_SCL_L();
    delay_100ns();
    unsigned char byte_val=(Byte>>i&0x01) ? 1:0;
    if(byte_val)
    {
	  TM_SDA_H();
    }
    else
    {
      TM_SDA_L();
    }
    delay_1us();
    TM_SCL_H();
    delay_1us();
  }
  TM_SCL_L();
  delay_100ns();
  TM_SDA_L();
  delay_1us();
}


/*清屏指令，传递参数确定要清除的位数,最大16位*/
static void Clear_screen(unsigned char num){
  while(num--)
  	I2CWritebyte(0x00); 
}
 
void TM1640_Init(void)
{
    printf("TM1640_Init...\n");

    TM1640_Display_switch(0);

    I2CStart();
    /*上电清零，防止显示乱码,根据硬件使用的位数确定*/
    I2CWritebyte(CMD_DATA_1);	      //数据命令设置：普通模式，地址自动加一
    I2CStop();

    I2CStart();
    I2CWritebyte(START_ADDRESS_1);  //显示设置起始地址
    Clear_screen(16);
    I2CStop();


#if 0
    //unsigned char D[8]={0,1,2,3,4,5,6,7};
    unsigned char D[4]={1,6,3,8};
    TM1640_Display_Time(D,0xff,4);

    I2CStart();
    I2CWritebyte(BRIGHTNESS_LEVEL_3);	//显示控制：显示开，脉冲宽度设为11/16 亮度
    I2CStop();
#endif
}
 
unsigned char seg8code[]={//显示段码 数码管字跟 
0x3f,	//    0 
0x06,	//    1
0x5b,	//    2
0x4f,	//    3
0x66,	//    4
0x6d,	//    5
0x7d,	//    6
0x07,	//    7
0x7f,	//    8
0x6f,	//    9 0110 0111
0x77,	// A	10  
0x7c,	// b	11
0x39,	// C	12
0x5e,	// d	13
0x79, // E  14
0x71, // F  15
0x67, // g  16
0x76, // H  17	
0x73,	// P	18
0x40,	// -	19
0x00,	//    20 息屏
0xc0, // -. 21  
};
 
/*
函数名：TM1640_Display
功  能：从第一个地址开始显示数据，显示的数量为disNum，disData的长度和它对应，flag确定相应位是否显示小数点
输  入：
输  出：无
*/
void TM1640_Display_Time(unsigned char* disData,unsigned int flag,unsigned char disNum){
    unsigned char i;
  	I2CStart();
    I2CWritebyte(START_ADDRESS_1);  //显示设置起始地址
     for(i=0;i<disNum;i++){
       (flag>>i &0x01) ? I2CWritebyte(seg8code[disData[i]]|0x80):I2CWritebyte(seg8code[disData[i]]);                  
     }
	I2CStop();
}


void TM1640_Display_switch(unsigned char display_on){
  	I2CStart();
    I2CWritebyte(display_on?BRIGHTNESS_LEVEL_3:CLOSE_DISPLAY);	//显示控制：显示关
    I2CStop();
}


#endif

