#include "file.h"
#include <stdio.h>
#include <unistd.h>



/**
 * [ReadFileInfo read file ]
 * @param  fileName [description]
 * @param  buf      [description]
 * @param  size     [description]
 * @return          [description]
 */
int ReadFileInfo(const char * fileName,char *buf,int size)
{
	FILE *fp; 
	if ((fp = fopen(fileName, "r")) == NULL)
	{
		perror("fopen");
		return -1;
	}
	int len =  fread( buf, size, 1, fp) ;
	if(len < 0 )
	{
		fclose(fp);
		perror("fread");
		return -1;
	}
	fclose(fp);
	return 0;
}


int WriteFileInfo(const char * fileName,const char *buf,int size)
{
	FILE *fp; 
	if ((fp = fopen(fileName, "w+")) == NULL)
	{
		perror("fopen");
		return -1;
	}
	int len =  fwrite( buf, size, 1, fp) ;
	if(len < 0 )
	{
		fclose(fp);
		perror("fwrite");
		return -1;
	}
	fflush(fp);
	fsync(fileno(fp));	//等待同步文件到磁盘
	fclose(fp);
	return 0;
}

int removeFile(const char * file)
{
	if(file == NULL){
		return -1;
	}
	if( remove(file) == 0 ){		
        printf("\n  ******Removed %s.\n", file);
        return 0;
	}
	perror("remove");
	return -1;
    
}


int renameFile(const char *oldpath, const char *newpath)
{
	if(oldpath == NULL || newpath ==NULL){
		return -1;
	}	
	if( rename(oldpath, newpath) == 0 ){		
        printf("\n  ******renameFile %s to %s\n", oldpath, newpath);
        return 0;
	}
	perror("renameFile");
	return -1;    
}