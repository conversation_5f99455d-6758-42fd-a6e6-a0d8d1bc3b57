#ifndef __FILE_H__
#define __FILE_H__


int ReadFileInfo(const char * fileName,char *buf,int size);
int WriteFileInfo(const char * fileName,const char *buf,int size);

int removeFile(const char * file);
int renameFile(const char *oldpath, const char *newpath);
long long getFileSize(const char * filePath);
int CompareDateIsContain(const char* destDate,const char * startDate,const char*endDate);
int CompareTimeIsContain(const char* destTime,const char * startTime,const char*endTime);
int isNewDateCompare(const char* srcDate,const char* destDate);

#endif