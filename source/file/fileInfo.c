#include <unistd.h>
#include <sys/stat.h>
#include "string.h"
#include "file.h"
#include "typedef.h"

long long getFileSize(const char * filePath)
{
	struct stat file_info;
	if(stat(filePath, &file_info) == 0) 
	{
	     return 	file_info.st_size;	     
	}
	return -1;
}


/**
 * [CompareDateIsContain 比较日期是否在指定日期范围内]
 * @param  destDate  [description]
 * @param  startDate [description]
 * @param  endDate   [description]
 * @return           [-1：日期小于开始日期 1：日期大于结束日期 0：在指定范围内]
 */
int CompareDateIsContain(const char* destDate,const char * startDate,const char*endDate)
{

	// destDate: 2018-02-03  startDate:2018-02-06  endDate: 2018-02-06
	if(strcmp(destDate,startDate) < 0){
		return -1; //日期小于开始日期返回-1
	}
	if(strcmp(destDate,endDate) > 0 ){
		return 1; //日期大于结束日期返回1
	}
	//printf("Find Compare Date...%s\n",destDate);
	return 0;
}
/**
 * [CompareTimeIsContain description]
 * @param  destTime  [description]
 * @param  startTime [description]
 * @param  endTime   [description]
 * @return           [-1：时间小于开始时间 1：时间大于结束时间 0：在指定范围内]
 */
int CompareTimeIsContain(const char* destTime,const char * startTime,const char*endTime)
{
	// destTime: 08:25:36  startTime:08:25:36  endTime: 08:25:50
	if(strcmp(destTime,startTime) < 0){
		return -1; //时间小于开始时间返回0
	}
	if(strcmp(destTime,endTime) > 0 ){
		return 1; //时间大于结束时间返回0
	}
	return 0;
}

/**
 * [isNewDateCompare 字符串比较日期是否最新]
 * @param  srcDate  [源字符串]
 * @param  destDate [description]
 * @return          [0:日期相同或者srcDate>=destDate 则原日期最新   1:目标日期最新 ]
 */
int isNewDateCompare(const char* srcDate,const char* destDate)
{
	if(strcmp(srcDate,destDate) >= 0){
		return 0; //
	}
	return 1;
}
