#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <semaphore.h>
#include "sysconf.h"


// 获取音频采集音源的channelID
int  GetAudioCollectorChannelBySrc(int src)
{
    int channelId=0;
    if(SOURCE_AUDIO_COLLECTOR_BASE <= src && src<= SOURCE_AUDIO_COLLECTOR_MAX)
    {
        channelId = (src-SOURCE_AUDIO_COLLECTOR_BASE)%4+1;
    }
    else
    {
        channelId=0;
    }
    return channelId;
}

#if(IS_DEVICE_AUDIO_COLLECTOR)

/*******音频采集器设备相关变量************/
int g_device_collector_channel_map[4]={COLLECTOR_CHANNEL_1,COLLECTOR_CHANNEL_2,COLLECTOR_CHANNEL_3,COLLECTOR_CHANNEL_4};
st_audio_collector_info audioCollector_info;
/*******音频采集器设备相关变量************/


static _stAudioCollectorData stAudioCollectorData;
static pthread_mutex_t mutex_DataPkg=PTHREAD_MUTEX_INITIALIZER;	//采集音频包锁
static sem_t sem_DataPkgRecv;  	//信号量-接收到音频采集包

// 256/32000 = 8ms,pkg size = 256*2*1 = 512字节，如果是写入ao，那么数据量为256*2*2 = 1024字节；
// 每收到两个包，开始发送？因为终端处理那边原来是1024字节；
// 新版本如何处理?如果按照新的规则，那么旧的终端将不支持，这样不太好。
/*********************************************************************
 * @fn      audioCollector_Data_Add
 *
 * @brief   音频采集数据加入
 *
 * @param   void
 *
 * @return	void
 */
void audioCollector_Data_Add(signed short int **AllChannelBuf,unsigned int SingleChannelLen)
{
    int i=0;
	if(SingleChannelLen > MAX_AUDIO_COLLECTOR_DATA_SIZE )
		return;
	pthread_mutex_lock(&mutex_DataPkg);
    
    for(i=0;i<MAX_AUDIO_COLLECTOR_CHANNEL_NUM;i++)
    {
	    memcpy(stAudioCollectorData.stAudioCollectorBuf[stAudioCollectorData.write_pos].buf+i,AllChannelBuf[i],SingleChannelLen);
    }
	stAudioCollectorData.stAudioCollectorBuf[stAudioCollectorData.write_pos].len = SingleChannelLen;

	//printf("audioCollector_Data_Add:len=%d\n",SingleChannelLen);
	stAudioCollectorData.write_pos++;
	if(stAudioCollectorData.write_pos >= MAX_AUDIO_COLLECTOR_PKG_NUM )
	{
		stAudioCollectorData.write_pos = 0;
	}
	sem_post(&sem_DataPkgRecv);
	pthread_mutex_unlock(&mutex_DataPkg);
}


/*********************************************************************
 * @fn      audioCollector_data_process
 *
 * @brief   音频采集数据处理
 *
 * @param   void
 *
 * @return	void
 */
void *audioCollector_data_process(void)
{
    unsigned int PkgCnt=0;
	unsigned char sendDataBuf[MAX_AUDIO_COLLECTOR_CHANNEL_NUM][MAX_AUDIO_COLLECTOR_DATA_SIZE];
	while(1)
	{
		sem_wait(&sem_DataPkgRecv);
		int singleBufLen=stAudioCollectorData.stAudioCollectorBuf[stAudioCollectorData.read_pos].len;
		for(int i=0;i<MAX_AUDIO_COLLECTOR_CHANNEL_NUM;i++)
		{
			int bufPos=0;
			if(PkgCnt%2 == 0)
			{
				bufPos = 0;
			}
			else
			{
				bufPos = singleBufLen;
			}
			memcpy(sendDataBuf[i]+bufPos,stAudioCollectorData.stAudioCollectorBuf[stAudioCollectorData.read_pos].buf[i],singleBufLen);

			if(PkgCnt%2)
			{
				//如果采集器启用了自动触发，那么当触发通道有效时才发送音频流，不影响其他通道数据。
				int canSend=0;
				if(i == audioCollector_info.m_nTriggerChannelId-1)
				{
					if(audioCollector_info.m_nTriggerSwitch)
					{
						if(audioCollector_info.m_nSignalValid)
						{
							canSend=1;
						}
					}
					else if(audioCollector_info.m_nSelectedStatus & g_device_collector_channel_map[i])
					{
						canSend=1;
					}
				}
				else if(audioCollector_info.m_nSelectedStatus & g_device_collector_channel_map[i])
				{
					canSend=1;
				}

				if(canSend)
				{
					SendAudioCollectorStreamToMultiCast(sendDataBuf[i],singleBufLen*2,i+1);
					SendAudioCollectorStreamToServer(sendDataBuf[i],singleBufLen*2,i+1);
				}
			}
		}
		
		PkgCnt++;

		memset(&stAudioCollectorData.stAudioCollectorBuf[stAudioCollectorData.read_pos], 0, sizeof(_stAudioCollectorBuf));

		stAudioCollectorData.read_pos++;
		if(stAudioCollectorData.read_pos >= MAX_AUDIO_COLLECTOR_PKG_NUM)
		{
			stAudioCollectorData.read_pos = 0;
		}

	}
	pthread_exit(NULL);
}

/*********************************************************************
 * @fn      start_audioCollector_data_process_pthread
 *
 * @brief   启动音频采集数据处理线程
 *
 * @param   void
 *
 * @return	void
 */
void start_audioCollector_data_process_pthread(void)
{
	sem_init(&sem_DataPkgRecv,0,0);

	int ret = -1;
	pthread_t Mcast_Pthread;
	pthread_attr_t Pthread_Attr;

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Mcast_Pthread, &Pthread_Attr, (void *)audioCollector_data_process, NULL);
	if (ret < 0)
	{
		printf("start_audioCollector_data_process_pthread create failed!!!\n");
	}
	else
	{
		printf("start_audioCollector_data_process_pthread create success!\n");
	}
	pthread_attr_destroy(&Pthread_Attr);
}





/*********************************************************************
 * @fn      Audio_Collector_CheckStatus
 *
 * @brief  	音频采集器检测状态任务
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void* Audio_Collector_CheckStatus(void *p_arg)
{
  	int i=0;
	int close_count=0;
	while(1)
	{
		if(!audioCollector_info.m_nSelectedStatus)
		{
			if(++close_count == 10)	//10s
			{
				printf("Audio_Collector_CheckStatus,close...\r\n");

				set_system_source(SOURCE_NULL);
				pkg_query_current_status(NULL);
			}
		}
		else
		{
			close_count=0;
		}
		
		sleep(1);
	}
}

void Audio_Collector_CheckStatus_Thread()
{
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Audio_Collector_CheckStatus, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}


#endif
