#ifndef _AUDIO_COLLECTOR_PROCESS_H_
#define _AUDIO_COLLECTOR_PROCESS_H_

#define AUDIO_COLLECTOR_SAMPLERATE 32000

#define MAX_AUDIO_COLLECTOR_PKG_NUM    4
#define MAX_AUDIO_COLLECTOR_CHANNEL_NUM 4
#define MAX_AUDIO_COLLECTOR_DATA_SIZE   1024

/**************音频采集器通道定义*******************************/
#define COLLECTOR_CHANNEL_1			0x01
#define COLLECTOR_CHANNEL_2			0x02
#define COLLECTOR_CHANNEL_3			0x04
#define COLLECTOR_CHANNEL_4			0x08
#define COLLECTOR_CHANNEL_ALL       0xFF
/***********************************************************/


/*******音频采集器设备相关变量************/
extern int g_device_collector_channel_map[4];

typedef struct {
    int m_nChannelStatus;                       //音频采集器各通道信号
    int m_nSignalValid;		                    //音频采集器信号是否有效（m_nTriggerSwitch=1且对应channel的status=1，其他情况务必置为0）
    //int g_device_collector_isReady;			//采集器准备就绪,初始化I2S时需要先置为0
    int m_nSelectedStatus;		                //该音频采集器被分区选择的状态(连续10分钟无分区选择采集音源，则将音频采集器的音源置为空闲)
    int m_nTransPort[4];		//音频采集器的传输端口

    unsigned char m_nTriggerSwitch;             //触发开关
    unsigned char m_nTriggerChannelId;          //触发通道ID(1~4)
    unsigned char m_nTriggerZoneVolume;         //触发分区音量
}st_audio_collector_info;
extern st_audio_collector_info audioCollector_info;

/*******音频采集器设备相关变量************/

typedef struct
{
    //unsigned char isValid;  //是否有效
    unsigned char buf[MAX_AUDIO_COLLECTOR_CHANNEL_NUM][MAX_AUDIO_COLLECTOR_DATA_SIZE];   //缓存大小
    int len;
}_stAudioCollectorBuf;

typedef struct
{
    int write_pos;
    int read_pos;
    _stAudioCollectorBuf stAudioCollectorBuf[MAX_AUDIO_COLLECTOR_PKG_NUM];
}_stAudioCollectorData;

void audioCollector_Data_Add(signed short int **AllChannelBuf,unsigned int SingleChannelLen);
void start_audioCollector_data_process_pthread(void);

// 获取音频采集音源的channelID
int  GetAudioCollectorChannelBySrc(int src);

void Audio_Collector_CheckStatus_Thread();

#endif