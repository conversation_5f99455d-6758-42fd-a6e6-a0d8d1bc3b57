
#ifndef __LOGGING__H_
#define __LOGGING__H_

#include "typedef.h"
#include <stdlib.h>


//#define IFNAME 				"eth0"	//网卡
//#define IFNAME_PC			"eth1"


#define MAX_RECOED_BUF_SIZE 	1024	//主机获取记录文件时上传的最大缓存数据
#define RECODE_QUERY_PORT 	6060

#define FILE_NAME_MAX 		128
#define RECORD_FILE_PATH 	"/customer/App/OperationRecord/"
#define RECORD_FILE_SIZE_MAX (1024*1024) 	//记录的文件最大为1M	
#define RECORD_DAY_MAX 		3  //

#define MERGE_FILE_PREFIX 	"Merge_Record_Play-"
#define RECORD_FILE_PREFIX 	"Record-"
#define RECORD_FILE_LOG_PREFIX 	"Record-log-" 	//log文件
#define RECORD_FILE_ERROR  "Record-error-log-"

#define RECORD_WRITE_WAIT_QUEUE_MAX 	1


#define SUCCEED		0
#define ERROR	    -1

#define PTHREAD_LOCK(MU) pthread_mutex_lock(&MU)
#define PTHREAD_UNLOCK(MU) pthread_mutex_unlock(&MU)
#define PTHREAD_INIT(MU) pthread_mutex_init(&MU, NULL)
#define MY_SYSTEM(...) {char _bf[1024] = {0}; snprintf(_bf, sizeof(_bf)-1, __VA_ARGS__); system(_bf);}


int IsWriteCheck();
void Creat_Date_File_Name(char *filename,int sWrite_Log_Flag);

/*Systemlog  [向文件写入系统日志]
 * @param    _log [写入日志的内容]
 
 * @return  
 */
void System_log(const char* prefix,const char * info, char*file ,int line);//系统日志

int Del_Record_Sort_File(void);


/*TcpSendRecordFileContent[向主机发送日志内容]
 * @param    ProtocolDataBuf [协议包内容]
   @param    sendBuf [发送给主机的协议包内容]
   @param    ProtocolLen[数据包长度]
 * @return 
*/
int TcpSendRecordFileContent(unsigned char *ProtocolDataBuf,int *ProtocolLen,unsigned char * sendBuf);


/*TCPSendRecordFileList[向主机发送日志列表]
 * @param    DataBuf [协议包内容]
   @param    Datalen[数据包长度]
 * @return 
*/
int TCPSendRecordFileList(unsigned char *ProtocolDataBuf,int *ProtocolLen);


#endif

