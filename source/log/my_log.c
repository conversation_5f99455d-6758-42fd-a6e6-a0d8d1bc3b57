/********************************************************************************
  * @file		*
  * <AUTHOR>
  * @version 	*
  * @date    	*
  * @brief   	*
  *          
  *          
  ******************************************************************************
  * @attention 
  * 保存播放记录的文件格式：//日期,时间,AUX,节目名称,音量,
  *
  *
  *
  * 
*******************************************************************************/
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <stdio.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <stdlib.h>
#include <linux/types.h>
//#include <linux/videodev.h>
#include <string.h>
#include <signal.h>
#include <errno.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <net/if.h>
#include <pthread.h>
#include <dirent.h>
#include <ctype.h>
//#include "protocol.h"
#include "my_log.h"
#include "debug.h"
#include "file.h"



//记录文件
#define CMD_HOST_QUERY_RECORD_LIST 		0x0033		//主机获取设备记录文件列表
#define CMD_SEND_RECORD_FILE_CONTENT	0x0034		//终端向主机发送记录文件内容

typedef struct stRecord
{
	//日期,时间,AUX,节目名称,音量,
	char date[8];//20 16-10-26
	char time[8];//14:03:14
	char sourceName[10];
	char programName[128];
	char volStr[2];
}stRecord;

typedef struct Record_file
{
	char *str;
	unsigned int len;
	unsigned long fileSize;
}Record_file;



/*****************    本地常量声明    ******************/

static unsigned char WaitWriteCount=0;
pthread_mutex_t pmWriteFile = PTHREAD_MUTEX_INITIALIZER;	//文件写入锁
static unsigned char sWrite_Log_Flag = 0;
static unsigned char Current_Update_FilePath[128]={0};




//char Current_Update_FileName[30]={0};
 FILE * fpCurrent_Update_File=NULL;//当前正在打开上传的文件描述符
 int Update_File_fseek=0;		//文件上传文件的偏移位置
 unsigned long  Update_File_Size=0;
 int  Update_File_Pkg_id = 0;		//当前上传包id
 int  Update_File_Pkg_Total_id = 0;//总id
char * sourceNameBuf[5]={0};


int IsWriteCheck()
{
	if(!WaitWriteCount)
	{
		return 1;
	}
	return 0;
}


void Get_Local_date(char *buf,int len)
{
	time_t rawtime; 
	struct tm * timeinfo; 
	time ( &rawtime ); 
	timeinfo = localtime ( &rawtime ); 
	strftime( buf, len,"%Y-%m-%d",timeinfo );
//	DBG("Date:%s\n",buf);
}
void Get_Local_time(char *buf,int len)
{
	time_t rawtime; 
	time ( &rawtime ); 
	strftime( buf, len, "%X",localtime(&rawtime));
//	DBG("time:%s\n",buf);
}
/*********************************************************************
 * @function:   
 *
 * @brief   :	
 *
 * @arguments:  	
 *       		
 *
 * @return  :
 *					 
 *********************************************************************/ 
static unsigned long Get_File_Size(const char *path)  
{  
	unsigned long filesize = -1;      
	struct stat statbuff;  
	if(stat(path, &statbuff) >= 0)  
	{  
	   filesize = statbuff.st_size;
	} 
	printf("file:%s Size:%ld\n",path,filesize);
	return filesize;  
}

int Find_Dir_File_Exist(const char * dir,const char *filename)
{
	struct dirent *entry;
	DIR *dpDir;

	dpDir =opendir(dir); //打开目录
	if(dpDir == NULL)
	{
		printf("opendir dir error!!!\n");
		return -1;
	}  
	while((entry = readdir(dpDir)) != NULL)
	{
		//过滤隐藏文件
		if(strcmp(".",entry->d_name)==0 || strcmp("..",entry->d_name)==0)   //比较字符串
			continue;
		if(strcmp(filename,entry->d_name)==0) //查找文件名
		{
			closedir(dpDir);
			return 1;
		}
	}
	closedir(dpDir);
	return 0;
}

/*********************************************************************
 * @function:  Del_Dir_File_By_Include_Str
 *
 * @brief   :	删除目录下包含指定字符串的文件名
 *
 * @arguments:  dir：目录 NameStr :需对比的字符串	
 *       		
 *
 * @return  : 返回删除的文件个数 错误返回-1
 *					 
 *********************************************************************/ 

int Del_Dir_File_By_Include_Str(const char * dir,const char *NameStr)
{
	struct dirent *entry;
	DIR *dpDir;
	int count=0;
	dpDir =opendir(dir); //打开目录
	if(dpDir == NULL)
	{
		printf("opendir dir error!!!\n");
		return -1;
	} 
	while((entry = readdir(dpDir)) != NULL)
	{
		//过滤隐藏文件
		if(strcmp(".",entry->d_name)==0 || strcmp("..",entry->d_name)==0)   //比较字符串
			continue;
		if(strstr(entry->d_name,NameStr) != NULL)//文件名包含有对比的字符串
		{
			printf("\nDelet file:%s%s\n",dir,entry->d_name);
			count++;
			MY_SYSTEM("rm %s%s",dir,entry->d_name);
		}
	}
	closedir(dpDir);
	return count;
}

/**
 * [Creat_Date_File_Name 自动创建文件名]
 * @param filename [description]
 */
void Creat_Date_File_Name(char *filename,int sWrite_Log_Flag)
{
	char buf[FILE_NAME_MAX] ={0};
	Get_Local_date(buf,FILE_NAME_MAX);
	if(sWrite_Log_Flag==1)
	{
		sWrite_Log_Flag = 0;
		sprintf(filename,"%s%slog-%s.txt",RECORD_FILE_PATH,RECORD_FILE_PREFIX,buf);
	}
	else if(sWrite_Log_Flag==2)//错误日志
	{
		sWrite_Log_Flag = 0;
		sprintf(filename,"%s%s%s.txt",RECORD_FILE_PATH,RECORD_FILE_ERROR,buf);
	}

	else
	{
		sprintf(filename,"%s%s%s.txt",RECORD_FILE_PATH,RECORD_FILE_PREFIX,buf);	
	}
	
}




/**
 * [WriteRecordInfo 向文件写入操作信息]
 * @param source      [音源]
 * @param programName [媒体播放名字，可为空]
 * @param vol         [音量]
 */
void WriteRecordInfo(char *PrioritySourceName,char *programName,char vol)
{

	FILE *fp=NULL;
	static unsigned int writeCount=0; //记录文件写入条数
	static char filePath[128]={0};
	char path[FILE_NAME_MAX]={0};
	
//	return;

	PTHREAD_LOCK(pmWriteFile);
	Creat_Date_File_Name(path,0);

#if 1
	if(writeCount==0||strcmp(path,filePath))	//首次读取最后一条记录计数
	{
		Del_Record_Sort_File();//日期改变需要检查是否已经超出文件记录总数
		strcpy(filePath,path);
		char cmd[128]={0};
		char readBuf[128]={0};
		sprintf(cmd,"tail -n 1 %s|cut -d . -f1",path);
		printf("cmd>>%s\n", cmd);
		fp=popen(cmd,"r");
		if(fp==NULL)
		{
			writeCount = 0;
			perror("open tail cmd");
		}else
		{			
			if(fgets(readBuf, sizeof(readBuf), fp) <= 0)
			{
				writeCount = 0;
				perror("fgets fp");
			}else
			{
				int i=0;
				for (i = 0; i < strlen(readBuf); ++i)
				{
					if(!isdigit(readBuf[i]))
						break;
				}
				if(i == strlen(readBuf))	//所有字符为数字
				{
					writeCount =atoi(readBuf);
					if(writeCount<0)
					{
						writeCount = 0;
					}
				}else{
					writeCount = 0;
				}				
				printf("最后一条记录计数:%d\n",writeCount);
			}			
			pclose(fp);
		}
	}
#endif

//	printf ( "/007The current date/time is: %s", asctime (timeinfo) ); 	
	fp = fopen(path,"at");
	if(fp == NULL)
	{		
		perror("open file");
		MY_SYSTEM("mkdir -p %s",RECORD_FILE_PATH);//判断 erron 创建目录
		fp = fopen(path,"at ");
		if(fp == NULL)
		{
			perror("open file");
			PTHREAD_UNLOCK(pmWriteFile);
			return ;
		}
	}
	if(Get_File_Size(path)> RECORD_FILE_SIZE_MAX)
	{
		printf("File:%s Over Size!\n",path);
		fclose(fp);
		PTHREAD_UNLOCK(pmWriteFile);
		return;
	}

	
	char buf[128] = {0};
//	char date_tmp[30]={0};
	char time_tmp[30]={0};
//	Get_Local_date(date_tmp,sizeof(date_tmp));
	Get_Local_time(time_tmp,sizeof(time_tmp));
	////日期,时间,AUX,节目名称,音量,
//	printf("date_tmp = %s\n", date_tmp);
	//if(source > PRIORITY_MAX_NUM)
	//{
		//source= 0;
	//}
	int len=0;
	/*
	if(programName==NULL)
	{
		len = sprintf(buf,"%d.%s,%s,%s,,%d\r\n",++writeCount,date_tmp,time_tmp,PrioritySourceName[source],vol);
	}else
	{
		len = sprintf(buf,"%d.%s,%s,%s,%s,%d\r\n",++writeCount,date_tmp,time_tmp,PrioritySourceName[source],programName,vol);
	}
	*/

	if(programName==NULL)
	{
		len = sprintf(buf,"%s,%s,%d\r\n",time_tmp,PrioritySourceName,vol);
	}else
	{
		len = sprintf(buf,"%s,%s,%s,%d\r\n",time_tmp,PrioritySourceName,programName,vol);
	
	}

	if(fwrite(buf, len,1, fp) < 0)
	{
		DBG("write %s Failed!\n",path);
		perror("write file");
	} 
	fclose(fp);
	PTHREAD_UNLOCK(pmWriteFile);
}

/**
 * [GetRecordFileList 获取记录文件列表]
 * @param  list [保存列表的结构体]
 * @return      [获取个数]
 */
int GetRecordFileList(Record_file * list)
{
	int Record_Count=0;
	char path[128]={0};	
	struct dirent **namelist;
	int n;
	n = scandir(RECORD_FILE_PATH, &namelist, NULL, alphasort);
	if (n < 0)
		perror("scandir");
	else {
		while (n--) {
			//过滤隐藏文件
			if(strcmp(".",namelist[n]->d_name)==0 || strcmp("..",namelist[n]->d_name)==0)   //比较字符串
				continue;
			if(strstr(namelist[n]->d_name,RECORD_FILE_PREFIX)!=NULL) //查找文件名
			{
				Record_Count++;
				if(Record_Count>RECORD_DAY_MAX)
				{
					printf("\nFile List Over %d.\n",RECORD_DAY_MAX );
					break;
				}
			//	DBG("scandir file name:%s\n", namelist[n]->d_name);
				//strcpy((*list)[pos++],entry->d_name);	//拷贝文件名				
				strcpy(list->str,namelist[n]->d_name);
				list->len = strlen(namelist[n]->d_name);
				memset(path,0,128);
				sprintf(path,"%s%s",RECORD_FILE_PATH,namelist[n]->d_name);
				list->fileSize = Get_File_Size(path);
				list++;
				
			}

			free(namelist[n]);
		}
		free(namelist);
	}


	return Record_Count;
}


int Get_Record_Count(const char* filePrefix)
{
	struct dirent *entry;
	DIR *dpDir;
	int Record_Count=0;
	int ret= -1;
	dpDir =opendir(RECORD_FILE_PATH); //打开目录
	if(dpDir == NULL)
	{
		printf("opendir %s error!!!\n",RECORD_FILE_PATH);
		return ret;
	}  
	while((entry = readdir(dpDir)) != NULL)
	{
		//过滤隐藏文件
		if(strcmp(".",entry->d_name)==0 || strcmp("..",entry->d_name)==0)   //比较字符串
			continue;
		if(strstr(entry->d_name,filePrefix)!=NULL) //查找文件名
		{
			Record_Count++;
		}
	}
	closedir(dpDir);
	return Record_Count;
}


int DeleteRedundantFile(const char * path,const char* filePrefix,int fileDeleteMax)
{
	FILE * fp;
	char buf[128]={0};
	char openPath[128]={0};
	char *pbuf;
	int del_count=0;
	sprintf(openPath,"find %s%s*|sort",path,filePrefix);
	printf("popen:%s\n",openPath);
	fp = popen(openPath,"r");
	if (fp == NULL)
	{
		perror("find|sort:popen ERROR!");
		return ERROR;
	}
	DBG("Delet...\n");
	//删除最前的多除文件
	while(!feof(fp))
	{
		memset(buf,0,sizeof(buf));
		if(fgets(buf,128,fp) != NULL)
		{
			if(strstr(buf,filePrefix) != NULL)//文件名包含有对比的字符串
			{
				pbuf = buf;
				while(*pbuf != '\n')
				{
					*pbuf++;
				}
				*(pbuf) = '\0';
				//DBG("Read File %s\n",buf);
				MY_SYSTEM("rm -rf %s",buf);
				del_count++;
				if(del_count >= fileDeleteMax)
				{
					break;
				}
			}
		}else
		{	
			DBG("fgets Failed!\n");
			break;
		}
	}
	DBG("Del %d File!\n",del_count);
	pclose(fp);
	return del_count;
}

/**
 * [Del_Record_Sort_File 删除设定天数外的文件]
 * @return [description]
 */
int Del_Record_Sort_File()
{	
	char filePrefix[128]={0};	
	int count=0;
/*
	sprintf(filePrefix,"%s2",RECORD_FILE_PREFIX); //得到Record-2*开头的文件个数
	count = Get_Record_Count(filePrefix);	//得到当前目录的普通记录文件个数
	DBG("Finded %d Record file!\n",count);
	if(count > (RECORD_DAY_MAX/2) ){
		//需要删除旧的文件
		printf("需要删除旧的普通记录文件(%d)\n",count - (RECORD_DAY_MAX/2) );
		//strcat(filePrefix,"*");//匹配符
		DeleteRedundantFile(RECORD_FILE_PATH,filePrefix,count - (RECORD_DAY_MAX/2) );
	}
*/
	memset(filePrefix,0,128);	
	count = Get_Record_Count(RECORD_FILE_LOG_PREFIX);	//得到当前目录的log记录文件个数
	DBG("Finded %d log Record file!\n",count);
	if(count > (RECORD_DAY_MAX) ){
		//需要删除旧的文件
		printf("需要删除旧的log记录文件(%d)\n",count - (RECORD_DAY_MAX) );		
		DeleteRedundantFile(RECORD_FILE_PATH,RECORD_FILE_LOG_PREFIX,count - (RECORD_DAY_MAX) );
	}
	return 1;
}



//以下通讯函数


void Out_array(unsigned char *buf,unsigned short len)
{
#ifdef DEBUG
	int i=0;
	printf("\nbuff:");
	for (i = 0; i <len ; ++i)
	{
		printf("%x ",buf[i]);
	}
	printf("\n");
#endif

}



void CleanUpdateFileInfo()
{
	memset(Current_Update_FilePath,0,sizeof(Current_Update_FilePath));
	
	fpCurrent_Update_File=NULL;//当前正在打开上传的文件描述符
	Update_File_fseek=0;		//文件上传文件的偏移位置
	Update_File_Size=0;
	Update_File_Pkg_id = 0;
	Update_File_Pkg_Total_id=0;
}




void System_log(const char* prefix,const char * info, char*file ,int line)
{
	return;
	//1.[日期] DEBUG file.c:200 :info

	FILE *fp=NULL;
	static unsigned int writeCount=0; //记录文件写入条数
	static char filePath[128]={0};
	char path[FILE_NAME_MAX]={0};

	PTHREAD_LOCK(pmWriteFile);
	sWrite_Log_Flag = 1;
	Creat_Date_File_Name(path,1);
#if 1
	
	if(RECORD_FILE_SIZE_MAX-getFileSize(path)<=50||fpCurrent_Update_File!=NULL)
	{
		
		//日志文件满
		PTHREAD_UNLOCK(pmWriteFile);
		return;
	}

	if(writeCount==0||strcmp(path,filePath))	//首次读取最后一条记录计数
	{
		Del_Record_Sort_File();//日期改变需要检查是否已经超出文件记录总数
		strcpy(filePath,path);
		char cmd[128]={0};
		char readBuf[128]={0};
		sprintf(cmd,"tail -n 1 %s|cut -d . -f1",path);
		printf("cmd>>%s\n", cmd);
		fp=popen(cmd,"r");
		if(fp==NULL)
		{
			writeCount = 0;
			perror("open tail cmd");
		}else
		{			
			if(fgets(readBuf, sizeof(readBuf), fp) <= 0)
			{
				writeCount = 0;
				perror("fgets fp");
			}else
			{
				printf("last str:%s\n", readBuf);
				int i=0;
				for (i = 0; i < strlen(readBuf); ++i)
				{
					if(!isdigit(readBuf[i]) && readBuf[i]!= '\n' )
						break;
				}
				if(i == strlen(readBuf))	//所有字符为数字
				{
					writeCount =atoi(readBuf);
					if(writeCount<0)
					{
						printf("writeCount<0\n");
						writeCount = 0;
					}
				}else{
					printf("readBuf not digit!\n");
					writeCount = 0;
				}				
				printf("最后一条记录计数:%d\n",writeCount);
			}			
			pclose(fp);
		}
	}
#endif
//	printf ( "/007The current date/time is: %s", asctime (timeinfo) ); 	
	fp = fopen(path,"at");
	if(fp == NULL)
	{		
		perror("open file");
		MY_SYSTEM("mkdir -p %s",RECORD_FILE_PATH);//判断 erron 创建目录
		fp = fopen(path,"at ");
		if(fp == NULL)
		{
			perror("open file");
			PTHREAD_UNLOCK(pmWriteFile);
			return ;
		}
	}
	if(Get_File_Size(path)> RECORD_FILE_SIZE_MAX)
	{
		printf("File:%s Over Size!\n",path);
		fclose(fp);
		PTHREAD_UNLOCK(pmWriteFile);
		return;
	}
	char buf[1024]={0};
//	char date_tmp[30]={0};
	char time_tmp[30]={0};
//	Get_Local_date(date_tmp,sizeof(date_tmp));
	Get_Local_time(time_tmp,sizeof(time_tmp));
	//int len = sprintf(buf,"%d.[%s] %s %s :%d %s\r\n",++writeCount,time_tmp,prefix,file,line,info);
	int len = sprintf(buf,"%d.[%s %s] %s(%d):%s\r\n",++writeCount,prefix,time_tmp,file,line,info);

	////日期,时间,AUX,节目名称,音量,
	if(fwrite(buf, len,1, fp) < 0)
	{
		DBG("write %s Failed!\n",path);
		perror("write file");
	}
	fclose(fp);
	PTHREAD_UNLOCK(pmWriteFile);
}


int TCPSendRecordFileList(unsigned char *ProtocolDataBuf,int *ProtocolLen)
{
	Record_file File_List[RECORD_DAY_MAX]={0};
	char listBuf[RECORD_DAY_MAX][128]={0};
	int i = 0;
	for (i=0;i<RECORD_DAY_MAX;++i)
	{
		File_List[i].str=listBuf[i];
		File_List[i].len=0;
		File_List[i].fileSize = 0;
	}
	//unsigned char sendBuf[1500]={0};//MAX_BUF_SIZE
	
	unsigned char * pData=ProtocolDataBuf;
	int Record_Count=0;
	int File_Total_ID=0;//文件总ID
	Del_Record_Sort_File();//先删除多余日期文件.
	Record_Count = GetRecordFileList(File_List);
	if(Record_Count< 0)
	{
		printf("GetRecordFileList failed.\n");
		return 0;
	}
	if(Record_Count > RECORD_DAY_MAX){
		Record_Count = RECORD_DAY_MAX;
	}
	DBG("\nRecord_Count = %d\n",Record_Count);
	*pData++ = Record_Count;
	DBG("File_List[0]:%s\n",File_List[0].str );
	for (i=0; i < Record_Count; ++i) 
	{

		*pData++ = File_List[i].len;
		strcpy((char *)pData,File_List[i].str); //赋值文件名称
		pData += File_List[i].len;
		//加入文件大小
		*pData++ = (File_List[i].fileSize>>24)&0xFF;
		*pData++ = (File_List[i].fileSize>>16)&0xFF;
		*pData++ = (File_List[i].fileSize>>8)&0xFF;
		*pData++ = (File_List[i].fileSize)&0xFF;
		File_Total_ID = File_List[i].fileSize/1024;
		if(File_List[i].fileSize%1024)
			File_Total_ID+=1;

		*pData++ = (File_Total_ID>>8)&0xFF;
		*pData++ = (File_Total_ID)&0xFF;
		
	}

	*ProtocolLen=pData-ProtocolDataBuf;
	return 1;

}


int TcpSendRecordFileContent(unsigned char *ProtocolDataBuf,int *ProtocolLen,unsigned char * sendBuf)
{
	
	//unsigned char recordbuf[1500]={0};
	unsigned char HostQueryFileName[128]={0};
	//unsigned char DataBuf[129*RECORD_DAY_MAX + 1]={0};//文件名长度128 + 1其他数据
	//unsigned char *pData = NULL;
	//unsigned char path[128]={0};
	static char Current_Update_FileName[30]={0};
	int Datalen=0;
	int currentGetFileID=0;//主机获取当前文件的ID包号
	unsigned char *pSendbuf;

	unsigned char * pPkgData = ProtocolDataBuf;

	strncpy((char*)HostQueryFileName,&ProtocolDataBuf[1],ProtocolDataBuf[0]);
	//DBG("HostQueryFileName:%s FileLen:%d\n",HostQueryFileName,*pPkgData++);
	pPkgData+=ProtocolDataBuf[0]; //偏移文件名称长度
	currentGetFileID = ((pPkgData[0]<<8)|pPkgData[1])&0xFFFF; //主机获取的id包号
	//DBG("currentGetFileID:%d\n",currentGetFileID);
	//传输过程中重新请求了其他文件
	if(fpCurrent_Update_File!=NULL && strcmp((const char * )Current_Update_FileName,(const char * )HostQueryFileName) != 0) 
	{
		NOTICE("正在传输，主机请求了其他文件:%s\n",HostQueryFileName);
		fclose(fpCurrent_Update_File);
		CleanUpdateFileInfo();
	}
	if(fpCurrent_Update_File==NULL )//首次传输
	{
		//printf("[%d] %s %d\n",__LINE__,HostQueryFileName,strlen(HostQueryFileName));
		memset(Current_Update_FilePath,0,sizeof(Current_Update_FilePath));
		memset(Current_Update_FileName,0,sizeof(Current_Update_FileName));
		strcpy(( char *)Current_Update_FilePath,( char *)RECORD_FILE_PATH);
		sprintf(Current_Update_FilePath,"%s%s",RECORD_FILE_PATH,HostQueryFileName);
		strcpy((char*)Current_Update_FileName,(char *)HostQueryFileName);//保存文件名称
		fpCurrent_Update_File = fopen(Current_Update_FilePath,"rt");	//只读打开一个文本文件，只允许读数据
		if(fpCurrent_Update_File == NULL)
		{
			CleanUpdateFileInfo();
			perror("fopen:");
			return 0;
		}
	}

	if(Update_File_Size <= 0 )	//获取要发送的文件大小
	{
		Update_File_Size = Get_File_Size(Current_Update_FilePath);
		DBG("Update_File_Size:%d\n",Update_File_Size);
		Update_File_Pkg_Total_id=Update_File_Size/MAX_RECOED_BUF_SIZE;	//包总ID数
		if(Update_File_Size%MAX_RECOED_BUF_SIZE)
		{
			Update_File_Pkg_Total_id+=1;
		}	
	}

	
	if(currentGetFileID <=0|| currentGetFileID > Update_File_Pkg_Total_id)//判断文件ID是否超过该文件最大包数
	{
		//DBG("文件ID超过该文件最大包数、默认发送完成.\n");
		fclose(fpCurrent_Update_File);
		CleanUpdateFileInfo();
		return 0;
	}


	if(fpCurrent_Update_File==NULL)
	{
		//DBG("fpCurrent_Update_File is NULL\n");
	}

	//名称
	pSendbuf = sendBuf;	
	//名称	
	*pSendbuf++ = strlen(Current_Update_FileName);//文件名称长度
	strcpy(pSendbuf,Current_Update_FileName);//文件名
	pSendbuf+=strlen(Current_Update_FileName);
	*pSendbuf++ = (currentGetFileID>>8)&0xff;
	*pSendbuf++ = currentGetFileID&0xff;
	
	fseek(fpCurrent_Update_File,(currentGetFileID-1)*MAX_RECOED_BUF_SIZE, SEEK_SET);	//文件偏移
	Datalen = fread(pSendbuf+2,1,MAX_RECOED_BUF_SIZE,fpCurrent_Update_File);
	if(Datalen<0)
	{
		//ERRORP("fread\n");
		fclose(fpCurrent_Update_File);
		CleanUpdateFileInfo();
	}
	if(Datalen==0)//文件读取完成
	{
		if(fpCurrent_Update_File!=NULL)
		{
			DBG("File:%s read Over.\n",Current_Update_FileName );
		}
	//	Update_File_Pkg_id = Update_File_Pkg_Total_id;
	
		//fwrite("caonima",1,10,fpCurrent_Update_File);
	}

	*pSendbuf++ = (Datalen>>8)&0xff;	//包内容长度
	*pSendbuf++ = Datalen&0xff;
	
	pSendbuf += Datalen;//指针偏移至最后
	Datalen+=4 + strlen(Current_Update_FileName) +1;	//文件包id两个字节
	//*pSendbuf = Calculate_XorDat(&sendBuf, Datalen);
	//printf("发送包长度%d\n",strlen(sendBuf));
	//printf("%s\n",sendBuf);
	//DBG("当前文件包发送长度:%d\n",Datalen);
	*ProtocolLen=Datalen;

		if( currentGetFileID  == Update_File_Pkg_Total_id)
		{
			printf("文件发送完成...\n");
			fclose(fpCurrent_Update_File);
			CleanUpdateFileInfo();
		}


	printf("record is encoding\n");
	return 1;
	
}

/*
void Systemlog(char * _log)
{
	FILE *fp=NULL;
	static unsigned int writeCount=0; //记录文件写入条数
	static char filePath[128]={0};
	char path[FILE_NAME_MAX]={0};

	PTHREAD_LOCK(pmWriteFile);
	sWrite_Log_Flag = 1;
	Creat_Date_File_Name(path,1);
#if 1
	printf("%d\n",__LINE__ );
	if(writeCount==0||strcmp(path,filePath))	//首次读取最后一条记录计数
	{
		Del_Record_Sort_File(RECORD_FILE_LOG_PREFIX);//日期改变需要检查是否已经超出文件记录总数
		strcpy(filePath,path);
		char cmd[128]={0};
		char readBuf[128]={0};
		sprintf(cmd,"tail -n 1 %s|cut -d . -f1",path);
		printf("cmd>>%s\n", cmd);
		fp=popen(cmd,"r");
		if(fp==NULL)
		{
			writeCount = 0;
			perror("open tail cmd");
		}else
		{			
			if(fgets(readBuf, sizeof(readBuf), fp) <= 0)
			{
				writeCount = 0;
				perror("fgets fp");
			}else
			{
				printf("last str:%s\n", readBuf);
				int i=0;
				for (i = 0; i < strlen(readBuf); ++i)
				{
					if(!isdigit(readBuf[i]) && readBuf[i]!= '\n' )
						break;
				}
				if(i == strlen(readBuf))	//所有字符为数字
				{
					writeCount =atoi(readBuf);
					if(writeCount<0)
					{
						printf("writeCount<0\n");
						writeCount = 0;
					}
				}else{
					printf("readBuf not digit!\n");
					writeCount = 0;
				}				
				printf("最后一条记录计数:%d\n",writeCount);
			}			
			pclose(fp);
		}
	}
#endif
//	printf ( "/007The current date/time is: %s", asctime (timeinfo) ); 	
	fp = fopen(path,"at");
	if(fp == NULL)
	{		
		perror("open file");
		MY_SYSTEM("mkdir -p %s",RECORD_FILE_PATH);//判断 erron 创建目录
		fp = fopen(path,"at ");
		if(fp == NULL)
		{
			perror("open file");
			PTHREAD_UNLOCK(pmWriteFile);
			return ;
		}
	}
	if(Get_File_Size(path)> RECORD_FILE_SIZE_MAX)
	{
		printf("File:%s Over Size!\n",path);
		fclose(fp);
		PTHREAD_UNLOCK(pmWriteFile);
		return;
	}
	char buf[1024]={0};
//	char date_tmp[30]={0};
	char time_tmp[30]={0};
//	Get_Local_date(date_tmp,sizeof(date_tmp));
	Get_Local_time(time_tmp,sizeof(time_tmp));
	int len = sprintf(buf,"%d.[%s] %s\r\n",++writeCount,time_tmp,_log);

	////日期,时间,AUX,节目名称,音量,
	if(fwrite(buf, len,1, fp) < 0)
	{
		DBG("write %s Failed!\n",path);
		perror("write file");
	}
	fclose(fp);
	PTHREAD_UNLOCK(pmWriteFile);
}


void SystemErrorlog(char * _log)
{
	FILE *fp=NULL;
	static unsigned int writeCount=0; //记录文件写入条数
	static char filePath[128]={0};
	char path[FILE_NAME_MAX]={0};

	PTHREAD_LOCK(pmWriteFile);
	sWrite_Log_Flag = 1;
	Creat_Date_File_Name(path,2);
#if 1
	printf("%d\n",__LINE__ );
	if(writeCount==0||strcmp(path,filePath))	//首次读取最后一条记录计数
	{
		Del_Record_Sort_File(RECORD_FILE_ERROR);//日期改变需要检查是否已经超出文件记录总数
		strcpy(filePath,path);
		char cmd[128]={0};
		char readBuf[128]={0};
		sprintf(cmd,"tail -n 1 %s|cut -d . -f1",path);
		printf("cmd>>%s\n", cmd);
		fp=popen(cmd,"r");
		if(fp==NULL)
		{
			writeCount = 0;
			perror("open tail cmd");
		}else
		{			
			if(fgets(readBuf, sizeof(readBuf), fp) <= 0)
			{
				writeCount = 0;
				perror("fgets fp");
			}else
			{
				printf("last str:%s\n", readBuf);
				int i=0;
				for (i = 0; i < strlen(readBuf); ++i)
				{
					if(!isdigit(readBuf[i]) && readBuf[i]!= '\n' )
						break;
				}
				if(i == strlen(readBuf))	//所有字符为数字
				{
					writeCount =atoi(readBuf);
					if(writeCount<0)
					{
						printf("writeCount<0\n");
						writeCount = 0;
					}
				}else{
					printf("readBuf not digit!\n");
					writeCount = 0;
				}				
				printf("最后一条记录计数:%d\n",writeCount);
			}			
			pclose(fp);
		}
	}
#endif
//	printf ( "/007The current date/time is: %s", asctime (timeinfo) ); 	
	fp = fopen(path,"at");
	if(fp == NULL)
	{		
		perror("open file");
		MY_SYSTEM("mkdir -p %s",RECORD_FILE_PATH);//判断 erron 创建目录
		fp = fopen(path,"at ");
		if(fp == NULL)
		{
			perror("open file");
			PTHREAD_UNLOCK(pmWriteFile);
			return ;
		}
	}
	if(Get_File_Size(path)> RECORD_FILE_SIZE_MAX)
	{
		printf("File:%s Over Size!\n",path);
		fclose(fp);
		PTHREAD_UNLOCK(pmWriteFile);
		return;
	}
	char buf[1024]={0};
//	char date_tmp[30]={0};
	char time_tmp[30]={0};
//	Get_Local_date(date_tmp,sizeof(date_tmp));
	Get_Local_time(time_tmp,sizeof(time_tmp));
	int len = sprintf(buf,"%d.[%s](%s:%d): %s\r\n",++writeCount,time_tmp,file,line,_log);

	////日期,时间,AUX,节目名称,音量,
	if(fwrite(buf, len,1, fp) < 0)
	{
		DBG("write %s Failed!\n",path);
		perror("write file");
	}
	fclose(fp);
	PTHREAD_UNLOCK(pmWriteFile);
}
*/

#if 0

int main(int argc, char const *argv[])
{
	
//	OS_Init_UDP_Record_M(); //初始化通讯线程
//	Out_Info_To_File(8,"/mnt/yaffs2/","FileName.mp3");
//	Del_Record_Sort_File();

	MyStr File_List[7]={0};
	char sendBuf[1500]={0};
	char listBuf[123][128]={0};

	int i = 0;
	for (; i < 7; ++i)
	{
		File_List[i].str=listBuf[i];
		File_List[i].len=0;
	}
	WriteRecordInfo("fire","FileName.mp3",12);
	
	
	LOG("diaonilaomu");
	LOG("gagag");
	
	//Del_Dir_File_By_Include_Str(RECORD_FILE_PATH,"04-09");
	return 0;
}
#endif