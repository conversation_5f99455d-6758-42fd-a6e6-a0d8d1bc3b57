/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-07 09:59:33 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2025-01-11 10:11:40
 */

#include "sysconf.h"
#include "netPlayer.h"
#include "mp3Decoder.h"
#if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
#include "call_ring.h"
#endif


unsigned char app_play_task_flag=0;          //播放线程开启标志 1:已经启动 0：未启动
unsigned char player_song_flag=0;            //歌曲播放标志 1：正在播放歌曲，0：没有播放歌曲
unsigned char app_play_need_exit=0;          //播放线程需要退出标志
static pthread_t app_NetPlay_pthread;        //网络播放线程id

unsigned char concentrated_pcm_buf[CONCENTRATED_MODE_DATA_BUF_MAX][CONCENTRATED_MODE_DATA_BUF_SIZE];
unsigned int  concentrated_pcm_buf_len[CONCENTRATED_MODE_DATA_BUF_MAX];
unsigned int  concentrated_pcm_buf_valid[CONCENTRATED_MODE_DATA_BUF_MAX];
unsigned char concentrated_read_pos=0;
unsigned char concentrated_write_pos=0;
unsigned int concentrated_read_pos_total=0;
unsigned int concentrated_write_pos_total=0;
int concentrated_write_need_wait=0;    //I2S模块没有多余数据可读，需要等待片刻再写入，避免持续卡顿



int pager_need_exit_flag = 0;       //需要退出寻呼标志
unsigned char pager_pcm_buf[PAGER_PCM_BUF_MAX][PAGER_PCM_READ_VALVE*PAGER_PCM_BASIC_VALUE];//寻呼数据缓冲区大小
unsigned int pager_pcm_buf_len[PAGER_PCM_BUF_MAX] = {0};//寻呼数据缓冲长度
unsigned char pager_pcm_buf_valid[PAGER_PCM_BUF_MAX] = {0};//寻呼数据接收标志
unsigned int pager_write_pos = 0;//数据写指针
unsigned int pager_read_pos = 0; //数据读指针
unsigned int pager_write_pos_total=0;
unsigned int pager_read_pos_total=0;
unsigned int pager_pcm_write=0;
unsigned int pager_pcm_capacity=0;

int mixed_need_exit_flag = 0;       //需要退出混音音源标志
int g_mixed_run_flag=0;				//音频混音运行标志
unsigned char audioMixer_stream_buf[AUDIO_MIXER_STREAM_BUF_MAX][AUDIO_MIXER_STREAM_BUF_SIZE];
unsigned int  audioMixer_stream_len[AUDIO_MIXER_STREAM_BUF_MAX];
unsigned int  audioMixer_stream_valid[AUDIO_MIXER_STREAM_BUF_MAX];
unsigned char audioMixer_stream_read_pos=0;
unsigned char audioMixer_stream_write_pos=0;
unsigned int  audioMixer_stream_read_pos_total=0;
unsigned int  audioMixer_stream_write_pos_total=0;


int phone_gateway_need_exit_flag = 0;       //需要退出电话网关标志
int g_phone_gateway_run_flag=0;		//电话网关运行标志
unsigned char phoneGateway_stream_buf[AUDIO_MIXER_STREAM_BUF_MAX][AUDIO_MIXER_STREAM_BUF_SIZE];
unsigned int  phoneGateway_stream_len[AUDIO_MIXER_STREAM_BUF_MAX];
unsigned int  phoneGateway_stream_valid[AUDIO_MIXER_STREAM_BUF_MAX];
unsigned char phoneGateway_stream_read_pos=0;
unsigned char phoneGateway_stream_write_pos=0;
unsigned int  phoneGateway_stream_read_pos_total=0;
unsigned int  phoneGateway_stream_write_pos_total=0;


void Clean_All_Audio(int conser_play_exit_type,int collector_exit_type)
{
    if(g_media_source == SOURCE_API_TTS_MUSIC)
	{
        #if SUPPORT_FFMPEG
        ffplay_stop();
        #endif
        int cnt=30;
        while(cnt-- && g_ApiPlayType)
        {
            usleep(10000);
        }
		g_ApiPlayType = API_PLAY_NULL;
	}

	stop_consentrated(conser_play_exit_type);   //退出集中播放线程
	exit_audio_collector(collector_exit_type);	//退出音频采集器接收线程
    exit_audio_mixed();                         //退出音频混音线程
    exit_phone_gateway();                         //退出音频混音线程
    Close_Audio_Out();                          //关闭音频输出

    //清理相关数据
    Clean_Concentrated_Info();
    Clean_Pager_Pcm_Buf();
    //清除音频混音数据
    Clean_Mixed_Info();
    //清除电话网关音频数据
    Clean_PhoneGateway_Info();

    #if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
    Clean_call_Buf();
    //printf("Clean_All_Audio:g_media_source1=%d\n",g_media_source);
    //播放挂断铃声
    if(g_media_source == SOURCE_CALL && m_stCallInfo.self_callStatus != CALL_STATUS_CONNECT)
    {
        usleep(20000);
        //此处先清除AO通道BUF，避免播放铃声的过程中挂断时，延时时间不够导致没有挂断声输出的问题
        #ifndef USE_PC_SIMULATOR
        MI_AO_ClearChnBuf(0, 0);
        #endif
        int callHandUpRing_bytes = sizeof(_acCallHangup);
        int fileReadTotal=0;
        int fileReadOnce=1024*4;
        while(fileReadTotal<callHandUpRing_bytes)
        {
            int readBytes=(callHandUpRing_bytes-fileReadTotal>=fileReadOnce)?fileReadOnce:callHandUpRing_bytes-fileReadTotal;
            #ifndef USE_PC_SIMULATOR
            mi_audio_write(_acCallHangup+fileReadTotal,readBytes);
            #endif
            fileReadTotal+=readBytes;
        }
        //需要延时，否则后面会立刻关闭功放，导致挂断声没有输出
        usleep(500000);
    }
    //printf("Clean_All_Audio:g_media_source2=%d\n",g_media_source);
    #endif
}


void Audio_Write(unsigned char *buffer, int count)
{
    #ifndef USE_PC_SIMULATOR
    #if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
    mi_audio_write(buffer, count);
    #elif(IS_DEVICE_AUDIO_MIXER)
    mi_audio_write_mixer(buffer, count);
    #endif
    #endif
}

void Open_Audio_Out(unsigned int sample_rate, unsigned char fmt, unsigned char channels)
{
    #ifndef USE_PC_SIMULATOR

    #if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
        mi_audio_out_init(sample_rate, fmt, channels);
    #elif(IS_DEVICE_AUDIO_COLLECTOR || IS_DEVICE_AUDIO_MIXER)
        mi_audio_out_collector_init(sample_rate, fmt, channels);
    #endif

    #endif
}

void Open_Audio_In(unsigned int sample_rate, unsigned char fmt, unsigned char channels)
{
    #ifndef USE_PC_SIMULATOR

    #if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
        mi_audio_in_init(sample_rate, fmt, channels);
    #elif(IS_DEVICE_AUDIO_COLLECTOR || IS_DEVICE_AUDIO_MIXER || IS_DEVICE_PHONE_GATEWAY)
        mi_audio_in_collector_init(sample_rate, fmt, channels);
    #endif
    
    #endif
}

void Close_Audio_Out()
{
    #ifndef USE_PC_SIMULATOR
    //mi_audio_out_deinit();
    #endif
}


/*********************************************************************
 * @fn      stop_consentrated
 *
 * @brief   退出集中模式
 *
 * @param   need_quit_timing -- 退出标志 0:不需要退出，1：需要退出并设置音量，2：需要退出但不设置音量
 *
 * @return	void
 *********************************************************************/
void stop_consentrated(int need_quit_timing)
{
	if(g_concentrated_start)	//已启动
	{
        g_concentrated_start=0;
		int count = 40;
		if(	g_concentrated_playing )
		{
			g_concentrated_need_exit_flag = 1;		//有一种情况是已经关闭了，没必要再等2s
			while( count-- && g_concentrated_need_exit_flag && g_concentrated_playing)
			{
				usleep(60000);      //至少50ms，因为mcast_recv_concentrated_data数据接收超时时间设置为50ms，要等待其退出
			}
			printf("stop_consentrated:exit time=%d\n", 40-count);
			g_concentrated_need_exit_flag = 0;
			g_concentrated_playing=0;
		}
		if(need_quit_timing == 1) //需要退出
		{
			g_centralized_mode_timeout_pause = 0;
			g_centralized_mode_timeout_count = 0; //退出计数
			//挂起超时任务
            suspend_Centralized_Mode_Timing_Task();
		}
		else					//不需要退出
		{
			g_centralized_mode_timeout_pause = 1;
			g_centralized_mode_timeout_count = 0;	//退出计数
		}
	}
	else
	{
		g_centralized_mode_timeout_pause = 0;
	}
}



/*********************************************************************
 * @fn      exit_audio_collector
 *
 * @brief   退出音频采集器线程
 *
 * @param   need_quit_timing--是否需要退出 0：不需要退出，其他：需要退出
 *
 * @return	void
 *********************************************************************/
void exit_audio_collector(int need_quit_timing)
{
	if(g_collector_run_flag)
	{
        g_collector_run_flag = 0;
		int count = 200;
        g_ac_exit_flag = 1;
		while(g_ac_exit_flag && count-- && player_song_flag)
		{
			usleep(10000);
		}
		printf("exit_audio_collector, count:%d\n", count);

		g_ac_exit_flag = 0;
		if(need_quit_timing)	//需要退出
		{
			g_ac_timing_wait = 0;
			g_ac_timing_count = 0;	//退出计数
 
			suspend_AudioCollector_Timing_Task();
		}
		else					//不需要退出
		{
			g_ac_timing_wait = 1;
			g_ac_timing_count = 0;	//退出计数
		}
	}
}


/*********************************************************************
 * @fn      exit_audio_mixed
 *
 * @brief   退出音频混音线程
 *
 * @param   
 *
 * @return	void
 *********************************************************************/
void exit_audio_mixed()
{
	if(g_mixed_run_flag)
	{
        g_mixed_run_flag = 0;
		int count = 200;
        mixed_need_exit_flag = 1;
		while(mixed_need_exit_flag && count-- && player_song_flag)
		{
			usleep(10000);
		}
        printf("exit_audio_mixed, count:%d\n", count);
        mixed_need_exit_flag = 0;

        g_audio_mixer_stream_timing_count = 0;	//退出计数
        suspend_AudioMixer_Timing_Task();
	}
}


/*********************************************************************
 * @fn      exit_phone_gateway
 *
 * @brief   退出电话网关线程
 *
 * @param   
 *
 * @return	void
 *********************************************************************/
void exit_phone_gateway()
{
    if(g_phone_gateway_run_flag)
	{
        g_phone_gateway_run_flag = 0;
		int count = 200;
        phone_gateway_need_exit_flag = 1;
		while(phone_gateway_need_exit_flag && count-- && player_song_flag)
		{
			usleep(10000);
		}
        printf("exit_phone_gateway, count:%d\n", count);
        phone_gateway_need_exit_flag = 0;

        g_phone_gateway_stream_timing_count = 0;	//退出计数
        suspend_PhoneGateway_Timing_Task();
	}
}

/*********************************************************************
 * @fn      APP_Play_Music
 *
 * @brief   集中模式下歌曲播放任务回调函数
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void  *APP_NetPlay_Music(void *p_arg)
{
    int wrtite_count = WRITE_TIMEOUT_COUNT;
    app_play_task_flag=1;
    player_song_flag=0;
    printf("enter APP_Play_Music\n");
    while(!app_play_need_exit)
    {  
		printf("g_media_source=%d\n",g_media_source);
    	if(  g_media_source == SOURCE_FIRE_ALARM
            || g_media_source == SOURCE_LOCAL_PLAY
            || g_media_source == SOURCE_TIMING
            || g_media_source == SOURCE_MONITOR_EV)
    	{
            player_song_flag = 1;
            g_concentrated_playing=1;						//集中模式正在播放
            if(g_concentrated_song_type == 1)//MP3
            {
                int after_count=10;         //concentrated_write_pos=11时开始读取，缓存1.067*11*1000/40=266ms，TCP模式下缓存不变
                if(g_network_mode == NETWORK_MODE_WAN)
                {
                    after_count=10;
                }
                if(!g_centralized_mode_is_existLocalSong)
                {
                    if(g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet)
                    {
                        while(--wrtite_count && player_song_flag && !app_play_need_exit)
                        {
                            if(concentrated_write_pos > after_count)//10
                            {
                                wrtite_count = WRITE_TIMEOUT_COUNT;
                                break;
                            }
                            if(g_concentrated_need_exit_flag)
                            {
                                g_concentrated_need_exit_flag = 0;
                                player_song_flag = 0;
                                break;
                            }
                            usleep(10000);
                        }
                    }
                    else
                    {
                        while(player_song_flag && !app_play_need_exit)
                        {
                            if(g_concentrated_need_exit_flag)
                            {
                                g_concentrated_need_exit_flag = 0;
                                player_song_flag = 0;
                                break;
                            }
                            if(concentrated_write_pos_total - concentrated_read_pos_total >=CONCENTRATED_MODE_DATA_BUF_MAX-10)//38*26.12ms=992ms
                            {
                                break;
                            }
                            usleep(10000);
                        }
                    }
                }
                if(player_song_flag)
                    concentrated_play_mp3_song();
            }
            else if(g_concentrated_song_type == 2)//WAV
            {
                int after_count=15;
                if(g_concentrated_song_sample_rate==8000)
                {       
                    if(g_concentrated_song_channels==2)
                    {
                        after_count = 18;
                    }
                    else
                    {
                        after_count = 10;
                    }
                }
                else if(g_concentrated_song_sample_rate == 11025)
                {
                    if(g_concentrated_song_channels==2)
                    {
                        after_count = 17;
                    }
                    else
                    {
                        after_count = 10;
                    }         
                }
                else if(g_concentrated_song_sample_rate == 12000 || g_concentrated_song_sample_rate==16000)
                {
                    if(g_concentrated_song_channels==2)
                    {
                        after_count = 16;
                    }
                    else
                    {
                        after_count = 10;
                    }
                }
                else    //44.1K, concentrated_write_pos=21时开始读取，缓存1*21*1000/176=120ms，TCP模式下缓存不变
                {
                    after_count = 20;
                }
                
                if(g_network_mode == NETWORK_MODE_WAN)          //1*31*1000/176=164ms
                {
                    after_count = 28;
                }
                
                if(!g_centralized_mode_is_existLocalSong)
                {
                    if(g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet)
                    {
                        while(--wrtite_count && player_song_flag && !app_play_need_exit)
                        {       
                            if(concentrated_write_pos > after_count)
                            {
                                wrtite_count = WRITE_TIMEOUT_COUNT;
                                break;
                            }
                            if(g_concentrated_need_exit_flag)
                            {
                                g_concentrated_need_exit_flag = 0;
                                player_song_flag = 0;
                                break;
                            }
                            usleep(10000);
                        }
                    }
                    else
                    {
                        double DelayPerPackage=(double)(1000*1024)/(double)((g_concentrated_song_sample_rate*(g_concentrated_song_fmt/8)*g_concentrated_song_channels));     //每秒的延时
                        int DelayNum = (int)(1000 / DelayPerPackage);
                        if(DelayNum>CONCENTRATED_MODE_DATA_BUF_MAX-10)
                        {
                            DelayNum=CONCENTRATED_MODE_DATA_BUF_MAX-10;
                        }
                        printf("wav:DelayNum=%d\n",DelayNum);
                            
                        while(player_song_flag && !app_play_need_exit)
                        {
                            if(concentrated_write_pos_total - concentrated_read_pos_total >=DelayNum)//38*26.12ms=992ms
                            {
                                break;
                            }
                            usleep(10000);
                        }
                        
                    }
                }
                if(player_song_flag)
                    //printf("ready concentrated_play_wav_song...\n");
                    concentrated_play_wav_song();
            }

            //退出组播地址
            //igmp_leavegroup(IP_ADDR_ANY, (struct ip_addr *)(&concentrated_ipgroup_rev));//移除IGMP
            g_concentrated_need_exit_flag = 0;
            player_song_flag = 0;
            g_concentrated_playing=0;						//集中模式正在播放
    	}
    	else if(g_media_source == SOURCE_NET_PAGING)
    	{
    		player_song_flag = 1;
			int after_count=1;    //pager_write_pos=2时开始读取，缓存1*2*1000/44.1=45ms，TCP模式下缓存90ms
            if(g_network_mode == NETWORK_MODE_WAN)
            {
                after_count=2;    //为减少延迟，演示时先设为1，2代表90ms
            }
            while(--wrtite_count && !app_play_need_exit)
            {
                if(pager_write_pos > after_count)//10
                {
                    wrtite_count = WRITE_TIMEOUT_COUNT;
                    break;
                }
                if(pager_need_exit_flag || g_media_source != SOURCE_NET_PAGING)
                {
                    pager_need_exit_flag = 0;
                    player_song_flag=0;
                    break;
                }
                usleep(10000);//10ms*500
            }
                
            if(player_song_flag)
                Pager_Play_Song();
            player_song_flag = 0;
    	}
        else if(g_media_source == SOURCE_AUDIO_MIXED)
    	{
    		player_song_flag = 1;
			int after_count=1;    //pager_write_pos=2时开始读取，缓存1*2*1000/44.1=45ms，TCP模式下缓存90ms
            if(g_network_mode == NETWORK_MODE_WAN)
            {
                after_count=2;
            }
            while(--wrtite_count && !app_play_need_exit)
            {
                if(audioMixer_stream_write_pos > after_count)//10
                {
                    wrtite_count = WRITE_TIMEOUT_COUNT;
                    break;
                }
                if(mixed_need_exit_flag || g_media_source != SOURCE_AUDIO_MIXED)
                {
                    mixed_need_exit_flag = 0;
                    player_song_flag=0;
                    break;
                }
                usleep(10000);//10ms*500
            }
            
            if(player_song_flag)
                audio_mixer_play_song();
            player_song_flag = 0;
    	}
        else if(g_media_source == SOURCE_PHONE_GATEWAY)
    	{
    		player_song_flag = 1;
			int after_count=1;    //pager_write_pos=2时开始读取，缓存1*2*1000/44.1=45ms，TCP模式下缓存90ms
            if(g_network_mode == NETWORK_MODE_WAN)
            {
                after_count=2;
            }
            while(--wrtite_count && !app_play_need_exit)
            {
                if(phoneGateway_stream_write_pos > after_count)//10
                {
                    wrtite_count = WRITE_TIMEOUT_COUNT;
                    break;
                }
                if(phone_gateway_need_exit_flag || g_media_source != SOURCE_PHONE_GATEWAY)
                {
                    phone_gateway_need_exit_flag = 0;
                    player_song_flag=0;
                    break;
                }
                usleep(10000);//10ms*500
            }
            
            if(player_song_flag)
                audio_phone_gateway_song();
            player_song_flag = 0;
    	}
    	else if(g_media_source >= SOURCE_AUDIO_COLLECTOR_BASE && g_media_source <= SOURCE_AUDIO_COLLECTOR_MAX)
    	{
            player_song_flag = 1;
            int  after_count = 2;

            if(GetAudioCollectorChannelBySrc(g_media_source) == 1)  //第一通道为MIC，所以缩短延时
            {
                after_count = 1;
            }
            
            if(g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet)
            {
                while(--wrtite_count && player_song_flag && !app_play_need_exit)
                {
                    if(concentrated_write_pos > after_count)//10
                    {
                        wrtite_count = WRITE_TIMEOUT_COUNT;
                        break;
                    }
                    if(g_ac_exit_flag)
                    {
                        g_ac_exit_flag = 0;
                        player_song_flag = 0;
                        break;
                    }
                    usleep(10000);//10ms*30
                }
            }
            else
            {
                while(player_song_flag && !app_play_need_exit)
                {
                    if(concentrated_write_pos_total - concentrated_read_pos_total >=CONCENTRATED_MODE_DATA_BUF_MAX-10)//38*26.12ms=992ms
                    {
                        break;
                    }
                    usleep(10000);
                }
            }
            if(player_song_flag)
                audio_collector_play_song();
            player_song_flag = 0;
    	}
        break;
    }
    printf("exit APP_Play_Music\n");
    //此处不能置0，否则启动线程前不执行join回收操作，容易造成线程资源耗尽。
    //所有直接create未分离的线程，都要使用join进行资源回收
    //app_play_task_flag=0;
    player_song_flag=0;
}


/*********************************************************************
 * @fn      Create_Concentrated_Mode_Play_Task
 *
 * @brief   创建集中模式下歌曲播放任务
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_Concentrated_Mode_Play_Task(void)
{
   //如果之前已经开启了此线程，那么等待原线程结束后再行开启
    if(app_play_task_flag)
    {
        printf("app_play_task_flag=1,need_exit\n");
        app_play_need_exit=1;
        pthread_join(app_NetPlay_pthread,NULL);
    }
    app_play_need_exit=0;
    printf("Create_Concentrated_Mode_Play_Task:create.\n");

	//pthread_t pid;
	//pthread_attr_t Pthread_Attr;
	//pthread_attr_init(&Pthread_Attr);
	//pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&app_NetPlay_pthread, NULL, (void *)APP_NetPlay_Music, NULL);
	//pthread_attr_destroy(&Pthread_Attr);
}
