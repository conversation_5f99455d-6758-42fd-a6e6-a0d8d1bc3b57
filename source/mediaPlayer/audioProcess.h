/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2022-02-23 16:26:10 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2025-01-10 10:09:24
 */

#ifndef _AudioProcess_H_
#define _AudioProcess_H_

#include "math.h"
#include <stdint.h>

enum
{
    PHASE_INVERT_LEFT=0,
    PHASE_INVERT_RIGHT,
    PHASE_INVERT_STERO
};

int get_signal_db(int16_t *pcmData16,uint16_t len16);

int16_t limit_value_16bit(int32_t value);

void pcmAudioMix2(int16_t *desBuffer,int16_t *bufferA, int16_t *bufferB, int bufferLength);
void pcmAudioMix3(int16_t *desBuffer,int16_t *bufferA, int16_t *bufferB, int16_t *bufferC, int bufferLength);
void pcmAudioMix4(int16_t *desBuffer,int16_t *bufferA, int16_t *bufferB, int16_t *bufferC, int16_t *bufferD, int bufferLength);

void StereoToMono(const int16_t* src_audio,int frames,int16_t* dst_audio);
void MonoToStereo(const int16_t* src_audio,int frames,int16_t* dst_audio);
void PhaseInvert(int16_t* buf,int frames,int LRChannel);

#endif