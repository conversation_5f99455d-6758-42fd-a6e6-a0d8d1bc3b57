/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> 
 * @Date: 2021-09-07 10:00:15 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-02-24 15:07:45
 */

#ifndef _NETPLAYER_H_
#define _NETPLAYER_H_



/*********集中模式歌曲数据缓冲区个数和大小*********/
#define CONCENTRATED_MODE_DATA_BUF_MAX			50			//26ms*40 = 1040ms	26ms*50 = 1300ms
#define CONCENTRATED_MODE_DATA_BUF_SIZE			1500
#define WRITE_TIMEOUT_COUNT 					300


#define PAGER_PCM_BUF_MAX			      50			    // 寻呼数据缓冲区个数
#define PAGER_PCM_READ_VALVE              2                 // 多少K才开始读取
#define PAGER_PCM_BASIC_VALUE             1024              // 基数，1KB


extern unsigned char app_play_task_flag;          //播放线程开启标志 1:已经启动 0：未启动
extern unsigned char player_song_flag;            //歌曲播放标志 1：正在播放歌曲，0：没有播放歌曲

extern unsigned char app_play_need_exit;          //播放线程需要退出标志

extern unsigned char concentrated_pcm_buf[CONCENTRATED_MODE_DATA_BUF_MAX][CONCENTRATED_MODE_DATA_BUF_SIZE];
extern unsigned int  concentrated_pcm_buf_len[CONCENTRATED_MODE_DATA_BUF_MAX];
extern unsigned int  concentrated_pcm_buf_valid[CONCENTRATED_MODE_DATA_BUF_MAX];
extern unsigned char concentrated_read_pos;
extern unsigned char concentrated_write_pos;
extern unsigned int concentrated_read_pos_total;
extern unsigned int concentrated_write_pos_total;
extern int concentrated_write_need_wait;    //I2S模块没有多余数据可读，需要等待片刻再写入，避免持续卡顿


extern int pager_need_exit_flag;       //寻呼需要退出寻呼标志
extern unsigned char pager_pcm_buf[PAGER_PCM_BUF_MAX][PAGER_PCM_READ_VALVE*PAGER_PCM_BASIC_VALUE];//寻呼数据缓冲区大小
extern unsigned int pager_pcm_buf_len[PAGER_PCM_BUF_MAX];//寻呼数据缓冲长度
extern unsigned char pager_pcm_buf_valid[PAGER_PCM_BUF_MAX];//寻呼数据接收标志

extern unsigned int pager_write_pos;//寻呼数据写指针
extern unsigned int pager_read_pos; //寻呼数据读指针
extern unsigned int pager_write_pos_total;
extern unsigned int pager_read_pos_total;
extern unsigned int pager_pcm_write;
extern unsigned int pager_pcm_capacity;


#define AUDIO_MIXER_STREAM_BUF_MAX			    30			//26ms*40 = 1040ms	26ms*50 = 1300ms
#define AUDIO_MIXER_STREAM_BUF_SIZE			    1500
extern int mixed_need_exit_flag;       //需要退出混音音源标志
extern int g_mixed_run_flag;		   //音频混音运行标志
extern unsigned char audioMixer_stream_buf[AUDIO_MIXER_STREAM_BUF_MAX][AUDIO_MIXER_STREAM_BUF_SIZE];
extern unsigned int  audioMixer_stream_len[AUDIO_MIXER_STREAM_BUF_MAX];
extern unsigned int  audioMixer_stream_valid[AUDIO_MIXER_STREAM_BUF_MAX];
extern unsigned char audioMixer_stream_read_pos;
extern unsigned char audioMixer_stream_write_pos;
extern unsigned int  audioMixer_stream_read_pos_total;
extern unsigned int  audioMixer_stream_write_pos_total;


#define PHONE_GATEWAY_STREAM_BUF_MAX			    30			//26ms*40 = 1040ms	26ms*50 = 1300ms
#define PHONE_GATEWAY_STREAM_BUF_SIZE			    1500
extern int phone_gateway_need_exit_flag;    //需要退出电话网关标志
extern int g_phone_gateway_run_flag;		//电话网关运行标志
extern unsigned char phoneGateway_stream_buf[AUDIO_MIXER_STREAM_BUF_MAX][AUDIO_MIXER_STREAM_BUF_SIZE];
extern unsigned int  phoneGateway_stream_len[AUDIO_MIXER_STREAM_BUF_MAX];
extern unsigned int  phoneGateway_stream_valid[AUDIO_MIXER_STREAM_BUF_MAX];
extern unsigned char phoneGateway_stream_read_pos;
extern unsigned char phoneGateway_stream_write_pos;
extern unsigned int  phoneGateway_stream_read_pos_total;
extern unsigned int  phoneGateway_stream_write_pos_total;


//创建集中模式下歌曲播放任务
void Create_Concentrated_Mode_Play_Task(void);  
void stop_consentrated(int need_quit_timing);
void Clean_All_Audio(int conser_play_exit_type,int collector_exit_type);
void Audio_Write(unsigned char *buffer, int count);
void Open_Audio_Out(unsigned int sample_rate, unsigned char fmt, unsigned char channels);
void Open_Audio_In(unsigned int sample_rate, unsigned char fmt, unsigned char channels);
void Close_Audio_Out();

void exit_audio_collector(int need_quit_timing);
void exit_audio_mixed();
void exit_phone_gateway();
#endif