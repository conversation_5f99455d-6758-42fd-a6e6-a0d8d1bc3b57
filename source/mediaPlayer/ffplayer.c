#include <stdio.h>
#include <string.h>
#include <stdbool.h>
#include <stdlib.h>
#include <pthread.h>
#include <unistd.h>
#include <signal.h>

#include "sysconf.h"

#if SUPPORT_FFMPEG
#include "mi_common.h"
#include "mi_sys.h"
#include "mi_ao.h"

#include "audio_params.h"
#include "audioProcess.h"

#include "ffplayer.h"

url_play_parm curUrlPlayParm;


static MI_S32 AoDevId = 0;
static MI_S32 AoChn = 0;

static bool g_bExit = false;
static bool g_bPlayThreadRun = false;
bool g_bDecodeDone = false;
static pthread_t g_playThread = 0;

extern pthread_mutex_t mutex_audio_out;
extern int mi_ao_init_flag;
extern unsigned int mi_ao_sampleRate;


static AudioState* gAudioState;

static AVDictionary *format_opts, *codec_opts;

void *mp3DecodeProc(void *pParam)
{
    AudioState *is = (AudioState *)pParam;
    AVPacket *packet = av_packet_alloc();
    AVFrame *frame = av_frame_alloc();
    uint8_t *out[] = { is->audio_buf };
    int data_size = 0, got_frame = 0;
    int wavDataLen = 0;

    while(g_bPlayThreadRun)    //1.2 循环读取mp3文件中的数据帧
    {
        if (av_read_frame(is->pFormatCtx, packet) < 0)
        {
            printf("av_read_frame <0!\n");
            av_packet_unref(packet);
            break;
        }
        if(packet->stream_index != is->sndindex)
        {
            av_packet_unref(packet);
            continue;
        }
        if(avcodec_decode_audio4(is->sndCodecCtx, frame, &got_frame, packet) < 0) //1.3 解码数据帧
        {
            printf("avcodec_decode_audio4<0!\n");
            av_packet_unref(packet);
            break;
        }

        if(got_frame <= 0) /* No data yet, get more frames */
        {
            av_packet_unref(packet);
            continue;
        }
        data_size = av_samples_get_buffer_size(NULL, is->sndCodecCtx->channels, frame->nb_samples, is->sndCodecCtx->sample_fmt, 1);
        //1.4下面将ffmpeg解码后的数据帧转为我们需要的数据(关于"需要的数据"下面有解释)
        if(NULL==is->swr_ctx)
        {
            if(is->swr_ctx != NULL)
                swr_free(&is->swr_ctx);
            printf("frame: channnels=%d,format=%d, sample_rate=%d", frame->channels, frame->format, frame->sample_rate);
            is->swr_ctx = swr_alloc_set_opts(NULL, AV_CH_LAYOUT_STEREO, AV_SAMPLE_FMT_S16, WAV_SAMPLERATE, av_get_default_channel_layout(frame->channels), frame->format, frame->sample_rate, 0, NULL);
            if(is->swr_ctx == NULL)
            {
                printf("swr_ctx == NULL");
            }
            swr_init(is->swr_ctx);
        }
        #if 0
        int dst_nb_samples = av_rescale_rnd(swr_get_delay(is->swr_ctx, frame->sample_rate) +
                                frame->nb_samples, WAV_SAMPLERATE, frame->sample_rate, AV_ROUND_UP);
        wavDataLen = swr_convert(is->swr_ctx, out, dst_nb_samples, (const uint8_t **)frame->extended_data, frame->nb_samples);
        #endif
        //如果固定采样帧，那么一个解码帧数据量超出一次swr_convert的数量，所以需要分多次

        wavDataLen = swr_convert(is->swr_ctx, out, MI_AUDIO_SAMPLE_PER_FRAME, (const uint8_t **)frame->extended_data, frame->nb_samples);
        if(wavDataLen<=0)
        {
            av_packet_unref(packet);
            continue;
        }



        //printf("wavDataLen=%d\n",wavDataLen);
        //先降10dB，再设置输出DAC参数
        //ffmpeg比较特别，如果歌曲是单声道，那么转换为立体声后会每个声道的音量会下降
        //所以需要判断声道数，如果是立体声，降-10dB；如果是单声道，降-7dB
        int16_t  progcess_stero[2048]={0};
        memcpy(progcess_stero,is->audio_buf,MI_AUDIO_SAMPLE_PER_FRAME*2*2);

        #if 0
        int SamplesPreFrame = MI_AUDIO_SAMPLE_PER_FRAME;
        int i=0;
        int stero_gain_val =  1295;     // -10dB
        int mono_gain_val =  1900;     // -7dB
        int gain_process = (frame->channels == 1) ? mono_gain_val:stero_gain_val;
        for(i=0; i<SamplesPreFrame; i++)
        {
            progcess_stero[2 * i + 0] = limit_value_16bit( ((((int32_t)progcess_stero[2 * i + 0]) * gain_process + 2048) >> 12) );
            progcess_stero[2 * i + 1] = limit_value_16bit( ((((int32_t)progcess_stero[2 * i + 1]) * gain_process + 2048) >> 12) );
        }
       #if 0
        int16_t mono_buf[1024]={0};
        StereoToMono(progcess_stero,SamplesPreFrame,mono_buf);
        //立体声转单声道并发送至终端
        int sysSource = get_system_source();
        if(sysSource == SOURCE_NET_PAGING && g_paging_type == PAGING_TYPE_MUSIC)
            send_paging_stream(mono_buf,wavDataLen*2);

        //500/3.162=158mv,158mv/1.585=100mv，功放端3.75V

        //监听音量
        int t_sysvol=mSysVol[(unsigned int)(g_listen_vol)];
        if(t_sysvol)    //如果非0，那么放大3dB，兼容8欧输出
        {
            t_sysvol = t_sysvol*1.42;
        }

        for(i=0; i<SamplesPreFrame; i++)
        {
            progcess_stero[2 * i + 0] = limit_value_16bit( (((((int32_t)progcess_stero[2 * i + 0]) * t_sysvol + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L] + 2048) >> 12 );
            progcess_stero[2 * i + 1] = limit_value_16bit( (((((int32_t)progcess_stero[2 * i + 1]) * 4096 + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R] + 2048) >> 12 );
        }

        #endif
        #endif


#if 0
        MI_AUDIO_Frame_t stAoSendFrame;
        memset(&stAoSendFrame, 0x0, sizeof(MI_AUDIO_Frame_t));
        stAoSendFrame.u32Len[0] = wavDataLen * 2 * 2;

        stAoSendFrame.apVirAddr[0] = (unsigned char*)progcess_stero;//(unsigned char*)is->audio_buf;
        stAoSendFrame.apVirAddr[1] = NULL;
        int s32Ret = MI_SUCCESS;
        
        pthread_mutex_lock(&mutex_audio_out);
        if(mi_ao_init_flag && mi_ao_sampleRate==WAV_SAMPLERATE)
        {
            do
            {
                s32Ret = MI_AO_SendFrame(AoDevId, AoChn, &stAoSendFrame, -1);

            }
            while (s32Ret == MI_AO_ERR_NOBUF);
        }
        pthread_mutex_unlock(&mutex_audio_out);
#endif
        #if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
        mi_audio_write((unsigned char*)progcess_stero,wavDataLen * 2 * 2);
        #endif

        while(wavDataLen>0)
        {
            wavDataLen = swr_convert(is->swr_ctx, out, MI_AUDIO_SAMPLE_PER_FRAME, NULL, 0);
            //printf("wavDataLen2=%d\n",wavDataLen);
            if(wavDataLen<=0)
            {
                break;
            }
            
            //先降10dB，再设置输出DAC参数
            memcpy(progcess_stero,is->audio_buf,MI_AUDIO_SAMPLE_PER_FRAME*2*2);

            #if 0
            i=0;
            for(i=0; i<SamplesPreFrame; i++)
            {
                progcess_stero[2 * i + 0] = limit_value_16bit( ((((int32_t)progcess_stero[2 * i + 0]) * gain_process + 2048) >> 12) );
                progcess_stero[2 * i + 1] = limit_value_16bit( ((((int32_t)progcess_stero[2 * i + 1]) * gain_process + 2048) >> 12) );
            }

            #if 0
            StereoToMono(progcess_stero,SamplesPreFrame,mono_buf);
            //立体声转单声道并发送至终端
            if(sysSource == SOURCE_NET_PAGING && g_paging_type == PAGING_TYPE_MUSIC)
                send_paging_stream(mono_buf,wavDataLen*2);

            for(i=0; i<SamplesPreFrame; i++)
            {
                progcess_stero[2 * i + 0] = limit_value_16bit( (((((int32_t)progcess_stero[2 * i + 0]) * t_sysvol + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L] + 2048) >> 12 );
                progcess_stero[2 * i + 1] = limit_value_16bit( (((((int32_t)progcess_stero[2 * i + 1]) * 4096 + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R] + 2048) >> 12 );
            }
            #endif
            #endif

#if 0
            memset(&stAoSendFrame, 0x0, sizeof(MI_AUDIO_Frame_t));
            stAoSendFrame.u32Len[0] = wavDataLen * 2 * 2;
            stAoSendFrame.apVirAddr[0] = (unsigned char*)progcess_stero;//(unsigned char*)is->audio_buf;
            stAoSendFrame.apVirAddr[1] = NULL;
            s32Ret = MI_SUCCESS;

            pthread_mutex_lock(&mutex_audio_out);
            if(mi_ao_init_flag && mi_ao_sampleRate==WAV_SAMPLERATE)
            {
                do
                {
                    s32Ret = MI_AO_SendFrame(AoDevId, AoChn, &stAoSendFrame, -1);

                }
                while (s32Ret == MI_AO_ERR_NOBUF);
            }
            pthread_mutex_unlock(&mutex_audio_out);
#endif
            #if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
            mi_audio_write((unsigned char*)progcess_stero,wavDataLen * 2 * 2);
            #endif

        }

        av_packet_unref(packet);
    }

	if (is->swr_ctx != NULL)
		swr_free(&is->swr_ctx);
    if(packet)
    {
        av_packet_free(&packet);
        packet=NULL;
    }
    av_frame_free(&frame);
    //如果是强行退出，那么播放完成标志不为true，不自动下一曲
    if(g_bPlayThreadRun)
        g_bDecodeDone = true;

    printf("Play End...\n");

    return NULL;
}

int init_ffplayer(AudioState* is, char* filepath)
{
    int i=0;
    int ret;
    is->sndindex = -1;

    g_bDecodeDone = false;

    if(NULL == filepath)
    {
        printf("input file is NULL");
        return -1;
    }

#if 0
    avcodec_register_all();
    //avfilter_register_all();
    av_register_all();
#endif
    is->pFormatCtx = avformat_alloc_context();

    is->pFormatCtx->probesize = 10 * 1024;   //10K，减小延时
    is->pFormatCtx->format_probesize = 10 * 1024;    //10K，减小延时
    is->pFormatCtx->max_analyze_duration = 3 * AV_TIME_BASE; // 设置为3s，avformat_find_stream_info

    av_dict_set(&format_opts, "protocol_whitelist", "file,udp,tcp,rtp,rtsp,http", 0);
    if( strstr(filepath,"rtsp://") || strstr(filepath,".sdp") )
	{
        av_log(NULL, AV_LOG_INFO, "is rtsp stream or sdp!\n");
        if(strstr(filepath,"rtsp://"))
        {
            char option_key[]="rtsp_transport";
            char option_value[]="udp";
            av_dict_set(&format_opts,option_key,option_value,0);

            char option_key4[]="stimeout";
            char option_value4[]="4000000"; //rtsp连接超时时间4秒
            av_dict_set(&format_opts,option_key4,option_value4,0);
        }
        else
        {
            char option_key3[]="timeout";
		    char option_value3[]="4000000"; //udp连接超时时间4秒
            av_dict_set(&format_opts,option_key3,option_value3,0);
        }
        //max_delay参数定义了解码器在等待下一帧数据的时间上限。如果在指定的最大延迟时间内没有新的数据帧到达解码器，在实时流处理中，解码器可能会决定丢弃当前的帧，以保证播放的实时性。
		char option_key2[]="max_delay";
		char option_value2[]="200000";  //解码超时时间200ms，
		av_dict_set(&format_opts,option_key2,option_value2,0);
    }

    if(avformat_open_input(&is->pFormatCtx, filepath, NULL, &format_opts)!=0)
        return -1;

    if(avformat_find_stream_info(is->pFormatCtx, NULL)<0 || is->pFormatCtx->nb_streams == 0)
        return -1;
    av_dump_format(is->pFormatCtx,0, 0, 0);
    //is->videoindex = av_find_best_stream(is->pFormatCtx, AVMEDIA_TYPE_VIDEO, is->videoindex, -1, NULL, 0); 
    is->sndindex = av_find_best_stream(is->pFormatCtx, AVMEDIA_TYPE_AUDIO,-1, -1, NULL, 0);
    printf("videoindex=%d, sndindex=%d\n", is->videoindex, is->sndindex);
    if(is->sndindex == 0)
    {
        is->sndCodecCtx = is->pFormatCtx->streams[is->sndindex]->codec;
        is->sndCodec = avcodec_find_decoder(is->sndCodecCtx->codec_id);
        if(is->sndCodec == NULL)
        {
            printf("Codec not found\n");
            return -1;
        }
        if(avcodec_open2(is->sndCodecCtx, is->sndCodec, NULL) < 0)
        {
            printf("avcodec_open2 -1...\n");
            return -1;
        }
    }
    else
    {
        printf("is->sndindex != 0,error!\n");
        return -1;
    }

    g_bPlayThreadRun = true;
    printf("create mp3DecodeProc...\n");
    ret = pthread_create(&g_playThread, NULL, mp3DecodeProc, (void *)is);
    if (ret != 0) 
    {
        printf("pthread_create mp3DecodeProc failed!\n");
    }
    return 0;
}

void deinit_ffplayer(AudioState* is)
{
    g_bPlayThreadRun = false;
    printf("deinit_ffplayer1...\n");
    if (g_playThread)
    {
        printf("deinit_ffplayer2...\n");
        pthread_join(g_playThread, NULL);
        g_playThread = 0;
    }
    
    if(is!=NULL)
    {
        printf("deinit_ffplayer3...\n");
        avcodec_close(is->sndCodecCtx);
        //avcodec_free_context(is->sndCodecCtx);
        avformat_close_input(&is->pFormatCtx);
    }
}



int ffmpeg_get_media_song_bitRate(char *filename)
{
    int ret=0;
    int bitRate=0;
    AVFormatContext *fmt_ctx = NULL;

    // 打开输入媒体文件
    if ((ret = avformat_open_input(&fmt_ctx, filename, NULL, NULL)) < 0) {
        av_log(NULL, AV_LOG_ERROR, "Cannot open input file\n");
        return 0;
    }

    // 探测媒体文件信息
    if ((ret = avformat_find_stream_info(fmt_ctx, NULL)) < 0) {
        av_log(NULL, AV_LOG_ERROR, "Cannot find stream information\n");
        avformat_close_input(&fmt_ctx);
        return 0;
    }

    // 判断是否为 MP3 音频文件
    AVStream *stream = fmt_ctx->streams[0];
    if (stream->codecpar->codec_type == AVMEDIA_TYPE_AUDIO && stream->codecpar->codec_id == AV_CODEC_ID_MP3) {
        av_log(NULL, AV_LOG_INFO, "File is a valid MP3 audio file\n");


        //获取元信息，album、artist、title、等
        //AVDictionaryEntry *tag = NULL;
        //while (tag = av_dict_get(fmt_ctx->metadata, "", tag, AV_DICT_IGNORE_SUFFIX))
        //{
        //    qDebug()<<tag->key<<tag->value;
        //}
        #if 0
        //时长
        //duration/AV_TIME_BASE单位为秒
        qDebug()<<"duration"<<guard.formatCtx->duration/(AV_TIME_BASE/1000.0)<<"ms";
        //文件格式，如wav
        qDebug()<<"format"<<guard.formatCtx->iformat->name<<":"<<guard.formatCtx->iformat->long_name;
        //帧数
        qDebug()<<"n stream"<<guard.formatCtx->nb_streams;
        #endif
        //获取容器的比特率
        bitRate=fmt_ctx->bit_rate/1000; //kbps
    }
    // 判断是否为 WAV 音频文件
    else if(stream->codecpar->codec_type == AVMEDIA_TYPE_AUDIO && stream->codecpar->codec_id == AV_CODEC_ID_PCM_S16LE)
    {
        const char *format_name = fmt_ctx->iformat->name;
        if (strcmp(format_name, "wav") != 0) {
            av_log(NULL, AV_LOG_INFO, "File is a valid WAV audio file\n");
        }
        else{
            bitRate=fmt_ctx->bit_rate/1000; //kbps
        }
    }

    // 关闭输入媒体文件
    avformat_close_input(&fmt_ctx);

    return bitRate;
}

void ffplay_stop()
{
    if(gAudioState!=NULL)
    {
        deinit_ffplayer(gAudioState);
        av_free(gAudioState);
        gAudioState=NULL;
        printf("ffplay_stop succeed!\n");
    }
}

bool ffplay_url_song(char *url)
{
    if(gAudioState)
    {
        ffplay_stop();
    }
    gAudioState = (AudioState*) av_mallocz(sizeof(AudioState));
    static int ffplay_succeed_cnt=0;
    int ret=0;
    if( (ret=init_ffplayer(gAudioState, url)) != 0)
    {
        printf("init_ffplayer error");
        #if 0
        if(++ffplay_succeed_cnt<=3)   //连续三次错误，则不再继续播放
        {
            g_bDecodeDone=true; //设置标志，以便播放下一曲
        }
        #endif

        return false;
    }
    else
    {
        ffplay_succeed_cnt=0;

        return true;
    }
}






/*********************************************************************
 * @fn      pthread_url_play
 *
 * @brief  	播放URL地址歌曲线程
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void *pthread_url_play(void *lpParam)
{
    url_play_parm* urlParm = (url_play_parm*)lpParam;

    int playTimesRemain=urlParm->m_playTimes;

    g_ApiPlayType = API_PLAY_URL;
    set_system_source(SOURCE_API_TTS_MUSIC);
    pkg_query_current_status(NULL);	//发送当前状态
    Open_Audio_Out(44100,16,2);

    //开始播放网络地址
    bool play_ok=ffplay_url_song(urlParm->m_strUrl);

    while((g_ApiPlayType == API_PLAY_URL) && play_ok)
    {
        usleep(100000);
        if(g_bDecodeDone)
        {
            if(--playTimesRemain<=0)
            {
                break;
            }
            int cnt=urlParm->m_playInterval*1000000/50000;
            while(cnt--)
            {
                usleep(50000);
                if(g_ApiPlayType == API_PLAY_NULL)
                {
                    break;
                }
            }
            if(g_ApiPlayType == API_PLAY_URL)
            {
                //开始播放网络地址
                play_ok=ffplay_url_song(urlParm->m_strUrl);
            }
        }
    }

    int cnt=10;
    while(cnt-- && (g_ApiPlayType == API_PLAY_URL))
    {
        usleep(50000);
    }
    if(g_ApiPlayType == API_PLAY_URL)
    {
        Set_zone_idle_status(NULL,__func__, __LINE__,true);
        g_ApiPlayType=API_PLAY_NULL;
    }
}

void Create_url_play_task(url_play_parm *urlParm)
{
    memcpy(&curUrlPlayParm,urlParm,sizeof(url_play_parm));
    printf("Create_url_play_task:PlayCnt=%d\n",curUrlPlayParm.m_playTimes);
    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)pthread_url_play, (void*)&curUrlPlayParm);
	pthread_attr_destroy(&Pthread_Attr);
}



void ffplay_init()
{
    avformat_network_init();
}


int UrlPlayCheckParam(url_play_parm *urlPlayParm)
{
    //printf("TTSCheckParam:m_nPitch=%d,m_nSpeed=%d,m_nVolume=%d,m_playTimes=%d,m_VoiceName=%s,m_strText=%s\n", \
    ttsParm->m_nPitch,ttsParm->m_nSpeed,ttsParm->m_nVolume,ttsParm->m_playTimes,ttsParm->m_VoiceName,ttsParm->m_strText);
    if(urlPlayParm->m_nVolume < 0 || urlPlayParm->m_nVolume > 100 ||
       urlPlayParm->m_playTimes < 1 || urlPlayParm->m_playTimes > 999 ||
       urlPlayParm->m_playInterval < 1 || urlPlayParm->m_playInterval > 999 ||
       strlen(urlPlayParm->m_strUrl)<=0)
    {
        printf("Parm error\n");
        return 0;
    }


    return 1;
}


#endif