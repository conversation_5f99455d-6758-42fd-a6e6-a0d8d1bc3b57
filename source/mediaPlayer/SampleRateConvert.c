/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-10-15 15:48:23 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-10-16 16:59:14
 */

#include "sysconf.h"
#include "SampleRateConvert.h"

#if ENABLE_LIBSAMPLERATE_SRC

#define SRC_DATA_BYTES_MAX  1152*2*2*5       
static pthread_mutex_t mutex_src=PTHREAD_MUTEX_INITIALIZER;	    //采样率转换锁
static int src_startFlag=0;

static SRC_DATA   src_DataResample;
static SRC_STATE* src_state = NULL;
static unsigned char src_channels=2;    //默认立体声
static double src_in_sampleRate;  //输入采样率
static double src_out_sampleRate; //输出采样率

static float src_data_in[SRC_DATA_BYTES_MAX]={0};
static float src_data_out[SRC_DATA_BYTES_MAX]={0};

static unsigned char char_src_data_out[SRC_DATA_BYTES_MAX]={0};
    
int SampleRateConvert_init(unsigned int in_sampleRate,unsigned int out_sampleRate,unsigned char channels)
{   
    int error = -1;
    pthread_mutex_lock(&mutex_src);
    if(src_startFlag)
    {
        pthread_mutex_unlock(&mutex_src);
        return -1;
    }

    src_state = src_new(SRC_LINEAR, channels, &error);
    if(!src_state)
    {
        printf ("\n\nError : src_new() failed : %s.\n\n", src_strerror (error)) ;
        pthread_mutex_unlock(&mutex_src);
        return -1;
    }
    src_channels=channels;
    src_in_sampleRate=in_sampleRate;
    src_out_sampleRate=out_sampleRate;
    src_startFlag=1;

    pthread_mutex_unlock(&mutex_src);
    return 0;
}

int SampleRateConvert_deinit()
{
    pthread_mutex_lock(&mutex_src);
    if(!src_startFlag)
    {
        pthread_mutex_unlock(&mutex_src);
        return -1;
    }
    src_delete (src_state);
    src_state=NULL;
    src_startFlag=0;

    pthread_mutex_unlock(&mutex_src);
}

int SampleRateConvert_process(unsigned char *pcmbuffer,unsigned int buffer_size,unsigned char **outBuf)
{
    pthread_mutex_lock(&mutex_src);
    if(!src_startFlag)
    {
        pthread_mutex_unlock(&mutex_src);
        return -1;
    }

    int src_sizePCM=0;    //转换后的大小
    //memset(src_data_in , 0 , 4096 * sizeof(float));
    //memset(src_data_out , 0 , 4096 * sizeof(float));
    
    #if 0
    for(int j = 0; j < SRC_DATA_BYTES_MAX && j < buffer_size; j++)
    {
        src_data_in[j] = pcmbuffer[j];
    }
    #endif

    src_short_to_float_array((short*)pcmbuffer,src_data_in,buffer_size);

    /* SRC_PROCESS 处理流程 */
    src_DataResample.end_of_input  = 1;
    src_DataResample.data_in       = src_data_in;
    src_DataResample.input_frames  = buffer_size/src_channels;
    src_DataResample.data_out      = src_data_out;
    src_DataResample.output_frames = SRC_DATA_BYTES_MAX/src_channels;
    src_DataResample.src_ratio     = src_out_sampleRate/src_in_sampleRate;  /* 输出采样率/输入采样率 */
    

    src_reset(src_state);
    int ret = src_process(src_state, &src_DataResample);
    if(0 == ret)
    {
        src_sizePCM = src_DataResample.output_frames_gen * src_channels;
        #if 0
        int i = 0, j = 0;
        for (; i < SRC_DATA_BYTES_MAX && i < src_sizePCM; i += 4, j += 2)
        {
            char_src_data_out[j]     = (unsigned char)(src_data_out[i]);
            char_src_data_out[j + 1] = (unsigned char)(src_data_out[i + 1]);
        }
        #endif

        src_float_to_short_array(src_data_out,(short *)char_src_data_out,
				 src_sizePCM);
        
        *outBuf = char_src_data_out;

        //printf("src_sizePCM=%d\n",src_sizePCM);

        //fwrite(out_buffer, 1, src_sizePCM, outfd_process);
        //fflush(outfd_process);
        
        //memset(char_src_data_out, 0, buffer_size);
        //memset(src_data_out , 0 , 4096 * sizeof(float));
        #if 0
        printf("-------- output_frames_gen[%ld], in_used_frame[%ld] end_of_input[%d] src_ratio[%f]------ \n", 
            src_DataResample.output_frames_gen, src_DataResample.input_frames_used, 
            src_DataResample.end_of_input, src_DataResample.src_ratio);
        #endif
    }
    else
    {
        printf("src_simple error: %s \n", src_strerror(ret));
    }

    pthread_mutex_unlock(&mutex_src);
    return src_sizePCM;
}

#endif
