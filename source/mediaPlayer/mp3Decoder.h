/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-07 10:30:40 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-09-15 20:15:50
 */

#ifndef _MP3DECODER_H_
#define _MP3DECODER_H_

// 获取音频采集音源的channelID
int  GetAudioCollectorChannelBySrc(int src);

void concentrated_play_mp3_song(void);
void concentrated_play_wav_song(void);
void audio_collector_play_song(void);
void Pager_Play_Song(void);
void audio_mixer_play_song(void);
void audio_phone_gateway_song(void);
#endif