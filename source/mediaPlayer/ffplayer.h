#ifndef _FFPLAYER_H_
#define _FFPLAYER_H_

#include <libavutil/avutil.h>
#include <libavutil/attributes.h>
#include <libavutil/opt.h>
#include <libavutil/mathematics.h>
#include <libavutil/imgutils.h>
#include <libavutil/samplefmt.h>
#include <libavutil/timestamp.h>
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
#include <libavutil/mathematics.h>
#include <libswresample/swresample.h>
#include <libavutil/channel_layout.h>
#include <libavutil/common.h>
#include <libavformat/avio.h>
#include <libavutil/file.h>
#include <libswresample/swresample.h>

#include "mi_audio.h"

#define AVCODEC_MAX_AUDIO_FRAME_SIZE 192000     // 1 second of 48khz 32bit audio
#define WAV_SAMPLERATE			    44100
#define MI_AUDIO_SAMPLE_PER_FRAME 	512


typedef struct
{
    int    m_nVolume;             // 音量
    char m_strUrl[256];           // URL地址
    int m_playTimes;              // 播放次数
    int m_playInterval;           // 间隔时间
}url_play_parm;

typedef struct {
    int videoindex;
    int sndindex;
    AVFormatContext* pFormatCtx;
    AVCodecContext* sndCodecCtx;
    AVCodec* sndCodec;
    SwrContext *swr_ctx;
    DECLARE_ALIGNED(16,uint8_t,audio_buf) [MI_AUDIO_SAMPLE_PER_FRAME * 4];
}AudioState;


extern url_play_parm curUrlPlayParm;

int init_ffplayer(AudioState* is, char* filepath);
void deinit_ffplayer(AudioState* is);
int ffmpeg_get_media_song_bitRate(char *filename);

void ffplay_init();
void ffplay_stop();
int UrlPlayCheckParam(url_play_parm *urlPlayParm);
void Create_url_play_task(url_play_parm *urlParm);
#endif
