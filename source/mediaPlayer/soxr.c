/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-10-16 14:21:29 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-10-16 17:04:29
 */

#include "sysconf.h"
#include "soxr_t.h"

#if ENABLE_SOXR_SRC

#define SRC_DATA_BYTES_MAX  32768//1152*2*2*5       
static pthread_mutex_t soxr_mutex=PTHREAD_MUTEX_INITIALIZER;	    //采样率转换锁
static int src_startFlag=0;

static soxr_t soxr_item;

static unsigned char src_channels=2;    //默认立体声
static double src_in_sampleRate;  //输入采样率
static double src_out_sampleRate; //输出采样率

static float src_data_in[SRC_DATA_BYTES_MAX]={0};
static float src_data_out[SRC_DATA_BYTES_MAX]={0};

static unsigned char char_src_data_out[SRC_DATA_BYTES_MAX]={0};


void src_short_to_float_array (const short *in, float *out, int len)
{
    while (len)
    {   len -- ;
        out [len] = (float) (in [len] / (1.0 * 0x8000)) ;
        } ;

    return ;
}
 
 void src_float_to_short_array (const float *in, short *out, int len)
 {   
     double scaled_value ;
     while (len)
     {   len -- ;
 
         scaled_value = in [len] * (8.0 * 0x10000000) ;
         if (scaled_value >= (1.0 * 0x7FFFFFFF))
         {   out [len] = 32767 ;
             continue ;
             } ;
         if (scaled_value <= (-8.0 * 0x10000000))
         {   out [len] = -32768 ;
             continue ;
             } ;
 
         out [len] = (short) (lrint (scaled_value) >> 16) ;
         } ;
 
 }
    
int soxr_init(unsigned int in_sampleRate,unsigned int out_sampleRate,unsigned char channels)
{   
    int error = -1;
    pthread_mutex_lock(&soxr_mutex);
    if(src_startFlag )
    {
        pthread_mutex_unlock(&soxr_mutex);
        return -1;
    }
    
    soxr_error_t err;
    soxr_quality_spec_t quality = soxr_quality_spec(SOXR_HQ, 0);
/*			switch (sizeof(float))
        case 4:
            conv->soxr = soxr_create ( from->rate, to->rate, to->channels, &err, {SOXR_FLOAT32_I, SOXR_FLOAT32_I}, quality, NULL );
            break;
        case 8:
            conv->soxr = soxr_create ( from->rate, to->rate, to->channels, &err, {SOXR_FLOAT64_I, SOXR_FLOAT64_I}, quality, NULL );
            break;
        default:
            error ("SoX resampling not supported for sizeof(float)=%d!",sizeof(float));
            return 0;*/      
    soxr_item = soxr_create ( in_sampleRate, out_sampleRate, channels, &err, NULL, &quality, NULL );
    if (err) {
        printf ("Can't resample with SoX from %dHz to %dHz: %s",
                in_sampleRate, out_sampleRate, soxr_strerror (err));
                    pthread_mutex_unlock(&soxr_mutex);
        return -2;
			}
   
    src_channels=channels;
    src_in_sampleRate=in_sampleRate;
    src_out_sampleRate=out_sampleRate;
    src_startFlag=1;
    printf("src_startFlag ok...\n");
    pthread_mutex_unlock(&soxr_mutex);
    return 0;
}

int soxr_deinit()
{
    pthread_mutex_lock(&soxr_mutex);
    if(!src_startFlag)
    {
        pthread_mutex_unlock(&soxr_mutex);
        return -1;
    }
    soxr_delete(soxr_item);
    src_startFlag=0;

    pthread_mutex_unlock(&soxr_mutex);
}

int soxr_ttt_process(unsigned char *pcmbuffer,unsigned int buffer_size,unsigned char **outBuf)
{
    pthread_mutex_lock(&soxr_mutex);
    if(!src_startFlag)
    {
        printf("soxr_ttt_process error...\n");
        pthread_mutex_unlock(&soxr_mutex);
        return -1;
    }

	soxr_error_t err;
	size_t in_done, out_done,out_done2;

	size_t in_len = buffer_size/src_channels;
	size_t out_len = SRC_DATA_BYTES_MAX/src_channels;;

    //printf("TGTJKJKJKJKJK...\n");

    src_short_to_float_array((short*)pcmbuffer,src_data_in,buffer_size);

    //printf("in_len=%zu,out_len=%zu\n",in_len,out_len);
    soxr_clear(soxr_item);
	err = soxr_process(soxr_item, src_data_in, in_len, &in_done, src_data_out, out_len, &out_done);

	if (err) {
		printf ("TG: soxr resampler error %s!",soxr_strerror (err));
	}

	//printf ("TG: processed %zu input samples, got %zu output samples\n", (size_t)in_done*src_channels, (size_t)out_done*src_channels);

	if (in_len != in_done) {
		printf ("TG: some samples not processed!");
	}

    err = soxr_process(soxr_item, NULL, 0, NULL, src_data_out+out_done*src_channels, out_len, &out_done2);

	if (err) {
		printf ("TG: soxr resampler error %s!",soxr_strerror (err));
	}

#if 0
	if (in_len != in_done) {
		printf ("TG: some samples not processed!");
	}
#endif

    size_t total_bytes=(out_done+out_done2)*src_channels;
    //printf ("TG: processed %zu input samples, got %zu output samples\n", (size_t)in_done*src_channels, total_bytes);
     src_float_to_short_array(src_data_out,(short *)char_src_data_out,
				 total_bytes);

	*outBuf = char_src_data_out;
    pthread_mutex_unlock(&soxr_mutex);
    return total_bytes;
}


#endif