/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-10-15 15:48:16 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-10-16 17:01:39
 */

#ifndef _SAMPLERATECONVERT_H_
#define _SAMPLERATECONVERT_H_

#include <samplerate.h>

#define SRC_LIBSAMPLE_RATE 48000

int SampleRateConvert_init(unsigned int in_sampleRate,unsigned int out_sampleRate,unsigned char channels);
int SampleRateConvert_deinit();
int SampleRateConvert_process(unsigned char *pcmbuffer,unsigned int buffer_size,unsigned char **outBuf);


#endif