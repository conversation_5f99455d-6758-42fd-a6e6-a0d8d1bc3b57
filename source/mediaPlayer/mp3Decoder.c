/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-07 10:29:39 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-02-24 17:12:45
 */
#include "sysconf.h"
#include "mpg123.h"
#include "mp3Decoder.h"

#if SUPPORT_CODEC_G722
#include "g722_decoder.h"
#define G722_BUFFER_SIZE 512
#endif

#if SUPPORT_CODEC_G7221
#include "../g7221/defs.h"
#include "../g7221/count.h"

#define MAX_SAMPLE_RATE 32000
#define MAX_FRAMESIZE   (MAX_SAMPLE_RATE/50)
#define MEASURE_WMOPS   0
#define WMOPS           0

typedef struct 
{
    Word16  syntax;
    Word32  bit_rate;
    Word16  bandwidth;
    Word16  number_of_bits_per_frame;
    Word16  number_of_regions;
    Word16  frame_size;
    FILE    *fpin;
    FILE    *fp_bitstream;
} DECODER_CONTROL;

#endif


mpg123_handle *m=NULL;
static int mpg123_status=0;

#define pcm_buf_single_size 16384
#define pcm_buf_play_size 32768

#define DECODE_FRAME_MAX		1
static unsigned char pcm_data_decode[pcm_buf_play_size];          //总解码缓冲区
static unsigned char pcm_data_decode_single[pcm_buf_single_size];         //单次解码缓冲区


void init_mpg123()
{
    int ret=MPG123_OK;
	mpg123_init();//初始化库
	m = mpg123_new(NULL, &ret);//创建对象
	mpg123_param(m, MPG123_VERBOSE, 2, 0);//解码设置
	mpg123_open_feed(m);//打开
	printf("init_mpg123 ret=%d\n",ret);
	mpg123_status=1;
}

void release_mpg123()
{
	if(mpg123_status)
	{
		mpg123_close(m);
		mpg123_delete(m);
		mpg123_exit();
		mpg123_status=0;
	}
	printf("release_mpg123\n");
}


/*********************************************************************
 * @fn      concentrated_play_mp3_song
 *
 * @brief   播放MP3歌曲
 *
 * @param   NULL
 *
 *
 * @return  none
*********************************************************************/
void concentrated_play_mp3_song(void)
{
    printf("enter concentrated_play_mp3_song...\n");
	short *data_pcm_buf;
	int read_offset = 0;	//读偏移指针
	unsigned char *read_ptr;
	int ret = 0;

    int decode_count=0;
    int total_decode_size=0;
    init_mpg123();

	#ifdef USE_PC_SIMULATOR
	int enable_resample=0;
	#else
	int enable_resample=(dsp_eq_info.eq_mode == 0)?0:1;
	#endif
	#if !ENABLE_LIBSAMPLERATE_SRC && !ENABLE_SOXR_SRC
	enable_resample=0;
	#endif

	if(enable_resample)
	{
		#if ENABLE_LIBSAMPLERATE_SRC
		SampleRateConvert_init(g_concentrated_song_sample_rate,SRC_LIBSAMPLE_RATE,g_concentrated_song_channels);
		#elif ENABLE_SOXR_SRC
		soxr_init(g_concentrated_song_sample_rate,SRC_SOXR_RATE,g_concentrated_song_channels);
		#endif
	}

    size_t decode_mp3_size;

	int isExistLocalSong=g_centralized_mode_is_existLocalSong;
	int isInitLocalSong=0;
	unsigned char local_pcm_buf[CONCENTRATED_MODE_DATA_BUF_SIZE];
	unsigned short frameLen=0;
	if(isExistLocalSong)
	{
		if(onlineSaver_ready_read_localSong(g_media_name))
		{
			isInitLocalSong=1;
		}
	}

	while( !app_play_need_exit && !g_concentrated_need_exit_flag && (g_concentrated_song_type == 1))
	{
		//printf("write-read=%d\n",concentrated_write_pos_total - concentrated_read_pos_total);
		if(isExistLocalSong && isInitLocalSong)
		{
			//一帧一帧读取
			onlineSaver_localSong_read_frame(local_pcm_buf,&frameLen);
			if(frameLen>0)
			{
				ret=mpg123_decode(m,local_pcm_buf,frameLen,pcm_data_decode_single,pcm_buf_single_size,&decode_mp3_size);
				if((ret <= MPG123_NEED_MORE || ret == MPG123_OK) )
				{
					total_decode_size = decode_mp3_size;
					memcpy(pcm_data_decode, pcm_data_decode_single, total_decode_size);
					//printf("decode_mp3_size=%d\n",decode_mp3_size);
					//Audio_Write(pcm_data_decode, total_decode_size);

					if(enable_resample)
					{
						#if ENABLE_LIBSAMPLERATE_SRC
						unsigned char *src_outBuf=NULL;
						if( (total_decode_size=SampleRateConvert_process(pcm_data_decode,total_decode_size,&src_outBuf)) >0 )
						{
							Audio_Write(src_outBuf, total_decode_size);
						}
						#elif ENABLE_SOXR_SRC
						unsigned char *src_outBuf=NULL;
						if(total_decode_size == 13824)	//立体声
						{
							//分成3次
							int count=0;
							while(count<3)
							{
								total_decode_size=13824/3;
								if(total_decode_size=soxr_ttt_process(pcm_data_decode+total_decode_size*count,total_decode_size,&src_outBuf))
								{
									Audio_Write(src_outBuf, total_decode_size);
								}
								else
								{
									break;
								}
								count++;
							}
						}
						else if(total_decode_size == 6912)	//单声道
						{
							//分成3次
							int count=0;
							while(count<3)
							{
								total_decode_size=6912/3;
								if(total_decode_size=soxr_ttt_process(pcm_data_decode+total_decode_size*count,total_decode_size,&src_outBuf))
								{
									Audio_Write(src_outBuf, total_decode_size);
								}
								else
								{
									break;
								}
								count++;
							}
						}
						else if( (total_decode_size=soxr_ttt_process(pcm_data_decode,total_decode_size,&src_outBuf)) >0 )
						{
							Audio_Write(src_outBuf, total_decode_size);
						}
						#else
						Audio_Write(pcm_data_decode, total_decode_size);
						#endif
					}
					else
					{
						Audio_Write(pcm_data_decode, total_decode_size);
					}
					total_decode_size=0;
				}
				else
				{
					printf("mpg123 error:%d...\n",ret);
				}
			}
			else
			{ 	
				if(st_triggerSong.trigger_local_play_flag)
				{
					//如果是本地触发播放，播放完毕后需要直接退出
					st_triggerSong.trigger_local_play_finish=1;
				}
				else
				{
					usleep(5000);
				}
			}
		}
		else if(concentrated_pcm_buf_valid[concentrated_read_pos])
		{
			//网络模式是LAN或者非公网TCP模式
			if(	(g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet) || (!concentrated_write_need_wait || (concentrated_write_pos_total-concentrated_read_pos_total)>=CONCENTRATED_MODE_DATA_BUF_MAX-10))
			{
				#if 1
				decode_mp3_size=0;
                ret=mpg123_decode(m,concentrated_pcm_buf[concentrated_read_pos],concentrated_pcm_buf_len[concentrated_read_pos],pcm_data_decode_single,pcm_buf_single_size,&decode_mp3_size);
				//printf("decode_mp3_size=%lu\n",decode_mp3_size);
				if(decode_mp3_size >0 && (ret <= MPG123_NEED_MORE || ret == MPG123_OK) )
                {
					memcpy(pcm_data_decode+(total_decode_size), pcm_data_decode_single, decode_mp3_size);
					decode_count++;
					total_decode_size += decode_mp3_size;
					if(decode_count >= (g_Is_tcp_real_internet?1:DECODE_FRAME_MAX))
					{
						if(enable_resample)
						{
							#if ENABLE_LIBSAMPLERATE_SRC
							unsigned char *src_outBuf=NULL;
							if( (total_decode_size=SampleRateConvert_process(pcm_data_decode,total_decode_size,&src_outBuf)) >0 )
							{
								Audio_Write(src_outBuf, total_decode_size);
							}
							#elif ENABLE_SOXR_SRC
							unsigned char *src_outBuf=NULL;
							if(total_decode_size == 13824)	//立体声
							{
								//分成3次
								int count=0;
								while(count<3)
								{
									total_decode_size=13824/3;
									if(total_decode_size=soxr_ttt_process(pcm_data_decode+total_decode_size*count,total_decode_size,&src_outBuf))
									{
										Audio_Write(src_outBuf, total_decode_size);
									}
									else
									{
										break;
									}
									count++;
								}
							}
							else if(total_decode_size == 6912)	//单声道
							{
								//分成3次
								int count=0;
								while(count<3)
								{
									total_decode_size=6912/3;
									if(total_decode_size=soxr_ttt_process(pcm_data_decode+total_decode_size*count,total_decode_size,&src_outBuf))
									{
										Audio_Write(src_outBuf, total_decode_size);
									}
									else
									{
										break;
									}
									count++;
								}
							}
							else if( (total_decode_size=soxr_ttt_process(pcm_data_decode,total_decode_size,&src_outBuf)) >0 )
							{
								Audio_Write(src_outBuf, total_decode_size);
							}
							#else
							Audio_Write(pcm_data_decode, total_decode_size);
							#endif
						}
						else
						{
							Audio_Write(pcm_data_decode, total_decode_size);
						}
                        //printf("Mp3 decode:totalSize=%d\n",total_decode_size);
                        memset(pcm_data_decode, 0, sizeof(pcm_data_decode));
                        total_decode_size = 0;
                        decode_count = 0;
					}
				}
				else
				{
					printf("mpg123 error:%d...\n",ret);
				}
				#endif

#if 0
                ret=mpg123_decode(m,concentrated_pcm_buf[concentrated_read_pos],concentrated_pcm_buf_len[concentrated_read_pos],pcm_data_decode_single,pcm_buf_single_size,&decode_mp3_size);
				printf("inlen=%d,decode_mp3_size=%d\n",concentrated_pcm_buf_len[concentrated_read_pos],decode_mp3_size);
				if(ret <=MPG123_NEED_MORE || ret == 0)
				{
					if(decode_mp3_size>0)
						mi_audio_write(pcm_data_decode_single, decode_mp3_size);
				}
				else
				{
					printf("mpg123_decode error:%d\n",ret);
				}
#endif

				concentrated_pcm_buf_valid[concentrated_read_pos] = 0;
				concentrated_read_pos++;
				concentrated_read_pos_total++;
				if(concentrated_read_pos >= CONCENTRATED_MODE_DATA_BUF_MAX)
					concentrated_read_pos = 0;
			}
		}
		if((g_media_source != SOURCE_FIRE_ALARM && g_media_source != SOURCE_LOCAL_PLAY
			&& g_media_source != SOURCE_TIMING && g_media_source != SOURCE_MONITOR_EV) || (g_concentrated_song_type != 1))
		{
			break;
		}
		if(	g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet )
        	usleep(2000);
		else
			usleep(2000);
	}

	release_mpg123();

	if(isExistLocalSong)
	{
		//关闭文件
		onlineSaver_close_localSong();
	}

	if(enable_resample)
	{
		#if ENABLE_LIBSAMPLERATE_SRC
		SampleRateConvert_deinit();
		#elif ENABLE_SOXR_SRC
		soxr_deinit();
		#endif
	}
	printf("exit concentrated_play_mp3_song...\n");
}




/*********************************************************************
 * @fn      concentrated_play_wav_song
 *
 * @brief   播放WAV歌曲
 *
 * @param   NULL
 *
 *
 * @return  none
*********************************************************************/
void concentrated_play_wav_song(void)
{
    printf("enter concentrated_play_wav_song...\n");
	short *data_pcm_buf;
	int read_offset = 0;	//读偏移指针
	unsigned char *read_ptr;
	int ret = 0;

    int decode_count=0;
    int total_decode_size=0;

	#ifdef USE_PC_SIMULATOR
	int enable_resample=0;
	#else
	int enable_resample=(dsp_eq_info.eq_mode == 0)?0:1;
	#endif
	#if !ENABLE_LIBSAMPLERATE_SRC && !ENABLE_SOXR_SRC
	enable_resample=0;
	#endif

	if(enable_resample)
	{
		#if ENABLE_LIBSAMPLERATE_SRC
		SampleRateConvert_init(g_concentrated_song_sample_rate,SRC_LIBSAMPLE_RATE,g_concentrated_song_channels);
		#elif ENABLE_SOXR_SRC
		soxr_init(g_concentrated_song_sample_rate,SRC_SOXR_RATE,g_concentrated_song_channels);
		#endif
	}

	int isExistLocalSong=g_centralized_mode_is_existLocalSong;
	int isInitLocalSong=0;
	unsigned char local_pcm_buf[CONCENTRATED_MODE_DATA_BUF_SIZE];
	unsigned short frameLen=0;
	if(isExistLocalSong)
	{
		if(onlineSaver_ready_read_localSong(g_media_name))
		{
			isInitLocalSong=1;
		}
	}

	while( !app_play_need_exit && !g_concentrated_need_exit_flag && (g_concentrated_song_type == 2))
	{
		//printf("write-read=%d,g_media_source=%d\n",concentrated_write_pos_total - concentrated_read_pos_total,g_media_source);
		if(isExistLocalSong && isInitLocalSong)
		{
			//一帧一帧读取
			onlineSaver_localSong_read_frame(local_pcm_buf,&frameLen);
			if(frameLen>0)
			{
				total_decode_size = frameLen;
				memcpy(pcm_data_decode,local_pcm_buf,total_decode_size);
				//Audio_Write(pcm_data_decode, total_decode_size);
				if(enable_resample)
				{
					#if ENABLE_LIBSAMPLERATE_SRC
					unsigned char *src_outBuf=NULL;
					if( (total_decode_size=SampleRateConvert_process(pcm_data_decode,total_decode_size,&src_outBuf)) >0 )
					{
						Audio_Write(src_outBuf, total_decode_size);
					}
					#elif ENABLE_SOXR_SRC
					unsigned char *src_outBuf=NULL;
					
					if( (total_decode_size=soxr_ttt_process(pcm_data_decode,total_decode_size,&src_outBuf)) >0 )
					{
						Audio_Write(src_outBuf, total_decode_size);
					}
					#else
					Audio_Write(pcm_data_decode, total_decode_size);
					#endif
				}
				else
				{
					Audio_Write(pcm_data_decode, total_decode_size);
				}
				total_decode_size=0;
			}
			else
			{
				if(st_triggerSong.trigger_local_play_flag)
				{
					//如果是本地触发播放，播放完毕后需要直接退出
					st_triggerSong.trigger_local_play_finish=1;
				}
				else
				{
					usleep(5000);
				}
			}
		}
		else if(concentrated_pcm_buf_valid[concentrated_read_pos])
		{
			//网络模式是LAN或者非公网TCP模式
			if(	(g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet) || (!concentrated_write_need_wait || (concentrated_write_pos_total-concentrated_read_pos_total)>=CONCENTRATED_MODE_DATA_BUF_MAX-10))
			{
				decode_count++;
				memcpy(pcm_data_decode+total_decode_size, concentrated_pcm_buf[concentrated_read_pos], concentrated_pcm_buf_len[concentrated_read_pos]);
				total_decode_size += concentrated_pcm_buf_len[concentrated_read_pos];

				if(decode_count >= (g_Is_tcp_real_internet?2:2))
				{
					if(total_decode_size>0)
					{
						if(enable_resample)
						{
							#if ENABLE_LIBSAMPLERATE_SRC
							unsigned char *src_outBuf=NULL;
							if( (total_decode_size=SampleRateConvert_process(pcm_data_decode,total_decode_size,&src_outBuf)) >0 )
							{
								Audio_Write(src_outBuf, total_decode_size);
							}
							#elif ENABLE_SOXR_SRC
							unsigned char *src_outBuf=NULL;
							
							if( (total_decode_size=soxr_ttt_process(pcm_data_decode,total_decode_size,&src_outBuf)) >0 )
							{
								Audio_Write(src_outBuf, total_decode_size);
							}
							#else
							Audio_Write(pcm_data_decode, total_decode_size);
							#endif
						}
						else
						{
							Audio_Write(pcm_data_decode, total_decode_size);
						}
					}
					//printf("Mp3 decode:totalSize=%d\n",total_decode_size);
					memset(pcm_data_decode, 0, sizeof(pcm_data_decode));
					total_decode_size = 0;
					decode_count = 0;
				}
				

				concentrated_pcm_buf_valid[concentrated_read_pos] = 0;
				concentrated_read_pos++;
				concentrated_read_pos_total++;
				if(concentrated_read_pos >= CONCENTRATED_MODE_DATA_BUF_MAX)
					concentrated_read_pos = 0;
			}
		}
		if((g_media_source != SOURCE_FIRE_ALARM && g_media_source != SOURCE_LOCAL_PLAY
			&& g_media_source != SOURCE_TIMING && g_media_source != SOURCE_MONITOR_EV) || (g_concentrated_song_type != 2))
		{
			break;
		}
		if(	g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet )
        	usleep(2000);
		else
			usleep(2000);
	}

	if(isExistLocalSong)
	{
		//关闭文件
		onlineSaver_close_localSong();
	}

	if(enable_resample)
	{
		#if ENABLE_LIBSAMPLERATE_SRC
		SampleRateConvert_deinit();
		#elif ENABLE_SOXR_SRC
		soxr_deinit();
		#endif
	}

	printf("exit concentrated_play_wav_song...\n");
}



/*********************************************************************
 * @fn      audio_collector_play_song
 *
 * @brief   播放音频采集
 *
 * @param   NULL
 *
 *
 * @return  none
*********************************************************************/
void audio_collector_play_song(void)
{
    printf("enter audio_collector_play_song...\n");
    int decode_count=0;
    int total_decode_size=0;


	#if SUPPORT_CODEC_G722
	G722_DEC_CTX *g722_dctx = NULL;
	int16_t *g722_obuf=NULL;
	#endif
	if(g_network_mode == NETWORK_MODE_WAN)
	{
		#if SUPPORT_CODEC_G722
		int srate = ~ G722_SAMPLE_RATE_8000;
		g722_obuf=(int16_t *)malloc( G722_BUFFER_SIZE * 4 );
		if(g722_obuf == NULL)
		{
			printf("g722_obuf malloc failed\r\n");
			g_ac_exit_flag = 0;
			return;
		}
		g722_dctx=g722_decoder_new(64000, srate);
		if (g722_dctx == NULL) {
			printf("g722_decoder_new() failed\r\n");
			free(g722_obuf);
			g722_obuf=NULL;

			g_ac_exit_flag = 0;
			return;
		}
		printf("init g722 decoder OK...\r\n");
		#endif
	}

	int acChannelId=GetAudioCollectorChannelBySrc(g_media_source);
	while( !app_play_need_exit && !g_ac_exit_flag && (g_media_source >= SOURCE_AUDIO_COLLECTOR_BASE && g_media_source <= SOURCE_AUDIO_COLLECTOR_MAX) )
	{
		if(concentrated_pcm_buf_valid[concentrated_read_pos])
		{	
			bool canPlay=false;
			if(g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet)
			{
				if(acChannelId == 1)	//第一通道MIC，优先保证时延。
				{
					if(!concentrated_write_need_wait)	//播放缓存正常，直接播放
					{
						canPlay=true;
					}
					else if(concentrated_write_pos_total-concentrated_read_pos_total>=2) //播放缓存不足，需要等待更多数据：1个包含两帧数据，8ms*2=16ms，16ms*4=64ms
					{
						canPlay=true;
					}
				}
				else	//不是第一通道（MIC)
				{
					if(!concentrated_write_need_wait)	//播放缓存正常，直接播放
					{
						canPlay=true;
					}
					else if(concentrated_write_pos_total-concentrated_read_pos_total>=5)	//播放缓存不足，需要等待更多数据：1个包含两帧数据，8ms*2=16ms，16ms*5=80ms
					{
						canPlay=true;
					}
				}
			}
			else
			{
				if(acChannelId == 1)	//第一通道MIC，优先保证时延。
				{
					if(!concentrated_write_need_wait)	//播放缓存正常，直接播放
					{
						canPlay=true;
					}
					else if(concentrated_write_pos_total-concentrated_read_pos_total>=5) //播放缓存不足，需要等待更多数据：1个包含两帧数据，8ms*2=16ms，16ms*4=80ms
					{
						canPlay=true;
					}
				}
				else	//不是第一通道（MIC)
				{
					if(!concentrated_write_need_wait)	//播放缓存正常，直接播放
					{
						canPlay=true;
					}
					else if(concentrated_write_pos_total-concentrated_read_pos_total>=15)	//播放缓存不足，需要等待更多数据：1个包含两帧数据，8ms*2=16ms，16ms*15=240ms
					{
						canPlay=true;
					}
				}
			}
			if(canPlay)
			{
				int decode_once_count=2;
				decode_count++;
				if(g_network_mode == NETWORK_MODE_WAN)
				{
					//G722
					#if SUPPORT_CODEC_G722
					int outlen=g722_decode(g722_dctx, concentrated_pcm_buf[concentrated_read_pos], concentrated_pcm_buf_len[concentrated_read_pos], g722_obuf);
					memcpy(pcm_data_decode+total_decode_size, g722_obuf,outlen*2);
					total_decode_size += outlen*2;
					#endif
				}
				else
				{
					memcpy(pcm_data_decode+total_decode_size, concentrated_pcm_buf[concentrated_read_pos], concentrated_pcm_buf_len[concentrated_read_pos]);
					total_decode_size += concentrated_pcm_buf_len[concentrated_read_pos];
				}
				if(decode_count >= decode_once_count)
				{
					Audio_Write(pcm_data_decode, total_decode_size);
					memset(pcm_data_decode, 0x00, sizeof(pcm_data_decode));
					total_decode_size = 0;
					decode_count = 0;
				}
				memset(concentrated_pcm_buf[concentrated_read_pos], 0x00, sizeof(concentrated_pcm_buf[concentrated_read_pos]));
				concentrated_pcm_buf_valid[concentrated_read_pos] = 0;
				concentrated_read_pos++;
				concentrated_read_pos_total++;
				if(concentrated_read_pos >= CONCENTRATED_MODE_DATA_BUF_MAX)
					concentrated_read_pos = 0;
			}
		}
        usleep(2000);
	}
	g_ac_exit_flag = 0;

	#if SUPPORT_CODEC_G722
	if(g722_obuf)
	{
		free(g722_obuf);
		g722_obuf=NULL;
	}
	if(g722_dctx)
	{
		g722_decoder_destroy(g722_dctx);
    	g722_dctx=NULL;
	}
	#endif

	printf("exit audio_collector_play_song...\n");
}



/*********************************************************************
 * @fn      Pager_Play_Song
 *
 * @brief   播放寻呼歌曲
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Pager_Play_Song(void)
{
    int decode_count = 0;
    int total_decode_size = 0;
    
    #if SUPPORT_CODEC_G722
    int srate = ~ G722_SAMPLE_RATE_8000;

	G722_DEC_CTX *g722_dctx = NULL;
	int16_t *g722_obuf=NULL;
	if(pager_property.decodeType == DECODE_G722)
	{
		g722_obuf=(int16_t *)malloc( G722_BUFFER_SIZE * 4 );
		if(g722_obuf == NULL)
		{
			printf("g722_obuf malloc failed\n");
			return;
		}
		g722_dctx=g722_decoder_new(64000, srate);
		if (g722_dctx == NULL) {
			printf("g722_decoder_new() failed\n");
			free(g722_obuf);
			g722_obuf=NULL;
			return;
		}
	}
	#endif


	#if SUPPORT_CODEC_G7221
	DECODER_CONTROL control;
	Word16  mag_shift;
	Word16	old_mag_shift=0;
	Bit_Obj bitobj;
	Rand_Obj randobj;
	Word16 frame_error_flag = 0;

	Word16  *output=NULL;
	Word16  *decoder_mlt_coefs=NULL;
	Word16	*old_decoder_mlt_coefs=NULL;
	Word16	*old_samples=NULL;

	control.bandwidth = 14000;
	control.bit_rate = 48000;
	control.frame_size = 640;
	control.number_of_bits_per_frame = 960;
	control.number_of_regions = 28;

	Word16 number_of_16bit_words_per_frame = (Word16)(control.number_of_bits_per_frame/16);

    int i=0;
	if( pager_property.decodeType == DECODE_G722_1 )
	{
		output = (Word16*)malloc(MAX_DCT_LENGTH*sizeof(Word16));
		decoder_mlt_coefs = (Word16*)malloc(MAX_DCT_LENGTH*sizeof(Word16));
		old_decoder_mlt_coefs = (Word16*)malloc(MAX_DCT_LENGTH*sizeof(Word16));
		old_samples = (Word16*)malloc((MAX_DCT_LENGTH*sizeof(Word16))>>1);

		for(i=0;i<control.frame_size;i++)
		{
			old_decoder_mlt_coefs[i] = 0;
		}
		for(i=0;i<(control.frame_size >> 1);i++)
		{
			old_samples[i] = 0;
		}

		randobj.seed0 = 1;
		randobj.seed1 = 1;
		randobj.seed2 = 1;
		randobj.seed3 = 1;
		#if MEASURE_WMOPS
		Init_WMOPS_counter ();
		#endif
	}
	#endif

	int temp_pagingType=PAGING_TYPE_NULL;
	int delay_flag=0;
	int init_ok=0;

	int delay_cnt=0;
	concentrated_write_need_wait=0;

	printf("Pager_Play_Song start...\n");

	while(!app_play_need_exit)
	{
		pthread_mutex_lock(&mutex_cleanPagerStream);
		if(pager_pcm_buf_valid[pager_read_pos])
		{
			if(temp_pagingType!=pager_property.pagingType)
			{
				printf("temp_pagingType=%d,pager_property.pagingType=%d\r\n",temp_pagingType,pager_property.pagingType);
				temp_pagingType=pager_property.pagingType;
				if(temp_pagingType == PAGING_TYPE_MUSIC)
				{
					delay_flag=1;
					init_ok=0;
					if(g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet)
					{
						init_ok=1;
					}
					else if(pager_write_pos_total-pager_read_pos_total>=PAGER_PCM_BUF_MAX-10)
					{
						init_ok=1;
						printf("temp_pagingType == PAGING_TYPE_MUSIC,init OK=1\r\n");
					}
				}
			}
			if(pager_property.pagingType != PAGING_TYPE_MUSIC)
			{
				delay_flag=0;
				#if NETWORK_VPN_INTERNET
				if(pager_write_pos_total-pager_read_pos_total>=15)
				{
					init_ok=1;
				}
				#else
				init_ok=1;
				#endif
			}
			if(delay_flag && !init_ok)
			{
				//保留
				if(pager_write_pos_total-pager_read_pos_total>=PAGER_PCM_BUF_MAX-10)
				{
					init_ok=1;
					printf("temp_pagingType == PAGING_TYPE_MUSIC,init2 OK=1\r\n");
				}
			}
			else
			{
				if( delay_flag )
				{
					delay_cnt = PAGER_PCM_BUF_MAX-20;
				}
				else
				{
					#if NETWORK_VPN_INTERNET
					delay_cnt = 15;
					#else
					if(g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet)
					{
						if(pager_property.pagingType == PAGING_TYPE_APP)
						{
							delay_cnt = 4;
						}
						else
						{
							delay_cnt = 2;
						}
					}
					else
					{
						delay_cnt = 4;
					}
					#endif
				}
				
				if( (!concentrated_write_need_wait || (pager_write_pos_total-pager_read_pos_total>=delay_cnt)))
				{
					if(concentrated_write_need_wait)
					{
						concentrated_write_need_wait=0;
					}
					if(pager_property.decodeType == DECODE_STANDARD_PCM)
					{
						decode_count++;
						memcpy(pcm_data_decode+total_decode_size, pager_pcm_buf[pager_read_pos], pager_pcm_buf_len[pager_read_pos]);
						total_decode_size += pager_pcm_buf_len[pager_read_pos];
						if(decode_count >= 1)
						{
							Audio_Write((char*)pcm_data_decode, total_decode_size);
							//memset(pcm_data_decode,0,PCM_DATA_SIZE_MAX);
							total_decode_size = 0;
							decode_count = 0;
						}
						memset(pager_pcm_buf[pager_read_pos], 0x00, sizeof(pager_pcm_buf[pager_read_pos]));
						pager_pcm_buf_valid[pager_read_pos] = 0;
						pager_read_pos++;
						if(pager_read_pos >= PAGER_PCM_BUF_MAX)
						pager_read_pos = 0;
					}
					#if SUPPORT_CODEC_G722
					else if(pager_property.decodeType == DECODE_G722)
					{
						int outlen=g722_decode(g722_dctx, pager_pcm_buf[pager_read_pos], pager_pcm_buf_len[pager_read_pos], g722_obuf);
						Audio_Write((char*)g722_obuf, outlen*2);      
						//printf("pos=%d,pcm_buf_len=%d,outlen=%d\r\n",pager_read_pos,pager_pcm_buf_len[pager_read_pos],outlen);

						memset(pager_pcm_buf[pager_read_pos], 0x00, sizeof(pager_pcm_buf[pager_read_pos]));
						pager_pcm_buf_valid[pager_read_pos] = 0;
						pager_read_pos++;
						if(pager_read_pos >= PAGER_PCM_BUF_MAX)
							pager_read_pos = 0;
					}
					#endif

					#if SUPPORT_CODEC_G7221
					else if(pager_property.decodeType == DECODE_G722_1)
					{
						bitobj.code_word_ptr = (Word16*)(pager_pcm_buf+pager_read_pos);
						bitobj.current_word = *(Word16*)(pager_pcm_buf+pager_read_pos);
						bitobj.code_bit_count = 0;
						bitobj.number_of_bits_left = control.number_of_bits_per_frame;

						#if MEASURE_WMOPS
						Reset_WMOPS_counter();
						#endif

						/* process the out_words into decoder_mlt_coefs */
						decoder(&bitobj,
								&randobj,
								control.number_of_regions,
								decoder_mlt_coefs,
								&mag_shift,
								&old_mag_shift,
								old_decoder_mlt_coefs,
								frame_error_flag);
						
						/* convert the decoder_mlt_coefs to samples */
						rmlt_coefs_to_samples(decoder_mlt_coefs, old_samples, output, control.frame_size, mag_shift);

						/* For ITU testing, off the 2 lsbs. */
						for (i=0; i<control.frame_size; i++)
							output[i] &= 0xfffc;

						Audio_Write((char*)output, control.frame_size*2);      
						//printf("audio_write_len=%d\r\n",control.frame_size*2);

						memset(pager_pcm_buf[pager_read_pos], 0x00, sizeof(pager_pcm_buf[pager_read_pos]));
						pager_pcm_buf_valid[pager_read_pos] = 0;
						pager_read_pos++;
						if(pager_read_pos >= PAGER_PCM_BUF_MAX)
							pager_read_pos = 0;
					}
					#endif

					pager_read_pos_total++; 
				}
			}
		}
		pthread_mutex_unlock(&mutex_cleanPagerStream);

		if(g_media_source != SOURCE_NET_PAGING )
		{
			pager_need_exit_flag = 1;
		}
		
		if(pager_need_exit_flag)
		{
			pager_need_exit_flag = 0;
			break;
		}
		usleep(2000);
	}

	#if SUPPORT_CODEC_G722
	if(g722_obuf)
	{
		free(g722_obuf);
		g722_obuf=NULL;
	}
	if(g722_dctx)
	{
		g722_decoder_destroy(g722_dctx);
    	g722_dctx=NULL;
	}
	#endif

	#if SUPPORT_CODEC_G7221
	if(output)
	{
		free(output);
		output=NULL;
	}
	if(decoder_mlt_coefs)
	{
		free(decoder_mlt_coefs);
		decoder_mlt_coefs=NULL;
	}
	if(old_decoder_mlt_coefs)
	{
		free(old_decoder_mlt_coefs);
		old_decoder_mlt_coefs=NULL;
	}
	if(old_samples)
	{
		free(old_samples);
		old_samples=NULL;
	}

	if(pager_property.decodeType == DECODE_G722_1)
	{
		#if MEASURE_WMOPS
		WMOPS_output (0);
		#endif
	}
	printf("Pager_Play_Song End...\n");
	#endif
}


/*********************************************************************
 * @fn      audio_mixer_play_song
 *
 * @brief   播放混音音源
 *
 * @param   NULL
 *
 *
 * @return  none
*********************************************************************/
void audio_mixer_play_song(void)
{
	int decode_count = 0;
    int total_decode_size = 0;
    
    #if SUPPORT_CODEC_G722
    int srate = ~ G722_SAMPLE_RATE_8000;

	G722_DEC_CTX *g722_dctx = NULL;
	int16_t *g722_obuf=NULL;
	if(g_network_mode == NETWORK_MODE_WAN)
	{
		g722_obuf=(int16_t *)malloc( G722_BUFFER_SIZE * 4 );
		if(g722_obuf == NULL)
		{
			printf("g722_obuf malloc failed\n");
			return;
		}
		g722_dctx=g722_decoder_new(64000, srate);
		if (g722_dctx == NULL) {
			printf("g722_decoder_new() failed\n");
			free(g722_obuf);
			g722_obuf=NULL;
			return;
		}
	}
	#endif

	int temp_pagingType=PAGING_TYPE_NULL;
	int delay_flag=0;
	int init_ok=0;

	int delay_cnt=0;
	concentrated_write_need_wait=0;

	while(!app_play_need_exit && !mixed_need_exit_flag && g_media_source == SOURCE_AUDIO_MIXED)
	{
		pthread_mutex_lock(&mutex_cleanMixedStream);
		if(audioMixer_stream_valid[audioMixer_stream_read_pos]		)
		{	
			bool canPlay=false;
			if(g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet)
			{
				if(g_audio_mixer_signalType == MIXER_SIGNAL_TYPE_MIC)	//MIC，优先保证时延。
				{
					if(!concentrated_write_need_wait)	//播放缓存正常，直接播放
					{
						canPlay=true;
					}
					else if(audioMixer_stream_write_pos_total-audioMixer_stream_read_pos_total>=2) //播放缓存不足，需要等待更多数据：1个包含两帧数据，8ms*2=16ms，16ms*4=64ms
					{
						canPlay=true;
					}
				}
				else	//不是第一通道（MIC)
				{
					if(!concentrated_write_need_wait)	//播放缓存正常，直接播放
					{
						canPlay=true;
					}
					else if(audioMixer_stream_write_pos_total-audioMixer_stream_read_pos_total>=5)	//播放缓存不足，需要等待更多数据：1个包含两帧数据，8ms*2=16ms，16ms*5=80ms
					{
						canPlay=true;
					}
				}
			}
			else
			{
				if(g_audio_mixer_signalType == MIXER_SIGNAL_TYPE_MIC)	//MIC，优先保证时延。
				{
					if(!concentrated_write_need_wait)	//播放缓存正常，直接播放
					{
						canPlay=true;
					}
					else if(audioMixer_stream_write_pos_total-audioMixer_stream_read_pos_total>=5) //播放缓存不足，需要等待更多数据：1个包含两帧数据，8ms*2=16ms，16ms*4=80ms
					{
						canPlay=true;
					}
				}
				else	//不是第一通道（MIC)
				{
					if(!concentrated_write_need_wait)	//播放缓存正常，直接播放
					{
						canPlay=true;
					}
					else if(audioMixer_stream_write_pos_total-audioMixer_stream_read_pos_total>=15)	//播放缓存不足，需要等待更多数据：1个包含两帧数据，8ms*2=16ms，16ms*15=240ms
					{
						canPlay=true;
					}
				}
			}

			if(canPlay)
			{
				int decode_once_count=2;
				decode_count++;
				if(g_network_mode == NETWORK_MODE_WAN)
				{
					if(!g_Is_tcp_real_internet)
					{
						decode_once_count=2;
					}
				}
				else
				{
					decode_once_count=2;
				}

				if(g_audio_mixer_codecs == DECODE_G722)
				{
					#if SUPPORT_CODEC_G722
					if(g722_dctx)
					{
						int outlen=g722_decode(g722_dctx, audioMixer_stream_buf[audioMixer_stream_read_pos], audioMixer_stream_len[audioMixer_stream_read_pos], g722_obuf);
						memcpy(pcm_data_decode+total_decode_size, g722_obuf,outlen*2);
						total_decode_size += outlen*2;
					}
					#endif
				}
				else if(g_audio_mixer_codecs == DECODE_STANDARD_PCM)
				{
					memcpy(pcm_data_decode+total_decode_size, audioMixer_stream_buf[audioMixer_stream_read_pos], audioMixer_stream_len[audioMixer_stream_read_pos]);
					total_decode_size += audioMixer_stream_len[audioMixer_stream_read_pos];
				}

				if(decode_count >= decode_once_count)
				{
					Audio_Write(pcm_data_decode, total_decode_size);
					memset(pcm_data_decode, 0x00, sizeof(pcm_data_decode));
					total_decode_size = 0;
					decode_count = 0;
				}
				memset(audioMixer_stream_buf[audioMixer_stream_read_pos], 0x00, sizeof(audioMixer_stream_buf[audioMixer_stream_read_pos]));
				audioMixer_stream_valid[audioMixer_stream_read_pos] = 0;
				audioMixer_stream_read_pos++;
				audioMixer_stream_read_pos_total++;
				if(audioMixer_stream_read_pos >= AUDIO_MIXER_STREAM_BUF_MAX)
					audioMixer_stream_read_pos = 0;
			}
		}
		pthread_mutex_unlock(&mutex_cleanMixedStream);
        usleep(2000);
	}
	mixed_need_exit_flag=0;

	#if SUPPORT_CODEC_G722
	if(g722_obuf)
	{
		free(g722_obuf);
		g722_obuf=NULL;
	}
	if(g722_dctx)
	{
		g722_decoder_destroy(g722_dctx);
    	g722_dctx=NULL;
	}
	#endif

	printf("exit audio_mixer_play_song...\n");
}



/*********************************************************************
 * @fn      audio_phone_gateway_song
 *
 * @brief   播放电话网关音源
 *
 * @param   NULL
 *
 *
 * @return  none
*********************************************************************/
void audio_phone_gateway_song(void)
{
	int decode_count = 0;
    int total_decode_size = 0;
    
    #if SUPPORT_CODEC_G722
    int srate = ~ G722_SAMPLE_RATE_8000;

	G722_DEC_CTX *g722_dctx = NULL;
	int16_t *g722_obuf=NULL;
	if(g_network_mode == NETWORK_MODE_WAN)
	{
		g722_obuf=(int16_t *)malloc( G722_BUFFER_SIZE * 4 );
		if(g722_obuf == NULL)
		{
			printf("g722_obuf malloc failed\n");
			return;
		}
		g722_dctx=g722_decoder_new(64000, srate);
		if (g722_dctx == NULL) {
			printf("g722_decoder_new() failed\n");
			free(g722_obuf);
			g722_obuf=NULL;
			return;
		}
	}
	#endif

	int delay_flag=0;
	int init_ok=0;

	int delay_cnt=0;
	concentrated_write_need_wait=0;

	while(!app_play_need_exit && !phone_gateway_need_exit_flag && g_media_source == SOURCE_PHONE_GATEWAY)
	{
		pthread_mutex_lock(&mutex_cleanPhoneGatewayStream);
		if(phoneGateway_stream_valid[phoneGateway_stream_read_pos]		)
		{	
			bool canPlay=false;
			if(g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet)
			{
				if(!concentrated_write_need_wait)	//播放缓存正常，直接播放
				{
					canPlay=true;
				}
				else if(phoneGateway_stream_write_pos_total-phoneGateway_stream_read_pos_total>=2) //播放缓存不足，需要等待更多数据：1个包含两帧数据，8ms*2=16ms，16ms*4=64ms
				{
					canPlay=true;
				}
			}
			else
			{
				if(!concentrated_write_need_wait)	//播放缓存正常，直接播放
				{
					canPlay=true;
				}
				else if(phoneGateway_stream_write_pos_total-phoneGateway_stream_read_pos_total>=5) //播放缓存不足，需要等待更多数据：1个包含两帧数据，8ms*2=16ms，16ms*4=80ms
				{
					canPlay=true;
				}
			}

			if(canPlay)
			{
				int decode_once_count=2;
				decode_count++;
				if(g_network_mode == NETWORK_MODE_WAN)
				{
					if(!g_Is_tcp_real_internet)
					{
						decode_once_count=2;
					}
				}
				else
				{
					decode_once_count=2;
				}

				if(g_phone_gateway_codecs == DECODE_G722)
				{
					#if SUPPORT_CODEC_G722
					if(g722_dctx)
					{
						int outlen=g722_decode(g722_dctx, phoneGateway_stream_buf[phoneGateway_stream_read_pos], phoneGateway_stream_len[phoneGateway_stream_read_pos], g722_obuf);
						memcpy(pcm_data_decode+total_decode_size, g722_obuf,outlen*2);
						total_decode_size += outlen*2;
					}
					#endif
				}
				else if(g_phone_gateway_codecs == DECODE_STANDARD_PCM)
				{
					memcpy(pcm_data_decode+total_decode_size, phoneGateway_stream_buf[phoneGateway_stream_read_pos], phoneGateway_stream_len[phoneGateway_stream_read_pos]);
					total_decode_size += phoneGateway_stream_len[phoneGateway_stream_read_pos];
				}

				if(decode_count >= decode_once_count)
				{
					Audio_Write(pcm_data_decode, total_decode_size);
					memset(pcm_data_decode, 0x00, sizeof(pcm_data_decode));
					total_decode_size = 0;
					decode_count = 0;
				}
				memset(phoneGateway_stream_buf[phoneGateway_stream_read_pos], 0x00, sizeof(phoneGateway_stream_buf[phoneGateway_stream_read_pos]));
				phoneGateway_stream_valid[phoneGateway_stream_read_pos] = 0;
				phoneGateway_stream_read_pos++;
				phoneGateway_stream_read_pos_total++;
				if(phoneGateway_stream_read_pos >= PHONE_GATEWAY_STREAM_BUF_MAX)
					phoneGateway_stream_read_pos = 0;
			}
		}
		pthread_mutex_unlock(&mutex_cleanPhoneGatewayStream);
        usleep(2000);
	}
	mixed_need_exit_flag=0;

	#if SUPPORT_CODEC_G722
	if(g722_obuf)
	{
		free(g722_obuf);
		g722_obuf=NULL;
	}
	if(g722_dctx)
	{
		g722_decoder_destroy(g722_dctx);
    	g722_dctx=NULL;
	}
	#endif

	printf("exit audio_phone_gateway_song...\n");
}