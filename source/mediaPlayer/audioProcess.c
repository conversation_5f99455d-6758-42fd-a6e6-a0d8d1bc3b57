/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2022-02-23 16:25:26 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-02-23 19:57:09
 */
#include "sysconf.h"
#include "mpg123.h"
#include "audioProcess.h"


//不精准，弃用此算法，改用AED分贝检测替代
int get_signal_db(int16_t *pcmData16,uint16_t len16)
{
    int ndb = 0;
    short int value;
    int i;
    double v = 0;
    for(i=0; i<len16;i++)
    {
        v += abs(*(pcmData16+i));
    }
    v = v/(len16);
    if(v != 0) {
        ndb = (int)(20.0*log10((double)v / 32767.0 ));
    }
    else {
        ndb = -96;
    }
    return ndb;
}


int16_t limit_value_16bit(int32_t value)
{
    int16_t result;
    if(value<-32767)
    {
        result = -32767;
    }
    else if(value>32766)
    {
        result=32766;
    }
    else
    {
        result=(int16_t)value;
    }
    return result;
}

static void AudioMix(char ** sourseFile,int number,char *objectFile, int bufferLength){
    //归一化混音
    int const MAX = 32766;
    int const MIN = -32767;
    
    double f = 1;
    int output;
    for (int i = 0; i < bufferLength/2; i++)
    {
        int temp = 0;
        for (int j = 0; j < number; j++)
        {
            
            char *point = sourseFile[j];
            
            if (j == 0) {
                int mixTemp = *(short *)(point + i*2);
                temp += (int)(mixTemp);
            }else{
                temp += *(short *)(point + i*2);
            }
        }
        
        output = (int)(temp * f);
        
        if (output > MAX)
        {
            f = (double)MAX / (double)(output);
            output = MAX;
        }
        if (output < MIN)
        {
            f = (double)MIN / (double)(output);
            output = MIN;
        }
        if (f < 1)
        {
	    //此处取SETPSIZE 为 32
            f += ((double)1 - f) / (double)32;
        }
        *(short *)(objectFile + i*2) = (short)output;
    }
}

void pcmAudioMix2(int16_t *desBuffer,int16_t *bufferA, int16_t *bufferB, int bufferLength){
    char * sourseFile[2];
    sourseFile[0] = (char *)bufferA;
    sourseFile[1] = (char *)bufferB;
    bufferLength *= 2;
    AudioMix(sourseFile, 2, (char *)desBuffer, bufferLength);
}

void pcmAudioMix3(int16_t *desBuffer,int16_t *bufferA, int16_t *bufferB, int16_t *bufferC, int bufferLength){
    char * sourseFile[3];
    sourseFile[0] = (char *)bufferA;
    sourseFile[1] = (char *)bufferB;
    sourseFile[2] = (char *)bufferC;
    bufferLength *= 2;
    AudioMix(sourseFile, 3, (char *)desBuffer, bufferLength);
}

void pcmAudioMix4(int16_t *desBuffer,int16_t *bufferA, int16_t *bufferB, int16_t *bufferC, int16_t *bufferD, int bufferLength){
    char * sourseFile[4];
    sourseFile[0] = (char *)bufferA;
    sourseFile[1] = (char *)bufferB;
    sourseFile[2] = (char *)bufferC;
    sourseFile[3] = (char *)bufferD;
    bufferLength *= 2;
    AudioMix(sourseFile, 4, (char *)desBuffer, bufferLength);
}



/*
	双转单
	参数1：资源buffer(双声道数据)
	参数2：资源buffer大小(单声道数据大小，注意：是buffer中音频数据帧数，而不是字节数)
	参数3：转换后buffer(单声道数据)
*/
void StereoToMono(const int16_t* src_audio,int frames,int16_t* dst_audio) 
{
    for (int i = 0; i < frames; i++) 
    {
    	//注意 >>1 只有在有符号整形的情况下才代表除以2
        dst_audio[i] = ((int)src_audio[2 * i] + src_audio[2 * i + 1]) >> 1;
    }
}

/*
	单转双
	参数1：资源buffer(单声道数据)
	参数2：资源buffer大小(单声道数据大小，注意：是buffer中音频数据帧数，而不是字节数)
	参数3：转换后buffer(双声道数据)
*/
void MonoToStereo(const int16_t* src_audio,int frames,int16_t* dst_audio) 
{
    for (int i = 0; i < frames; i++) 
    {
        dst_audio[2 * i] = src_audio[i];
        dst_audio[2 * i + 1] = src_audio[i];
    }
}


/*
	相位翻转
	参数1：音频buf
    参数2：帧数
    LR：0代表只翻转左声道 1代表只翻转右声道 2代表翻转左右声道 
*/
void PhaseInvert(int16_t* buf,int frames,int LRChannel) 
{
    for (int i = 0; i < frames; i++)
    {
        if( LRChannel == PHASE_INVERT_LEFT && ( (i%2) == 0 ) )  //偶数
        {
            buf[i] *= -1;
        }
        else if( LRChannel == PHASE_INVERT_RIGHT && ( (i%2) != 0 ))   //奇数
        {
           buf[i] *= -1;
        }
        else if( LRChannel == PHASE_INVERT_STERO)
        {
            buf[i] *= -1;
        }
       
        buf[i] = limit_value_16bit(buf[i]);
    }
}


/*
	PCM单声道音频流48K转16K
	参数1：资源buffer(单声道数据)
	参数2：资源buffer大小(单声道数据大小，注意：是buffer中音频数据帧数，而不是字节数)
	参数3：转换后buffer(双声道数据)
*/
void PCM_Mono_48KTo16K(const int16_t* src_audio,int frames,int16_t* dst_audio) 
{
    int pos=0;
    for (int i = 0; i < frames; i+=3)
    {
        dst_audio[pos++] = src_audio[i];
    }
    #if 0
    //如果是立体声,一个采样点4个字节，两个short int型
    for (int i = 0; i < frames; i+=3)
    {
        dst_audio[pos] = src_audio[i*2];
        dst_audio[pos+1] = src_audio[i*2+1];
        pos+=2;
    }
    #endif
}
