#ifndef _CONST_H_
#define _CONST_H_

#include "network_protocol.h"
#include "sigmastar/audio_params.h"
#include "extension/extension.h"

extern int g_device_moduleId;				    	//设备型号ID
extern int g_Is_tcp_real_internet;					//TCP模式是否处于internet公网上（主机IP是169开头或者192开头代表是内网TCP）

#define CURRENT_DEVICE_MODEL	MODEL_IP_SPEAKER_D		//配置应用设备型号

#define LZY_CUSTOMER_MQTT					1		//启用LZY客户MQTT

#define APP_ALL_GENERAL             0
#define APP_AISP_GENERAL            1
#define APP_JUSBE_GENERAL           2
#define APP_AIPU_GENERAL            20

#if YIHUI_VERSION
#define APP_THEME_COLOR APP_ALL_GENERAL	//可以根据客制化进行修改：APP_AISP_GENERAL或者APP_JUSBE_GENERAL
#elif AIPU_VERSION
#define APP_THEME_COLOR APP_AIPU_GENERAL	//固定，不能修改
#else
#define APP_THEME_COLOR APP_ALL_GENERAL		//固定，不能修改
#endif

#define LOCAL_FIRMWARE_VERSION		"2.5.0725"		//固件版本号


#define IS_DEVICE_DECODER_TERMINAL	(CURRENT_DEVICE_MODEL == MODEL_IP_SPEAKER_D || CURRENT_DEVICE_MODEL == MODEL_IP_SPEAKER_F)		//解码终端S006
#define IS_DEVICE_AUDIO_COLLECTOR	(CURRENT_DEVICE_MODEL == MODEL_AUDIO_COLLECTOR_B || CURRENT_DEVICE_MODEL == MODEL_AUDIO_COLLECTOR_C || CURRENT_DEVICE_MODEL == MODEL_AUDIO_COLLECTOR_F)	//音频采集器S014
#define IS_DEVICE_FIRE_COLLECTOR	(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_B || CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_C || CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_F)	//消防采集器S015
#define IS_DEVICE_POWER_SEQUENCE	(CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_B || CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_C || CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_F)	//电源时序器S016
#define IS_DEVICE_AUDIO_MIXER		(CURRENT_DEVICE_MODEL == MODEL_AUDIO_MIXER_ENCODER || CURRENT_DEVICE_MODEL == MODEL_AUDIO_MIXER_ENCODER_C)	//音频中继器S017
#define IS_DEVICE_REMOTE_CONTROLER	(CURRENT_DEVICE_MODEL == MODEL_REMOTE_CONTROLER || CURRENT_DEVICE_MODEL == MODEL_REMOTE_CONTROLER_C || CURRENT_DEVICE_MODEL == MODEL_REMOTE_CONTROLER_F)	//远程遥控器S018
#define IS_DEVICE_INTERCOM_TERMINAL	(CURRENT_DEVICE_MODEL == MODEL_IP_SPEAKER_E)		//对讲终端S007
#define IS_DEVICE_PHONE_GATEWAY		(CURRENT_DEVICE_MODEL == MODEL_PHONE_GATEWAY)		//电话网关S021
#define IS_DEVICE_AMP_CONTROLER		(CURRENT_DEVICE_MODEL == MODEL_AMP_CONTROLER)		//功放控制器S036
#define IS_DEVICE_NOISE_DETECTOR	(CURRENT_DEVICE_MODEL == MODEL_NOISE_DETECTOR)		//噪声检测器S037
#define IS_DEVICE_GPS_SYNCHRONIZER	(CURRENT_DEVICE_MODEL == MODEL_GPS_SYNCHRONIZER)	//GPS校时器S038

#if !(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_AUDIO_COLLECTOR || IS_DEVICE_FIRE_COLLECTOR || IS_DEVICE_POWER_SEQUENCE\
	 || IS_DEVICE_AUDIO_MIXER || IS_DEVICE_REMOTE_CONTROLER || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_PHONE_GATEWAY\
	 || IS_DEVICE_AMP_CONTROLER || IS_DEVICE_NOISE_DETECTOR || IS_DEVICE_GPS_SYNCHRONIZER)
#error Device Model Error!
#endif


#if (YIHUI_VERSION && !(CURRENT_DEVICE_MODEL == MODEL_IP_SPEAKER_F || CURRENT_DEVICE_MODEL == MODEL_AUDIO_COLLECTOR_F\
							|| CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_F || CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_F\
							|| CURRENT_DEVICE_MODEL == MODEL_REMOTE_CONTROLER_F) )
#error Customer Selected YH_SERIES,but the Device Model Error!
#endif

#if (!YIHUI_VERSION && (CURRENT_DEVICE_MODEL == MODEL_IP_SPEAKER_F || CURRENT_DEVICE_MODEL == MODEL_AUDIO_COLLECTOR_F\
							|| CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_F || CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_F\
							|| CURRENT_DEVICE_MODEL == MODEL_REMOTE_CONTROLER_F) )
#error the Device Model Selected YH_SERIES,But Customer Error!
#endif

//不能用enum定义，否则在编译阶段会报错
#define DISPLAY_TYPE_NULL 0
#define DISPLAY_TYPE_SPI_2P1_320X240 1	//SPI 2.1寸屏 AIPU
#define DISPLAY_TYPE_SPI_1P9_320X170 2	//SPI 1.9寸屏 YH
#define DISPLAY_TYPE_RGB_4P3_480X272 3	//RGB 4.3寸屏 YH

#ifndef	SELECTED_DISPLAY_TYPE
	#if AIPU_VERSION
	#define SELECTED_DISPLAY_TYPE DISPLAY_TYPE_SPI_2P1_320X240
	#elif YIHUI_VERSION
		#if(CURRENT_DEVICE_MODEL == MODEL_IP_SPEAKER_F)
		#define SELECTED_DISPLAY_TYPE DISPLAY_TYPE_RGB_4P3_480X272		//可选（编译的时候注意选择）
		#elif(CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_F)
		#define SELECTED_DISPLAY_TYPE DISPLAY_TYPE_RGB_4P3_480X272		//固定
		#else
		#define SELECTED_DISPLAY_TYPE DISPLAY_TYPE_SPI_1P9_320X170		//固定
		#endif
	#else
	#define SELECTED_DISPLAY_TYPE	DISPLAY_TYPE_NULL
	#endif

	#if USE_PC_SIMULATOR
	#undef SELECTED_DISPLAY_TYPE
	#define SELECTED_DISPLAY_TYPE DISPLAY_TYPE_RGB_4P3_480X272
	#endif
#endif


#if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)

#define POWER_P13_2x10W	1	//AD52068
#define POWER_P13_2x20W_OLD	2	//AMP经过TPF605A,未割线版本，噪声大
#define POWER_P13_2x20W	3	//AD52068
#define POWER_P13_2x30W	4	//AD52090
#define POWER_P13_2x20W_V20_E 5	//V20硬件版本(未使用，需跳过该定义)
#define POWER_P13_2x20W_V20 6	//V20,仍是解码终端D
#define POWER_P13_2x20W_V22 7	//V22,仍是解码终端D
#define POWER_P13_2x20W_LED 8	//V22,UART-LED
#define POWER_P13_2x20W_V25	9	//V25,经过运放

#define POWER_P17_2x10W 100	//AD52068
#define POWER_P17_2x15W 101	//AD52068
#define POWER_P17_2x20W_V11 102	//AD52068
#define POWER_P17_2x30W_OLD 103	//AD52090	//AMP经过TPF605A,未割线版本，噪声大
#define POWER_P17_2x30W_V11 104	//AD52090
#define POWER_P17_2x20W_V12 105	//AD52090
#define POWER_P17_2x30W_V12 106	//AD52090
#define POWER_P17_2x15W_V12 107	//AD52068
#define POWER_P21_2x30W_V10 110	//P021V10 AD52090
#define POWER_P21_2x15W_V10 111	//P021V10 AD52068
#define POWER_P21_1x30W_V10 112	//P021V10 AD52068
#define POWER_P21_2x20W_V10 113	//P021V10 AD52068
#define POWER_P15_2x33W_V10 140	//P015V10 3116

#define POWER_P21_2x18W_V20	150	//P021V20,GPIO跟P28一致

#define POWER_P16_NORMAL	200
#define POWER_P16_LZY		201

#define POWER_P19_INTERCOM_V10	300

#define POWER_P20_2x30W		500		//4G板
#define POWER_P20_2x30W_V12	501		//4G板V12
#define POWER_P20_2x30W_780E_V20	510		//4G板V20(AIR780E)

/**********************************************/
#define POWER_P26_NORMAL	2000
#define POWER_P26_LZY		2001
#define POWER_P26_V12		2002		//P26从此版本开始支持SPI显示

#define POWER_P28_18W		2100

#define POWER_P29_5W		2120
/*********************************************/

#define POWER_P31_RGB_4P3	2200	//易会4.3寸屏功放+电源时序器使用
#define POWER_P31_SPI_1P9	2201	//易会1.9寸屏


#if UPGRADE_PACKAGE
	#define DEFAULT_MODULE_ID	(POWER_P13_2x20W_OLD)	//升级包需固定为POWER_P13_2x20W_OLD,不可更改

#else
	#define DEFAULT_MODULE_ID	POWER_P28_18W		//烧录包根据实际情况更改
#endif

#define IS_DECODER_DEVICE	(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)

#ifndef USE_PC_SIMULATOR
#define IS_MODEL_WOODEN	 	(IS_DEVICE_DECODER_TERMINAL && ( (g_device_moduleId>=POWER_P13_2x10W && g_device_moduleId<=POWER_P13_2x10W+98) ||\
								(g_device_moduleId == POWER_P17_2x20W_V11 && dsp_firmware_feature.module_switch[0] && dsp_firmware_feature.module_switch[1] && dsp_firmware_feature.module_switch[2]) ) )
#else
#define IS_MODEL_WOODEN	 	0
#endif

#define IS_MODEL_OUTDOOR 	(!IS_MODEL_WOODEN && g_device_moduleId>=POWER_P17_2x10W && g_device_moduleId<=POWER_P15_2x33W_V10)

#define IS_MODEL_DECODER_NORMAL	(g_device_moduleId == POWER_P16_NORMAL)
#define IS_MODEL_DECODER_LZY  	(g_device_moduleId == POWER_P16_LZY)
#define IS_MODEL_DECODER		(IS_MODEL_DECODER_NORMAL || IS_MODEL_DECODER_LZY)

#define IS_MODEL_INTERCOM_NORMAL (g_device_moduleId == POWER_P19_INTERCOM_V10)

#define IS_MODEL_WITH_4G		 (g_device_moduleId == POWER_P20_2x30W || g_device_moduleId == POWER_P20_2x30W_V12 || g_device_moduleId == POWER_P20_2x30W_780E_V20)


#define IS_NEED_WEBRTC_LINE_DEVICE ( (g_device_moduleId>=POWER_P13_2x10W && g_device_moduleId<=POWER_P13_2x20W_V20) ||\
									 (g_device_moduleId>=POWER_P17_2x10W && g_device_moduleId<=POWER_P17_2x30W_V11) )


//NEW
#define IS_MODEL_SIMPLE_DECODER	 (g_device_moduleId == POWER_P26_NORMAL || g_device_moduleId == POWER_P26_LZY || g_device_moduleId == POWER_P26_V12)
#define IS_MODEL_SIMPLE_AMP		 (g_device_moduleId == POWER_P28_18W || g_device_moduleId == POWER_P29_5W || g_device_moduleId == POWER_P21_2x18W_V20)

#define IS_MODEL_P26_V10		 (g_device_moduleId == POWER_P26_NORMAL || g_device_moduleId == POWER_P26_LZY)
#define IS_MODEL_P26_V12		 (g_device_moduleId == POWER_P26_V12)

#define IS_SIMPLE_AMP_SUPPORT_INTERCOM	(IS_MODEL_SIMPLE_AMP && IS_EXTENSION_HAS_INTERCOM)
#define IS_DEVICE_SUPPORT_INTERCOM	(IS_DEVICE_INTERCOM_TERMINAL || IS_SIMPLE_AMP_SUPPORT_INTERCOM)


#define IS_DEVICE_YIHUI_SERIES_BOARD	(g_device_moduleId == POWER_P31_RGB_4P3 || g_device_moduleId == POWER_P31_SPI_1P9)


#if IS_DEVICE_INTERCOM_TERMINAL
	#define IS_SUPPORT_SPEECH_RECOGNITION 	0		//启用语音识别
#endif

#else
	#define IS_DECODER_DEVICE		0
	#define IS_MODEL_WOODEN  		0
	#define IS_MODEL_OUTDOOR 		0
	#define IS_MODEL_DECODER_NORMAL 0
	#define IS_MODEL_DECODER_LZY    0
	#define IS_MODEL_DECODER 		0
	#define IS_MODEL_INTERCOM_NORMAL 0
	#define IS_MODEL_SIMPLE_DECODER 0
	#define IS_MODEL_SIMPLE_AMP		0
	#define IS_DEVICE_SUPPORT_INTERCOM	0
	#define IS_SIMPLE_AMP_SUPPORT_INTERCOM	0
	#define IS_MODEL_P26_V10			0
	#define IS_MODEL_P26_V12			0

	#if IS_DEVICE_PHONE_GATEWAY
	#define IS_MODEL_WITH_4G		1
	#else
	#define IS_MODEL_WITH_4G		0
	#endif

	#if IS_DEVICE_POWER_SEQUENCE
		#define MODEL_SEQUENCE_POWER_NORMAL 	0
		#define	MODEL_SEQUENCE_POWER_WEISHENG 	1
		#define MODEL_SEQUENCE_POWER_AIPU		2

		#if CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_C
		#define CUR_POWER_SEQUENCE_TYPE MODEL_SEQUENCE_POWER_AIPU
		#else
		#define CUR_POWER_SEQUENCE_TYPE MODEL_SEQUENCE_POWER_NORMAL
		#endif
	#endif

#endif


#define IS_ENABLE_ONLINESAVER   (g_Is_tcp_real_internet)

#define ENBALE_WATCHDOG			1	  //开启看门狗

#if IS_DEVICE_DECODER_TERMINAL
#define SUPPORT_VOLUME_CONTROL_UART		0		//是否使用音控器串口
#define SUPPORT_TM1640_DISPLAY			0		// TM1640数码管显示
#else
#define SUPPORT_VOLUME_CONTROL_UART		0		//是否使用音控器串口
#define SUPPORT_TM1640_DISPLAY			0		// TM1640数码管显示
#endif

#define SUPPORT_CODEC_G722      1     //支持G722编解码
#define SUPPORT_CODEC_G7221     1     //支持G722.1编解码
#define ENABLE_TCP_CLIENT		1	   //开启TCP
#define ENABLE_LIBSAMPLERATE_SRC				0	   //开启AO采样率转换(LIBSAMPLERATE)
#define ENABLE_SOXR_SRC							1	   //开启AO采样率转换(SOXR)
#define ENABLE_SPEEX_DENOISE	1	  //开启speex噪声抑制
#define ENABLE_WEBRTC_DENOISE	1	  //开启WEBRTC噪声抑制

#define AUDIO_PHASE_INVERT		1	  //音频相位翻转

#define DECODER_RELAY_TRIGGER_MUSIC	0	//1代表解码板P16有音乐信号时触发继电器，否则消防才触发(默认是消防)

#define DECODER_LZY_VERESION_FM_C4A1 	0		//龙之音调频版本(C4A1),关闭功放，调频信号高电平时切换到本地音源（调频），否则网络音源
#define DECODER_LZY_VERESION_FM_C4A2 	0		//龙之音大功率电源版本(C4A2),外挂功放，信号控制脚IO94翻转控制
#if DECODER_LZY_VERESION_FM_C4A1
	#define LZY_FM_SIGNAL_DETECT_GPIO	PAD_SD_D3	//调频信号检测脚
#endif

#define NETWORK_VPN_INTERNET		0		//VPN INTERNET环境，g_Is_tcp_real_internet固定为1

#define LOCAL_SOURCE_PRIORITY_HIGHEST	0	//本地音源最高优先级

#define SUPPORT_UART_LED_PLAYER_MODE	2	//	0-关闭 1-强制启用 2-自动，根据g_device_moduleId处理

#define CANCEL_PAGER_SOURCE_PRIORITY	0	// 1-取消寻呼台的音源优先级，默认0

#define SUPPORT_HTTP_SERVER				1	//是否内建http服务器

#define SUPPORT_TTS						1	//是否支持TTS
#define SUPPORT_FFMPEG					1	//是否支持FFMPEG

#if IS_DEVICE_DECODER_TERMINAL
#define SUPPORT_SIP						1	//是否支持SIP
#define SUPPORT_INFORMATION_PUBLISH		1	//是否支持信息发布
#endif


#if AIPU_VERSION
#undef ENABLE_DISP_UART_MODULE
#define ENABLE_DISP_UART_MODULE 		0
#undef SUPPORT_DISPLAY
#define SUPPORT_DISPLAY				1	//艾普版本，默认支持屏幕显示
#undef DECODER_RELAY_TRIGGER_MUSIC
#define DECODER_RELAY_TRIGGER_MUSIC	0	//艾普版本，解码板P26 V12有消防信号才触发(默认是消防)
#elif LZY_COMMERCIAL_VERSION
#undef ENABLE_DISP_UART_MODULE
#define ENABLE_DISP_UART_MODULE 		0
#undef SUPPORT_DISPLAY
#define SUPPORT_DISPLAY				0	//是否支持屏幕显示
#elif YIHUI_VERSION
#undef ENABLE_WEBRTC_DENOISE
#define ENABLE_WEBRTC_DENOISE			0	//易会版本默认关闭WEBRTC噪声抑制
#undef ENABLE_DISP_UART_MODULE
#define ENABLE_DISP_UART_MODULE 		0
#undef SUPPORT_DISPLAY
#define SUPPORT_DISPLAY				1	//易会版本默认支持屏幕显示

#define SUPPORT_UDISK_PLAY			0	//易会版本是否支持USB播放
#define SUPPORT_BASS_TREBLE_CONTROL		0	//易会版本是否支持高低音调节
#else
	#if SUPPORT_VOLUME_CONTROL_UART || SUPPORT_TM1640_DISPLAY
	#define ENABLE_DISP_UART_MODULE 0
	#else
	#define ENABLE_DISP_UART_MODULE	( (IS_DEVICE_DECODER_TERMINAL && (IS_MODEL_DECODER_NORMAL)) || IS_DEVICE_AUDIO_COLLECTOR || IS_DEVICE_FIRE_COLLECTOR || IS_DEVICE_POWER_SEQUENCE || IS_DEVICE_AUDIO_MIXER || IS_DEVICE_REMOTE_CONTROLER)	  //开启易会智能显示模块串口通讯
	#endif

	#undef SUPPORT_DISPLAY
	#define SUPPORT_DISPLAY				0	//不支持屏幕显示
#endif


#if ENABLE_LIBSAMPLERATE_SRC
#include "mediaPlayer/SampleRateConvert.h"
#elif ENABLE_SOXR_SRC
#include "mediaPlayer/soxr_t.h"
#endif


#ifdef USE_PC_SIMULATOR
#undef SUPPORT_TTS
#define SUPPORT_TTS						0	//不支持TTS
#undef SUPPORT_FFMPEG
#define SUPPORT_FFMPEG					0	//不支持FFMPEG
#undef SUPPORT_SIP
#define SUPPORT_SIP						0	//不支持SIP

#undef SUPPORT_DISPLAY
#define SUPPORT_DISPLAY				1	//支持屏幕显示
#endif


//定义INI文件路径
#ifdef USE_PC_SIMULATOR
#define CONFIG_DIR_PATH				  "/home/<USER>/app/Config"
#define BASIC_CONFIG_INI_FILE         "/home/<USER>/app/Config/basic_config"
#define DEVICE_CONFIG_INI_FILE        "/home/<USER>/app/Config/device_config"
#define NETWORK_CONFIG_INI_FILE       "/home/<USER>/app/Config/network_config"
#define DSP_FIRMWARE_CONFIG_INI_FILE  "/home/<USER>/app/Config/dsp_firmware_config"
#define DSP_EQ_CONFIG_INI_FILE        "/home/<USER>/app/Config/dsp_eq_config"
#define MFR_CONFIG_INI_FILE        	  "/home/<USER>/app/Config/mfr_config"
#define BLUETOOTH_CONFIG_INI_FILE     "/home/<USER>/app/Config/bluetooth_config"
#define SEQUENCEPOWER_CONFIG_INI_FILE "/home/<USER>/app/Config/sequence_power_config"
#define FIRE_CONFIG_INI_FILE 		  "/home/<USER>/app/Config/fire_config"
#define INTERCOM_CONFIG_INI_FILE 	  "/home/<USER>/app/Config/intercom_config"
#define TRIGGER_CONFIG_INI_FILE		  "/home/<USER>/app/Config/trigger_config"
#define COLLECTOR_CONFIG_INI_FILE	  "/home/<USER>/app/Config/collector_config"
#define MIXER_CONFIG_INI_FILE		  "/home/<USER>/app/Config/mixer_config"
#define PHONE_GATEWAY_CONFIG_INI_FILE "/home/<USER>/app/Config/phone_gateway_config"
#define INI_SIP_INI_FILE	  		  "/home/<USER>/app/Config/sip_config"
#define INI_EXTENSION_INI_FILE	  	  "/home/<USER>/app/Config/extension_config"
#define INI_INFORMATION_PUB_INI_FILE  "/home/<USER>/app/Config/information_pub_config"
#define INI_REMOTE_INI_FILE  	      "/home/<USER>/app/Config/remote_config"
#define INI_NOISE_DETECTOR_INI_FILE   "/home/<USER>/app/Config/noise_detector_config"
#else
#define CONFIG_DIR_PATH				  "/customer/App/Config"
#define BASIC_CONFIG_INI_FILE         "/customer/App/Config/basic_config"
#define DEVICE_CONFIG_INI_FILE        "/customer/App/Config/device_config"
#define NETWORK_CONFIG_INI_FILE       "/customer/App/Config/network_config"
#define DSP_FIRMWARE_CONFIG_INI_FILE  "/customer/App/Config/dsp_firmware_config"
#define DSP_EQ_CONFIG_INI_FILE        "/customer/App/Config/dsp_eq_config"
#define MFR_CONFIG_INI_FILE           "/customer/App/Config/mfr_config"
#define BLUETOOTH_CONFIG_INI_FILE     "/customer/App/Config/bluetooth_config"
#define SEQUENCEPOWER_CONFIG_INI_FILE "/customer/App/Config/sequence_power_config"
#define FIRE_CONFIG_INI_FILE 		  "/customer/App/Config/fire_config"
#define INTERCOM_CONFIG_INI_FILE 	  "/customer/App/Config/intercom_config"
#define TRIGGER_CONFIG_INI_FILE		  "/customer/App/Config/trigger_config"
#define COLLECTOR_CONFIG_INI_FILE	  "/customer/App/Config/collector_config"
#define MIXER_CONFIG_INI_FILE		  "/customer/App/Config/mixer_config"
#define PHONE_GATEWAY_CONFIG_INI_FILE "/customer/App/Config/phone_gateway_config"
#define INI_SIP_INI_FILE	  		  "/customer/App/Config/sip_config"
#define INI_EXTENSION_INI_FILE	  	  "/customer/App/Config/extension_config"
#define INI_INFORMATION_PUB_INI_FILE  "/customer/App/Config/information_pub_config"
#define INI_REMOTE_INI_FILE  	      "/customer/App/Config/remote_config"
#define INI_NOISE_DETECTOR_INI_FILE   "/customer/App/Config/noise_detector_config"
#endif

#define INI_SECTION_BASIC   		"Basic"
#define INI_SECTION_DEVICE  		"Device"
#define INI_SETCION_NETWORK 		"Network"
#define INI_SETCION_DSP_Firmware 	"Firmware"
#define INI_SETCION_DSP_EQ 			"Eq"
#define INI_SETCION_MFR 			"Manufacturer"
#define INI_SETCION_BLUETOOTH 		"BlueTooth"
#define INI_SETCION_SEQUENCEPOWER 	"SequencePower"
#define INI_SETCION_FIRE 			"Fire"
#define INI_SECTION_INTERCOM		"Intercom"
#define INI_SECTION_TRIGGER			"Trigger"
#define INI_SECTION_COLLECTOR		"Collector"
#define INI_SECTION_MIXER			"Mixer"
#define INI_SECTION_PHONE_GATEWAY	"PhoneGateway"
#define INI_SECTION_EXTENSION		"Extension"
#define INI_SECTION_INFORMATIONPUB	"InformationPub"
#define INI_SECTION_REMOTE			"Remote"
#define INI_SECTION_NOISE_DETECTOR	"Noise"

/********网络相关*****************/
#define NETWORK_MODE_LAN	1
#define NETWORK_MODE_WAN	2

#define IP_ASSIGN_DHCP 		0
#define IP_ASSIGN_STATIC    1

/************************/

#define HOST_TIMEOUT_VALUE		60	  //主机超时时间

/********优先级定义*******************/
enum
{
	#if LOCAL_SOURCE_PRIORITY_HIGHEST
	PRIORITY_AUX,
	#endif
  	PRIORITY_NET_PAGING,								//寻呼
	PRIORITY_FIRE_ALARM,								//火警信号
	PRIORITY_MONITOR_EV,								//监控事件
	PRIORITY_CALL,										//对讲
	PRIORITY_SIP,						    			//SIP对讲
	PRIORITY_AUDIO_MIXED,								//音频混音
	PRIORITY_PHONE_GATEWAY,								//电话网关
	PRIORITY_API_TTS_MUSIC,								//API播放TTS或者音乐
	PRIORITY_AUDIO_HIGH_PRIORITY,						//音频采集音源(高优先级)
	PRIORITY_TIMING,									//定時
	PRIORITY_AUDIO_COLLECTOR,							//音频采集音源
	PRIORITY_NET_PLAY,									//网络点播
	PRIORITY_100V,										//100v
	#if !LOCAL_SOURCE_PRIORITY_HIGHEST
	PRIORITY_AUX,										//AUX
	#endif
    PRIORITY_NULL,										//空闲
	PRIORITY_MAX_NUM									//MAX
};

typedef enum
{
	DF_BLUETOOTH		=   0X00000001,	// 蓝牙设备
    DF_LOCAL_MONITOR	=   0X00000002, // 本地监听
    DF_CALL             =   0X00000004, // 对讲
	DF_VIDEO            =   0x00000008, // 可视
	DF_SIP            	=   0x00000010, // SIP
	DF_INFORMATION_PUBLISH =   0x00000020 // 信息发布
}Device_Feature;

//设备拓展特性，包含消防采集C、电源时序器C、远程遥控器C、音频采集器C（如果P030主板，则默认支持，无需启用）、音频混音器C
typedef enum
{
	DF_EXTRA_FIRE_COLLECTOR	  =   0X00000001,	// 消防采集器
    DF_EXTRA_POWER_SEQUENCE   =   0X00000002, 	// 电源时序器
    DF_EXTRA_REMOTE_CONTROLER =   0X00000004, 	// 远程遥控器 
	DF_EXTRA_AUDIO_COLLECTOR  =   0x00000008, 	// 音频采集器（P030主板默认支持，无需启用）
	DF_EXTRA_AUDIO_MIXER      =   0x00000010, 	// 音频混音器
	DF_EXTRA_GPS_SYNCHRONIZER =   0x00000020, 	// GPS校时器
}Device_ExtraFeature;


typedef enum
{
	CHINESE=0,
	ENGLISH
}language_type_t;

#endif