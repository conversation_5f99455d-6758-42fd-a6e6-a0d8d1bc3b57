#ifndef _DEBUG_H
#define _DEBUG_H
#include <sys/types.h>
#include <stdio.h>
#include <stdlib.h>
#include <sys/stat.h>
#include <string.h>
#include "my_log.h" //系统日志写入

#define LOG_FLAG 		0X01
#define DBG_FLAG 		0X02
#define NOTICE_FLAG 	0X04
#define WARNING_FLAG 	0X08
#define ERROR_FLAG 		0X10

#define CURRENT_OUT_FLAG 	(0|LOG_FLAG|DBG_FLAG|NOTICE_FLAG|WARNING_FLAG|ERROR_FLAG)


#define PRINTF_RED 			"\033[1m\033[47;31m"
#define PRINTF_NORMAL 		"\033[0m"

//操作信息输出日志
#define OPT(PrioritySourceName,programName,vol)  WriteRecordInfo(PrioritySourceName,programName,vol)

//调试信息是输出
#define DBG(...) {\
if(DBG_FLAG& CURRENT_OUT_FLAG ){\
char _bf[1024] = {0}; \
int len=snprintf(_bf, 1023,"\r\n[DEBUG ](%s, %s(), %d): ", __FILE__, __FUNCTION__, __LINE__);\
snprintf(&_bf[len],1023-len,__VA_ARGS__);\
printf("%s\r\n",_bf);\
}\
}

//提示输出
#define NOTICE(...){\
if(NOTICE_FLAG& CURRENT_OUT_FLAG ){\
char _bf[1024] = {0}; \
int len=snprintf(_bf, 1023,"[NOTICE ]: ");\
snprintf(&_bf[len],1023-len,__VA_ARGS__);\
printf("%s\r\n",_bf);\
}\
}
//警告
#define WARNINGP(...){\
if(WARNING_FLAG& CURRENT_OUT_FLAG ){\
char _bf[1024] = {0}; \
memset(_bf,0,1024);\
int len=snprintf(_bf, 1023,"\r\n[\033[1m\033[47;31mWARNING\033[0m ](%s(), %d): ", __FUNCTION__, __LINE__);\
snprintf(&_bf[len],1023-len,__VA_ARGS__);\
printf("%s\r\n",_bf);\
}\
}


#if 0
//严重错误 	红色\033[1m\033[47;31m%d \033[0m
#define ERRORP(...){\
if(ERROR_FLAG& CURRENT_OUT_FLAG ){\
char _bf[1024] = {0}; \
int len=snprintf(_bf, 1023,"\r\n[\033[1m\033[47;31mERROR\033[0m ](%s(), %d): ", __FUNCTION__, __LINE__);\
snprintf(&_bf[len],1023-len,__VA_ARGS__);\
printf("%s\r\n",_bf);System_log("ERROR",&_bf[len],__FILE__,__LINE__);\
}\
}


#define LOG(...) {\
if(LOG_FLAG& CURRENT_OUT_FLAG ){\
char _bf[1024] = {0}; \
int len=snprintf(_bf, 1023,"[LOG ](%s(),%d): ",__FUNCTION__, __LINE__);\
snprintf(&_bf[len],1023-len,__VA_ARGS__);\
printf("%s\r\n",_bf);System_log("LOG",&_bf[len],__FILE__,__LINE__);\
}\
}

#else

//严重错误 	红色\033[1m\033[47;31m%d \033[0m
#define ERRORP(...){\
if(ERROR_FLAG& CURRENT_OUT_FLAG ){\
char _bf[1024] = {0}; \
int len=snprintf(_bf, 1023,"\r\n[\033[1m\033[47;31mERROR\033[0m ](%s(), %d): ", __FUNCTION__, __LINE__);\
snprintf(&_bf[len],1023-len,__VA_ARGS__);\
printf("%s\r\n",_bf);\
}\
}


#define LOG(...) {\
if(LOG_FLAG& CURRENT_OUT_FLAG ){\
char _bf[1024] = {0}; \
int len=snprintf(_bf, 1023,"[LOG ](%s(),%d): ",__FUNCTION__, __LINE__);\
snprintf(&_bf[len],1023-len,__VA_ARGS__);\
printf("%s\r\n",_bf);\
}\
}


#endif


#endif