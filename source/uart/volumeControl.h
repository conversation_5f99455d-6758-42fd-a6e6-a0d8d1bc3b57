#ifndef __VOLUME_CONTROL_H_
#define __VOLUME_CONTROL_H_

#include <stdbool.h>
#include "const.h"

extern int g_exist_volumeControl_device;

#define UART_VolumeControl_DEVICE     "/dev/ttyS1" //uart device name

#define VOLUME_CONTROL_UART_DATA_LENGTH_MAX	   256
#define VOLUME_CONTROL_UART_RECEIVE_BUFFER_MAX (VOLUME_CONTROL_UART_DATA_LENGTH_MAX)

#define VC_UART_REG_ADDR_CONFIG 0x1003
#define VC_UART_LIGHT_CONFIG    0x1008

bool Uart_VolumeControl_Init(void);


#endif