#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <termios.h>
#include <pthread.h>

#include "const.h"
#include "uartCommon.h"

int g_bluetooth_Uartfd;

int Init_Serial_Port(char *dev,int baud)
{
    int fd, res;
    struct termios  oldtio, newtio;
    char  ch;
    char buf[256] = {0};

    //open uart dev file, not set O_NONBLOCK attr
    fd = open(dev, O_RDWR | O_NOCTTY);
    if (fd < 0) {
        perror(dev);
        return -1;
    }
    else
        printf("Open %s successfully\n", dev);

    //get current operation parameter
    tcgetattr(fd, &oldtio);
    memset(&newtio, 0, sizeof(newtio));

    //buad = 460800,data type = 8bit,enable receive mode
    newtio.c_cflag = baud | CS8 | CLOCAL | CREAD;
    
    //fuart enable cts/rts
    //newtio.c_cflag |= CRTSCTS;
    newtio.c_cflag &= ~CRTSCTS;  // 明确禁用流控
    
    //ignore parity sum frame errors
    newtio.c_iflag = IGNPAR;

    //enable output options
    //newtio.c_oflag = OPOST;

    //set normal mode
    //newtio.c_lflag = ICANON;
#if 0
2.VTIME和VMIN 决定了read()函数什么时候返回
1．当VTIME>0，VMIN>0时。read调用将保持阻塞直到读取到第一个字符，读到了第一个字符之后开始计时，此后若时间到了VTIME或者时间未到但已读够了VMIN个字符则会返回；若在时间未到之前又读到了一个字符(但此时读到的总数仍不够VMIN)则计时重新开始。
2. 当VTIME>0，VMIN=0时。read调用读到数据则立即返回，否则将为每个字符最多等待VTIME时间。
3. 当VTIME=0，VMIN>0时。read调用一直阻塞，直到读到VMIN个字符后立即返回。
4. 若在open或fcntl设置了O_NDELALY或O_NONBLOCK标志，read调用不会阻塞而是立即返回，那么VTIME和VMIN就没有意义，效果等同于与把VTIME和VMIN都设为了0。
VTIME 和  VMIN
VTIME  定义要求等待的零到几百毫秒的值(通常是一个8位的unsigned char变量)。
VMIN 定义了要求等待的最小字节数, 这个字节数可能是0。
只有设置为阻塞时这两个参数才有效，仅针对于读操作。
#endif

    //此处很重要，避免BP1048丢包，最少接收到20个字符或者等待10ms
    newtio.c_cc[VTIME] = 10;
    #if SUPPORT_VOLUME_CONTROL_UART
    newtio.c_cc[VMIN] = 8;
    #elif CURRENT_DEVICE_MODEL == MODEL_REMOTE_CONTROLER_C
    newtio.c_cc[VMIN] = 3;
    #elif CURRENT_DEVICE_MODEL == MODEL_AMP_CONTROLER
    newtio.c_cc[VMIN] = 26;
    #elif CURRENT_DEVICE_MODEL == MODEL_GPS_SYNCHRONIZER || CURRENT_DEVICE_MODEL == MODEL_SEQUENCE_POWER_C
    newtio.c_cc[VMIN] = 0;
    #else
    newtio.c_cc[VMIN] = 0;
    #endif

    //flush input and output cache
    tcflush(fd, TCIFLUSH);
    //set new operation parameter
    tcsetattr(fd, TCSANOW, &newtio);

    return fd;
}


/*********************************************************************
 * @fn      Checksum
 *
 * @brief   计算数据累加和校验和
 *
 * @param   Data - 校验数据
 *			Length - 校验数据长度
 *
 * @return  Xordata - 校验和
 */
unsigned char Checksum(unsigned char cmd,int len,unsigned char *buf)
{
	int i = 0;
	int sum = 0;
	sum = cmd + (len>>8)+(len);
	for (i = 0; i < len; i++) {
		sum += buf[i];
	}
	return (unsigned char)sum;
}


unsigned short crc16_ccitt(unsigned char *pbBuf, unsigned int dwLen)
{
    unsigned short wCRC=0;
	unsigned char  bCycle;
	while (dwLen-- > 0) {
		wCRC ^= (unsigned short) *pbBuf++ << 8;
		for (bCycle = 0; bCycle < 8; bCycle++){
			if (wCRC & 0x8000){
				wCRC = wCRC << 1 ^ 0x1021;
			}else{
				wCRC <<= 1;
			}
		}
	}
	return wCRC;
}


unsigned short crc16_ccitt_volumeControl(unsigned char *data, unsigned int len) 
{ 
   unsigned short crc = 0xFFFF;
    unsigned int i, j;

    for (i = 0; i < len; i++) {
        crc ^= (unsigned short)data[i];
        for (j = 0; j < 8; j++) {
            if (crc & 0x0001)
                crc = (crc >> 1) ^ 0xA001;
            else
                crc >>= 1;
        }
    }
    // 进行字节序调整
    //crc = ((crc << 8) & 0xFF00) | ((crc >> 8) & 0x00FF);
    return crc;
} 