#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <termios.h>
#include <pthread.h>

#include "sysconf.h"
#include "uartCommon.h"
#include "remote.h"

#if CURRENT_DEVICE_MODEL == MODEL_REMOTE_CONTROLER_C

static int g_remote_Uartfd;

static int manual_key_change=0;

/*********************************************************************
 * @fn      Recv_Uart_Remote_Pthread
 *
 * @brief   消防模块串口数据接收线程(回调函数)
 *
 * @param   void
 *
 * @return  none
 */
void *Recv_Uart_Remote_Pthread(void)
{
	printf("Recv_Uart_Remote_Pthread...\n");
	int Rxlen;
	int ret, i;
	int Index = 0;
	fd_set readfd;
	struct timeval timeout;
	int max_fd;
	int Pkg_Length=0;
	int Read_Size = 3;

	unsigned char Rxbuf[MAX_UART_BUF_SIZE]={0};


	FD_ZERO(&readfd);               //清空读文件描述集合
	FD_SET(g_remote_Uartfd, &readfd);  //注册套接字文件描述符

	while (1)
	{
		timeout.tv_sec = 0;      // 获取数据超时时间设置
		timeout.tv_usec = 100000;

		FD_ZERO(&readfd);               //清空读文件描述集合
		FD_SET(g_remote_Uartfd, &readfd);  //注册套接字文件描述符
		ret = select(g_remote_Uartfd+1, &readfd, NULL, NULL, &timeout);
		switch(ret)
		{
			case -1 : //调用出错
				perror("select");
				Index = 0;
				Read_Size = 3;
				memset(Rxbuf,0, MAX_UART_BUF_SIZE);
				break;

			case 0 : //超时
				//printf("timeout!\n");
				Index = 0;
				Read_Size = 3;
				memset(Rxbuf,0, MAX_UART_BUF_SIZE);
				break;

			default : //判断是否有数据可读

				/*接收对应串口的数据*/

				Rxlen = read(g_remote_Uartfd, &Rxbuf[Index], Read_Size);

				/*数据校验*/
				if (Rxlen < 0 || Rxlen != 3)
				{
					perror("Rxlen<0\n");
					Index = 0;
					Read_Size = 3;
					memset(Rxbuf,0, MAX_UART_BUF_SIZE);
					continue;
				}
				else
				{

					#if 0
					printf("Terminal:The Recv Data Is : \n");
					for (i = 0; i < Rxlen; i++)
					{
						printf("0x%x ", Rxbuf[i+Index]);
					}
					printf("\n");
					#endif
		

					Read_Size = 3;
					Index = 0;
					
					unsigned char addressCodeLow=Rxbuf[0];
					unsigned char addressCodeHigh=Rxbuf[1];
					unsigned short keyCode=Rxbuf[2];
					
					memset(Rxbuf,0, MAX_UART_BUF_SIZE);

					if(addressCodeHigh!=g_remote_controler_addressCode)
					{
						printf("addressCodeLow!=g_addressCode\n");
						continue;
					}
					
					int temp_keyCode=0;
					switch(keyCode)
					{
						case 0x3f:
							printf("KEY:1\n");
							temp_keyCode=1;
							break;
						case 0xf3:
							printf("KEY:2\n");
							temp_keyCode=2;
							break;
						case 0xcf:
							printf("KEY:3\n");
							temp_keyCode=3;
							break;
						case 0xfc:
							printf("KEY:4\n");
							temp_keyCode=4;
							break;
						case 0xf:
							printf("KEY:5\n");
							temp_keyCode=5;
							break;
						case 0xf0:
							printf("KEY:6\n");
							temp_keyCode=6;
							break;
						case 0x3c:
							printf("KEY:7\n");
							temp_keyCode=7;
							break;
						case 0xcc:
							printf("KEY:8\n");
							temp_keyCode=8;
							break;
						case 0xc:
							printf("KEY:9\n");
							temp_keyCode=9;
							break;
						case 0x3:
							printf("KEY:10\n");
							temp_keyCode=10;
							break;
						case 0xc3:
							printf("KEY:11\n");
							temp_keyCode=11;
							break;
						case 0x33:
							printf("KEY:12\n");
							temp_keyCode=12;
							break;
					}
					if(temp_keyCode)
					{
						//printf("temp_keyCode=%d\n",temp_keyCode);
						g_remote_controler_keyCode = temp_keyCode;
						manual_key_change=1;
						SendRemoteControlerKeyToServer(temp_keyCode);
					}
				}
				break;
		}
	}
	pthread_exit(NULL);
}




/*********************************************************************
 * @fn      Uart_Remote_Init
 *
 * @brief   初始化消防信号检测串口模块
 *
 * @param   void
 *
 * @return  ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
bool Uart_Remote_Init(void)
{
	int ret=-1;
	pthread_t t_uart_fire_Pthread;
	pthread_attr_t Pthread_TERMINAL_Attr;
	pthread_attr_init(&Pthread_TERMINAL_Attr);
	pthread_attr_setdetachstate(&Pthread_TERMINAL_Attr, PTHREAD_CREATE_DETACHED);

	#if(CURRENT_DEVICE_MODEL == MODEL_REMOTE_CONTROLER_C)
	g_remote_Uartfd = Init_Serial_Port(UART_REMOTE_DEVICE, B19200);
	#endif

    if(g_remote_Uartfd == -1)
    {
        return false;
    }
	/*创建一个线程单独接收终端串口数据*/
	pthread_create(&t_uart_fire_Pthread, &Pthread_TERMINAL_Attr, (void *)Recv_Uart_Remote_Pthread, NULL);
	pthread_attr_destroy(&Pthread_TERMINAL_Attr);
	return true;
}





void *remote_disp_key_refresh(void)
{
	bool isKeyPress=false;
	int changeEvent=0;	//0-准备阶段 1-低电平 2-按键抬起
	int changeTime=0;
	int temp_keyCode=g_remote_controler_keyCode;
	while(1)
	{
		if(temp_keyCode!=g_remote_controler_keyCode || manual_key_change)
		{
			temp_keyCode = g_remote_controler_keyCode;
			manual_key_change=0;
			changeTime=0;
			changeEvent=0;

			if(temp_keyCode!=0)
			{
				isKeyPress=true;
			}
			else
			{
				isKeyPress=false;
			}
		}

		if(isKeyPress)
		{
			changeTime++;
			if(changeEvent == 0)
			{
				Set_Gpio_High(PAD_KEY7);
				printf("Disp Show Key1...\n");
				display_update_mainWin_external();
				changeEvent=1;
			}
	
			if(changeTime == 10 )
			{
				if(changeEvent == 1)
				{
					Set_Gpio_Low(PAD_KEY7);
				}
			}
			if(changeTime == 50)
			{
				if(changeEvent == 1)
				{
					printf("Disp Show Key2...\n");
					if(g_remote_controler_keyCode)
					{
						g_remote_controler_keyCode=0;
						display_update_mainWin_external();
					}
				}
			}
		}

		usleep(20000);
	}
}

/*********************************************************************
 * @fn      remote_disp_key_refresh
 *
 * @brief   远程遥控器显示按键刷新线程
 *
 * @param   void
 *
 * @return  ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
void remote_disp_key_refresh_task(void)
{
	int ret=-1;
	pthread_t pid;
	pthread_attr_t Pthread_TERMINAL_Attr;
	pthread_attr_init(&Pthread_TERMINAL_Attr);
	pthread_attr_setdetachstate(&Pthread_TERMINAL_Attr, PTHREAD_CREATE_DETACHED);

	pthread_create(&pid, &Pthread_TERMINAL_Attr, (void *)remote_disp_key_refresh, NULL);
	pthread_attr_destroy(&Pthread_TERMINAL_Attr);
}


#endif