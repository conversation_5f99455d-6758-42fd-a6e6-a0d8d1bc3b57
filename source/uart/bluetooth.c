#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <termios.h>
#include <pthread.h>

#include "const.h"
#include "uartCommon.h"
#include "bluetooth.h"

static int g_bluetooth_Uartfd;

int g_exist_bluetooth_module=0; //是否存在蓝牙模块

st_bp1048_info g_bp1048_info;
st_g_bp1048_bt_info g_bp1048_bt_info;

extern unsigned char g_mac_addr[6];

void Bp1048_get_boot_status(unsigned short datalen,unsigned char *rcbuf);
void Bp1048_receive_ChipInfo(unsigned short datalen,unsigned char *rcbuf);
void Bp1048_Send_Set_Source(unsigned long source_type);
void Bp1048_Receive_Bluetooth(unsigned short datalen,unsigned char *rcbuf);

void Bp1048_Command_Proc( unsigned char cmd, unsigned short datalen,unsigned char *data )
{
	switch(cmd)
	{
		case BP1048_CMD_BOOT:
			Bp1048_get_boot_status(datalen,data);
		case BP1048_CMD_CHIP_INFO:
            Bp1048_receive_ChipInfo(datalen,data);
		break;
		#if 0
		case BP1048_CMD_BT_INFO:
			Bp1048_Receive_Bluetooth(datalen,data);
		#endif
	}
}


void Bp1048_uart_send(unsigned char cmd, unsigned short datalen,unsigned char *data) {
	unsigned char *send_buf=NULL;
	send_buf= (unsigned char*)malloc(BP1048_UART_RECEIVE_BUFFER_MAX);

	int pos = 0, i;
	send_buf[pos++] = BP1048_UART_FRAME_HEAD1;
	send_buf[pos++] = BP1048_UART_FRAME_HEAD2;
	send_buf[pos++] = cmd;
	send_buf[pos++] = datalen;
	send_buf[pos++] = datalen>>8;

	for (i = 0; i < datalen; i++) {
		send_buf[pos++] = data[i];
	}

	send_buf[pos++] = Checksum(cmd,datalen,data);

	write(g_bluetooth_Uartfd, send_buf, pos); //发送数据

#if 1
	printf("Bp1048_uart_send:cmd=0x%x,datalen=%d\r\n",cmd,datalen);

#else
	printf("Bp1048_uart_send:\r\n");
	for(i=0;i<pos;i++)
	{
		printf("%02x ",send_buf[i]);
	}
	printf("\r\n");
#endif

	free(send_buf);
}


void Bp1048_get_boot_status(unsigned short datalen,unsigned char *rcbuf)
{
    printf("Bp1048_get_boot_status:%d\r\n",rcbuf[0]);
	if(rcbuf[0] == 0)
	{
        g_exist_bluetooth_module=1;
		//获取蓝牙信息
		//Bp1048_Send_Get_Info(BP1048_CMD_BT_INFO);
		Bp1048_Send_Set_Bluetooth();
		Bp1048_Send_Set_Source(BP1048_BLUETOOTH_SOURCE);
    }
	else if(rcbuf[0] == 1)
	{
		Bp1048_Send_Set_Reboot();
	}
}


void Bp1048_receive_ChipInfo(unsigned short datalen,unsigned char *rcbuf)
{
	if(datalen <= 1)
		return;
	int pos=0,i=0;
	printf("Bp1048_receive_ChipInfo:datalen=%d\n",datalen);
	while(pos<datalen)
	{
		int ref_type = rcbuf[pos++];
		int ref_len = rcbuf[pos++];

		switch(ref_type)
		{
			case 0:			//UUID数据
		  			memcpy(g_bp1048_info.uuid,rcbuf+pos,ref_len);
			  	break;
			case 1:			//版本信息
			 		g_bp1048_info.firmware_type=*(rcbuf+pos);
			  		memcpy(g_bp1048_info.version,rcbuf+pos+1,ref_len-1);
					sprintf(g_bp1048_info.version,"%d.%d.%d",g_bp1048_info.version[0],g_bp1048_info.version[1],g_bp1048_info.version[2]);
		  		break;
		}
		pos+=ref_len;
	}
	printf("g_bp1048_info.uuid:");
	for(i=0;i<8;i++)
	{
		printf("%x ",g_bp1048_info.uuid[i]);
	}
	printf("\r\n");
	printf("g_bp1048_info.version:%s,type=%d\r\n",g_bp1048_info.version,g_bp1048_info.firmware_type);

    g_exist_bluetooth_module=1;
	//获取蓝牙信息
	//Bp1048_Send_Get_Info(BP1048_CMD_BT_INFO);
	Bp1048_Send_Set_Bluetooth();
	Bp1048_Send_Set_Source(BP1048_BLUETOOTH_SOURCE);
}


void Bp1048_Send_Get_Info(unsigned char cmd)
{
  	//数据长度为0代表获取
	Bp1048_uart_send(cmd,0,NULL);
}


void Bp1048_Send_Set_Bluetooth()
{
	int i=0;
  	unsigned char cmd = BP1048_CMD_BT_INFO;
	unsigned short datalen = 0;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };
	
	send_buf[datalen++] = 0;
    //增加唯一标识;
    char mod_btName[40]={0};
    int unique_addr=(g_mac_addr[3]<<16)+(g_mac_addr[4]<<8)+(g_mac_addr[5]);
    sprintf(mod_btName,"%s(%x)",g_bp1048_bt_info.name,unique_addr);
    printf("Bp1048_Send_Set_Bluetooth:name=%s,haspassword=%d,pin=%s!\r\n",mod_btName,g_bp1048_bt_info.hasPassword,g_bp1048_bt_info.password);
	send_buf[datalen++] = strlen(mod_btName);
	memcpy(send_buf+datalen,mod_btName,strlen(mod_btName));
	datalen+=strlen(mod_btName);
	send_buf[datalen++] = 1;
	if(g_bp1048_bt_info.hasPassword)
	{
		send_buf[datalen++] = BP1048_BLUETOOTH_PASSWORD_MAX_LENGTH;
		memcpy(send_buf+datalen,g_bp1048_bt_info.password,BP1048_BLUETOOTH_PASSWORD_MAX_LENGTH);
		datalen+=BP1048_BLUETOOTH_PASSWORD_MAX_LENGTH;
	}
	else
	{
		send_buf[datalen++] = 0;
	}
	
	Bp1048_uart_send(cmd,datalen,send_buf);
}


void Bp1048_Receive_Bluetooth(unsigned short datalen,unsigned char *rcbuf)
{
	int pos=0,i=0;
	st_g_bp1048_bt_info tmp_g_bp1048_bt_info;
	if(datalen == 0)	//如果只是应答设置，则不处理
		return;
	while(pos<datalen)
	{
		int ref_type = rcbuf[pos++];
		int ref_len = rcbuf[pos++];

		switch(ref_type)
		{
			case 0:			//蓝牙名称
		  		memcpy(tmp_g_bp1048_bt_info.name,rcbuf+pos,ref_len);
			  	break;
			case 1:			//蓝牙密码
				if(ref_len == 0)
				{
					tmp_g_bp1048_bt_info.hasPassword = 0;
				}
				else
				{
					tmp_g_bp1048_bt_info.hasPassword = 1;
					memcpy(tmp_g_bp1048_bt_info.password,rcbuf+pos,ref_len);
				}
		  		break;
		}
		pos+=ref_len;
	}
	
	//判断是否相等
	if(	!(strcmp(tmp_g_bp1048_bt_info.name,g_bp1048_bt_info.name) == 0 && tmp_g_bp1048_bt_info.hasPassword == g_bp1048_bt_info.hasPassword && \
		tmp_g_bp1048_bt_info.password,g_bp1048_bt_info.password == 0) )
	{
		printf("Bp1048_Receive_Bluetooth,Not Equal...\n");
		Bp1048_Send_Set_Bluetooth();
	}
	else
	{
		Bp1048_Send_Set_Source(BP1048_BLUETOOTH_SOURCE);
	}
}




void Bp1048_Send_Set_Reboot()
{
	Bp1048_uart_send(BP1048_CMD_REBOOT,0,NULL);
}

static void Bp1048_Send_Set_ChannelSource(st_bp1048_output_channel_info *output_channel_info)
{
	int i,j;
  	unsigned char cmd = BP1048_CMD_SOURCE;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };

	unsigned short datalen=0;
	for(i=0;i<BP1048_MAX_OUTPUT_CHANNEL_COUNT;i++)
	{
	  	int output_channel_id=0;
		switch(i)
		{
			case BP1048_OUTPUT_CHANNEL_DAC0_L:
			  output_channel_id=BP1048_CHANNEL_DAC0_L;
		  	break;
			case BP1048_OUTPUT_CHANNEL_DAC0_R:
			  output_channel_id=BP1048_CHANNEL_DAC0_R;
		  	break;
			case BP1048_OUTPUT_CHANNEL_I2S0_OUT_L:
			  output_channel_id=BP1048_CHANNEL_I2S0_OUT_L;
		  	break;
			case BP1048_OUTPUT_CHANNEL_I2S0_OUT_R:
			  output_channel_id=BP1048_CHANNEL_I2S0_OUT_R;
		  	break;
			case BP1048_OUTPUT_CHANNEL_I2S1_OUT_L:
			  output_channel_id=BP1048_CHANNEL_I2S1_OUT_L;
			break;
			case BP1048_OUTPUT_CHANNEL_I2S1_OUT_R:
			  output_channel_id=BP1048_CHANNEL_I2S1_OUT_R;
			break;
		}
		if(output_channel_info->output_channel_vaild[i])
		{
			send_buf[datalen++] = output_channel_id;
			int sub_len=datalen++;
			for(j=0;j<BP1048_MAX_CHANNEL_COUNT;j++)
			{
				if(output_channel_info->contain_channel_valid[i][j])
				{
					send_buf[datalen++] = BP1048_CHANNEL_Line5_L+j;
					send_buf[datalen++] = output_channel_info->contain_channel_gain[i][j];
					send_buf[sub_len]+=2;
				}
			}
			//由于BP1048判断逻辑错误，作如下变更：如果DAC0有效，但又没有选择LINE IN(LINE5 L)或者MIC1的情况下,需要将这两路输入增益设为-72dB，否则即使DAC没有选择LINE IN,也会有输出
			if(	i == BP1048_OUTPUT_CHANNEL_DAC0_L || i == BP1048_OUTPUT_CHANNEL_DAC0_R )
			{
				if(!output_channel_info->contain_channel_valid[i][BP1048_CHANNEL_Line5_L])
				{
					send_buf[datalen++] = BP1048_CHANNEL_Line5_L+BP1048_CHANNEL_Line5_L;
					send_buf[datalen++] = 72;	//-72dB
					send_buf[sub_len]+=2;
				}
				if(!output_channel_info->contain_channel_valid[i][BP1048_CHANNEL_MIC1])
				{
					send_buf[datalen++] = BP1048_CHANNEL_Line5_L+BP1048_CHANNEL_MIC1;
					send_buf[datalen++] = 72;	//-72dB
					send_buf[sub_len]+=2;
				}
			}
		}
		else if( i == BP1048_OUTPUT_CHANNEL_DAC0_L || i == BP1048_OUTPUT_CHANNEL_DAC0_R )//由于BP1048判断逻辑错误，作如下变更：如果DAC0有效，但又没有选择LINE IN(LINE5 L)或者MIC1的情况下,需要将这两路输入增益设为-72dB，否则即使DAC没有选择LINE IN,也会有输出
		{
			if(!output_channel_info->contain_channel_valid[i][BP1048_CHANNEL_Line5_L])
			{
				send_buf[datalen++] = output_channel_id;
				int sub_len=datalen++;

				send_buf[datalen++] = BP1048_CHANNEL_Line5_L+BP1048_CHANNEL_Line5_L;
				send_buf[datalen++] = 72;	//-72dB
				send_buf[sub_len]+=2;
			}
			if(!output_channel_info->contain_channel_valid[i][BP1048_CHANNEL_MIC1])
			{
				send_buf[datalen++] = output_channel_id;
				int sub_len=datalen++;

				send_buf[datalen++] = BP1048_CHANNEL_Line5_L+BP1048_CHANNEL_MIC1;
				send_buf[datalen++] = 72;	//-72dB
				send_buf[sub_len]+=2;
			}
		}
	}


#if 0
	printf("Bp1048_Send_Set_ChannelSource:\r\n");
	for(i=0;i<datalen;i++)
	{
		printf("0x%02x ",send_buf[i]);
	}
	printf("\r\n");

#endif

	Bp1048_uart_send(cmd,datalen,send_buf);
}




static void Bp1048_full_output_channel_struct(st_bp1048_output_channel_info *stInfo,unsigned char output_channel_index,unsigned char channel_index,unsigned char channel_gain)
{
	stInfo->contain_channel_valid[output_channel_index][channel_index]=1;
	stInfo->contain_channel_gain[output_channel_index][channel_index]=channel_gain;
}


void Bp1048_Send_Set_Source(unsigned long source_type)
{
	st_bp1048_output_channel_info output_channel_info;
	memset(&output_channel_info,0,sizeof(output_channel_info));
	unsigned char contain_channel_count[BP1048_MAX_OUTPUT_CHANNEL_COUNT]={0};

	int i2s_gain=-1;


	if( (source_type & BP1048_SOURCE_DECODE_DAC_I2S0_STEREO_MUSIC) || \
		(source_type & BP1048_SOURCE_DECODE_DAC_I2S0_STEREO_OTHER) || \
		(source_type & BP1048_SOURCE_DECODE_DAC_I2S0_MONO_MUSIC) || \
		(source_type & BP1048_SOURCE_DECODE_DAC_I2S0_MONO_OTHER) || \
		(source_type & BP1048_SOURCE_DECODE_DAC_LINE) || \
		(source_type & BP1048_SOURCE_DECODE_DAC_MIC1) || \
		(source_type & BP1048_SOURCE_DECODE_DAC_BT) || \
		(source_type & BP1048_SOURCE_DECODE_DAC_100V) || \
		(source_type & BP1048_SOURCE_PAGER_DAC_I2S)
	  )
	{
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_DAC0_L]=1;
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_DAC0_R]=1;
	}

	if( (source_type & BP1048_SOURCE_PAGER_I2S0_MIC1) || \
		(source_type & BP1048_SOURCE_PAGER_I2S0_LINE) || \
		(source_type & BP1048_SOURCE_DECODE_I2S0_MIC2) || \
		(source_type & BP1048_SOURCE_PAGER_I2S0_BT)
	  )
	{
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_I2S0_OUT_L]=1;
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_I2S0_OUT_R]=1;
	}

	if( (source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S0_MIC1) || \
		(source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S0_MIC2)
	  )
	{
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_I2S0_OUT_L]=1;
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_I2S0_OUT_R]=1;
	}

	if( (source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S1_LINEL) || \
		(source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S1_LINER)
	  )
	{
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_I2S1_OUT_L]=1;
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_I2S1_OUT_R]=1;
	}


	if(source_type & BP1048_SOURCE_DECODE_DAC_I2S0_STEREO_MUSIC)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_I2S0_IN_L,0);
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_I2S0_IN_R,0);
	}
	else if(source_type & BP1048_SOURCE_DECODE_DAC_I2S0_STEREO_OTHER)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_I2S0_IN_L,0);
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_I2S0_IN_R,0);
	}
	else if(source_type & BP1048_SOURCE_DECODE_DAC_I2S0_MONO_MUSIC)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_I2S0_IN_L,0);
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_I2S0_IN_L,0);
	}
	else if(source_type & BP1048_SOURCE_DECODE_DAC_I2S0_MONO_OTHER)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_I2S0_IN_L,0);
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_I2S0_IN_L,0);
	}

	if(source_type & BP1048_SOURCE_DECODE_DAC_LINE)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_Line5_L,0);
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_Line5_L,0);
	}

	if(source_type & BP1048_SOURCE_DECODE_DAC_MIC1)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_MIC1,0);
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_MIC1,0);
	}

	if(source_type & BP1048_SOURCE_DECODE_DAC_BT)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_BT_L,0);
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_BT_R,0);
	}

	if(source_type & BP1048_SOURCE_DECODE_DAC_100V)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_Line5_R,0);
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_Line5_R,0);
	}

	if(source_type & BP1048_SOURCE_DECODE_I2S0_MIC2)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_L,BP1048_CHANNEL_MIC2,0);
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_R,BP1048_CHANNEL_MIC2,0);
	}

	if(source_type & BP1048_SOURCE_PAGER_I2S0_BT)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_L,BP1048_CHANNEL_BT_L,0);
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_R,BP1048_CHANNEL_BT_R,0);
	}


	/****************Audio Collector********************************************/
	if(source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S0_MIC1)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_L,BP1048_CHANNEL_MIC1,0);
	}
	if(source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S0_MIC2)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_R,BP1048_CHANNEL_MIC2,0);
	}
	if(source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S1_LINEL)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S1_OUT_L,BP1048_CHANNEL_Line5_L,0);
	}
	if(source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S1_LINER)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S1_OUT_R,BP1048_CHANNEL_Line5_R,0);
	}

	/************Net Pager*********************************************/
	if(source_type & BP1048_SOURCE_PAGER_I2S0_MIC1)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_L,BP1048_CHANNEL_MIC1,0);
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_R,BP1048_CHANNEL_MIC1,0);
	}
	if(source_type & BP1048_SOURCE_PAGER_I2S0_LINE)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_L,BP1048_CHANNEL_Line5_L,0);
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_R,BP1048_CHANNEL_Line5_L,0);
	}
	if(source_type & BP1048_SOURCE_PAGER_DAC_I2S)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_I2S0_IN_L,0);
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_I2S0_IN_R,0);
	}

	Bp1048_Send_Set_ChannelSource(&output_channel_info);
}




/*********************************************************************
 * @fn      Recv_Uart_Bluetooth_Pthread
 *
 * @brief   蓝牙串口数据接收线程(回调函数)
 *
 * @param   void
 *
 * @return  none
 */
void *Recv_Uart_Bluetooth_Pthread(void)
{
	printf("Recv_Uart_Bluetooth_Pthread...\n");
	int Rxlen;
	int ret, i;
	int Index = 0;
	fd_set readfd;
	struct timeval timeout;
	int max_fd;
	int Pkg_Length=0;
	int Read_Size = 2;

	unsigned char Rxbuf[MAX_UART_BUF_SIZE]={0};


	FD_ZERO(&readfd);               //清空读文件描述集合
	FD_SET(g_bluetooth_Uartfd, &readfd);  //注册套接字文件描述符

	while (1)
	{
		timeout.tv_sec = 0;      // 获取数据超时时间设置
		timeout.tv_usec = 100000;

		FD_ZERO(&readfd);               //清空读文件描述集合
		FD_SET(g_bluetooth_Uartfd, &readfd);  //注册套接字文件描述符
		ret = select(g_bluetooth_Uartfd+1, &readfd, NULL, NULL, &timeout);
		switch(ret)
		{
			case -1 : //调用出错
				perror("select");
				Index = 0;
				Read_Size = 2;
				memset(Rxbuf,0, MAX_UART_BUF_SIZE);
				break;

			case 0 : //超时
				//printf("timeout!\n");
				Index = 0;
				Read_Size = 2;
				memset(Rxbuf,0, MAX_UART_BUF_SIZE);
				break;

			default : //判断是否有数据可读

				/*接收对应串口的数据*/

				Rxlen = read(g_bluetooth_Uartfd, &Rxbuf[Index], Read_Size);

				/*数据校验*/
				if (Rxlen < 0)
				{
					perror("Rxlen<0\n");
					Index = 0;
					Read_Size = 2;
					memset(Rxbuf,0, MAX_UART_BUF_SIZE);
					continue;
				}
				else
				{

					#if 0
					printf("Terminal:The Recv Data Is : \n");
					for (i = 0; i < Rxlen; i++)
					{
						printf("0x%x ", Rxbuf[i+Index]);
					}
					printf("\n");
					#endif

					if(Index == 0)	/*判断包头是否正确*/
					{
						if(Rxbuf[0] == BP1048_UART_FRAME_HEAD1 && Rxbuf[1] == BP1048_UART_FRAME_HEAD2)
						{
							Index = Rxlen; //移位接收包后续数据
							Read_Size = 3; //包剩余包头信息
							continue;
						}
						else
						{
							/*接收的包错误，重新接收下一个数据包*/
							printf("Terminal ERROR : Pkg Head Fault : %x\n", Rxbuf[0]);
							Index = 0;
							Read_Size = 2;
							memset(Rxbuf,0, MAX_UART_BUF_SIZE);
							continue;
						}
					}
					else if(Index == 2)
					{
						Index = Rxlen+2; //移位接收包后续数据
						Read_Size = (Rxbuf[4]<<8)+Rxbuf[3]+1; //包剩余数据个数
						Pkg_Length = 5+Read_Size;
						if(Pkg_Length >MAX_UART_BUF_SIZE)	//包长超出最大长度
						{
							Index = 0;
							Read_Size = 2;
							memset(Rxbuf,0, MAX_UART_BUF_SIZE);
						}
						continue;
					}
					else
					{
						Index = Index + Rxlen;
						if (Index < Pkg_Length)
						{
							/*还没接收完包数据，继续接收*/
							printf("Terminal:Package Length Too Short,Continue......\n");
							Read_Size = Pkg_Length - Index;
							continue;
						}
						else
						{
							Index = 0;
							Read_Size = 2;
							if(Pkg_Length < 6)
							{
								printf("Terminal:ERROR : Package Length<6!\n");
								memset(Rxbuf,0, MAX_UART_BUF_SIZE);
								continue;
							}
							int cmd=Rxbuf[2];
							int data_len=Pkg_Length-6;
							if ( Checksum(cmd, data_len,Rxbuf+5) != Rxbuf[Pkg_Length-1] ) //校验数据
							{
								printf("Terminal:ERROR : Package Check Fail\n");
								memset(Rxbuf,0, MAX_UART_BUF_SIZE);
								continue;
							}

						 	printf("Bluetooth Checksum succeed,CMD=0x%02x\n",cmd);
							Bp1048_Command_Proc(cmd,data_len,Rxbuf+5);

							memset(Rxbuf,0, MAX_UART_BUF_SIZE);
						}
					}
				}
				break;
		}
	}
	pthread_exit(NULL);
}




/*********************************************************************
 * @fn      Uart_Bluetooth_Init
 *
 * @brief   初始化蓝牙串口模块
 *
 * @param   void
 *
 * @return  ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
bool Uart_Bluetooth_Init(void)
{
	int ret=-1;
	pthread_t t_uart_bluetooth_Pthread;
	pthread_attr_t Pthread_TERMINAL_Attr;
	pthread_attr_init(&Pthread_TERMINAL_Attr);
	pthread_attr_setdetachstate(&Pthread_TERMINAL_Attr, PTHREAD_CREATE_DETACHED);

	g_bluetooth_Uartfd = Init_Serial_Port(UART_BLUETOOTH_DEVICE, B460800);

    if(g_bluetooth_Uartfd == -1)
    {
        return false;
    }
	/*创建一个线程单独接收终端串口数据*/
	pthread_create(&t_uart_bluetooth_Pthread, &Pthread_TERMINAL_Attr, (void *)Recv_Uart_Bluetooth_Pthread, NULL);
	pthread_attr_destroy(&Pthread_TERMINAL_Attr);
	return true;
}