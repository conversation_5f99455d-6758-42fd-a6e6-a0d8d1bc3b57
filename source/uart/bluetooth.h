#ifndef __BLUETOOTH_H_
#define __BLUETOOTH_H_

#include <stdbool.h>

#define INI_KEY_BLUETOOTH_NAME     	"NAME"
#define INI_KEY_BLUETOOTH_HAS_PIN   "HAS_PIN"
#define INI_KEY_BLUETOOTH_PIN  		"PIN"

#define DEFAULT_BLUETOOTH_NAME		"NetSpeaker"
#define DEFAULT_BLUETOOTH_PIN		"0000"	

#define UART_BLUETOOTH_DEVICE     "/dev/ttyS3" //uart device name

#define BP1048_UART_FRAME_HEAD1	0xAA
#define BP1048_UART_FRAME_HEAD2	0x55
#define BP1048_UART_FRAME_HEAD	(BP1048_UART_FRAME_HEAD2<<8)+BP1048_UART_FRAME_HEAD1


#define BP1048_UART_DATA_LENGTH_MAX	   256
#define BP1048_UART_RECEIVE_BUFFER_MAX (6+BP1048_UART_DATA_LENGTH_MAX)

#define BP1048_CMD_CHIP_INFO		0x00
#define BP1048_CMD_FEATURES			0x01
#define BP1048_CMD_SAMPLERATE		0x02
#define BP1048_CMD_BOOT				0x03
#define BP1048_CMD_REBOOT			0x04
#define BP1048_CMD_SOURCE			0x11
#define BP1048_CMD_VOLUME			0x12
#define BP1048_CMD_BT_INFO			0x13
#define BP1048_CMD_SET_RP			0x18
#define BP1048_CMD_CUSTOM_SIGNAL	0x19
#define BP1048_CMD_UPGRADE			0xFE


enum{
	BP1048_CHANNEL_Line5_L,
	BP1048_CHANNEL_Line5_R,
	BP1048_CHANNEL_MIC1,
	BP1048_CHANNEL_MIC2,
	BP1048_CHANNEL_I2S0_IN_L,
	BP1048_CHANNEL_I2S0_IN_R,
	BP1048_CHANNEL_I2S1_IN_L,
	BP1048_CHANNEL_I2S1_IN_R,
	BP1048_CHANNEL_I2S0_OUT_L,
	BP1048_CHANNEL_I2S0_OUT_R,
	BP1048_CHANNEL_I2S1_OUT_L,
	BP1048_CHANNEL_I2S1_OUT_R,
	BP1048_CHANNEL_DAC0_L,
	BP1048_CHANNEL_DAC0_R,
	BP1048_CHANNEL_BT_L,
	BP1048_CHANNEL_BT_R,
	BP1048_MAX_CHANNEL_COUNT
};


//自定义，非协议部分
enum{
	BP1048_OUTPUT_CHANNEL_DAC0_L,
	BP1048_OUTPUT_CHANNEL_DAC0_R,
	BP1048_OUTPUT_CHANNEL_I2S0_OUT_L,
	BP1048_OUTPUT_CHANNEL_I2S0_OUT_R,
	BP1048_OUTPUT_CHANNEL_I2S1_OUT_L,
	BP1048_OUTPUT_CHANNEL_I2S1_OUT_R,
	BP1048_MAX_OUTPUT_CHANNEL_COUNT
};

//自定义，非协议部分
#define BP1048_SOURCE_DECODE_DAC_I2S0_STEREO_MUSIC		0x01
#define BP1048_SOURCE_DECODE_DAC_I2S0_STEREO_OTHER		0x02
#define BP1048_SOURCE_DECODE_DAC_I2S0_MONO_MUSIC		0x04
#define BP1048_SOURCE_DECODE_DAC_I2S0_MONO_OTHER		0x08
#define BP1048_SOURCE_DECODE_DAC_LINE					0x10
#define BP1048_SOURCE_DECODE_DAC_MIC1					0x20
#define BP1048_SOURCE_DECODE_DAC_BT						0x40
#define BP1048_SOURCE_DECODE_DAC_100V					0x80
#define BP1048_SOURCE_DECODE_I2S0_MIC2					0x100

#define BP1048_SOURCE_PAGER_I2S0_MIC1					0x1000
#define BP1048_SOURCE_PAGER_I2S0_LINE					0x2000
#define BP1048_SOURCE_PAGER_DAC_I2S						0x4000
#define BP1048_SOURCE_PAGER_I2S0_BT						0x8000

#define BP1048_SOURCE_AUDIO_COLLECTOR_I2S0_MIC1			0x10000
#define BP1048_SOURCE_AUDIO_COLLECTOR_I2S0_MIC2			0x20000
#define BP1048_SOURCE_AUDIO_COLLECTOR_I2S1_LINEL		0x40000
#define BP1048_SOURCE_AUDIO_COLLECTOR_I2S1_LINER		0x80000


#define BP1048_BLUETOOTH_SOURCE					    BP1048_SOURCE_DECODE_DAC_LINE | BP1048_SOURCE_DECODE_DAC_BT


#define BP1048_BLUETOOTH_NAME_MAX_LENGTH		33
#define BP1048_BLUETOOTH_PASSWORD_MAX_LENGTH	4
typedef struct {
	unsigned char name[BP1048_BLUETOOTH_NAME_MAX_LENGTH];			//蓝牙名称
	unsigned char hasPassword;										//是否有密码
	unsigned char password[BP1048_BLUETOOTH_PASSWORD_MAX_LENGTH+1];	//蓝牙密码
}st_g_bp1048_bt_info;
extern st_g_bp1048_bt_info g_bp1048_bt_info;

typedef struct {
	unsigned char uuid[8];			//UUID
	unsigned char firmware_type;	//固件类型
	unsigned char version[10];		//version
}st_bp1048_info;
extern st_bp1048_info g_bp1048_info;

typedef struct {
	unsigned char contain_channel_valid[BP1048_MAX_OUTPUT_CHANNEL_COUNT][BP1048_MAX_CHANNEL_COUNT];
	unsigned char contain_channel_gain[BP1048_MAX_OUTPUT_CHANNEL_COUNT][BP1048_MAX_CHANNEL_COUNT];
	unsigned char output_channel_vaild[BP1048_MAX_OUTPUT_CHANNEL_COUNT];
}st_bp1048_output_channel_info;


extern int g_exist_bluetooth_module;    //是否存在蓝牙模块

bool Uart_Bluetooth_Init(void);     //初始化蓝牙串口模块
void Bp1048_Send_Get_Info(unsigned char cmd);
void Bp1048_Send_Set_Reboot();
void Bp1048_Send_Set_Bluetooth();

#endif