#ifndef __GPS_H_
#define __GPS_H_

#include <stdbool.h>
#include "const.h"

#if IS_DEVICE_GPS_SYNCHRONIZER

#define UART_GPS_DEVICE     "/dev/ttyS3" //GPS模块串口设备名

// NMEA协议相关定义
#define GPS_NMEA_FRAME_START    '$'
#define GPS_NMEA_FRAME_END      '\n'
#define GPS_NMEA_MAX_LENGTH     256
#define GPS_NMEA_RECEIVE_BUFFER_MAX 512

// GPS数据有效性状态
enum {
    GPS_STATUS_INVALID = 0,     // 无效
    GPS_STATUS_VALID = 1,       // 有效
    GPS_STATUS_SEARCHING = 2    // 搜星中
};

// GPS定位模式
enum {
    GPS_MODE_NO_FIX = 1,        // 未定位
    GPS_MODE_2D_FIX = 2,        // 2D定位
    GPS_MODE_3D_FIX = 3         // 3D定位
};

// GPS卫星系统类型
enum {
    GPS_SYSTEM_GPS = 1,         // GPS
    GPS_SYSTEM_GLONASS = 2,     // GLONASS
    GPS_SYSTEM_GALILEO = 3,     // Galileo
    GPS_SYSTEM_BEIDOU = 4       // 北斗
};

// GPS时间结构体
typedef struct {
    int year;           // 年
    int month;          // 月
    int day;            // 日
    int hour;           // 时
    int minute;         // 分
    int second;         // 秒
    int millisecond;    // 毫秒
} gps_time_t;

// GPS位置信息结构体
typedef struct {
    double latitude;        // 纬度 (度)
    double longitude;       // 经度 (度)
    double altitude;        // 海拔高度 (米)
    char lat_direction;     // 纬度方向 N/S
    char lon_direction;     // 经度方向 E/W
} gps_position_t;

// GPS卫星信息结构体
typedef struct {
    int satellites_used;    // 参与定位的卫星数量
    int satellites_view;    // 可见卫星数量
    float hdop;            // 水平精度因子
    float vdop;            // 垂直精度因子
    float pdop;            // 位置精度因子
} gps_satellite_t;

// GPS完整信息结构体
typedef struct {
    int status;                 // GPS状态
    int fix_mode;              // 定位模式
    gps_time_t utc_time;       // UTC时间
    gps_position_t position;   // 位置信息
    gps_satellite_t satellite; // 卫星信息
    float speed;               // 速度 (节)
    float course;              // 航向角 (度)
    int last_update_time;      // 最后更新时间戳
    bool data_valid;           // 数据有效标志
} gps_info_t;

// 全局GPS信息变量
extern gps_info_t g_gps_info;
extern int g_gps_module_status;    // GPS模块状态

// 函数声明
bool Uart_GPS_Init(void);                           // 初始化GPS串口模块
void GPS_Parse_NMEA_Data(char *nmea_data);          // 解析NMEA数据
void GPS_Update_System_Time(void);                  // 更新系统时间
bool GPS_Is_Time_Valid(void);                       // 检查GPS时间是否有效
void GPS_Get_Current_Time(gps_time_t *time);        // 获取当前GPS时间
void GPS_Get_Position_Info(gps_position_t *pos);    // 获取位置信息
void GPS_Print_Status(void);                        // 打印GPS状态信息

// NMEA语句解析函数
static void GPS_Parse_GPRMC(char *sentence);        // 解析GPRMC语句
static void GPS_Parse_GPGGA(char *sentence);        // 解析GPGGA语句
static void GPS_Parse_GPGSA(char *sentence);        // 解析GPGSA语句
static void GPS_Parse_GPGSV(char *sentence);        // 解析GPGSV语句

// 工具函数
static double GPS_Convert_Coordinate(char *coord, char direction);  // 坐标转换
static bool GPS_Verify_Checksum(char *sentence);                   // 校验和验证
static void GPS_Extract_Time(char *time_str, gps_time_t *time);    // 提取时间
static void GPS_Extract_Date(char *date_str, gps_time_t *time);    // 提取日期

#endif // IS_DEVICE_GPS_SYNCHRONIZER

#endif // __GPS_H_