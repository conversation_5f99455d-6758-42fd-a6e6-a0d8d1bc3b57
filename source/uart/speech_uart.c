#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <termios.h>
#include <pthread.h>

#include "sysconf.h"
#include "uartCommon.h"
#include "speech_uart.h"

#if IS_DEVICE_INTERCOM_TERMINAL

void recog_command_call(char *command);

static int g_speech_Uartfd;

void Speech_Command_Proc( unsigned char cmd, unsigned short datalen,unsigned char *data )
{
	switch(cmd)
	{
		case UART_SPEECH_CMD_RECOG_COMMAND:
		SPEECH_receive_recog_command(datalen,data);
	  	break;
	}
}


void Speech_uart_send(unsigned char cmd, unsigned short datalen,unsigned char *data) {
	unsigned char *send_buf=NULL;
	send_buf= (unsigned char*)malloc(SPEECH_UART_RECEIVE_BUFFER_MAX);

	int pos = 0, i;
	send_buf[pos++] = SPEECH_UART_FRAME_HEAD1;
	send_buf[pos++] = SPEECH_UART_FRAME_HEAD2;
	send_buf[pos++] = cmd;
	send_buf[pos++] = datalen;
	send_buf[pos++] = datalen>>8;

	for (i = 0; i < datalen; i++) {
		send_buf[pos++] = data[i];
	}

	send_buf[pos++] = Checksum(cmd,datalen,data);

	write(g_speech_Uartfd, send_buf, pos); //发送数据

#if 1
	printf("Speech_uart_send:cmd=0x%x,datalen=%d\r\n",cmd,datalen);

#else
	printf("Speech_uart_send:\r\n");
	for(i=0;i<pos;i++)
	{
		printf("%02x ",send_buf[i]);
	}
	printf("\r\n");
#endif

	free(send_buf);
}


static void SPEECH_receive_recog_command(unsigned short datalen,unsigned char *rcbuf)
{
	int pos=0;
	int len=rcbuf[pos++];
	char recog_command[64]={0};
	memcpy(recog_command,rcbuf+pos,len);
	printf("recog_command=%s\n",recog_command);

	//先本地播放警报声，然后呼叫寻呼台
	recog_command_call(recog_command);
}


void SPEECH_Control_Relay(int firstRelay,int secondRelay)
{
  	unsigned char cmd = UART_SPEECH_CMD_RECOG_COMMAND;
	unsigned short datalen = 2;
	unsigned char send_buf[SPEECH_UART_RECEIVE_BUFFER_MAX] = { 0 };
	
	send_buf[0] = firstRelay;
	send_buf[1] = secondRelay;

	Speech_uart_send(cmd,datalen,send_buf);
}


/*********************************************************************
 * @fn      Recv_Uart_Speech_Pthread
 *
 * @brief   消防模块串口数据接收线程(回调函数)
 *
 * @param   void
 *
 * @return  none
 */
void *Recv_Uart_Speech_Pthread(void)
{
	printf("Recv_Uart_Speech_Pthread...\n");
	int Rxlen;
	int ret, i;
	int Index = 0;
	fd_set readfd;
	struct timeval timeout;
	int max_fd;
	int Pkg_Length=0;
	int Read_Size = 2;

	unsigned char Rxbuf[MAX_UART_BUF_SIZE]={0};


	FD_ZERO(&readfd);               //清空读文件描述集合
	FD_SET(g_speech_Uartfd, &readfd);  //注册套接字文件描述符

	while (1)
	{
		timeout.tv_sec = 0;      // 获取数据超时时间设置
		timeout.tv_usec = 100000;

		FD_ZERO(&readfd);               //清空读文件描述集合
		FD_SET(g_speech_Uartfd, &readfd);  //注册套接字文件描述符
		ret = select(g_speech_Uartfd+1, &readfd, NULL, NULL, &timeout);
		switch(ret)
		{
			case -1 : //调用出错
				perror("select");
				Index = 0;
				Read_Size = 2;
				memset(Rxbuf,0, MAX_UART_BUF_SIZE);
				break;

			case 0 : //超时
				//printf("timeout!\n");
				Index = 0;
				Read_Size = 2;
				memset(Rxbuf,0, MAX_UART_BUF_SIZE);
				break;

			default : //判断是否有数据可读

				/*接收对应串口的数据*/

				Rxlen = read(g_speech_Uartfd, &Rxbuf[Index], Read_Size);
				#if 0
				printf("Rx_Len=%d\n",Rxlen);
				for(int i=0;i<Rxlen;i++)
				{
					printf("0x%x ",Rxbuf[i]);
				}
				printf("\n");
				#endif

				/*数据校验*/
				if (Rxlen < 0)
				{
					perror("Rxlen<0\n");
					Index = 0;
					Read_Size = 2;
					memset(Rxbuf,0, MAX_UART_BUF_SIZE);
					continue;
				}
				else
				{

					#if 0
					printf("Terminal:The Recv Data Is : \n");
					for (i = 0; i < Rxlen; i++)
					{
						printf("0x%x ", Rxbuf[i+Index]);
					}
					printf("\n");
					#endif

					if(Index == 0)	/*判断包头是否正确*/
					{
						if(Rxbuf[0] == SPEECH_UART_FRAME_HEAD1 && Rxbuf[1] == SPEECH_UART_FRAME_HEAD2)
						{
							Index = Rxlen; //移位接收包后续数据
							Read_Size = 3; //包剩余包头信息
							continue;
						}
						else
						{
							/*接收的包错误，重新接收下一个数据包*/
							printf("Terminal ERROR : Pkg Head Fault : %x\n", Rxbuf[0]);
							Index = 0;
							Read_Size = 2;
							memset(Rxbuf,0, MAX_UART_BUF_SIZE);
							continue;
						}
					}
					else if(Index == 2)
					{
						Index = Rxlen+2; //移位接收包后续数据
						Read_Size = (Rxbuf[4]<<8)+Rxbuf[3]+1; //包剩余数据个数
						Pkg_Length = 5+Read_Size;
						if(Pkg_Length >MAX_UART_BUF_SIZE)	//包长超出最大长度
						{
							Index = 0;
							Read_Size = 2;
							memset(Rxbuf,0, MAX_UART_BUF_SIZE);
						}
						continue;
					}
					else
					{
						Index = Index + Rxlen;
						if (Index < Pkg_Length)
						{
							/*还没接收完包数据，继续接收*/
							printf("Terminal:Package Length Too Short,Continue......\n");
							Read_Size = Pkg_Length - Index;
							continue;
						}
						else
						{
							Index = 0;
							Read_Size = 2;
							if(Pkg_Length < 6)
							{
								printf("Terminal:ERROR : Package Length<6!\n");
								memset(Rxbuf,0, MAX_UART_BUF_SIZE);
								continue;
							}
							int cmd=Rxbuf[2];
							int data_len=Pkg_Length-6;
							if ( Checksum(cmd, data_len,Rxbuf+5) != Rxbuf[Pkg_Length-1] ) //校验数据
							{
								printf("Terminal:ERROR : Package Check Fail\n");
								memset(Rxbuf,0, MAX_UART_BUF_SIZE);
								continue;
							}

						 	printf("Speech Checksum succeed,CMD=0x%02x\n",cmd);
							Speech_Command_Proc(cmd,data_len,Rxbuf+5);

							memset(Rxbuf,0, MAX_UART_BUF_SIZE);
						}
					}
				}
				break;
		}
	}
	pthread_exit(NULL);
}




/*********************************************************************
 * @fn      Uart_Speech_Init
 *
 * @brief   初始化语音识别模块
 *
 * @param   void
 *
 * @return  ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
bool Uart_Speech_Init(void)
{
	int ret=-1;
	pthread_t t_uart_speech_Pthread;
	pthread_attr_t Pthread_TERMINAL_Attr;
	pthread_attr_init(&Pthread_TERMINAL_Attr);
	pthread_attr_setdetachstate(&Pthread_TERMINAL_Attr, PTHREAD_CREATE_DETACHED);

	system("/customer/riu_w 0x103e 0x3a 0x0075");
	
	g_speech_Uartfd = Init_Serial_Port(UART_SPEECH_DEVICE, B115200);

    if(g_speech_Uartfd == -1)
    {
        return false;
    }
	/*创建一个线程单独接收终端串口数据*/
	pthread_create(&t_uart_speech_Pthread, &Pthread_TERMINAL_Attr, (void *)Recv_Uart_Speech_Pthread, NULL);
	pthread_attr_destroy(&Pthread_TERMINAL_Attr);
	return true;
}

#endif