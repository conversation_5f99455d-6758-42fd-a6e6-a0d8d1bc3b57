#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <termios.h>
#include <pthread.h>

#include "const.h"
#include "uartCommon.h"
#include "ampControler.h"
#include "newUartDevice.h"

static int g_ADC_Uartfd;

int g_exist_adcCheck_module=0; //是否存在adc电压检测模块
int g_exist_ampControler_module=0; //是否存在功放控制器检测模块
int adcCheckVolumeVal[4]={100,100,100,100};

#define ADC_SECTOR_VAL  10      //10为0,20为1...

void ADC_receive_ChipInfo(unsigned char deviceId,unsigned short datalen,unsigned char *rcbuf);
void ADC_Send_Get_adcInfo(unsigned char deviceId,unsigned short intervalMs);
void ADC_receive_adc_data(unsigned char deviceId,unsigned short datalen,unsigned char *rcbuf);
#if IS_DEVICE_AMP_CONTROLER
void ADC_Send_Set_ampInfo();
void ADC_Send_Get_ampInfo(unsigned short intervalMs);
void ADC_receive_amp_data(unsigned char deviceId,unsigned short datalen,unsigned char *rcbuf);
#endif

void ADC_Command_Proc( unsigned char deviceId, unsigned char cmd, unsigned short datalen,unsigned char *data )
{
	switch(cmd)
	{
		case ADC_UART_CMD_CHIP_INFO:
			ADC_receive_ChipInfo(deviceId,datalen,data);
		#if(!IS_DEVICE_AMP_CONTROLER)
		case ADC_UART_CMD_ADC_INFO:
			ADC_receive_adc_data(deviceId,datalen,data);
		break;
		#endif
		#if IS_DEVICE_AMP_CONTROLER
		case ADC_UART_CMD_AMP_INFO:
			ADC_receive_amp_data(deviceId,datalen,data);
		break;
		#endif
	}
}


void ADC_uart_send(unsigned char deviceId,unsigned char cmd,unsigned char deviceAddr, unsigned short datalen,unsigned char *data) {
	unsigned char *send_buf=NULL;
	send_buf= (unsigned char*)malloc(ADC_UART_RECEIVE_BUFFER_MAX);

	int pos = 0, i;
	send_buf[pos++] = ADC_UART_FRAME_HEAD1;
	send_buf[pos++] = ADC_UART_FRAME_HEAD2;
	send_buf[pos++] = deviceId;		//设备编号
	send_buf[pos++] = deviceAddr;	//设备地址
	send_buf[pos++] = cmd;
	send_buf[pos++] = datalen;
	send_buf[pos++] = datalen>>8;

	for (i = 0; i < datalen; i++) {
		send_buf[pos++] = data[i];
	}

	cmd+=send_buf[2]+send_buf[3];
	send_buf[pos++] = Checksum(cmd,datalen,data);

	write(g_ADC_Uartfd, send_buf, pos); //发送数据

#if 1
	printf("ADC_uart_send:cmd=0x%x,datalen=%d\r\n",cmd,datalen);

#else
	printf("ADC_uart_send:\r\n");
	for(i=0;i<pos;i++)
	{
		printf("%02x ",send_buf[i]);
	}
	printf("\r\n");
#endif

	free(send_buf);
}



void ADC_receive_ChipInfo(unsigned char deviceId,unsigned short datalen,unsigned char *rcbuf)
{
	if(datalen <= 1)
		return;
	int pos=0,i=0;
	printf("ADC_receive_ChipInfo:datalen=%d\n",datalen);
	char version[20]={0};
	while(pos<datalen)
	{
		int ref_type = rcbuf[pos++];
		int ref_len = rcbuf[pos++];

		switch(ref_type)
		{
			case 1:
		  			memcpy(version,rcbuf+pos,ref_len);
			  	break;
		}
		pos+=ref_len;
	}

	if(deviceId == UART_NEW_DEVICEID_ADC_CHECK)
	{
		ADC_Send_Get_adcInfo(deviceId,300);
	}
	#if IS_DEVICE_AMP_CONTROLER
	else if(deviceId == UART_NEW_DEVICEID_AMP_CONTROLER)
	{
		#if 1
		ADC_Send_Set_ampInfo();
		ADC_Send_Get_ampInfo(100);
		#else
		//AMP_CONTROLER_Send_Control_Channel(7);
		//ADC_Send_Get_adcInfo(deviceId,10);
		#endif
	}
	#endif
}




void ADC_receive_adc_data(unsigned char deviceId,unsigned short datalen,unsigned char *rcbuf)
{
	if(datalen <= 1)
		return;
	int pos=0,i=0;
	//printf("ADC_receive_adc_data:datalen=%d\n",datalen);
	int dataMode=rcbuf[pos++];
	if(deviceId == UART_NEW_DEVICEID_ADC_CHECK)
	{
		int adc_val[4]={0};
		for(int i=0;i<4;i++)
		{
			adc_val[i] = rcbuf[pos]+(rcbuf[pos+1]<<8);
			pos+=2;
			//printf("adc_val[%d]=%d\n",i,adc_val[i]);

			if(adc_val[i]>=1000)
			{
				adc_val[i]=100;
			}
			else if(adc_val[i]<=10)
			{
				adc_val[i]=0;
			}
			else
			{
				adc_val[i]=adc_val[i]/ADC_SECTOR_VAL;
				if(adc_val[i]<0)
					adc_val[i]=0;
				if(adc_val[i]>100)
					adc_val[i]=100;
			}
		}
		if(adcCheckVolumeVal[0]!=adc_val[0])
		{
			adcCheckVolumeVal[0]=adc_val[0];
			printf("ADC_UART_CMD_ADC_INFO:adc_val[0]=%d\n",adc_val[0]);
		}
		if(adcCheckVolumeVal[1]!=adc_val[1])
		{
			adcCheckVolumeVal[1]=adc_val[1];
			//printf("ADC_UART_CMD_ADC_INFO:adc_val[1]=%d\n",adc_val[1]);
		}
		if(adcCheckVolumeVal[2]!=adc_val[2])
		{
			adcCheckVolumeVal[2]=adc_val[2];
			printf("ADC_UART_CMD_ADC_INFO:adc_val[2]=%d\n",adc_val[2]);
		}
		if(adcCheckVolumeVal[3]!=adc_val[3])
		{
			adcCheckVolumeVal[3]=adc_val[3];
			//printf("ADC_UART_CMD_ADC_INFO:adc_val[3]=%d\n",adc_val[3]);
		}
	}
	#if(IS_DEVICE_AMP_CONTROLER)
	else if(deviceId == UART_NEW_DEVICEID_AMP_CONTROLER)
	{
		static int last_adc_values[12] = {0}; // 保存上一次的ADC值
    	static bool last_adc_valid[12] = {0}; // 保存上一次的adc_valid状态
		bool values_changed = false;
		int adc_value=0;
		bool adc_valid=0;
		for(int i=0;i<12;i++)
		{
			adc_value = rcbuf[pos]+(rcbuf[pos+1]<<8);
			pos+=2;

			unsigned char lineIn_throshold = 30; // 阈值
			unsigned char ampOut_throshold = 5; // 阈值
			if(i%2 == 0)  // 偶数索引为输入通道
            {
                adc_valid = adc_value>=lineIn_throshold?1:0;
            }
            else  // 奇数索引为输出通道
            {
                adc_valid = adc_value>=ampOut_throshold?1:0;
            }
			

			last_adc_values[i] = adc_value; // 更新保存的值
			// 检查adc_valid是否有变化
			if(last_adc_valid[i] != adc_valid) {
				values_changed = true;
				last_adc_valid[i] = adc_valid;
			}
			

			// 更新通道状态
            if(i < 10)  // 主通道1-5 (i=0-9)
            {
                int channel = i/2;  // 通道号(0-4)
                if(i%2 == 0)  // 输入
                    g_ampControler_info.masterChannelLineIn[channel] = adc_valid;
                else  // 输出
                    g_ampControler_info.masterChannelAmpOut[channel] = adc_valid;
            }
            else if(i == 10)  // 备用通道输入
            {
                g_ampControler_info.backupChannelLineIn = adc_valid;
            }
            else if(i == 11)  // 备用通道输出
            {
                g_ampControler_info.backupChannelAmpOut = adc_valid;
            }
			//printf("adc_value[%d]=%d,adc_valid=%d\n",i,adc_value,adc_valid);
		}
		#if 0
		// 如果有变化，打印所有通道的值
		if(values_changed) {
			printf("values changed:\n");
			for(int i=0;i<12;i++) {
				printf("Channel %d: %d\n", i, last_adc_values[i]);
			}
		}
		#endif

		// 处理完数据后发送信号量通知
        sem_post(&g_ampControler_info.data_ready_sem);
	}
	#endif
}

#if(IS_DEVICE_AMP_CONTROLER)
void ADC_receive_amp_data(unsigned char deviceId,unsigned short datalen,unsigned char *rcbuf)
{
	if(datalen <= 1)
		return;
	int pos=0,i=0;
	//printf("ADC_receive_amp_data:datalen=%d\n",datalen);
	int dataMode=rcbuf[pos++];

	static st_ampControler_info preAmpControler_info;
	static bool isFirstTime = true;
	if (isFirstTime) {
		preAmpControler_info = g_ampControler_info;  // 第一次调用时才赋值
		isFirstTime = false;
	}

	if(deviceId == UART_NEW_DEVICEID_AMP_CONTROLER)
	{
		for(int i=0;i<5;i++)
		{
			g_ampControler_info.masterChannelStatusArray[i] = rcbuf[pos++];
		}
		g_ampControler_info.backupChannelStatus = rcbuf[pos++];
		int backupChannelId = rcbuf[pos++];
		if(backupChannelId == 0)
		{
			g_ampControler_info.backupChannelId = 0xff;
		}
		else
		{
			g_ampControler_info.backupChannelId = backupChannelId-1;
		}
		//判断preAmpControler_info是否相同
		if(memcmp(&preAmpControler_info,&g_ampControler_info,sizeof(st_ampControler_info)) != 0)
		{
			printf("ampControler_info changed:");
			#if 0
			//打印收到的指令
			for(i=0;i<datalen;i++)
			{
				printf("%02x ",rcbuf[i]);
			}
			printf("\n");
			#endif
			//打印状态值
			for(int i=0;i<5;i++)
			{
				printf("masterChannelStatusArray[%d]=%d\n",i,g_ampControler_info.masterChannelStatusArray[i]);
			}
			printf("backupChannelStatus=%d\n",g_ampControler_info.backupChannelStatus);
			printf("backupChannelId=%d\n",g_ampControler_info.backupChannelId);

			preAmpControler_info = g_ampControler_info;

			//通知服务器
			Host_Query_Set_Amp_Controler_Status(NULL);
			//更新串口显示屏
			amp_controler_uart_ChannelChange();
		}
	}
}
#endif


void ADC_Send_Get_Chip_Info(unsigned char deviceId)
{
	unsigned char cmd = ADC_UART_CMD_CHIP_INFO;
	ADC_uart_send(deviceId,cmd,0xFF,0,NULL);
}


void ADC_Send_Get_adcInfo(unsigned char deviceId,unsigned short intervalMs)
{
	unsigned char cmd = ADC_UART_CMD_ADC_INFO;
	int dataLen=2;
	unsigned char data[2]={0};
	data[0]=intervalMs&0xFF;
	data[1]=intervalMs>>8;

	ADC_uart_send(deviceId,cmd,0x00,dataLen,data);
}


#if IS_DEVICE_AMP_CONTROLER

void ADC_Send_Set_ampInfo()
{
	unsigned char cmd = ADC_AURT_CMD_AMP_PARM;
	int dataLen=7;
	unsigned char data[7]={0};

	int signalDetectCnt = 75;
	int backupAmpResumeVal = 2600;
	data[0]=1;	//工作模式 0-关闭 1-MCU自行切换继电器 2-网络板切换继电器
	data[1]=4;	//ADC采样间隔
	data[2]=30;	//音频信号阈值
	data[3]=5;	//功放信号阈值
	data[4]=signalDetectCnt;	//信号检测累计次数
	data[5]=backupAmpResumeVal&0xFF;	//备用功放恢复数值低位
	data[6]=backupAmpResumeVal>>8;	//备用功放恢复数值高位

	ADC_uart_send(UART_NEW_DEVICEID_AMP_CONTROLER,cmd,0x00,dataLen,data);
}


void ADC_Send_Get_ampInfo(unsigned short intervalMs)
{
	unsigned char cmd = ADC_UART_CMD_AMP_INFO;
	int dataLen=2;
	unsigned char data[2]={0};
	data[0]=intervalMs&0xFF;
	data[1]=intervalMs>>8;

	ADC_uart_send(UART_NEW_DEVICEID_AMP_CONTROLER,cmd,0x00,dataLen,data);
}

void AMP_CONTROLER_Send_Control_Channel(unsigned char channelId)
{
	unsigned char cmd = AMP_CONTROLER_UART_CMD_CONTROL_CHANNEL;
	int dataLen=2;
	unsigned char data[2]={0};
	data[0]=channelId;	//ID有效值：1~6
	data[1]=channelId;
	ADC_uart_send(UART_NEW_DEVICEID_AMP_CONTROLER,cmd,0x00,dataLen,data);
	printf("AMP_CONTROLER_Send_Control_Channel:%d\n",channelId);
}
#endif


/*********************************************************************
 * @fn      Recv_Uart_ADC_Pthread
 *
 * @brief   蓝牙串口数据接收线程(回调函数)
 *
 * @param   void
 *
 * @return  none
 */
void *Recv_Uart_ADC_Pthread(void)
{
	printf("Recv_Uart_ADC_Pthread...\n");
	int Rxlen;
	int ret, i;
	int Index = 0;
	fd_set readfd;
	struct timeval timeout;
	int max_fd;
	int Pkg_Length=0;
	int Read_Size = 2;

	unsigned char Rxbuf[MAX_UART_BUF_SIZE]={0};


	FD_ZERO(&readfd);               //清空读文件描述集合
	FD_SET(g_ADC_Uartfd, &readfd);  //注册套接字文件描述符

	#if IS_DEVICE_AMP_CONTROLER
		ADC_Send_Get_Chip_Info(UART_NEW_DEVICEID_AMP_CONTROLER);
	#endif

	while (1)
	{
		timeout.tv_sec = 0;      // 获取数据超时时间设置
		timeout.tv_usec = 100000;

		FD_ZERO(&readfd);               //清空读文件描述集合
		FD_SET(g_ADC_Uartfd, &readfd);  //注册套接字文件描述符
		ret = select(g_ADC_Uartfd+1, &readfd, NULL, NULL, &timeout);
		switch(ret)
		{
			case -1 : //调用出错
				perror("select");
				Index = 0;
				Read_Size = 2;
				memset(Rxbuf,0, MAX_UART_BUF_SIZE);
				break;

			case 0 : //超时
				//printf("timeout!\n");
				Index = 0;
				Read_Size = 2;
				memset(Rxbuf,0, MAX_UART_BUF_SIZE);
				break;

			default : //判断是否有数据可读

				/*接收对应串口的数据*/

				Rxlen = read(g_ADC_Uartfd, &Rxbuf[Index], Read_Size);

				/*数据校验*/
				if (Rxlen < 0)
				{
					perror("Rxlen<0\n");
					Index = 0;
					Read_Size = 2;
					memset(Rxbuf,0, MAX_UART_BUF_SIZE);
					continue;
				}
				else
				{
					#if 0
					printf("Terminal:The Recv Data Is : \n");
					for (i = 0; i < Rxlen; i++)
					{
						printf("0x%x ", Rxbuf[i+Index]);
					}
					printf("\n");
					#endif

					if(Index == 0)	/*判断包头是否正确*/
					{
						if(Rxbuf[0] == ADC_UART_FRAME_HEAD1 && Rxbuf[1] == ADC_UART_FRAME_HEAD2)
						{
							Index = Rxlen; //移位接收包后续数据
							Read_Size = 5; //包剩余包头信息
							continue;
						}
						else
						{
							/*接收的包错误，重新接收下一个数据包*/
							printf("Terminal ERROR : Pkg Head Fault : %x\n", Rxbuf[0]);
							Index = 0;
							Read_Size = 2;
							memset(Rxbuf,0, MAX_UART_BUF_SIZE);
							continue;
						}
					}
					else if(Index == 2)
					{
						Index = 2 + Rxlen; //移位接收包后续数据
						Read_Size = (Rxbuf[6]<<8)+Rxbuf[5]+1; //包剩余数据个数
						Pkg_Length = 7+Read_Size;
						//printf("Index=%d,Read_Size=%d,Pkg_Length=%d\n",Index,Read_Size,Pkg_Length);
						if(Pkg_Length >MAX_UART_BUF_SIZE)	//包长超出最大长度
						{
							Index = 0;
							Read_Size = 2;
							memset(Rxbuf,0, MAX_UART_BUF_SIZE);
						}
						continue;
					}
					else
					{
						Index = Index + Rxlen;
						if (Index < Pkg_Length)
						{
							/*还没接收完包数据，继续接收*/
							printf("uart recv too Short,Continue......,Index=%d,Pkg_Length=%d,data_length=%d\n",Index,Pkg_Length,Read_Size-1);
							Read_Size = Pkg_Length - Index;
							continue;
						}
						else
						{
							Index = 0;
							Read_Size = 2;
							if(Pkg_Length < 8)
							{
								printf("Terminal:ERROR : Package Length<6!\n");
								memset(Rxbuf,0, MAX_UART_BUF_SIZE);
								continue;
							}
							int deviceId=Rxbuf[2];
							int cmd=Rxbuf[4];
							int sum1=cmd+Rxbuf[2]+Rxbuf[3];
							int data_len=Pkg_Length-8;
							if ( Checksum(sum1, data_len,Rxbuf+7) != Rxbuf[Pkg_Length-1] ) //校验数据
							{
								printf("Terminal:ERROR : Package Check Fail\n");
								memset(Rxbuf,0, MAX_UART_BUF_SIZE);
								continue;
							}

						 	//printf("ADC Checksum succeed,CMD=0x%02x\n",cmd);
							if(deviceId==UART_NEW_DEVICEID_ADC_CHECK)
							{
								g_exist_adcCheck_module=1;
							}
							else if(deviceId==UART_NEW_DEVICEID_AMP_CONTROLER)
							{
								g_exist_ampControler_module=1;
							}
							ADC_Command_Proc(deviceId,cmd,data_len,Rxbuf+7);

							memset(Rxbuf,0, MAX_UART_BUF_SIZE);
						}
					}
				}
				break;
		}
	}
	pthread_exit(NULL);
}




/*********************************************************************
 * @fn      Uart_NewDevice_Init
 *
 * @brief   初始化新设备串口模块
 *
 * @param   void
 *
 * @return  ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
bool Uart_NewDevice_Init(void)
{
	int ret=-1;
	pthread_t t_uart_ADC_Pthread;
	pthread_attr_t Pthread_TERMINAL_Attr;
	pthread_attr_init(&Pthread_TERMINAL_Attr);
	pthread_attr_setdetachstate(&Pthread_TERMINAL_Attr, PTHREAD_CREATE_DETACHED);

	g_ADC_Uartfd = Init_Serial_Port(UART_ADC_DEVICE, B115200);

    if(g_ADC_Uartfd == -1)
    {
        return false;
    }
	/*创建一个线程单独接收终端串口数据*/
	pthread_create(&t_uart_ADC_Pthread, &Pthread_TERMINAL_Attr, (void *)Recv_Uart_ADC_Pthread, NULL);
	pthread_attr_destroy(&Pthread_TERMINAL_Attr);
	return true;
}