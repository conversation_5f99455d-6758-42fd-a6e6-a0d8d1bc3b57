#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <termios.h>
#include <pthread.h>
#include <time.h>
#include <sys/time.h>
#include <math.h>

#include "const.h"
#include "uartCommon.h"
#include "mi_gpio.h"
#include "gps.h"

#if IS_DEVICE_GPS_SYNCHRONIZER

void aipu_main_win_gps_time_update(int IsSelfcall);

static int g_gps_Uartfd = -1;
gps_info_t g_gps_info;
int g_gps_module_status = GPS_STATUS_SEARCHING;

void Gps_send_time_sync();

// GPS串口接收线程
void *Recv_GPS_Pthread(void *arg);

/**
 * @brief 初始化GPS串口模块
 * @return true 成功, false 失败
 */
bool Uart_GPS_Init(void)
{
    int ret = -1;
    pthread_t t_uart_gps_Pthread;
    pthread_attr_t Pthread_GPS_Attr;
    
    pthread_attr_init(&Pthread_GPS_Attr);
    pthread_attr_setdetachstate(&Pthread_GPS_Attr, PTHREAD_CREATE_DETACHED);
    
    // 初始化GPS信息结构体
    memset(&g_gps_info, 0, sizeof(gps_info_t));
    g_gps_info.status = GPS_STATUS_SEARCHING;
    g_gps_info.data_valid = false;
    
    // 打开GPS串口，波特率9600，8N1
    g_gps_Uartfd = Init_Serial_Port(UART_GPS_DEVICE, B9600);
    
    if(g_gps_Uartfd == -1)
    {
        printf("GPS UART init failed!\n");
        return false;
    }
    
    printf("GPS UART init success, fd=%d\n", g_gps_Uartfd);
    
    // 创建GPS数据接收线程
    ret = pthread_create(&t_uart_gps_Pthread, &Pthread_GPS_Attr, Recv_GPS_Pthread, NULL);
    pthread_attr_destroy(&Pthread_GPS_Attr);
    
    if(ret != 0)
    {
        printf("Create GPS receive thread failed!\n");
        close(g_gps_Uartfd);
        g_gps_Uartfd = -1;
        return false;
    }
    
    return true;
}

/**
 * @brief GPS串口数据接收线程
 */
void *Recv_GPS_Pthread(void *arg)
{
    printf("Enter GPS receive thread...\n");
    
    int rxlen;
    fd_set readfd;
    struct timeval timeout;
    int max_fd;
    char rxbuf[GPS_NMEA_RECEIVE_BUFFER_MAX] = {0};
    char nmea_sentence[GPS_NMEA_MAX_LENGTH] = {0};
    int sentence_pos = 0;
    
    while(1)
    {
        if(g_gps_Uartfd < 0)
        {
            sleep(1);
            continue;
        }
        
        FD_ZERO(&readfd);
        FD_SET(g_gps_Uartfd, &readfd);
        max_fd = g_gps_Uartfd + 1;
        
        timeout.tv_sec = 1;
        timeout.tv_usec = 0;
        
        int ret = select(max_fd, &readfd, NULL, NULL, &timeout);
        
        if(ret > 0 && FD_ISSET(g_gps_Uartfd, &readfd))
        {
            rxlen = read(g_gps_Uartfd, rxbuf, sizeof(rxbuf) - 1);
            //printf("rxlen=%d\n", rxlen);
            
            if(rxlen > 0)
            {
                rxbuf[rxlen] = '\0';
                
                // 逐字符处理，组装完整的NMEA语句
                for(int i = 0; i < rxlen; i++)
                {
                    char ch = rxbuf[i];
                    
                    if(ch == GPS_NMEA_FRAME_START)
                    {
                        // 开始新的NMEA语句
                        sentence_pos = 0;
                        nmea_sentence[sentence_pos++] = ch;
                    }
                    else if(ch == GPS_NMEA_FRAME_END || ch == '\r')
                    {
                        // NMEA语句结束
                        if(sentence_pos > 0)
                        {
                            nmea_sentence[sentence_pos] = '\0';
                            GPS_Parse_NMEA_Data(nmea_sentence);
                            sentence_pos = 0;
                        }
                    }
                    else if(sentence_pos < GPS_NMEA_MAX_LENGTH - 1)
                    {
                        // 继续接收字符
                        nmea_sentence[sentence_pos++] = ch;
                    }
                }
            }
        }
        else if(ret == 0)
        {
            // 超时，检查GPS模块状态
            // 如果长时间没有数据，可能需要重新初始化
        }
    }
    
    return NULL;
}

/**
 * @brief 解析NMEA数据
 * @param nmea_data NMEA语句字符串
 */
void GPS_Parse_NMEA_Data(char *nmea_data)
{
    if(!nmea_data || strlen(nmea_data) < 6)
        return;
    
    // 验证校验和
    if(!GPS_Verify_Checksum(nmea_data))
    {
        printf("GPS NMEA checksum error: %s\n", nmea_data);
        return;
    }
    
    // 根据NMEA语句类型进行解析
    if(strncmp(nmea_data, "$GPRMC", 6) == 0 || strncmp(nmea_data, "$GNRMC", 6) == 0)
    {
        GPS_Parse_GPRMC(nmea_data);
    }
    else if(strncmp(nmea_data, "$GPGGA", 6) == 0 || strncmp(nmea_data, "$GNGGA", 6) == 0)
    {
        GPS_Parse_GPGGA(nmea_data);
    }
    else if(strncmp(nmea_data, "$GPGSA", 6) == 0 || strncmp(nmea_data, "$GNGSA", 6) == 0)
    {
        GPS_Parse_GPGSA(nmea_data);
    }
    else if(strncmp(nmea_data, "$GPGSV", 6) == 0 || strncmp(nmea_data, "$GNGSV", 6) == 0)
    {
        GPS_Parse_GPGSV(nmea_data);
    }
}

/**
 * @brief 解析GPRMC语句（推荐最小定位信息）
 * @param sentence NMEA语句
 */
static void GPS_Parse_GPRMC(char *sentence)
{
    char *token;
    char *saveptr;
    int field = 0;
    char temp_str[32];
    
    // 复制字符串用于分割
    char sentence_copy[GPS_NMEA_MAX_LENGTH];
    strncpy(sentence_copy, sentence, sizeof(sentence_copy) - 1);
    sentence_copy[sizeof(sentence_copy) - 1] = '\0';
    
    token = strtok_r(sentence_copy, ",", &saveptr);
    
    while(token != NULL && field < 12)
    {
        switch(field)
        {
            case 1: // UTC时间
                if(strlen(token) >= 6)
                {
                    GPS_Extract_Time(token, &g_gps_info.utc_time);
                }
                break;
                
            case 2: // 数据有效性
            {
                bool isDataValid =  (token[0] == 'A');
                if(isDataValid != g_gps_info.data_valid)
                {
                    g_gps_info.data_valid = isDataValid;
                    #if (AIPU_VERSION && SELECTED_DISPLAY_TYPE == DISPLAY_TYPE_SPI_2P1_320X240)
                    aipu_main_win_gps_time_update(0);
                    //控制LED灯
                    if(g_gps_info.data_valid)
                        Set_Gpio_High(PAD_KEY7);
                    else
                        Set_Gpio_Low(PAD_KEY7);
                    #endif
                }
                g_gps_info.status = isDataValid ? GPS_STATUS_VALID : GPS_STATUS_INVALID;
            }
            break;
                
            case 3: // 纬度
                if(strlen(token) > 0)
                {
                    strncpy(temp_str, token, sizeof(temp_str) - 1);
                    temp_str[sizeof(temp_str) - 1] = '\0';
                }
                break;
                
            case 4: // 纬度方向
                if(strlen(temp_str) > 0 && strlen(token) > 0)
                {
                    g_gps_info.position.latitude = GPS_Convert_Coordinate(temp_str, token[0]);
                    g_gps_info.position.lat_direction = token[0];
                }
                break;
                
            case 5: // 经度
                if(strlen(token) > 0)
                {
                    strncpy(temp_str, token, sizeof(temp_str) - 1);
                    temp_str[sizeof(temp_str) - 1] = '\0';
                }
                break;
                
            case 6: // 经度方向
                if(strlen(temp_str) > 0 && strlen(token) > 0)
                {
                    g_gps_info.position.longitude = GPS_Convert_Coordinate(temp_str, token[0]);
                    g_gps_info.position.lon_direction = token[0];
                }
                break;
                
            case 7: // 速度
                if(strlen(token) > 0)
                {
                    g_gps_info.speed = atof(token);
                }
                break;
                
            case 8: // 航向角
                if(strlen(token) > 0)
                {
                    g_gps_info.course = atof(token);
                }
                break;
                
            case 9: // 日期
                if(strlen(token) >= 6)
                {
                    GPS_Extract_Date(token, &g_gps_info.utc_time);
                }
                break;
        }
        
        token = strtok_r(NULL, ",", &saveptr);
        field++;
    }
    
    // 更新最后更新时间
    g_gps_info.last_update_time = time(NULL);
    
    // 如果数据有效，更新系统时间
    if(g_gps_info.data_valid)
    {
        GPS_Update_System_Time();
    }
}

/**
 * @brief 解析GPGGA语句（全球定位系统固定数据）
 * @param sentence NMEA语句
 */
static void GPS_Parse_GPGGA(char *sentence)
{
    char *token;
    char *saveptr;
    int field = 0;
    
    char sentence_copy[GPS_NMEA_MAX_LENGTH];
    strncpy(sentence_copy, sentence, sizeof(sentence_copy) - 1);
    sentence_copy[sizeof(sentence_copy) - 1] = '\0';
    
    token = strtok_r(sentence_copy, ",", &saveptr);
    
    while(token != NULL && field < 15)
    {
        switch(field)
        {
            case 6: // 定位质量指示
                if(strlen(token) > 0)
                {
                    int quality = atoi(token);
                    if(quality > 0)
                    {
                        g_gps_info.status = GPS_STATUS_VALID;
                    }
                }
                break;
                
            case 7: // 参与定位的卫星数量
                if(strlen(token) > 0)
                {
                    g_gps_info.satellite.satellites_used = atoi(token);
                }
                break;
                
            case 8: // 水平精度因子
                if(strlen(token) > 0)
                {
                    g_gps_info.satellite.hdop = atof(token);
                }
                break;
                
            case 9: // 海拔高度
                if(strlen(token) > 0)
                {
                    g_gps_info.position.altitude = atof(token);
                }
                break;
        }
        
        token = strtok_r(NULL, ",", &saveptr);
        field++;
    }
}

/**
 * @brief 解析GPGSA语句（当前卫星信息）
 * @param sentence NMEA语句
 */
static void GPS_Parse_GPGSA(char *sentence)
{
    char *token;
    char *saveptr;
    int field = 0;
    
    char sentence_copy[GPS_NMEA_MAX_LENGTH];
    strncpy(sentence_copy, sentence, sizeof(sentence_copy) - 1);
    sentence_copy[sizeof(sentence_copy) - 1] = '\0';
    
    token = strtok_r(sentence_copy, ",", &saveptr);
    
    while(token != NULL && field < 18)
    {
        switch(field)
        {
            case 2: // 定位模式
                if(strlen(token) > 0)
                {
                    g_gps_info.fix_mode = atoi(token);
                }
                break;
                
            case 15: // 位置精度因子
                if(strlen(token) > 0)
                {
                    g_gps_info.satellite.pdop = atof(token);
                }
                break;
                
            case 16: // 水平精度因子
                if(strlen(token) > 0)
                {
                    g_gps_info.satellite.hdop = atof(token);
                }
                break;
                
            case 17: // 垂直精度因子
                if(strlen(token) > 0)
                {
                    g_gps_info.satellite.vdop = atof(token);
                }
                break;
        }
        
        token = strtok_r(NULL, ",", &saveptr);
        field++;
    }
}

/**
 * @brief 解析GPGSV语句（可见卫星信息）
 * @param sentence NMEA语句
 */
static void GPS_Parse_GPGSV(char *sentence)
{
    char *token;
    char *saveptr;
    int field = 0;
    
    char sentence_copy[GPS_NMEA_MAX_LENGTH];
    strncpy(sentence_copy, sentence, sizeof(sentence_copy) - 1);
    sentence_copy[sizeof(sentence_copy) - 1] = '\0';
    
    token = strtok_r(sentence_copy, ",", &saveptr);
    
    while(token != NULL && field < 4)
    {
        switch(field)
        {
            case 3: // 可见卫星总数
                if(strlen(token) > 0)
                {
                    g_gps_info.satellite.satellites_view = atoi(token);
                }
                break;
        }
        
        token = strtok_r(NULL, ",", &saveptr);
        field++;
    }
}

/**
 * @brief 坐标转换（度分格式转十进制度）
 * @param coord 坐标字符串
 * @param direction 方向字符
 * @return 十进制度坐标
 */
static double GPS_Convert_Coordinate(char *coord, char direction)
{
    if(!coord || strlen(coord) < 4)
        return 0.0;
    
    double coordinate = atof(coord);
    
    // 提取度和分
    int degrees = (int)(coordinate / 100);
    double minutes = coordinate - (degrees * 100);
    
    // 转换为十进制度
    double decimal_degrees = degrees + (minutes / 60.0);
    
    // 根据方向确定正负
    if(direction == 'S' || direction == 'W')
    {
        decimal_degrees = -decimal_degrees;
    }
    
    return decimal_degrees;
}

/**
 * @brief 验证NMEA语句校验和
 * @param sentence NMEA语句
 * @return true 校验成功, false 校验失败
 */
static bool GPS_Verify_Checksum(char *sentence)
{
    if(!sentence || strlen(sentence) < 4)
        return false;
    
    // 查找校验和位置
    char *checksum_pos = strrchr(sentence, '*');
    if(!checksum_pos)
        return false;
    
    // 计算校验和
    unsigned char calculated_checksum = 0;
    for(char *p = sentence + 1; p < checksum_pos; p++)
    {
        calculated_checksum ^= *p;
    }
    
    // 提取原始校验和
    unsigned char original_checksum = 0;
    sscanf(checksum_pos + 1, "%2hhx", &original_checksum);
    
    return (calculated_checksum == original_checksum);
}

/**
 * @brief 从时间字符串提取时间信息
 * @param time_str 时间字符串 (HHMMSS.sss)
 * @param time 时间结构体指针
 */
static void GPS_Extract_Time(char *time_str, gps_time_t *time)
{
    if(!time_str || !time || strlen(time_str) < 6)
        return;
    
    char temp[8];
    
    // 提取小时
    strncpy(temp, time_str, 2);
    temp[2] = '\0';
    time->hour = atoi(temp);
    
    // 提取分钟
    strncpy(temp, time_str + 2, 2);
    temp[2] = '\0';
    time->minute = atoi(temp);
    
    // 提取秒
    strncpy(temp, time_str + 4, 2);
    temp[2] = '\0';
    time->second = atoi(temp);
    
    // 提取毫秒（如果有）
    if(strlen(time_str) > 7 && time_str[6] == '.')
    {
        strncpy(temp, time_str + 7, 3);
        temp[3] = '\0';
        time->millisecond = atoi(temp);
    }
    else
    {
        time->millisecond = 0;
    }
}

/**
 * @brief 从日期字符串提取日期信息
 * @param date_str 日期字符串 (DDMMYY)
 * @param time 时间结构体指针
 */
static void GPS_Extract_Date(char *date_str, gps_time_t *time)
{
    if(!date_str || !time || strlen(date_str) < 6)
        return;
    
    char temp[4];
    
    // 提取日
    strncpy(temp, date_str, 2);
    temp[2] = '\0';
    time->day = atoi(temp);
    
    // 提取月
    strncpy(temp, date_str + 2, 2);
    temp[2] = '\0';
    time->month = atoi(temp);
    
    // 提取年
    strncpy(temp, date_str + 4, 2);
    temp[2] = '\0';
    int year = atoi(temp);
    time->year = (year < 80) ? (2000 + year) : (1900 + year);
}

/**
 * @brief 更新系统时间
 */
void GPS_Update_System_Time(void)
{
    if(!GPS_Is_Time_Valid())
        return;
    
    struct tm tm_time;
    struct timeval tv;
    
    // 设置时间结构体
    tm_time.tm_year = g_gps_info.utc_time.year - 1900;
    tm_time.tm_mon = g_gps_info.utc_time.month - 1;
    tm_time.tm_mday = g_gps_info.utc_time.day;
    tm_time.tm_hour = g_gps_info.utc_time.hour;
    tm_time.tm_min = g_gps_info.utc_time.minute;
    tm_time.tm_sec = g_gps_info.utc_time.second;
    tm_time.tm_isdst = 0;
    
    // 转换为时间戳
    time_t timestamp = timegm(&tm_time);
    
    if(timestamp != -1)
    {
        #if 1
        // 北京时间比UTC快8小时，因此需要将时间戳增加8小时
        timestamp += 8 * 3600;
        // 将时间戳转换回tm结构体以获取正确的日期时间显示
        struct tm *bj_time = localtime(&timestamp);
        #endif

        tv.tv_sec = timestamp;
        tv.tv_usec = g_gps_info.utc_time.millisecond * 1000;
        
        // 设置系统时间
        if(settimeofday(&tv, NULL) == 0)
        {
            #if 0
            printf("GPS time synchronized (UTC Time): %04d-%02d-%02d %02d:%02d:%02d\n",
                   g_gps_info.utc_time.year, g_gps_info.utc_time.month, g_gps_info.utc_time.day,
                   g_gps_info.utc_time.hour, g_gps_info.utc_time.minute, g_gps_info.utc_time.second);
            #else
            printf("GPS time synchronized (Beijing Time): %04d-%02d-%02d %02d:%02d:%02d\n",
                   bj_time->tm_year + 1900, bj_time->tm_mon + 1, bj_time->tm_mday,
                   bj_time->tm_hour, bj_time->tm_min, bj_time->tm_sec);
            #endif
        }
        else
        {
            printf("Failed to set system time\n");
        }
    }

    //将时间发送给服务器
    Gps_send_time_sync();
}

/**
 * @brief 检查GPS时间是否有效
 * @return true 有效, false 无效
 */
bool GPS_Is_Time_Valid(void)
{
    return (g_gps_info.data_valid && 
            g_gps_info.utc_time.year > 2020 && 
            g_gps_info.utc_time.month >= 1 && g_gps_info.utc_time.month <= 12 &&
            g_gps_info.utc_time.day >= 1 && g_gps_info.utc_time.day <= 31 &&
            g_gps_info.utc_time.hour >= 0 && g_gps_info.utc_time.hour <= 23 &&
            g_gps_info.utc_time.minute >= 0 && g_gps_info.utc_time.minute <= 59 &&
            g_gps_info.utc_time.second >= 0 && g_gps_info.utc_time.second <= 59);
}

/**
 * @brief 获取当前GPS时间
 * @param time 时间结构体指针
 */
void GPS_Get_Current_Time(gps_time_t *time)
{
    if(time)
    {
        memcpy(time, &g_gps_info.utc_time, sizeof(gps_time_t));
    }
}

/**
 * @brief 获取位置信息
 * @param pos 位置结构体指针
 */
void GPS_Get_Position_Info(gps_position_t *pos)
{
    if(pos)
    {
        memcpy(pos, &g_gps_info.position, sizeof(gps_position_t));
    }
}

/**
 * @brief 打印GPS状态信息
 */
void GPS_Print_Status(void)
{
    printf("=== GPS Status ===\n");
    printf("Status: %s\n", 
           (g_gps_info.status == GPS_STATUS_VALID) ? "Valid" :
           (g_gps_info.status == GPS_STATUS_SEARCHING) ? "Searching" : "Invalid");
    printf("Fix Mode: %s\n",
           (g_gps_info.fix_mode == GPS_MODE_3D_FIX) ? "3D Fix" :
           (g_gps_info.fix_mode == GPS_MODE_2D_FIX) ? "2D Fix" : "No Fix");
    printf("Time: %04d-%02d-%02d %02d:%02d:%02d.%03d UTC\n",
           g_gps_info.utc_time.year, g_gps_info.utc_time.month, g_gps_info.utc_time.day,
           g_gps_info.utc_time.hour, g_gps_info.utc_time.minute, g_gps_info.utc_time.second,
           g_gps_info.utc_time.millisecond);
    printf("Position: %.6f%c, %.6f%c\n",
           fabs(g_gps_info.position.latitude), g_gps_info.position.lat_direction,
           fabs(g_gps_info.position.longitude), g_gps_info.position.lon_direction);
    printf("Altitude: %.2f m\n", g_gps_info.position.altitude);
    printf("Satellites: %d used, %d visible\n",
           g_gps_info.satellite.satellites_used, g_gps_info.satellite.satellites_view);
    printf("HDOP: %.2f, VDOP: %.2f, PDOP: %.2f\n",
           g_gps_info.satellite.hdop, g_gps_info.satellite.vdop, g_gps_info.satellite.pdop);
    printf("Speed: %.2f knots, Course: %.2f°\n", g_gps_info.speed, g_gps_info.course);
    printf("Data Valid: %s\n", g_gps_info.data_valid ? "Yes" : "No");
    printf("==================\n");
}

#endif // IS_DEVICE_GPS_SYNCHRONIZER