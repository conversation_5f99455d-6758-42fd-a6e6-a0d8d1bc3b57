#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <termios.h>
#include <pthread.h>

#include "sysconf.h"
#include "uartCommon.h"
#include "volumeControl.h"

int g_exist_volumeControl_device=0; //是否存在音控器设备

static int g_volumeControl_Uartfd;

static const unsigned char buf_config_light[]={0x02,0x06,0x10,0x08,0x01,0xff,0x4d,0x2b};

static const unsigned char buf_key_aux_volume_up[]={0x02, 0x03, 0x00, 0x02, 0x07, 0x01, 0x27, 0xc9};
static const unsigned char buf_key_aux_volume_down[]={0x02, 0x03, 0x00, 0x02, 0x08, 0x02, 0x62, 0x38};
static const unsigned char buf_key_net_volume_up[]={0x02, 0x03, 0x00, 0x02, 0x09, 0x04, 0xe3, 0xaa};
static const unsigned char buf_key_net_volume_down[]={0x02, 0x03, 0x00, 0x02, 0x0a, 0x08, 0xe3, 0x5f};



void VolumeControl_Command_Proc( unsigned char cmdCode, unsigned short datalen,unsigned char *data )
{
	switch(cmdCode)
	{
		case 1:
	  	break;
	}
}


void VolumeControl_uart_send(bool bIsWrite, short int regAddr, unsigned short datalen,unsigned char *data) {
	unsigned char *send_buf=NULL;
	send_buf= (unsigned char*)malloc(VOLUME_CONTROL_UART_RECEIVE_BUFFER_MAX);

	int pos = 0, i;
	send_buf[pos++] = 0x02; //通讯机号
    send_buf[pos++] = bIsWrite?0x06:0x03;  //命令码，03为读，06为写
    send_buf[pos++] = regAddr>>8;  //寄存器地址，高位在前（按键主动发送时这两个字节为数据长度高字节）
    send_buf[pos++] = regAddr;  //寄存器地址，低位在后（按键主动发送时这两个字节为数据长度低字节）
	
    for(i=0;i<datalen;i++)
    {
        send_buf[pos++] = data[i];
    }

    int crc16=crc16_ccitt_volumeControl(send_buf,pos);
    send_buf[pos++] = crc16;   //CRC低位在前
    send_buf[pos++] = crc16>>8;   //CRC高位在后

	write(g_volumeControl_Uartfd, send_buf, pos); //发送数据

#if 1
	printf("VolumeControl_uart_send:isWrite=%d,regAddr=0x%x,datalen=%d\r\n",bIsWrite,regAddr,datalen);

#else
	printf("VolumeControl_uart_send:\r\n");
	for(i=0;i<pos;i++)
	{
		printf("%02x ",send_buf[i]);
	}
	printf("\r\n");
#endif

	free(send_buf);
}


void FIRE_Send_Set_Config_WorkMode()
{
	unsigned short datalen = 2;
	unsigned char send_buf[VOLUME_CONTROL_UART_RECEIVE_BUFFER_MAX] = { 0 };

	send_buf[0]=0x00;
	send_buf[1]=0x04;

	VolumeControl_uart_send(true,VC_UART_REG_ADDR_CONFIG,datalen,send_buf);
}



void FIRE_Send_Set_Config_Light()
{
	unsigned short datalen = 2;
	unsigned char send_buf[VOLUME_CONTROL_UART_RECEIVE_BUFFER_MAX] = { 0 };

	send_buf[0]=0x01;
	send_buf[1]=0xff;

	VolumeControl_uart_send(true,VC_UART_LIGHT_CONFIG,datalen,send_buf);
}

/*********************************************************************
 * @fn      Recv_Uart_VolumeControl_Pthread
 *
 * @brief   消防模块串口数据接收线程(回调函数)
 *
 * @param   void
 *
 * @return  none
 */
void *Recv_Uart_VolumeControl_Pthread(void)
{
	printf("Recv_Uart_VolumeControl_Pthread...\n");
	int Rxlen;
	int ret, i;
	int Index = 0;
	fd_set readfd;
	struct timeval timeout;
	int max_fd;
	int Pkg_Length=0;
	int Read_Size = 16;

	unsigned char Rxbuf[MAX_UART_BUF_SIZE]={0};


	FD_ZERO(&readfd);               //清空读文件描述集合
	FD_SET(g_volumeControl_Uartfd, &readfd);  //注册套接字文件描述符

	while (1)
	{
		timeout.tv_sec = 0;      // 获取数据超时时间设置
		timeout.tv_usec = 100000;

		FD_ZERO(&readfd);               //清空读文件描述集合
		FD_SET(g_volumeControl_Uartfd, &readfd);  //注册套接字文件描述符

        static bool bSetConfig=false;
        if(!bSetConfig)
        {
            bSetConfig=true;
            //不主动改变按键灯状态，出厂前手动配置
            //FIRE_Send_Set_Config_WorkMode();
            FIRE_Send_Set_Config_Light();
        }

		ret = select(g_volumeControl_Uartfd+1, &readfd, NULL, NULL, &timeout);
		switch(ret)
		{
			case -1 : //调用出错
				perror("select");
				Index = 0;
				Read_Size = 16;
				memset(Rxbuf,0, MAX_UART_BUF_SIZE);
				break;

			case 0 : //超时
				//printf("timeout!\n");
				Index = 0;
				Read_Size = 16;
				memset(Rxbuf,0, MAX_UART_BUF_SIZE);
				break;

			default : //判断是否有数据可读

				/*接收对应串口的数据*/

				Rxlen = read(g_volumeControl_Uartfd, &Rxbuf[Index], Read_Size);

				/*数据校验*/
				if (Rxlen < 8)
				{
					perror("Rxlen<7\n");
					Index = 0;
					Read_Size = 16;
					memset(Rxbuf,0, MAX_UART_BUF_SIZE);
					continue;
				}
				else
				{

					#if 0
					printf("Terminal:The Recv Data Is : \n");
					for (i = 0; i < Rxlen; i++)
					{
						printf("0x%x ", Rxbuf[i+Index]);
					}
					printf("\n");
					#endif

                    int crcChecksum=crc16_ccitt_volumeControl(Rxbuf, Rxlen-2);
                    int recvChecksum=(Rxbuf[Rxlen-1]<<8)+(Rxbuf[Rxlen-2]);
                    if ( recvChecksum != crcChecksum ) //校验数据
                    {
                        printf("Terminal:ERROR : Package Check Fail\n");
                        memset(Rxbuf,0, MAX_UART_BUF_SIZE);
                        continue;
                    }

                    printf("VolumeControl Checksum succeed\n");

                    g_exist_volumeControl_device=1;

					bool isCmd_volume=false;
					int volumeType=0;
                    if(memcmp(buf_key_aux_volume_up,Rxbuf,sizeof(buf_key_aux_volume_up)) == 0)
                    {
                        printf("key_aux_volume_up\n");
						volumeType = 1;
						isCmd_volume=true;
                    }
                    if(memcmp(buf_key_aux_volume_down,Rxbuf,sizeof(buf_key_aux_volume_down)) == 0)
                    {
                        printf("key_aux_volume_down\n");
						volumeType = 2;
						isCmd_volume=true;
                    }
                    if(memcmp(buf_key_net_volume_up,Rxbuf,sizeof(buf_key_net_volume_up)) == 0)
                    {
                        printf("key_net_volume_up\n");
						volumeType = 3;
						isCmd_volume=true;
                    }
                    if(memcmp(buf_key_net_volume_down,Rxbuf,sizeof(buf_key_net_volume_down)) == 0)
                    {
                        printf("key_net_volume_down\n");
						volumeType = 4;
						isCmd_volume=true;
                    }

					if(isCmd_volume)
					{
						int event_IsAdd=(volumeType == 1 || volumeType == 3);
						int step = 10;
						if(!event_IsAdd)
						{
							step *=-1;
						}
						if(volumeType == 1 || volumeType == 2)
						{
							unsigned int temp_aux_volume=g_volumeCD_aux_volume;
							//如果是音量减，且当前AUX音量已经比步进小了,那么音量为0
							if( step<0 && (g_volumeCD_aux_volume < abs(step)) )
							{
								g_volumeCD_aux_volume=0;
							}
							else
							{
								g_volumeCD_aux_volume+=step;
							}
							if(g_volumeCD_aux_volume>100)
								g_volumeCD_aux_volume=100;
							
							if(temp_aux_volume!=g_volumeCD_aux_volume)
							{
								printf("g_volumeCD_aux_volume=%d\n",g_volumeCD_aux_volume);
								save_sysconf(INI_SECTION_BASIC,"VolumeControl_Aux");
							}
						}
						else if(volumeType == 3 || volumeType == 4)
						{
							unsigned int temp_net_volume=g_volumeCD_net_volume;
							//如果是音量减，且当前NET音量已经比步进小了,那么音量为0
							if( step<0 && (g_volumeCD_net_volume < abs(step)) )
							{
								g_volumeCD_net_volume=0;
							}
							else
							{
								g_volumeCD_net_volume+=step;
							}
							if(g_volumeCD_net_volume>100)
								g_volumeCD_net_volume=100;
							
							if(temp_net_volume!=g_volumeCD_net_volume)
							{
								printf("g_volumeCD_net_volume=%d\n",g_volumeCD_net_volume);
								save_sysconf(INI_SECTION_BASIC,"VolumeControl_Net");
							}
						}
					}

                    memset(Rxbuf,0, MAX_UART_BUF_SIZE);
				}
				break;
		}
	}
	pthread_exit(NULL);
}




/*********************************************************************
 * @fn      Uart_VolumeControl_Init
 *
 * @brief   初始化音控器串口模块
 *
 * @param   void
 *
 * @return  ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
bool Uart_VolumeControl_Init(void)
{
	int ret=-1;
	pthread_t t_uart_fire_Pthread;
	pthread_attr_t Pthread_TERMINAL_Attr;
	pthread_attr_init(&Pthread_TERMINAL_Attr);
	pthread_attr_setdetachstate(&Pthread_TERMINAL_Attr, PTHREAD_CREATE_DETACHED);

	system("/customer/riu_w 0x103e 0x3a 0x0075");
	
	g_volumeControl_Uartfd = Init_Serial_Port(UART_VolumeControl_DEVICE, B9600);

    if(g_volumeControl_Uartfd == -1)
    {
        return false;
    }
	/*创建一个线程单独接收终端串口数据*/
	pthread_create(&t_uart_fire_Pthread, &Pthread_TERMINAL_Attr, (void *)Recv_Uart_VolumeControl_Pthread, NULL);
	pthread_attr_destroy(&Pthread_TERMINAL_Attr);
	return true;
}