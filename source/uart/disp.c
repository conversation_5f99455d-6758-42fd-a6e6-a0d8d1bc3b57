#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <termios.h>
#include <pthread.h>

#include "sysconf.h"
#include "uartCommon.h"
#include "disp.h"

static int g_disp_Uartfd=-1;

#if LZY_COMMERCIAL_VERSION
static int g_lzy_disp_Uartfd=-1;
#endif

#if IS_DEVICE_AMP_CONTROLER
static int g_amp_controler_Uartfd=-1;
#endif


/***********显示模块存在标记***********/
unsigned char g_PreExistDispModule = 0;	//之前是否存在串口模块
unsigned char g_CurExistDsipModule = 0;	//当前是否存在串口模块
unsigned char g_CurDispUpgrading = 0;	//当前正处于升级状态
unsigned char g_DispVersion[16];		//显示模块版本号，存在版本号才允许升级
/*****************************************/

// 串口错误统计变量
uart_error_stats_t g_uart_error_stats = {0};

st_disp_upgrade_info disp_upgrade_info;

void Disp_Command_Proc( unsigned char cmd, unsigned short datalen,unsigned char *data )
{
	static int receiveFirst=0;
	if(!receiveFirst)
	{
		receiveFirst=1;

		g_CurExistDsipModule=1;
		if(!g_PreExistDispModule)
		{
			g_PreExistDispModule=1;
			save_sysconf(INI_SECTION_BASIC,"Exist_DispModule");
		}

		#if (IS_DEVICE_POWER_SEQUENCE)
		Init_Sequence_Power();
		#endif
	}

	switch(cmd)
	{
		case UART_DISP_CMD_DEVICE:
        	Disp_Receive_device_info(datalen,data);
	  	break;
		case UART_DISP_CMD_STATUS:
			Disp_Receive_status_info(datalen,data);
	  	break;
		case UART_DISP_CMD_NETWORK:
			Disp_Receive_network_info(datalen,data);
	  	break;
		case UART_DISP_CMD_TIME:
			Disp_Receive_time_info(datalen,data);
	  	break;
		case UART_DISP_CMD_VOLUME:
			Disp_Receive_volume_info(datalen,data);
	  	break;
#if (IS_DEVICE_AUDIO_COLLECTOR)
		case UART_DISP_CMD_AC_CHANNEL_STATUS:
			Disp_Send_Audio_Collector_Channel_Status();
		break;
#endif
#if (IS_DEVICE_POWER_SEQUENCE)
		case UART_DISP_CMD_SEQUENCE_POWER_CONTROL_MODE:
			Disp_Receive_SequencePower_ControlMode(datalen,data);
		break;
		case UART_DISP_CMD_SEQUENCE_POWER_CHANNEL_STATUS:
			Disp_Receive_SequencePower_ChannelStatus(datalen,data);
		break;
		case UART_DISP_CMD_SEQUENCE_POWER_TRIGGER_INPUT:
			Disp_Receive_SequencePower_TriggerInput(datalen,data);
		break;
		case UART_DISP_CMD_SEQUENCE_POWER_TRIGGER_ALL_OPEN_CLOSE_KEY:
			Disp_Receive_SequencePower_oneKey(datalen,data);
		break;
#endif
		case UART_DISP_CMD_SN:
			Disp_Send_DeviceSN();
		break;
#if (IS_DEVICE_FIRE_COLLECTOR)
		case UART_DISP_CMD_FIRE_TRIGGER_STATUS:
			Disp_Send_Fire_TriggerStatus();
		break;
#endif
#if (IS_DEVICE_REMOTE_CONTROLER)
		case UART_DISP_CMD_REMOTE_CONTROLER_KEY:
			Disp_Receive_RemoteControler_Key(datalen,data);
		break;
#endif

		case UART_DISP_CMD_UPGRADE_INFO:
			if(data[0] == 0)		//接受
			{
				disp_upgrade_info.Upgrade_process = 1;
			}
			else if(data[0] == 1)	//拒绝
			{
				//提示失败
				disp_upgrade_info.Upgrade_process = 3;
			}
			break;
		case UART_DISP_CMD_UPGRADE_STATUS:
			if(!g_CurDispUpgrading)
				g_CurDispUpgrading=1;
			printf("UART_DISP_CMD_UPGRADE_STATUS:%d\n",data[0]);
			if(data[0] == 0)
			{
				Disp_Send_Upgrade_Info();
			}
			else if(data[0] == 1)
			{
				if(disp_upgrade_info.IsStart == 0 && disp_upgrade_info.Upgrade_process == 1)
				{
					disp_upgrade_info.upgrade_valid=1;
					disp_upgrade_info.IsStart = 1;
					printf("disp_upgrade_info.IsStart=1...\n");
				}
			}
			break;
		case UART_DISP_CMD_UPGRADE_PKG:
		{
			disp_upgrade_info.upgrade_valid=1;
			int pkgId=data[0]+(data[1]<<8);
			printf("recv id=%d\n",pkgId);	
			break;
		}
	}
}


void Disp_uart_send(unsigned char cmd, unsigned short datalen, unsigned char *data) {
    #if LZY_COMMERCIAL_VERSION
    return;
    #endif
    
    if (!ENABLE_DISP_UART_MODULE)
        return;

    if (is_Enable_UART_LED_PLAYER) {
        return;
    }

    #if IS_DEVICE_POWER_SEQUENCE
        #if(CUR_POWER_SEQUENCE_TYPE == MODEL_SEQUENCE_POWER_WEISHENG || CUR_POWER_SEQUENCE_TYPE == MODEL_SEQUENCE_POWER_AIPU)
        if (cmd != UART_DISP_CMD_SEQUENCE_POWER_CONTROL_MODE && cmd != UART_DISP_CMD_SEQUENCE_POWER_CHANNEL_STATUS
            && cmd != UART_DISP_CMD_SEQUENCE_POWER_TRIGGER_INPUT && cmd != UART_DISP_CMD_SEQUENCE_POWER_TRIGGER_ALL_OPEN_CLOSE_KEY
            && cmd != UART_DISP_CMD_NET_STATUS) {
            return;
        }
        #endif
    #endif

    // 检查串口文件描述符有效性
    if (g_disp_Uartfd < 0) {
        printf("UART send error: invalid file descriptor\n");
        return;
    }

    // 检查数据长度合法性
    if (datalen > DISP_UART_DATA_LENGTH_MAX) {
        printf("UART send error: data length too large: %d\n", datalen);
        return;
    }

    // 检查数据指针有效性（当datalen > 0时）
    if (datalen > 0 && data == NULL) {
        printf("UART send error: data pointer is NULL\n");
        return;
    }

    unsigned char *send_buf = NULL;
    int buffer_size;
    
    if (cmd == UART_DISP_CMD_UPGRADE_PKG) {
        buffer_size = DISP_UART_UPGRADE_BUFFER_MAX;
    } else {
        buffer_size = DISP_UART_RECEIVE_BUFFER_MAX;
    }
    
    send_buf = (unsigned char*)malloc(buffer_size);
    if (send_buf == NULL) {
        printf("UART send error: malloc failed\n");
        return;
    }

    // 构建数据帧
    int pos = 0;
    send_buf[pos++] = DISP_UART_FRAME_HEAD1;
    send_buf[pos++] = DISP_UART_FRAME_HEAD2;
    send_buf[pos++] = cmd;
    send_buf[pos++] = datalen & 0xFF;        // 长度低字节
    send_buf[pos++] = (datalen >> 8) & 0xFF; // 长度高字节

    // 复制数据
    if (datalen > 0 && data != NULL) {
        memcpy(send_buf + pos, data, datalen);
        pos += datalen;
    }

    // 添加校验和
    send_buf[pos++] = Checksum(cmd, datalen, data);

    // 发送数据，增加重试机制
    int retry_count = 0;
    const int MAX_RETRY = 3;
    ssize_t bytes_written = 0;
    #if 0
    while (retry_count < MAX_RETRY) {
        bytes_written = write(g_disp_Uartfd, send_buf, pos);
        
        if (bytes_written == pos) {
            // 发送成功
            g_uart_error_stats.total_sent++;
            break;
        } else if (bytes_written < 0) {
            // 发送错误
            perror("UART write error");
            g_uart_error_stats.send_errors++;
            retry_count++;
            if (retry_count < MAX_RETRY) {
                printf("UART send retry %d/%d\n", retry_count, MAX_RETRY);
                usleep(10000); // 延时10ms后重试
            }
        } else {
            // 部分发送成功，这种情况比较少见
            printf("UART partial write: %zd/%d bytes\n", bytes_written, pos);
            g_uart_error_stats.send_errors++;
            retry_count++;
            if (retry_count < MAX_RETRY) {
                usleep(10000);
            }
        }
    }
	#endif

	bytes_written = write(g_disp_Uartfd, send_buf, pos); //发送数据

    if (bytes_written != pos) {
        printf("UART send failed after %d retries, cmd=0x%02x\n", MAX_RETRY, cmd);
    } else {
        #if 1
        printf("UART send success: cmd=0x%02x, datalen=%d\n", cmd, datalen);
        #else
        printf("UART send data: ");
        for (int i = 0; i < pos; i++) {
            printf("%02x ", send_buf[i]);
        }
        printf("\n");
        #endif
    }

    free(send_buf);
}



//启动完成通知，主动发送
void Disp_Send_Boot_OK()
{
  	unsigned char cmd = UART_DISP_CMD_BOOT;
	unsigned short datalen = 0;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };
	
	Disp_uart_send(cmd,datalen,send_buf);
}

//设备信息
static void Disp_Receive_device_info(unsigned short datalen,unsigned char *rcbuf)
{
	//查询时才送设备信息
	if(datalen == 0)	
	{
		Disp_Send_device_info();
	}
	else	//显示模块发送自身的设备信息，保存
	{
		printf("Disp_Receive_device_info...\n");
		//接收版本号,用于升级
		int pos = 0;
		pos+=6;	//跳过MAC
		pos+=1;	//跳过设备类型
		int deviceNameLen=rcbuf[pos++];	//设备名称长度
		pos+=deviceNameLen;		//跳过设备名称
		int verisonLen=rcbuf[pos++];	//固件版本长度
		if(verisonLen>=sizeof(g_DispVersion))
		{
			return;
		}
		char verison[sizeof(g_DispVersion)]={0};
		memcpy(verison,rcbuf+pos,verisonLen);
		sprintf(g_DispVersion,"%s",verison);
		printf("DispVerison=%s\n",g_DispVersion);
		pos+=verisonLen;
	}
}

//设备信息
void Disp_Send_device_info()
{
	unsigned char cmd = UART_DISP_CMD_DEVICE;
	unsigned short datalen = 0;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };

	int pos=0;
	//MAC
	memcpy(send_buf,g_mac_addr,6);
	pos+=6;
	//设备类型
	send_buf[pos++]=CURRENT_DEVICE_MODEL;
	//设备名称长度
	int alias_len=strlen(g_device_alias);
	send_buf[pos++]=alias_len;
	//设备名称
	memcpy(send_buf+pos,g_device_alias,alias_len);
	pos+=alias_len;
	//固件版本长度
	int firmware_version_len = strlen(LOCAL_FIRMWARE_VERSION);
	send_buf[pos++]=firmware_version_len;
	//固件版本
	memcpy(send_buf+pos,LOCAL_FIRMWARE_VERSION,firmware_version_len);
	pos+=firmware_version_len;

	datalen = pos;

	Disp_uart_send(cmd,datalen,send_buf);
}

//状态信息
static void Disp_Receive_status_info(unsigned short datalen,unsigned char *rcbuf)
{
	Disp_Send_status_info();
}

//状态信息
void Disp_Send_status_info()
{
	unsigned char cmd = UART_DISP_CMD_STATUS;
	unsigned short datalen = 0;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };

	int pos=0;
	//服务器连接状态
	send_buf[pos++] = (g_host_device_TimeOut == -1)?0:1;
	//音量
	send_buf[pos++] = g_system_volume;
	//节目源ID
	send_buf[pos++] = g_media_source;
	//节目名称长度
	int media_name_len = strlen(g_media_name);
	send_buf[pos++] = media_name_len;
	//节目名称
	memcpy(send_buf+pos,g_media_name,media_name_len);
	pos+=media_name_len;
	//播放状态
	send_buf[pos++] = g_media_status;

	datalen = pos;

	Disp_uart_send(cmd,datalen,send_buf);
}


//网络信息
static void Disp_Receive_network_info(unsigned short datalen,unsigned char *rcbuf)
{
	if(datalen>0)
	{
		//设置
		int pos=0;
		printf("Set IP INFO!\n");
		int temp_len = 0;
		//IP分配方式
		int IP_Assign_temp = rcbuf[pos++];
		printf("IP_Assign_temp=%d\n",IP_Assign_temp);

		if(IP_Assign_temp == IP_ASSIGN_DHCP && g_IP_Assign == IP_ASSIGN_DHCP) //都为DHCP
		{
			printf("IP_Assign_temp=g_IP_Assign=IP_ASSIGN_DHCP,return!\n");
			return;
		}
		char TempStatic_ip_address[32]={0};
		char TempSubnet_Mask[32]={0};
		char TempGateWay[32]={0};

		if(IP_Assign_temp == IP_ASSIGN_STATIC)
		{
			//静态IP地址
			temp_len = rcbuf[pos++];
			memset(TempStatic_ip_address,0,sizeof(TempStatic_ip_address));
			memcpy(TempStatic_ip_address,&rcbuf[pos],temp_len);
			printf("g_Static_ip_address=%s,temp_len=%d\n",TempStatic_ip_address,temp_len);
			pos+=temp_len;
			//子网掩码
			temp_len=rcbuf[pos++];
			memset(TempSubnet_Mask,0,sizeof(TempSubnet_Mask));
			memcpy(TempSubnet_Mask,&rcbuf[pos],temp_len);
			printf("g_Subnet_Mask=%s,temp_len=%d\n",TempSubnet_Mask,temp_len);
			pos+=temp_len;
			//网关
			temp_len=rcbuf[pos++];
			memset(TempGateWay,0,sizeof(TempGateWay));
			memcpy(TempGateWay,&rcbuf[pos],temp_len);
			printf("g_GateWay=%s,temp_len=%d\n",TempGateWay,temp_len);
			pos+=temp_len;

			if(strcmp(g_Static_ip_address, TempStatic_ip_address) == 0 && strcmp(g_Subnet_Mask, TempSubnet_Mask) == 0 && strcmp(g_GateWay, TempGateWay) == 0 && (g_IP_Assign == IP_Assign_temp))
			{
				printf("HOST_QUERY_SET_IP_INFO:No parameter changed.%d\n", g_IP_Assign);
				return;
			}

			//检测IP、掩码、网关、主DNS、备用DNS以及内容是否输入错误
			if(!NET_INFO_ERROR(TempStatic_ip_address, TempSubnet_Mask, TempGateWay) && \
				!isGatewayByNetmask_Error(TempStatic_ip_address,TempSubnet_Mask,TempGateWay))
			{
				printf("Net parameter check ok!\n");
			}
			else
			{
				//长度或内容错误,返回错误
				return;
			}

			memcpy(g_Static_ip_address, TempStatic_ip_address, 32);
			memcpy(g_Subnet_Mask, TempSubnet_Mask, 32);
			memcpy(g_GateWay,TempGateWay,32);
			printf("g_Static_ip_address:%s\n", g_Static_ip_address);
			printf("g_Subnet_Mask:%s\n", g_Subnet_Mask);
			printf("g_GateWay:%s\n", g_GateWay);

			Send_Unonline_Info();
		}

		g_IP_Assign = IP_Assign_temp;
		
		//保存网络信息
		save_sysconf(INI_SETCION_NETWORK,NULL);	
		System_Reboot();
	}
	else
	{
		//查询
		Disp_Send_network_info();
	}
}

//网络信息
void Disp_Send_network_info()
{
	unsigned char cmd = UART_DISP_CMD_NETWORK;
	unsigned short datalen = 0;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };

	int pos=0;
	//IP分配方式
	send_buf[pos++] = g_IP_Assign;

	char ipAddress[32]={0};
	char netMask[32]={0};
	char gateWay[32]={0};

	Get_Network_Info(ipAddress,netMask,gateWay);

	//IP地址长度
	int ip_len = strlen(ipAddress);
	send_buf[pos++] = ip_len;
	//IP地址
	memcpy(send_buf+pos,ipAddress,ip_len);
	pos+=ip_len;

	//子网掩码长度
	int netMask_len = strlen(netMask);
	send_buf[pos++] = netMask_len;
	//子网掩码
	memcpy(send_buf+pos,netMask,netMask_len);
	pos+=netMask_len;

	//网关长度
	int gateWay_len = strlen(gateWay);
	send_buf[pos++] = gateWay_len;
	//网关掩码
	memcpy(send_buf+pos,gateWay,gateWay_len);
	pos+=gateWay_len;

	datalen = pos;

	Disp_uart_send(cmd,datalen,send_buf);
}


//时间信息
static void Disp_Receive_time_info(unsigned short datalen,unsigned char *rcbuf)
{
	Disp_Send_time_info();
}

//时间信息
void Disp_Send_time_info()
{
	unsigned char cmd = UART_DISP_CMD_TIME;
	unsigned short datalen = 20;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };

	int i;
	send_buf[0] = 10;
	for(i = 0; i < 10; i++)
	{
		send_buf[1+i] = sys_date_buf[i];
	}
	send_buf[11] = 8;
	for(i = 0; i < 8; i++)
	{
		send_buf[12+i] = sys_time_buf[i];
	}

	printf("Disp_Send_time_info:%s %s\n",sys_date_buf,sys_time_buf);
	Disp_uart_send(cmd,datalen,send_buf);
}


//设备音量信息
static void Disp_Receive_volume_info(unsigned short datalen,unsigned char *rcbuf)
{
	if(datalen>0)
	{
		//设置
		int vol=rcbuf[0];
		if(g_paging_status == PAGING_START)
		{       
							
			pager_property.volume = vol;
			pkg_query_current_status(NULL);

			printf("set g_paging_vol:%d\n", pager_property.volume);
		}
		else
		{
			int temp_volume = g_system_volume;
			g_system_volume = vol;
			pkg_query_current_status(NULL);
			//if(g_system_volume != 0)
			g_pre_system_volume = g_system_volume;

			//如果是定时音源，那么记录此音量
			if( get_system_source() == SOURCE_TIMING)
			{
				g_timing_volume = g_system_volume;
			}
			
			if(temp_volume == g_system_volume)
			{
				return;
			}
			save_sysconf(INI_SECTION_BASIC,"System_Volume");//主机设置音量保存
		}

		//Disp_Send_volume_info();
	}
	else
	{
		//查询
		Disp_Send_volume_info();
	}
}

//音量信息
void Disp_Send_volume_info()
{
	unsigned char cmd = UART_DISP_CMD_VOLUME;
	unsigned short datalen = 1;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };

	send_buf[0]=g_system_volume;
	Disp_uart_send(cmd,datalen,send_buf);
}

//控制显示模块重启
void Disp_Send_control_reboot(unsigned char mode)
{
	unsigned char cmd = UART_DISP_CMD_REBOOT;
	unsigned short datalen = 1;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };
	send_buf[0]=mode;
	Disp_uart_send(cmd,datalen,send_buf);
}


#if (IS_DEVICE_AUDIO_COLLECTOR)
//音频采集卡通道状态
void Disp_Send_Audio_Collector_Channel_Status()
{
	unsigned char cmd = UART_DISP_CMD_AC_CHANNEL_STATUS;
	unsigned short datalen = 2;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };

	send_buf[0]=MAX_AUDIO_COLLECTOR_CHANNEL_NUM;
	send_buf[1]=audioCollector_info.m_nSelectedStatus;
	printf("Disp_Send_AC_Channel:%d\n",audioCollector_info.m_nSelectedStatus);
	Disp_uart_send(cmd,datalen,send_buf);
}
#endif


#if (IS_DEVICE_POWER_SEQUENCE)
//接收时序器控制模式
static void Disp_Receive_SequencePower_ControlMode(unsigned short datalen,unsigned char *rcbuf)
{
	if(datalen>0)
	{
		int controlMode = rcbuf[0];
		if(	sequence_power_info.control_mode != controlMode)
		{
			Set_Sequence_Power_Info(controlMode,sequence_power_info.channel_status,sequence_power_info.open_delay_value,0);
		}
	}
	else
	{
		//查询
		Disp_Send_SequencePower_ControlMode();
	}
}

//回复时序器控制模式
void Disp_Send_SequencePower_ControlMode()
{
	unsigned char cmd = UART_DISP_CMD_SEQUENCE_POWER_CONTROL_MODE;
	unsigned short datalen = 1;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };

	send_buf[0]=sequence_power_info.control_mode;
	Disp_uart_send(cmd,datalen,send_buf);
}

//发送网络连接状态给时序器
void Disp_Send_SequencePower_NetStatus(int status)
{
	unsigned char cmd = UART_DISP_CMD_NET_STATUS;
	unsigned short datalen = 1;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };

	send_buf[0]=status;
	Disp_uart_send(cmd,datalen,send_buf);
}


//接收时序器通道状态
static void Disp_Receive_SequencePower_ChannelStatus(unsigned short datalen,unsigned char *rcbuf)
{
	if(datalen>0)
	{
		//自动模式下不响应单个通道开关
		#if(CUR_POWER_SEQUENCE_TYPE == MODEL_SEQUENCE_POWER_NORMAL)
		if(sequence_power_info.control_mode == SEQUENCE_POWER_MODE_AUTO)
		{
			return;
		}
		#endif
		//int channel_num = rcbuf[0];
		//int channel_delay = (rcbuf[1]) + (rcbuf[2]<<8);
		int channel_status = (rcbuf[3]) + (rcbuf[4]<<8);
		if(	sequence_power_info.channel_status != channel_status)
		{
			printf("cur_channel_status=%d,new_channel_status=%d\n",sequence_power_info.channel_status,channel_status);
			#if(CUR_POWER_SEQUENCE_TYPE == MODEL_SEQUENCE_POWER_AIPU)
			sequence_power_info.channel_status = channel_status;
			Host_Query_Set_Sequence_Power_Info(NULL);
			#else
			Set_Sequence_Power_Info(sequence_power_info.control_mode,channel_status,sequence_power_info.open_delay_value,0);
			#endif
		}
		else
		{
			printf("cur_channel_status=new_channel_status=%d\n",channel_status);
		}
	}
	else
	{
		//查询
		Disp_Send_SequencePower_ChannelStatus(0);
	}
}

//发送时序器通道状态
void Disp_Send_SequencePower_ChannelStatus(int IsQuickResponse)
{
	unsigned char cmd = UART_DISP_CMD_SEQUENCE_POWER_CHANNEL_STATUS;
	unsigned short datalen = 5;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };

	send_buf[0]=SEQUENCE_POWER_MAX_CHANNEL_COUNT;

	int openDelay=0xFFFF;
	if(IsQuickResponse)
	{
		#if(CUR_POWER_SEQUENCE_TYPE == MODEL_SEQUENCE_POWER_WEISHENG || CUR_POWER_SEQUENCE_TYPE == MODEL_SEQUENCE_POWER_AIPU)
		openDelay = 200;
		#elif(CUR_POWER_SEQUENCE_TYPE == MODEL_SEQUENCE_POWER_NORMAL)
		openDelay = 100;
		#endif
	}
	#if 0
	send_buf[1]=sequence_power_info.open_delay_value;
	send_buf[2]=sequence_power_info.open_delay_value>>8;
	#else
	send_buf[1]=openDelay;
	send_buf[2]=openDelay>>8;
	#endif
	send_buf[3]=sequence_power_info.channel_status;
	send_buf[4]=sequence_power_info.channel_status>>8;
	Disp_uart_send(cmd,datalen,send_buf);

	printf("Disp_Send_SequencePower_ChannelStatus:%d\n",sequence_power_info.channel_status);

	//设置触发输出，当通道全开时打开
	if(sequence_power_info.channel_status == SEQUENCE_POWER_CHANNEL_OPEN_ALL)
	{
		Disp_Send_SequencePower_TriggerOutput(1);
	}
	else
	{
		Disp_Send_SequencePower_TriggerOutput(0);
	}
}



//接收电源板触发输入状态
static void Disp_Receive_SequencePower_TriggerInput(unsigned short datalen,unsigned char *rcbuf)
{
	if(datalen>0)
	{
		int trigger_input = rcbuf[0];
		if(	sequence_power_info.input_trigger != trigger_input)
		{
			Set_Sequence_Power_Trigger_Input(trigger_input);
		}
	}
}

//发送给电源板获取触发输入状态
void Disp_Send_Get_SequencePower_TriggerInput()
{
	unsigned char cmd = UART_DISP_CMD_SEQUENCE_POWER_TRIGGER_INPUT;
	unsigned short datalen = 0;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };

	Disp_uart_send(cmd,datalen,send_buf);
}

//通知电源板触发输出
void Disp_Send_SequencePower_TriggerOutput(int enable)
{
	unsigned char cmd = UART_DISP_CMD_SEQUENCE_POWER_TRIGGER_OUTPUT;
	unsigned short datalen = 1;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };

	send_buf[0]=enable;
	Disp_uart_send(cmd,datalen,send_buf);
}


//接收电源板全开/全关按键状态
static void Disp_Receive_SequencePower_oneKey(unsigned short datalen,unsigned char *rcbuf)
{
	if(datalen>0)
	{
		//自动模式下不应答按键全开/全关
		if(sequence_power_info.control_mode == SEQUENCE_POWER_MODE_AUTO)
		{
			//应答当前通道状态，避免状态出错
			Disp_Send_SequencePower_ChannelStatus(0);
			return;
		}
		int openAll = rcbuf[0];
		//sequence_power_info.openAllKey = openAll;
		int channel_status = openAll ? SEQUENCE_POWER_CHANNEL_OPEN_ALL:SEQUENCE_POWER_CHANNEL_CLOSE_ALL;
		if(	sequence_power_info.channel_status != channel_status)
		{
			Set_Sequence_Power_Info(sequence_power_info.control_mode,channel_status,sequence_power_info.open_delay_value,0);
		}
		//应答当前通道状态，避免状态出错
		Disp_Send_SequencePower_ChannelStatus(0);
	}
}

#endif



void Disp_Send_DeviceSN()
{
	int snLen=strlen(g_device_serialNum);

	unsigned char cmd = UART_DISP_CMD_SN;
	unsigned short datalen = 1+snLen;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };

	send_buf[0]=snLen;
	memcpy(send_buf+1,g_device_serialNum,snLen);

	printf("Disp_Send_DeviceSN:%s\n",g_device_serialNum);
	Disp_uart_send(cmd,datalen,send_buf);
}


#if (IS_DEVICE_FIRE_COLLECTOR)
void Disp_Send_Fire_TriggerStatus()
{
	unsigned char cmd = UART_DISP_CMD_FIRE_TRIGGER_STATUS;
	unsigned short datalen = 4;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };

	send_buf[0]=fire_collector_info.trigger_status;
	send_buf[1]=fire_collector_info.trigger_status>>8;
	send_buf[2]=fire_collector_info.trigger_status>>16;
	send_buf[3]=fire_collector_info.trigger_status>>24;

	printf("Disp_Send_Fire_TriggerStatus:0x%x\n",fire_collector_info.trigger_status);
	Disp_uart_send(cmd,datalen,send_buf);
}
#endif

#if (IS_DEVICE_REMOTE_CONTROLER)
void Disp_Receive_RemoteControler_Key(unsigned short datalen,unsigned char *rcbuf)
{
	int keyId=rcbuf[0];
	if(keyId>=1 && keyId<=12)
	{
		SendRemoteControlerKeyToServer(keyId);

		unsigned char cmd = UART_DISP_CMD_REMOTE_CONTROLER_KEY;
		unsigned short datalen = 1;
		unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };
		send_buf[0]=keyId;
		printf("Disp_Receive_RemoteControler_Key:%d\n",keyId);
		Disp_uart_send(cmd,datalen,send_buf);
	}
}
#endif


void Disp_Send_Upgrade_Info()
{
	//此处发送整个文件名而不仅仅是版本号
	char *fileName = g_url_buf;
	//找到最后一个/
	char *lastPtr=strrchr(g_url_buf,'/');
	if(lastPtr)
	{
		fileName = lastPtr+1;
	}
	int fileNameLen=strlen(fileName);

	unsigned char cmd = UART_DISP_CMD_UPGRADE_INFO;
	unsigned short datalen = 4+2+1+fileNameLen;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX] = { 0 };

	send_buf[0]=disp_upgrade_info.file_length;
	send_buf[1]=disp_upgrade_info.file_length>>8;
	send_buf[2]=disp_upgrade_info.file_length>>16;
	send_buf[3]=disp_upgrade_info.file_length>>24;

	unsigned short crc16=crc16_ccitt(firmware_download_buffer,firmware_download_length);
	send_buf[4]=crc16;
	send_buf[5]=crc16>>8;

	send_buf[6]=fileNameLen;
	memcpy(send_buf+7,fileName,fileNameLen);
	printf("Disp_Send_Upgrade_Info:fileName=%s...\n",fileName);
	Disp_uart_send(cmd,datalen,send_buf);
}


void Disp_Send_Firmware_Pkg()
{
	unsigned short datalen=0;
	unsigned char cmd = UART_DISP_CMD_UPGRADE_PKG;
	if(disp_upgrade_info.pkg_index == disp_upgrade_info.pkg_count)
	{
		datalen = 2 + disp_upgrade_info.file_length - (disp_upgrade_info.pkg_index-1)*DISP_FIRMEARE_UPGRADE_ONCE_SIZE;
	}
	else
	{	
		datalen = 2 + DISP_FIRMEARE_UPGRADE_ONCE_SIZE;
	}
	unsigned char send_buf[DISP_UART_UPGRADE_BUFFER_MAX] = { 0 };

	send_buf[0]=disp_upgrade_info.pkg_index;
	send_buf[1]=disp_upgrade_info.pkg_index>>8;
	

	memcpy(send_buf+2,firmware_download_buffer+(disp_upgrade_info.pkg_index-1)*DISP_FIRMEARE_UPGRADE_ONCE_SIZE,DISP_FIRMEARE_UPGRADE_ONCE_SIZE);

	printf("Disp_Send_Firmware_Pkg:pkgId=%d,dataLen=%d\n",disp_upgrade_info.pkg_index,datalen);
	Disp_uart_send(cmd,datalen,send_buf);
}


/*********************************************************************
 * @fn      Recv_Uart_Disp_Pthread
 *
 * @brief   显示模块串口数据接收线程(回调函数)
 *
 * @param   void
 *
 * @return  none
 */
void *Recv_Uart_Disp_Pthread(void)
{
    printf("Recv_Uart_Disp_Pthread...\n");
    
    int rxlen;
    int ret;
    fd_set readfd;
    struct timeval timeout;
    
    // 接收状态机相关变量
    uart_recv_state_t recv_state = UART_STATE_WAIT_HEAD1;
    unsigned char rxbuf[MAX_UART_BUF_SIZE] = {0};
    unsigned char cmd = 0;
    unsigned short data_len = 0;
    unsigned short recv_data_count = 0;
    unsigned char checksum_expected = 0;
    
    // 错误统计和恢复
    unsigned int error_count = 0;
    unsigned int timeout_count = 0;
    const unsigned int MAX_CONTINUOUS_ERRORS = 10;
    
    Disp_Send_Boot_OK();
    
    while (1)
    {
        // 设置超时时间
        timeout.tv_sec = 0;
        timeout.tv_usec = 100000;  // 100ms
        
        FD_ZERO(&readfd);
        FD_SET(g_disp_Uartfd, &readfd);
        
        ret = select(g_disp_Uartfd + 1, &readfd, NULL, NULL, &timeout);
        
        switch(ret)
        {
            case -1:  // select调用出错
                perror("UART select error");
                error_count++;
                if (error_count >= MAX_CONTINUOUS_ERRORS) {
                    printf("Too many continuous errors, resetting state\n");
                    recv_state = UART_STATE_WAIT_HEAD1;
                    error_count = 0;
                }
                usleep(10000);  // 延时10ms后重试
                continue;
                
            case 0:   // 超时
                timeout_count++;
                // 如果在接收数据过程中超时，重置状态机
                if (recv_state != UART_STATE_WAIT_HEAD1) {
                    printf("UART receive timeout, resetting state machine\n");
                    g_uart_error_stats.timeout_errors++;
                    recv_state = UART_STATE_WAIT_HEAD1;
                    memset(rxbuf, 0, sizeof(rxbuf));
                }
                // 每1000次超时打印一次状态（约100秒）
                if (timeout_count % 1000 == 0) {
                    printf("UART receive thread alive, timeout_count: %u\n", timeout_count);
                }
                continue;
                
            default:  // 有数据可读
                if (!FD_ISSET(g_disp_Uartfd, &readfd)) {
                    continue;
                }
                
                // 读取一个字节数据
                rxlen = read(g_disp_Uartfd, rxbuf + recv_data_count, 1);
                
                if (rxlen <= 0) {
                    if (rxlen < 0) {
                        perror("UART read error");
                        error_count++;
                    }
                    continue;
                }
                
                // 重置错误计数
                error_count = 0;
                
                // 状态机处理
                switch(recv_state)
                {
                    case UART_STATE_WAIT_HEAD1:
                        if (rxbuf[0] == DISP_UART_FRAME_HEAD1) {
                            recv_state = UART_STATE_WAIT_HEAD2;
                            recv_data_count = 1;
                        }
                        // 如果不是帧头1，继续等待
                        break;
                        
                    case UART_STATE_WAIT_HEAD2:
                        if (rxbuf[1] == DISP_UART_FRAME_HEAD2) {
                            recv_state = UART_STATE_WAIT_CMD;
                            recv_data_count = 2;
                        } else {
                            // 帧头2错误，重新开始
                            printf("UART frame head2 error: 0x%02x\n", rxbuf[1]);
                            g_uart_error_stats.frame_errors++;
                            recv_state = UART_STATE_WAIT_HEAD1;
                            recv_data_count = 0;
                        }
                        break;
                        
                    case UART_STATE_WAIT_CMD:
                        cmd = rxbuf[2];
                        recv_state = UART_STATE_WAIT_LEN_LOW;
                        recv_data_count = 3;
                        break;
                        
                    case UART_STATE_WAIT_LEN_LOW:
                        recv_state = UART_STATE_WAIT_LEN_HIGH;
                        recv_data_count = 4;
                        break;
                        
                    case UART_STATE_WAIT_LEN_HIGH:
                        data_len = rxbuf[3] | (rxbuf[4] << 8);
                        
                        // 检查数据长度合法性
                        if (data_len > DISP_UART_DATA_LENGTH_MAX) {
                            printf("UART data length too large: %d\n", data_len);
							//打印收到的字节
							for (int i = 0; i < 5; i++) {
								printf("0x%02x ", rxbuf[i]);
							}
							printf("\n");
                            g_uart_error_stats.frame_errors++;
                            recv_state = UART_STATE_WAIT_HEAD1;
                            recv_data_count = 0;
                            break;
                        }
                        
                        recv_data_count = 5;
                        if (data_len == 0) {
                            // 没有数据，直接等待校验和
                            recv_state = UART_STATE_WAIT_CHECKSUM;
                        } else {
                            recv_state = UART_STATE_WAIT_DATA;
                        }
                        break;
                        
                    case UART_STATE_WAIT_DATA:
                        recv_data_count++;
                        if (recv_data_count >= (5 + data_len)) {
                            recv_state = UART_STATE_WAIT_CHECKSUM;
                        }
                        break;
                        
                    case UART_STATE_WAIT_CHECKSUM:
                        // 计算并验证校验和
                        checksum_expected = Checksum(cmd, data_len, rxbuf + 5);
                        
                        if (checksum_expected == rxbuf[5 + data_len]) {
                            // 校验成功，处理命令
                            printf("UART checksum succeed, CMD=0x%02x, len=%d\n", cmd, data_len);
                            Disp_Command_Proc(cmd, data_len, rxbuf + 5);
                            g_uart_error_stats.total_received++;
                        } else {
                            printf("UART checksum failed: expected=0x%02x, received=0x%02x\n", 
                                   checksum_expected, rxbuf[5 + data_len]);
                            g_uart_error_stats.checksum_errors++;
                        }
                        
                        // 重置状态机，准备接收下一帧
                        recv_state = UART_STATE_WAIT_HEAD1;
                        recv_data_count = 0;
                        memset(rxbuf, 0, sizeof(rxbuf));
                        break;
                        
                    default:
                        // 异常状态，重置状态机
                        printf("UART state machine error, resetting\n");
                        recv_state = UART_STATE_WAIT_HEAD1;
                        recv_data_count = 0;
                        memset(rxbuf, 0, sizeof(rxbuf));
                        break;
                }
                break;
        }
    }
    
    pthread_exit(NULL);
}




/*
 * 获取串口错误统计信息
 */
void Disp_Get_Error_Stats(uart_error_stats_t *stats)
{
    if (stats != NULL) {
        memcpy(stats, &g_uart_error_stats, sizeof(uart_error_stats_t));
    }
}

/*
 * 清除串口错误统计信息
 */
void Disp_Clear_Error_Stats(void)
{
    memset(&g_uart_error_stats, 0, sizeof(uart_error_stats_t));
}

/*
 * 打印串口错误统计信息
 */
void Disp_Print_Error_Stats(void)
{
    printf("[UART_DISP] Error Statistics:\n");
    printf("  Total received: %u\n", g_uart_error_stats.total_received);
    printf("  Total sent: %u\n", g_uart_error_stats.total_sent);
    printf("  Checksum errors: %u\n", g_uart_error_stats.checksum_errors);
    printf("  Frame errors: %u\n", g_uart_error_stats.frame_errors);
    printf("  Timeout errors: %u\n", g_uart_error_stats.timeout_errors);
    printf("  Send errors: %u\n", g_uart_error_stats.send_errors);
    
    if (g_uart_error_stats.total_received > 0) {
        float error_rate = (float)(g_uart_error_stats.checksum_errors + 
                                  g_uart_error_stats.frame_errors + 
                                  g_uart_error_stats.timeout_errors) / 
                          g_uart_error_stats.total_received * 100.0f;
        printf("  Error rate: %.2f%%\n", error_rate);
    }
}

/*********************************************************************
 * @fn      Uart_Disp_Init
 *
 * @brief   初始化显示串口模块
 *
 * @param   void
 *
 * @return  ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
bool Uart_Disp_Init(void)
{
	#if LZY_COMMERCIAL_VERSION
	return false;
	#endif

	int ret=-1;
	pthread_t t_uart_disp_Pthread;
	pthread_attr_t Pthread_TERMINAL_Attr;
	pthread_attr_init(&Pthread_TERMINAL_Attr);
	pthread_attr_setdetachstate(&Pthread_TERMINAL_Attr, PTHREAD_CREATE_DETACHED);

	//注意此处不能用条件编译，因为有些条件如IS_MODEL_WOODEN是程序运行后才确定的
	if (IS_DEVICE_AUDIO_COLLECTOR || IS_DEVICE_AUDIO_MIXER || (IS_DEVICE_DECODER_TERMINAL && IS_MODEL_WOODEN))
	{
		system("/customer/riu_w 0x103e 0x3a 0x0075");
		if (!is_Enable_UART_LED_PLAYER)
		{
			g_disp_Uartfd = Init_Serial_Port("/dev/ttyS1", B460800);
		}
		else
		{
			g_disp_Uartfd = Init_Serial_Port("/dev/ttyS1", B57600);
		}
	}
	else
	{
		#if IS_DEVICE_POWER_SEQUENCE
			#if(CUR_POWER_SEQUENCE_TYPE == MODEL_SEQUENCE_POWER_WEISHENG)
			g_disp_Uartfd = Init_Serial_Port("/dev/ttyS3", B115200);
			#elif(CUR_POWER_SEQUENCE_TYPE == MODEL_SEQUENCE_POWER_AIPU)
			g_disp_Uartfd = Init_Serial_Port("/dev/ttyS2", B9600);
			#else
			g_disp_Uartfd = Init_Serial_Port("/dev/ttyS3", B460800);
			#endif
		#else
			g_disp_Uartfd = Init_Serial_Port("/dev/ttyS3", B460800);
		#endif
	}

    if(g_disp_Uartfd == -1)
    {
        return false;
    }

	if (!is_Enable_UART_LED_PLAYER)
	{
		/*创建一个线程单独接收终端串口数据*/
		pthread_create(&t_uart_disp_Pthread, &Pthread_TERMINAL_Attr, (void *)Recv_Uart_Disp_Pthread, NULL);
		pthread_attr_destroy(&Pthread_TERMINAL_Attr);
	}
	return true;
}


void Uart_led_player_set_parm() {
	if(!ENABLE_DISP_UART_MODULE)
		return;

	unsigned char *send_buf=NULL;
	send_buf= (unsigned char*)malloc(DISP_UART_RECEIVE_BUFFER_MAX);
	int pos = 0, i;
	send_buf[pos++] = 0x55;	//4字节帧头
	send_buf[pos++] = 0xAA;
	send_buf[pos++] = 0x00;
	send_buf[pos++] = 0x00;

	send_buf[pos++] = 0x01;	//地址
	send_buf[pos++] = 0x01;	//标志

	send_buf[pos++] = 0x00;	//操作码低字节
	send_buf[pos++] = 0xC1;	//操作码高字节

	send_buf[pos++] = 0x00;	//保留
	send_buf[pos++] = 0x00;	//保留

	send_buf[pos++] = 0x00;	//帧序号最低字节1
	send_buf[pos++] = 0x00;	//帧序号三低字节2
	send_buf[pos++] = 0x00;	//帧序号二低字节3
	send_buf[pos++] = 0x00;	//帧序号高字节4

	send_buf[pos++] = 0x0F;	//总长度最低字节1
	send_buf[pos++] = 0x00;	//总长度三低字节2
	send_buf[pos++] = 0x00;	//总长度二低字节3
	send_buf[pos++] = 0x00;	//总长度高字节4

	send_buf[pos++] = 0x0F;	//帧长度低字节
	send_buf[pos++] = 0x00;	//帧长度高字节

	//数据
	send_buf[pos++] = 0x10;	//D0[4]:系统日期时间配置使能位

	//未知配置项
	send_buf[pos++] = 0x00;	
	send_buf[pos++] = 0x00;
	send_buf[pos++] = 0x00;
	send_buf[pos++] = 0x00;
	send_buf[pos++] = 0x00;
	send_buf[pos++] = 0x00;
	send_buf[pos++] = 0x00;
	
	//时间配置项
	send_buf[pos++] = st_CurrentTime.year;
	send_buf[pos++] = st_CurrentTime.year>>8;
	send_buf[pos++] = st_CurrentTime.mon;
	send_buf[pos++] = st_CurrentTime.day;
	send_buf[pos++] = st_CurrentTime.hour;
	send_buf[pos++] = st_CurrentTime.min;
	send_buf[pos++] = st_CurrentTime.sec;

	//4字节帧尾
	send_buf[pos++] = 0x00;
	send_buf[pos++] = 0x00;
	send_buf[pos++] = 0x0D;
	send_buf[pos++] = 0x0A;

	write(g_disp_Uartfd, send_buf, pos); //发送数据

#if 0
	printf("Disp_uart_send:\r\n");
	for(i=0;i<pos;i++)
	{
		printf("%02x ",send_buf[i]);
	}
	printf("\r\n");
#endif

	printf("Uart_led_player_set_parm:len=%d\n",pos);

	free(send_buf);
}


#if LZY_COMMERCIAL_VERSION
void lzy_commercial_uart_init() {
	g_lzy_disp_Uartfd = Init_Serial_Port("/dev/ttyS3", B115200);
	printf("lzy_commercial_uart_init...\n");
}

void lzy_uart_show_ip() {
	if(g_lzy_disp_Uartfd == -1)
		return;
	if(strlen(g_ipAddress)<7)
	{
		return;
	}
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX];
	int pos = 0, i;
	char mainId[128]="main.t0.txt=";
	memcpy(send_buf,mainId,strlen(mainId));
	pos+=strlen(mainId);
	send_buf[pos++]='\"';
	memcpy(send_buf+pos,g_ipAddress,strlen(g_ipAddress));
	pos+=strlen(g_ipAddress);
	send_buf[pos++]='\"';
	
	send_buf[pos++]=0xFF;
	send_buf[pos++]=0xFF;
	send_buf[pos++]=0xFF;

	#if 0
	printf("lzy_uart_show_ip:\r\n");
	for(i=0;i<pos;i++)
	{
		printf("%02x ",send_buf[i]);
	}
	printf("\r\n");
	#endif

	write(g_lzy_disp_Uartfd, send_buf, pos); //发送数据
}
#endif


#if IS_DEVICE_AMP_CONTROLER
void amp_controler_uart_init() {
	g_amp_controler_Uartfd = Init_Serial_Port("/dev/ttyS1", B115200);
	printf("amp_controler_uart_init...\n");
}
void amp_controler_uart_show_ip() {
	if(g_amp_controler_Uartfd == -1)
		return;
	if(strlen(g_ipAddress)<7)
	{
		return;
	}
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX];
	int pos = 0, i;
	char mainId[128]="main.t0.txt=";
	memcpy(send_buf,mainId,strlen(mainId));
	pos+=strlen(mainId);
	send_buf[pos++]='\"';
	memcpy(send_buf+pos,g_ipAddress,strlen(g_ipAddress));
	pos+=strlen(g_ipAddress);
	send_buf[pos++]='\"';
	
	send_buf[pos++]=0xFF;
	send_buf[pos++]=0xFF;
	send_buf[pos++]=0xFF;

	#if 0
	printf("amp_controler_uart_show_ip:\r\n");
	for(i=0;i<pos;i++)
	{
		printf("%02x ",send_buf[i]);
	}
	printf("\r\n");
	#endif

	write(g_amp_controler_Uartfd, send_buf, pos); //发送数据
}

void amp_controler_uart_ChannelChange() {
	if(g_amp_controler_Uartfd == -1)
		return;
	unsigned char send_buf[DISP_UART_RECEIVE_BUFFER_MAX];
	int pos = 0;

	    // 处理5个主通道和1个备用通道
    for(int i=0; i<MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM+1; i++) {
        unsigned char channelStatus;
        if(i < MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM) {
            channelStatus = g_ampControler_info.masterChannelStatusArray[i];
        } else {
            channelStatus = g_ampControler_info.backupChannelStatus;
        }
        
        // 根据状态确定q编号
        unsigned char qNum;
        if(channelStatus == AMP_CONTROLER_CHANNEL_STATUS_NORMAL) {
            qNum = i;  // q0~q5
        } else if(channelStatus == AMP_CONTROLER_CHANNEL_STATUS_FAULT) {
            qNum = i + 6;  // q6~q11
        } else {  // IDLE状态
            qNum = i + 12;  // q12~q17
        }
        
        // 构建指令 vis qX,1
        char cmd[16];
        snprintf(cmd, sizeof(cmd), "vis q%d,1", qNum);
		//printf("cmd=%s\n",cmd);
        memcpy(send_buf+pos, cmd, strlen(cmd));
        pos += strlen(cmd);
        
        // 添加3个0xff分隔符
        send_buf[pos++] = 0xFF;
        send_buf[pos++] = 0xFF;
        send_buf[pos++] = 0xFF;

		//如果是备用通道，则再加上一个备份通道号显示
		if(i == MAX_AMP_CONTROLER_MASTER_CHANNEL_NUM)
		{
			if(g_ampControler_info.backupChannelStatus == AMP_CONTROLER_CHANNEL_STATUS_NORMAL &&
				g_ampControler_info.backupChannelId != 0xff)
			{
				char mainId[32]="main.t1.txt=";
				memcpy(send_buf+pos,mainId,strlen(mainId));
				pos+=strlen(mainId);
				send_buf[pos++]='\"';
				
				unsigned char displayBackupChannelId = g_ampControler_info.backupChannelId+1;
				char cmd[16];
        		snprintf(cmd, sizeof(cmd), "%d", displayBackupChannelId);
				memcpy(send_buf+pos,cmd,strlen(cmd));
				pos+=strlen(cmd);
				//printf("displayBackupChannelId=%d\n",displayBackupChannelId);
				
				send_buf[pos++]='\"';

				// 添加3个0xff分隔符
				send_buf[pos++] = 0xFF;
				send_buf[pos++] = 0xFF;
				send_buf[pos++] = 0xFF;
			}
		}
    }

	#if 0
	printf("amp_controler_uart_ChannelChange:\r\n");
	for(int i=0;i<pos;i++)
	{
		printf("%02x ",send_buf[i]);
	}
	printf("\r\n");
	#endif

	write(g_amp_controler_Uartfd, send_buf, pos); //发送数据
}

#endif

void serial_display_show_ip() 
{
	#if LZY_COMMERCIAL_VERSION
	lzy_uart_show_ip();
	#endif

	#if IS_DEVICE_AMP_CONTROLER
	amp_controler_uart_show_ip();
	#endif
}

void serial_display_init()
{
	#if LZY_COMMERCIAL_VERSION
	lzy_commercial_uart_init();
	#endif

	#if IS_DEVICE_AMP_CONTROLER
	amp_controler_uart_init();
	#endif
}