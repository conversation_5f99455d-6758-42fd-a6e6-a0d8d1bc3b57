#ifndef __ADC_UART_H_
#define __ADC_UART_H_

#include "sysconf.h"

#define UART_ADC_DEVICE     "/dev/ttyS3" //uart device name

#define UART_NEW_DEVICEID_ADC_CHECK      0xA1   //ADC电压检测模块
#define UART_NEW_DEVICEID_AMP_CONTROLER  0xA2   //五主一备控制模块

#define ADC_UART_FRAME_HEAD1	0x55
#define ADC_UART_FRAME_HEAD2	0xAA
#define ADC_UART_FRAME_HEAD	(ADC_UART_FRAME_HEAD2<<8)+ADC_UART_FRAME_HEAD1

#define ADC_UART_RECEIVE_BUFFER_MAX 256+8

#define ADC_UART_CMD_CHIP_INFO		0x51
#define ADC_UART_CMD_ADC_INFO		0x61
#define ADC_UART_CMD_AMP_INFO		0x62
#define ADC_AURT_CMD_AMP_PARM       0xA0

#define AMP_CONTROLER_UART_CMD_CONTROL_CHANNEL  0xA1

extern int g_exist_adcCheck_module;     //是否存在adc电压检测模块
extern int adcCheckVolumeVal[4];        //adc电压检测模块通道值

extern int g_exist_ampControler_module; //是否存在功放控制器检测模块

bool Uart_NewDevice_Init(void);
void ADC_Send_Get_Chip_Info(unsigned char deviceId);

#if IS_DEVICE_AMP_CONTROLER
void AMP_CONTROLER_Send_Control_Channel(unsigned char channelId);
#endif

#endif