#ifndef __FIRE_H_
#define __FIRE_H_

#include <stdbool.h>
#include "const.h"

#if IS_DEVICE_FIRE_COLLECTOR

#define UART_FIRE_DEVICE_UART1     "/dev/ttyS1" //uart device name
#define UART_FIRE_DEVICE_UART2     "/dev/ttyS3" //uart device name
#define UART_FIRE_DEVICE_FUART     "/dev/ttyS2" //uart device name

#if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_F)
#define FIRE_UART_FRAME_HEAD1	0xAA
#define FIRE_UART_FRAME_HEAD2	0x66
#else
#define FIRE_UART_FRAME_HEAD1	0xAA
#define FIRE_UART_FRAME_HEAD2	0x55
#endif

#define FIRE_UART_FRAME_HEAD	(FIRE_UART_FRAME_HEAD1<<8)+FIRE_UART_FRAME_HEAD2

#define FIRE_UART_DATA_LENGTH_MAX	   256
#define FIRE_UART_RECEIVE_BUFFER_MAX (6+FIRE_UART_DATA_LENGTH_MAX)

#define TRIG_MODE_LEVEL		0x00	// 电平触发
#define TRIG_MODE_SHORT		0x01	// 短路触发

typedef struct {
	unsigned int trigger_mode;					//32位继电器模式（最高位代表channel 32，最低位代表channel1，与网络协议相反）
	unsigned int trigger_status;				//触发状态（最高位代表channel32，最低位代表channel1，与网络协议相反）
	unsigned char relay_channel[2];				//两路继电器对应的通道(通道从1开始)
	unsigned char IsReady;						//上电收到状态应答，然后发送设置模式，如果有应答，代表ready
	unsigned char IsHost_received;				//服务器是否已经接收状态变化
}st_fire_collector_info;

extern st_fire_collector_info fire_collector_info;

extern bool isForceStopFire;


/***********消防采集器相关********/
#if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_F)
#define UART_FIRE_CMD_MODE		0x01
#define UART_FIRE_CMD_STATUS	0x02
#define UART_FIRE_CMD_RELAY		0x03
#else
#define UART_FIRE_CMD_MODE		0x30
#define UART_FIRE_CMD_STATUS	0x31
#define UART_FIRE_CMD_RELAY		0x32
#endif
/*********************************/

static void FIRE_receive_TriggerStatus(unsigned short datalen,unsigned char *rcbuf);
void FIRE_Send_Set_TriggerMode();
bool Uart_Fire_Init(void);
unsigned int invert_int(unsigned int x);
void FIRE_Control_Relay(int firstRelay,int secondRelay);

#endif

#endif