#ifndef __<PERSON>EECH_UART_H_
#define __SPEECH_UART_H_

#include <stdbool.h>
#include "const.h"

#if IS_DEVICE_INTERCOM_TERMINAL

#define UART_SPEECH_DEVICE     "/dev/ttyS1" //uart device name

#define SPEECH_UART_FRAME_HEAD1	0xBB
#define SPEECH_UART_FRAME_HEAD2	0x66
#define SPEECH_UART_FRAME_HEAD	(SPEECH_UART_FRAME_HEAD1<<8)+SPEECH_UART_FRAME_HEAD2

#define SPEECH_UART_DATA_LENGTH_MAX	   256
#define SPEECH_UART_RECEIVE_BUFFER_MAX (6+SPEECH_UART_DATA_LENGTH_MAX)


/***********语音识别相关********/
#define UART_SPEECH_CMD_RECOG_COMMAND		0x01
/*********************************/

static void SPEECH_receive_recog_command(unsigned short datalen,unsigned char *rcbuf);
bool Uart_Speech_Init(void);

#endif

#endif