#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <termios.h>
#include <pthread.h>

#include "sysconf.h"
#include "uartCommon.h"
#include "fire.h"

#if IS_DEVICE_FIRE_COLLECTOR

static int g_fire_Uartfd;

st_fire_collector_info fire_collector_info;

#if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_C)
// 定义TM1640的CLK和DIO引脚
#define TM1640_CLK_PIN  PAD_KEY5
#define TM1640_DIO_PIN  PAD_KEY7

// TM1640基础命令
#define TM1640_CMD_ADDR_AUTO  0x40 // 地址自动增加模式
#define TM1640_CMD_DISPLAY_ON 0x88 // 显示开 + 亮度（0x88~0x8F对应亮度0~7）
#define TM1640_CMD_START_ADDR 0xC0 // 起始地址

void tm1640_start(void);
void tm1640_stop(void);
void tm1640_send_byte(uint8_t data);
void tm1640_init(void);
void tm1640_update_leds(uint8_t *led_data);
void TM1640_setAllLEDs();
#endif


#if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_F)
void FIRE_Send_Get_TriggerStatus();
#endif

bool isForceStopFire = false;

void Fire_Command_Proc( unsigned char cmd, unsigned short datalen,unsigned char *data )
{
	switch(cmd)
	{
		case UART_FIRE_CMD_MODE:
		  fire_collector_info.IsReady=1;
	  	break;
		case UART_FIRE_CMD_STATUS:
		#if(CURRENT_DEVICE_MODEL != MODEL_FIRE_COLLECTOR_F)
		  if(!fire_collector_info.IsReady)
		  {
			printf("ready set Trigger!\n");
		  	FIRE_Send_Set_TriggerMode();
		  }
		  else
		  {
		  	 FIRE_receive_TriggerStatus(datalen,data);
		  }
		#else
		FIRE_receive_TriggerStatus(datalen,data);
		#endif
	  	break;
		case UART_FIRE_CMD_RELAY:
	  	break;
	}
}


void Fire_uart_send(unsigned char cmd, unsigned short datalen,unsigned char *data) {
	unsigned char *send_buf=NULL;
	send_buf= (unsigned char*)malloc(FIRE_UART_RECEIVE_BUFFER_MAX);

	int pos = 0, i;
	send_buf[pos++] = FIRE_UART_FRAME_HEAD1;
	send_buf[pos++] = FIRE_UART_FRAME_HEAD2;
	send_buf[pos++] = cmd;
	send_buf[pos++] = datalen;
	send_buf[pos++] = datalen>>8;

	for (i = 0; i < datalen; i++) {
		send_buf[pos++] = data[i];
	}

	send_buf[pos++] = Checksum(cmd,datalen,data);

	write(g_fire_Uartfd, send_buf, pos); //发送数据

#if 1
	printf("Fire_uart_send:cmd=0x%x,datalen=%d\r\n",cmd,datalen);

#else
	printf("Fire_uart_send:\r\n");
	for(i=0;i<pos;i++)
	{
		printf("%02x ",send_buf[i]);
	}
	printf("\r\n");
#endif

	free(send_buf);
}


void FIRE_Send_Set_TriggerMode()
{
  	unsigned char cmd = UART_FIRE_CMD_MODE;
	unsigned short datalen = 0;
	unsigned char send_buf[FIRE_UART_RECEIVE_BUFFER_MAX] = { 0 };

#if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_F)
	int curTriggerMode = ~fire_collector_info.trigger_mode;	//易会主板反过来
#else
	int curTriggerMode = fire_collector_info.trigger_mode;
#endif
	send_buf[datalen++]=curTriggerMode;
	send_buf[datalen++]=curTriggerMode>>8;
	send_buf[datalen++]=curTriggerMode>>16;
	send_buf[datalen++]=curTriggerMode>>24;
	
	Fire_uart_send(cmd,datalen,send_buf);
}


#if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_F)
void FIRE_Send_Get_TriggerStatus()
{
  	unsigned char cmd = UART_FIRE_CMD_STATUS;
	unsigned short datalen = 0;
	unsigned char send_buf[FIRE_UART_RECEIVE_BUFFER_MAX] = { 0 };
	
	Fire_uart_send(cmd,datalen,send_buf);
}
#endif


static void FIRE_receive_TriggerStatus(unsigned short datalen,unsigned char *rcbuf)
{
	unsigned int temp_status;
	memcpy(&temp_status,rcbuf,4);
	static int has_received=0;
	//第一次，先将两个继电器都关闭，避免重启后继电器还是打开状态
	if(!has_received)
	{
		has_received=1;
		FIRE_Control_Relay(0,0);
	}
	if(fire_collector_info.trigger_status!=temp_status)
	{
	  	fire_collector_info.IsHost_received=0;
		fire_collector_info.trigger_status=temp_status;
		//发送触发状态给主机
		Fire_Collector_Send_Trig_Status(fire_collector_info.trigger_status);
		printf("Fire collector:trigger_status=0x%x\n",fire_collector_info.trigger_status);

		isForceStopFire = false;

		//有信号触发，将继电器打开
		if(fire_collector_info.trigger_status)
		{
			g_media_source = SOURCE_FIRE_ALARM;
			FIRE_Control_Relay(1,1);
			GPIO_OutPut_NetAudio_Led(1);		//打开网络音频信号灯输出
			#if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_F)
			Set_Gpio_High(PAD_KEY9);	
			#endif
		}
		else
		{
			g_media_source = SOURCE_NULL;
			FIRE_Control_Relay(0,0);
			GPIO_OutPut_NetAudio_Led(0);		//打开网络音频信号灯输出
			#if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_F)
			Set_Gpio_Low(PAD_KEY9);	
			#endif
		}

		#if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_C)
		TM1640_setAllLEDs();
		#endif
		
		if(ENABLE_DISP_UART_MODULE)
			Disp_Send_Fire_TriggerStatus();

		display_update_mainWin_external();
	}
	else if(!fire_collector_info.IsHost_received)
	{
		//发送触发状态给主机
		Fire_Collector_Send_Trig_Status(fire_collector_info.trigger_status);
	}
}


void FIRE_Control_Relay(int firstRelay,int secondRelay)
{
	#if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_F)
	return;
	#endif
	
  	unsigned char cmd = UART_FIRE_CMD_RELAY;
	unsigned short datalen = 2;
	unsigned char send_buf[FIRE_UART_RECEIVE_BUFFER_MAX] = { 0 };
	
	send_buf[0] = firstRelay;
	send_buf[1] = secondRelay;

	Fire_uart_send(cmd,datalen,send_buf);
}



unsigned int invert_int(unsigned int x)
{
	x= (x&0x55555555)<<1| (x>>1)&0x55555555;
	x= (x&0x33333333)<<2| (x>>2)&0x33333333;
	x= (x&0x0F0F0F0F)<<4| (x>>4)&0x0F0F0F0F;
	x= (x<<24)| ((x&0xFF00)<<8)| \
		((x>>8)&0xFF00)| (x>>24);
	return x;
}


/*********************************************************************
 * @fn      Recv_Uart_Fire_Pthread
 *
 * @brief   消防模块串口数据接收线程(回调函数)
 *
 * @param   void
 *
 * @return  none
 */
void *Recv_Uart_Fire_Pthread(void)
{
	printf("Recv_Uart_Fire_Pthread...\n");
	int Rxlen;
	int ret, i;
	int Index = 0;
	fd_set readfd;
	struct timeval timeout;
	int max_fd;
	int Pkg_Length=0;
	int Read_Size = 2;

	unsigned char Rxbuf[MAX_UART_BUF_SIZE]={0};


	FD_ZERO(&readfd);               //清空读文件描述集合
	FD_SET(g_fire_Uartfd, &readfd);  //注册套接字文件描述符
#if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_F)
	FIRE_Send_Set_TriggerMode();
	usleep(100000);
	FIRE_Send_Get_TriggerStatus();
#endif
	while (1)
	{
		timeout.tv_sec = 0;      // 获取数据超时时间设置
		timeout.tv_usec = 100000;

		FD_ZERO(&readfd);               //清空读文件描述集合
		FD_SET(g_fire_Uartfd, &readfd);  //注册套接字文件描述符
		ret = select(g_fire_Uartfd+1, &readfd, NULL, NULL, &timeout);
		switch(ret)
		{
			case -1 : //调用出错
				perror("select");
				Index = 0;
				Read_Size = 2;
				memset(Rxbuf,0, MAX_UART_BUF_SIZE);
				break;

			case 0 : //超时
				//printf("timeout!\n");
				Index = 0;
				Read_Size = 2;
				memset(Rxbuf,0, MAX_UART_BUF_SIZE);
				break;

			default : //判断是否有数据可读

				/*接收对应串口的数据*/

				Rxlen = read(g_fire_Uartfd, &Rxbuf[Index], Read_Size);

				/*数据校验*/
				if (Rxlen < 0)
				{
					perror("Rxlen<0\n");
					Index = 0;
					Read_Size = 2;
					memset(Rxbuf,0, MAX_UART_BUF_SIZE);
					continue;
				}
				else
				{

					#if 0
					printf("Terminal:The Recv Data Is : \n");
					for (i = 0; i < Rxlen; i++)
					{
						printf("0x%x ", Rxbuf[i+Index]);
					}
					printf("\n");
					#endif

					if(Index == 0)	/*判断包头是否正确*/
					{
						if(Rxbuf[0] == FIRE_UART_FRAME_HEAD1 && Rxbuf[1] == FIRE_UART_FRAME_HEAD2)
						{
							Index = Rxlen; //移位接收包后续数据
							Read_Size = 3; //包剩余包头信息
							continue;
						}
						else
						{
							/*接收的包错误，重新接收下一个数据包*/
							printf("Terminal ERROR : Pkg Head Fault : %x\n", Rxbuf[0]);
							Index = 0;
							Read_Size = 2;
							memset(Rxbuf,0, MAX_UART_BUF_SIZE);
							continue;
						}
					}
					else if(Index == 2)
					{
						Index = Rxlen+2; //移位接收包后续数据
						Read_Size = (Rxbuf[4]<<8)+Rxbuf[3]+1; //包剩余数据个数
						Pkg_Length = 5+Read_Size;
						if(Pkg_Length >MAX_UART_BUF_SIZE)	//包长超出最大长度
						{
							Index = 0;
							Read_Size = 2;
							memset(Rxbuf,0, MAX_UART_BUF_SIZE);
						}
						continue;
					}
					else
					{
						Index = Index + Rxlen;
						if (Index < Pkg_Length)
						{
							/*还没接收完包数据，继续接收*/
							printf("Terminal:Package Length Too Short,Continue......\n");
							Read_Size = Pkg_Length - Index;
							continue;
						}
						else
						{
							Index = 0;
							Read_Size = 2;
							if(Pkg_Length < 6)
							{
								printf("Terminal:ERROR : Package Length<6!\n");
								memset(Rxbuf,0, MAX_UART_BUF_SIZE);
								continue;
							}
							int cmd=Rxbuf[2];
							int data_len=Pkg_Length-6;
							if ( Checksum(cmd, data_len,Rxbuf+5) != Rxbuf[Pkg_Length-1] ) //校验数据
							{
								printf("Terminal:ERROR : Package Check Fail\n");
								memset(Rxbuf,0, MAX_UART_BUF_SIZE);
								continue;
							}

						 	printf("Fire Checksum succeed,CMD=0x%02x\n",cmd);
							Fire_Command_Proc(cmd,data_len,Rxbuf+5);

							memset(Rxbuf,0, MAX_UART_BUF_SIZE);
						}
					}
				}
				break;
		}
	}
	pthread_exit(NULL);
}




/*********************************************************************
 * @fn      Uart_Fire_Init
 *
 * @brief   初始化消防信号检测串口模块
 *
 * @param   void
 *
 * @return  ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
bool Uart_Fire_Init(void)
{
	int ret=-1;
	pthread_t t_uart_fire_Pthread;
	pthread_attr_t Pthread_TERMINAL_Attr;
	pthread_attr_init(&Pthread_TERMINAL_Attr);
	pthread_attr_setdetachstate(&Pthread_TERMINAL_Attr, PTHREAD_CREATE_DETACHED);

	#if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_B)
	system("/customer/riu_w 0x103e 0x3a 0x0075");
	g_fire_Uartfd = Init_Serial_Port(UART_FIRE_DEVICE_UART1, B115200);
	#elif(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_C)
	Set_Gpio_Input(PAD_SD_GPIO1);
	g_fire_Uartfd = Init_Serial_Port(UART_FIRE_DEVICE_UART2, B115200);

	tm1640_init();
	#if 0
	char led_data[5]={0xff,0xff,0x01,0x02,0x03};
	tm1640_update_leds(led_data);
	#else
	TM1640_setAllLEDs();
	tm1640_start();
    tm1640_send_byte(TM1640_CMD_DISPLAY_ON | 0x07); // 最大亮度
    tm1640_stop();
	#endif
	#elif(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_F)
	system("/customer/riu_w 0x103e 0x3a 0x0075");
	g_fire_Uartfd = Init_Serial_Port(UART_FIRE_DEVICE_UART1, B460800);
	#endif

    if(g_fire_Uartfd == -1)
    {
        return false;
    }
	/*创建一个线程单独接收终端串口数据*/
	pthread_create(&t_uart_fire_Pthread, &Pthread_TERMINAL_Attr, (void *)Recv_Uart_Fire_Pthread, NULL);
	pthread_attr_destroy(&Pthread_TERMINAL_Attr);
	return true;
}

#if(CURRENT_DEVICE_MODEL == MODEL_FIRE_COLLECTOR_C)

// 发送起始信号
void tm1640_start(void) {
    Set_Gpio_High(TM1640_CLK_PIN);
    Set_Gpio_High(TM1640_DIO_PIN);
    usleep(10);
    Set_Gpio_Low(TM1640_DIO_PIN); // DIO在CLK高时拉低
    usleep(10);
    Set_Gpio_Low(TM1640_CLK_PIN);
    usleep(10);
}

// 发送结束信号
void tm1640_stop(void) {
    Set_Gpio_Low(TM1640_CLK_PIN);
    Set_Gpio_Low(TM1640_DIO_PIN);
    usleep(10);
    Set_Gpio_High(TM1640_CLK_PIN);
    usleep(10);
    Set_Gpio_High(TM1640_DIO_PIN); // CLK高时DIO拉高形成停止信号
    usleep(10);
}

// 发送一个字节（LSB first）
void tm1640_send_byte(uint8_t data) {
    for(uint8_t i=0; i<8; i++) {
        Set_Gpio_Low(TM1640_CLK_PIN);
        usleep(10);
        // 先发送最低位（bit0）
        if(data & 0x01) { // ← 关键修改点
            Set_Gpio_High(TM1640_DIO_PIN);
        } else {
            Set_Gpio_Low(TM1640_DIO_PIN);
        }
        usleep(10);
        Set_Gpio_High(TM1640_CLK_PIN);
        usleep(10);
        data >>= 1; // ← 改为右移
    }
    // TM1640不会返回ACK，此处无需等待
}

// 初始化TM1640
void tm1640_init(void) {
    // 设置地址自动增加模式 + 显示开 + 亮度
    tm1640_start();
    tm1640_send_byte(TM1640_CMD_ADDR_AUTO);
    tm1640_stop();
    #if 0
    tm1640_start();
    tm1640_send_byte(TM1640_CMD_DISPLAY_ON | 0x07); // 最大亮度
    tm1640_stop();
	#endif
}

// 更新LED显示（32个LED对应4字节数据，每字节8位）
void tm1640_update_leds(uint8_t *led_data) {
    tm1640_start();
    tm1640_send_byte(TM1640_CMD_START_ADDR);
    
    // 发送5字节数据（假设每字节控制8个LED）
    for(uint8_t i=0; i<5; i++) {
        tm1640_send_byte(led_data[i]);
    }
    
    tm1640_stop();
}

// 控制所有34个LED
void TM1640_setAllLEDs() {
	tm1640_start();
    tm1640_send_byte(TM1640_CMD_START_ADDR);

    for (uint8_t i = 0; i < 4; i++) {
        uint8_t grid_data = (fire_collector_info.trigger_status >> (i * 8)) & 0xFF;  // 每次取8位，表示一个GRID的状态
        // 发送GRID的数据
        tm1640_send_byte(grid_data);
    }
	//最后一个字节是两个继电器的灯，默认有任意通道触发，即显示两路继电器
	if(!fire_collector_info.trigger_status)
	{
		tm1640_send_byte(0x00);
	}
	else
	{
		tm1640_send_byte(0x03);
	}

	tm1640_stop();
}

#endif


#endif