#ifndef __DISP_H_
#define __DISP_H_

#include <stdbool.h>
#include "const.h"

/***********显示模块存在标记***********/
extern unsigned char g_PreExistDispModule;	//之前是否存在串口模块
extern unsigned char g_CurExistDsipModule;	//当前是否存在串口模块
extern unsigned char g_CurDispUpgrading;	//当前正处于升级状态
extern unsigned char g_DispVersion[16];		//显示模块版本号，存在版本号才允许升级
/*****************************************/

enum
{
    DISP_REBOOT_MODE=0,
    DISP_UPGRADE_MODE
};

typedef struct {
	unsigned char IsStart;			//UUID
	unsigned char Upgrade_process;	//0 ready  1 upgrading 2 end  3 failed
	unsigned int pkg_index;			//包序号（从1开始）
	unsigned char upgrade_valid;    //每发送一个包，需要收到应答才能发下一个包
	unsigned long file_length;		//升级文件总大小
	unsigned int pkg_count;			//升级文件总包数
}st_disp_upgrade_info;

extern st_disp_upgrade_info disp_upgrade_info;

void Disp_Command_Proc( unsigned char cmd, unsigned short datalen,unsigned char *data );
bool Uart_Disp_Init(void);

void Uart_led_player_set_parm();

#define DISP_UART_DATA_LENGTH_MAX	   256
#define DISP_UART_RECEIVE_BUFFER_MAX   (6+DISP_UART_DATA_LENGTH_MAX)

#define DISP_UART_FRAME_HEAD1	0xAA
#define DISP_UART_FRAME_HEAD2	0x66
#define DISP_UART_FRAME_HEAD	(DISP_UART_FRAME_HEAD2<<8)+DISP_UART_FRAME_HEAD1

#define DISP_FIRMEARE_UPGRADE_ONCE_SIZE   2048
#define DISP_UART_UPGRADE_BUFFER_MAX   (6+2+DISP_FIRMEARE_UPGRADE_ONCE_SIZE)

// 串口接收状态机状态定义
typedef enum {
    UART_STATE_WAIT_HEAD1,      // 等待帧头1
    UART_STATE_WAIT_HEAD2,      // 等待帧头2
    UART_STATE_WAIT_CMD,        // 等待命令字
    UART_STATE_WAIT_LEN_LOW,    // 等待长度低字节
    UART_STATE_WAIT_LEN_HIGH,   // 等待长度高字节
    UART_STATE_WAIT_DATA,       // 等待数据
    UART_STATE_WAIT_CHECKSUM    // 等待校验和
} uart_recv_state_t;

// 串口错误统计结构
typedef struct {
    unsigned int checksum_errors;    // 校验和错误次数
    unsigned int frame_errors;       // 帧格式错误次数
    unsigned int timeout_errors;     // 超时错误次数
    unsigned int send_errors;        // 发送错误次数
    unsigned int total_received;     // 总接收包数
    unsigned int total_sent;         // 总发送包数
} uart_error_stats_t;

extern uart_error_stats_t g_uart_error_stats;


// 串口错误统计相关函数
extern void Disp_Get_Error_Stats(uart_error_stats_t *stats);
extern void Disp_Clear_Error_Stats(void);
extern void Disp_Print_Error_Stats(void);

/***********显示模块串口通信相关********/
#define UART_DISP_CMD_BOOT			0x01
#define UART_DISP_CMD_DEVICE		0x02
#define UART_DISP_CMD_STATUS		0x03
#define UART_DISP_CMD_NETWORK		0x04
#define UART_DISP_CMD_TIME			0x05
#define UART_DISP_CMD_VOLUME		0x06
#define UART_DISP_CMD_AC_CHANNEL_STATUS		0x07

#define UART_DISP_CMD_SEQUENCE_POWER_CONTROL_MODE	0x08
#define UART_DISP_CMD_SEQUENCE_POWER_CHANNEL_STATUS	0x09
#define UART_DISP_CMD_SEQUENCE_POWER_TRIGGER_OUTPUT	0x0A
#define UART_DISP_CMD_SEQUENCE_POWER_TRIGGER_INPUT	0x0B
#define UART_DISP_CMD_SEQUENCE_POWER_TRIGGER_ALL_OPEN_CLOSE_KEY 0x0C
#define UART_DISP_CMD_REBOOT 		0x0E	//原0x0D，解决因易会智能没有处理冲击的问题改为0x0E
#define UART_DISP_CMD_SN 		    0x0F
#define UART_DISP_CMD_FIRE_TRIGGER_STATUS  0x10
#define UART_DISP_CMD_REMOTE_CONTROLER_KEY	0x11
#define UART_DISP_CMD_NET_STATUS     0x12


#define UART_DISP_CMD_UPGRADE_INFO 		0x60
#define UART_DISP_CMD_UPGRADE_STATUS	0x61
#define UART_DISP_CMD_UPGRADE_PKG 		0x62

/*********************************/

static void Disp_Receive_device_info(unsigned short datalen,unsigned char *rcbuf);
static void Disp_Receive_status_info(unsigned short datalen,unsigned char *rcbuf);
static void Disp_Receive_network_info(unsigned short datalen,unsigned char *rcbuf);
static void Disp_Receive_time_info(unsigned short datalen,unsigned char *rcbuf);
static void Disp_Receive_volume_info(unsigned short datalen,unsigned char *rcbuf);

#if (IS_DEVICE_POWER_SEQUENCE)
static void Disp_Receive_SequencePower_ControlMode(unsigned short datalen,unsigned char *rcbuf);
static void Disp_Receive_SequencePower_ChannelStatus(unsigned short datalen,unsigned char *rcbuf);
static void Disp_Receive_SequencePower_TriggerInput(unsigned short datalen,unsigned char *rcbuf);
static void Disp_Receive_SequencePower_oneKey(unsigned short datalen,unsigned char *rcbuf);
#endif

void Disp_Send_Boot_OK();
void Disp_Send_device_info();
void Disp_Send_status_info();
void Disp_Send_network_info();
void Disp_Send_time_info();
void Disp_Send_volume_info();
void Disp_Send_Audio_Collector_Channel_Status();
void Disp_Send_control_reboot(unsigned char mode);

#if (IS_DEVICE_POWER_SEQUENCE)
void Disp_Send_SequencePower_ControlMode();
void Disp_Send_SequencePower_ChannelStatus(int IsQuickResponse);
void Disp_Send_Get_SequencePower_TriggerInput();
void Disp_Send_SequencePower_TriggerOutput(int enable);
void Disp_Send_SequencePower_NetStatus(int status);
#endif

void Disp_Send_DeviceSN();

#if (IS_DEVICE_FIRE_COLLECTOR)
void Disp_Send_Fire_TriggerStatus();
#endif

#if (IS_DEVICE_REMOTE_CONTROLER)
void Disp_Receive_RemoteControler_Key(unsigned short datalen,unsigned char *rcbuf);
#endif

void Disp_Send_Upgrade_Info();
void Disp_Send_Firmware_Pkg();

void serial_display_init();
void serial_display_show_ip();
void amp_controler_uart_ChannelChange();

#endif