#include <stdio.h>
#include <unistd.h>
#include <string.h>
#include <fcntl.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <pthread.h>
#include "sysconf.h"
#include "mi_sar.h"
#include "yhBoard.h"
#include "../sigmastar/mi_gpio.h"

#if YIHUI_VERSION

typedef struct
{
    int channel_value;
    int adc_value;
} SAR_ADC_CONFIG_READ;


#define SARADC_IOC_MAGIC                     'a'
#define IOCTL_SAR_INIT                       _IO(SARADC_IOC_MAGIC, 0)
#define IOCTL_SAR_SET_CHANNEL_READ_VALUE     _IO(SARADC_IOC_MAGIC, 1)


#define ADC_AMP_FAN_START_THRESHOLD     665 //65度开启风机
#define ADC_AMP_FAN_STOP_THRESHOLD      460 //45度关闭风机

static bool bAmpFanStarted = false;
static bool bAmpProtectLedOn = false;

static bool adc0_key_press=false;

//PAD_SAR_GPIO0-按键检测，PAD_SAR_GPIO1-温度检测

void YH_Sar_Adc0_Handle(int value)
{
    int keyId=-1;
	if(value>1000)
    {
        adc0_key_press=false;
    }
    if(adc0_key_press)
    {
        return;
    }
    else if(value>0 && value<=25)
    {
        keyId = KEY_ID_PLAY;
    }
    else if(value>275 && value<=305)
    {
        keyId = KEY_ID_PRE;
    }
    else if(value>505 && value<=535)
    {
    	keyId = KEY_ID_NXT;
    }
    else if(value>735 && value<=765)
    {
    	keyId = KEY_ID_MENU;
    }
    if(keyId!=-1)
    {
        adc0_key_press=true;
        YH_Sar_Key_Press(keyId);
    }
}

void* YH_board_sar()
{
    #if USE_PC_SIMULATOR
    return NULL;
    #endif
    printf("YH_board_sar start...\n");
    int i;
    SAR_ADC_CONFIG_READ  adcCfg_sar0,adcCfg_sar1;
    adcCfg_sar0.channel_value = 0;   //注意：PAD_SAR_GPIO0 = 0 PAD_SAR_GPIO1 = 1 PAD_SAR_GPIO2 = 2
    adcCfg_sar1.channel_value = 1;

    YH_control_amp_fan(0);   //关闭风机

    int fd = open("/dev/sar", O_WRONLY);
    if(fd == -1) {
        int err = errno;
        printf("\n!!! FAILED to open /dev/sar, errno: %d %s\n", err, strerror(err));
        return NULL;
    }

    if (ioctl(fd, IOCTL_SAR_INIT, NULL) < 0) {   
        int err = errno;
        printf("\n!!! IOCTL_SAR_INIT FAILED, errno: %d, %s\n", err, strerror(err));
    }

    int thread_delay_us=50000;    //50ms

    int adc0_value=0,adc1_value=0;
    while(1)
    {
        if (ioctl(fd, IOCTL_SAR_SET_CHANNEL_READ_VALUE, &adcCfg_sar0) < 0) {
            int err = errno;
            printf("YH:IOCTL_SAR0 FAILED, errno: %d, %s\n", err, strerror(err));
        }
        else {
            if(adc0_value!=adcCfg_sar0.adc_value)
            {
                adc0_value=adcCfg_sar0.adc_value;
                YH_Sar_Adc0_Handle(adc0_value);
            }
        }

        #if (CURRENT_DEVICE_MODEL==MODEL_IP_SPEAKER_F && SELECTED_DISPLAY_TYPE==DISPLAY_TYPE_RGB_4P3_480X272)
        {
            if (ioctl(fd, IOCTL_SAR_SET_CHANNEL_READ_VALUE, &adcCfg_sar1) < 0) {
                int err = errno;
                printf("YH:IOCTL_SAR1 FAILED, errno: %d, %s\n", err, strerror(err));
            }
            else {
                if(adc1_value!=adcCfg_sar1.adc_value)
                {
                    adc1_value=adcCfg_sar1.adc_value;
                    //printf("SAR%d:value %04d\n", adcCfg_sar1.channel_value,adcCfg_sar1.adc_value);
                }
            }

            if(adc1_value>=ADC_AMP_FAN_START_THRESHOLD)
            {
                if(!bAmpFanStarted)
                {
                    printf("YH:AMP FAN START\n");
                    bAmpFanStarted = true;
                    YH_control_amp_fan(1);   //打开风机
                }
            }
            else if(adc1_value<ADC_AMP_FAN_STOP_THRESHOLD)
            {
                if(bAmpFanStarted)
                {
                    printf("YH:AMP FAN STOP\n");
                    bAmpFanStarted = false;
                    YH_control_amp_fan(0);   //关闭风机
                }
            }

            //功放保护LED
            if(Get_Gpio_Value(PAD_GPIO2))
            {
                if(!bAmpProtectLedOn)
                {
                    //空闲的时候不判断功放保护状态
                    if(get_system_source()!=SOURCE_NULL)
                    {
                        Set_Gpio_High(PAD_KEY3);
                        bAmpProtectLedOn=true;
                    }
                }
            }
            else
            {
                if(bAmpProtectLedOn)
                {
                    Set_Gpio_Low(PAD_KEY3);
                    bAmpProtectLedOn=false;
                }
            }

            int gpio5_value = Get_Gpio_Value(PAD_GPIO5);
            int gpio6_value = Get_Gpio_Value(PAD_GPIO6);
            if(!gpio5_value || !gpio6_value)
            {
                //MIC1或者MIC2插入，开启ADC0
                if(!bMicInsert)
                {
                    bMicInsert=true;
                    printf("MicInsert=true!\n");
                }
            }
            else
            {
                //MIC1且MIC2拔出，关闭ADC0
                if(bMicInsert)
                {
                    bMicInsert=false;
                    printf("MicInsert=false!\n");
                }
            }
        }
        #endif

        usleep(thread_delay_us);
    }
}


void YH_board_sar_thread()
{
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)YH_board_sar, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}


void YH_init_gpio()
{
    #if (CURRENT_DEVICE_MODEL==MODEL_IP_SPEAKER_F && SELECTED_DISPLAY_TYPE==DISPLAY_TYPE_RGB_4P3_480X272)
    Set_Gpio_Input(PAD_GPIO2);
    Set_Gpio_Input(PAD_GPIO5);
    Set_Gpio_Input(PAD_GPIO6);
    #endif
}



#endif

