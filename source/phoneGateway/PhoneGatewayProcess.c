#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <semaphore.h>
#include "sysconf.h"
#include "../network/udp_client.h"
#include "../network/network_process.h"
#include "PhoneGatewayProcess.h"

#if(IS_DEVICE_PHONE_GATEWAY)

/*******电话网关相关变量************/
st_phone_gateway_info phoneGateway_info;

int g_device_phone_gateway_ready;			// 电话网关准备好可以发送数据
/*******电话网关相关变量************/


static _stPhoneGatewayData stPhoneGatewayData;
static pthread_mutex_t mutex_DataPkg=PTHREAD_MUTEX_INITIALIZER;	//采集音频包锁
static sem_t sem_DataPkgRecv;  	//信号量-接收到音频采集包

static unsigned char sendDataBuf[MAX_PHONE_GATEWAY_DATA_SIZE];


int isMatchTelPhoneNumber(char *telPhoneNumber)
{
	//功能开关关闭
	if(!phoneGateway_info.m_nMasterSwitch)
		return 0;
	//匹配电话白名单
	//如果含有+86，先去除
	char modTelPhone[128]={0};
	// 检查输入字符串是否以"+86"开头
    if (strncmp(telPhoneNumber, "+86", 3) == 0) {
        strcpy(modTelPhone, telPhoneNumber + 3);  // 复制去除"+86"后的内容到output
    } else {
        strcpy(modTelPhone,telPhoneNumber);
    }
	if(strstr(phoneGateway_info.telWhitelist,modTelPhone))
		return 1;
	return 0;
}

void phoneGateway_clear()
{
	memset(&stPhoneGatewayData,0,sizeof(stPhoneGatewayData));
}

/*********************************************************************
 * @fn      phoneGateway_Data_Add
 *
 * @brief   电话网关数据加入
 *
 * @param   void
 *
 * @return	void
 */
void phoneGateway_Data_Add(signed short int *AllChannelBuf,unsigned int BufferLen)
{
	if(BufferLen > MAX_PHONE_GATEWAY_DATA_SIZE)
		return;
    int i=0;
	pthread_mutex_lock(&mutex_DataPkg);
    
	memcpy(stPhoneGatewayData.stPhoneGatewayBuf[stPhoneGatewayData.write_pos].buf+i,AllChannelBuf,BufferLen);
    
	stPhoneGatewayData.stPhoneGatewayBuf[stPhoneGatewayData.write_pos].len = BufferLen;

	//printf("phoneGateway_Data_Add:len=%d\n",BufferLen);
	stPhoneGatewayData.write_pos++;
	if(stPhoneGatewayData.write_pos >= MAX_PHONE_GATEWAY_PKG_NUM )
	{
		stPhoneGatewayData.write_pos = 0;
	}
	sem_post(&sem_DataPkgRecv);
	pthread_mutex_unlock(&mutex_DataPkg);
}


/*********************************************************************
 * @fn      phoneGateway_data_process
 *
 * @brief   电话网关数据处理
 *
 * @param   void
 *
 * @return	void
 */
void *phoneGateway_data_process(void)
{
    unsigned int PkgCnt=0;
	unsigned char sendDataBuf[MAX_PHONE_GATEWAY_DATA_SIZE];
	while(1)
	{
		sem_wait(&sem_DataPkgRecv);

		int bufLen=stPhoneGatewayData.stPhoneGatewayBuf[stPhoneGatewayData.read_pos].len;
		static int bufPos=0;
		if(PkgCnt%2 == 0)
		{
			bufPos = 0;
		}
		memcpy(sendDataBuf+bufPos,stPhoneGatewayData.stPhoneGatewayBuf[stPhoneGatewayData.read_pos].buf,bufLen);
		bufPos+=bufLen;

		if(PkgCnt%2 && (g_phone_gateway_signal_valid && g_device_phone_gateway_ready))
		{
			SendPhoneGatewayStreamToMultiCast(sendDataBuf,bufPos);
			SendPhoneGatewayStreamToServer(sendDataBuf,bufPos);
		}
		
		
		PkgCnt++;

		memset(&stPhoneGatewayData.stPhoneGatewayBuf[stPhoneGatewayData.read_pos], 0, sizeof(_stPhoneGatewayBuf));

		stPhoneGatewayData.read_pos++;
		if(stPhoneGatewayData.read_pos >= MAX_PHONE_GATEWAY_PKG_NUM)
		{
			stPhoneGatewayData.read_pos = 0;
		}

	}
	pthread_exit(NULL);
}

/*********************************************************************
 * @fn      start_phoneGateway_data_process_pthread
 *
 * @brief   启动电话网关数据处理线程
 *
 * @param   void
 *
 * @return	void
 */
void start_phoneGateway_data_process_pthread(void)
{
	sem_init(&sem_DataPkgRecv,0,0);

	int ret = -1;
	pthread_t Mcast_Pthread;
	pthread_attr_t Pthread_Attr;

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Mcast_Pthread, &Pthread_Attr, (void *)phoneGateway_data_process, NULL);
	if (ret < 0)
	{
		printf("start_phoneGateway_data_process_pthread create failed!!!\n");
	}
	else
	{
		printf("start_phoneGateway_data_process_pthread create success!\n");
	}
	pthread_attr_destroy(&Pthread_Attr);
}





#endif
