#ifndef _PHONE_GATEWAY_PROCESS_H_
#define _PHONE_GATEWAY_PROCESS_H_

#define MAX_PHONE_GATEWAY_PHONE_NUMBER  5

#define PHONE_GATEWAY_SAMPLERATE  32000

#define MAX_PHONE_GATEWAY_PKG_NUM     4
#define MAX_PHONE_GATEWAY_CHANNEL_NUM 4
#define MAX_PHONE_GATEWAY_DATA_SIZE   1024

#define MAX_PHONE_GATEWAY_WHITELIST_LENGTH 256

/*******音频混音器相关变量************/
typedef struct {
    unsigned char m_nMasterSwitch;                          //主开关（0/1，默认为0关闭）
    unsigned char m_nVolume;                                //混音音量
    char telWhitelist[MAX_PHONE_GATEWAY_WHITELIST_LENGTH]; //电话白名单,逗号隔开
}st_phone_gateway_info;
extern st_phone_gateway_info phoneGateway_info;

extern int g_device_phone_gateway_ready;			//电话网关准备好可以发送数据
/*******音频混音器相关变量************/

typedef struct
{
    //unsigned char isValid;  //是否有效
    unsigned char buf[MAX_PHONE_GATEWAY_DATA_SIZE];   //缓存大小
    int len;
}_stPhoneGatewayBuf;

typedef struct
{
    int write_pos;
    int read_pos;
    _stPhoneGatewayBuf stPhoneGatewayBuf[MAX_PHONE_GATEWAY_PKG_NUM];
}_stPhoneGatewayData;

int isMatchTelPhoneNumber(char *telPhoneNumber);
void phoneGateway_clear();
void phoneGateway_Data_Add(signed short int *AllChannelBuf,unsigned int BufferLen);
void start_phoneGateway_data_process_pthread(void);

#endif