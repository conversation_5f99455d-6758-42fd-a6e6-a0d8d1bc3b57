CSRCS += main.c

DEPPATH += --dep-path $(BUILD_DIR)/$(SOURCE_DIR_NAME)
VPATH += :$(BUILD_DIR)/$(SOURCE_DIR_NAME)

CFLAGS += "-I$(BUILD_DIR)/$(SOURCE_DIR_NAME)"

LVGL_DIR ?= $(BUILD_DIR)/$(SOURCE_DIR_NAME)
LVGL_DIR_NAME ?= lvgl

LVGL_EXAMPLES_DIR ?= $(BUILD_DIR)/$(SOURCE_DIR_NAME)
LVGL_EXAMPLES_DIR_NAME ?= lv_examples

LVGL_DRIVERS_DIR ?= $(BUILD_DIR)/$(SOURCE_DIR_NAME)
LVGL_DRIVERS_DIR_NAME ?= lv_drivers

include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/network/network.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/system/system.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/sigmastar/sigmastar.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/thirdParty/thirdParty.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/priority/priority.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/mediaPlayer/mediaPlayer.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/g722/g722.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/g7221/g7221.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/kcp/kcp.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/uart/uart.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/songCache/songCache.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/hardware/hardware.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/audioCollector/audioCollector.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/sequencePower/sequencePower.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/audioMixer/audioMixer.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/intercom/intercom.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/module4G/module4G.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/phoneGateway/phoneGateway.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/mongoose/mongoose.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/cJSON/cJSON.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/file/file.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/log/log.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/timerEx/timerEx.mk


include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/lv_drivers/lv_drivers.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/lv_porting_sstar/lv_porting_sstar.mk

ifeq ($(USE_PC_SIMULATOR),)
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/pjsip/pjsip.mk
else
$(WARNINGS USE_PC_SIMULATOR is set,dont include pjsip.mk)
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/lv_examples/lv_examples.mk
endif

include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/extension/extension.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/tts/tts.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/informationPublish/informationPublish.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/lv_freetype/lv_freetype.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/lvgl/lvgl.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/win/win.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/mqtt_client/mqtt_client.mk
include $(BUILD_DIR)/$(SOURCE_DIR_NAME)/icons/icons.mk