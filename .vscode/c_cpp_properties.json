{"configurations": [{"name": "Linux", "includePath": ["${workspaceFolder}/**", "/home/<USER>/project/exdisk/sigmastar/ssd212/V015/project/release/include/**", "/home/<USER>/project/exdisk/sigmastar/ssd212/V015/project/release/disp/p3/common/glibc/9.1.0/mi_libs/dynamic", "/home/<USER>/project/speex/cross/include", "/mnt/hgfs/project/webrtc/WebRTC_NS_CPP"], "defines": [], "compilerPath": "/usr/bin/gcc", "cStandard": "gnu17", "cppStandard": "gnu++14", "intelliSenseMode": "linux-gcc-x64"}], "version": 4}