{"fileheader.LastModifiedBy": "Mingshu.Jiang", "fileheader.Author": "Mingshu.Jiang", "files.associations": {"mi_sys.h": "c", "mi_common_datatype.h": "c", "sockios.h": "c", "nettools.h": "c", "in.h": "c", "stdlib.h": "c", "mi_functions.h": "c", "mi_audio.h": "c", "sysconf.h": "c", "globalmethod.h": "c", "inet.h": "c", "pthread.h": "c", "netdb.h": "c", "string.h": "c", "sysmethod.h": "c", "minini.h": "c", "network_protocol.h": "c", "udp_client.h": "c", "network_process.h": "c", "multicast.h": "c", "paging_stream.h": "c", "array": "c", "string": "c", "string_view": "c", "mp3common.h": "c", "coder.h": "c", "mp3dec.h": "c", "assembly.h": "c", "netplayer.h": "c", "mp3decoder.h": "c", "mpg123.h": "c", "recv_stream.h": "c", "mi_ao.h": "c", "audio_par_common.h": "c", "initializer_list": "c", "utility": "c", "g722.h": "c", "stdint.h": "c", "stdint-gcc.h": "c", "inttypes.h": "c", "tcp_client.h": "c", "mkcp.h": "c", "algorithm": "c", "audio_params.h": "c", "samplerate.h": "c", "samplerateconvert.h": "c", "soxr.h": "c", "soxr_t.h": "c", "g722_decoder.h": "c", "md5.h": "c", "stdio.h": "c", "cctype": "c", "mi_gpio.h": "c", "speex_preprocess.h": "c", "webrtc_ns.h": "c", "const.h": "c", "audioaedprocess.h": "c", "ioctl.h": "c", "types.h": "c", "audioprocess.h": "c", "ethtool.h": "c", "bluetooth.h": "c", "uartcommon.h": "c", "typeinfo": "c", "onlinesaver.h": "c", "onlineDLink.h": "c", "http_client.h": "c", "ifaddrs.h": "c", "es7210.h": "c", "i2c_interface.h": "c", "stdbool.h": "c", "i2c.h": "c", "mi_audio_collector.h": "c", "audiocollectorprocess.h": "c", "limits.h": "c", "semaphore.h": "c", "time.h": "c", "sequencepowerprocess.h": "c", "disp.h": "c", "socket.h": "c", "fire.h": "c", "mi_sar.h": "c", "watchdog.h": "c", "signal.h": "c", "intercomprocess.h": "c", "audioaecprocess.h": "c", "optional": "c", "istream": "c", "ostream": "c", "system_error": "c", "functional": "c", "tuple": "c", "type_traits": "c", "call_ring.h": "c", "*.tcc": "c", "fstream": "c", "syscall.h": "c", "g722_encoder.h": "c", "triggersong.h": "c", "audiomixerprocess.h": "c", "fcntl.h": "c", "phonegatewayprocess.h": "c", "module4g.h": "c", "volumecontrol.h": "c", "typedef.h": "c", "file.h": "c", "debug.h": "c", "log.h": "c", "util.h": "c", "timerex.h": "c", "voice.h": "c", "priority.h": "c", "appconfig.h": "c", "rtpmulticast.h": "c", "sip.h": "c", "sysconfig.h": "c", "pjsuacommon.h": "c", "random": "c", "shm.h": "c", "ipc.h": "c", "apitest.h": "c", "sem.h": "c", "extension.h": "c", "tts.h": "c", "mongoose.h": "c", "printf.h": "c", "config.h": "c", "fmt.h": "c", "logmongoose.h": "c", "url.h": "c", "informationpublish.h": "c", "termios.h": "c", "iodetection.h": "c", "my_md5.h": "c", "lv_conf_internal.h": "c", "display.h": "c", "lvgl.h": "c", "lv_freetype.h": "c", "spi_operation.h": "c", "systimer.h": "c", "unistd.h": "c", "st7789.h": "c", "win.h": "c", "doublelink.h": "c", "mi_common.h": "c", "ffplayer.h": "c", "samplefmt.h": "c", "avutil.h": "c", "swresample.h": "c", "avcodec.h": "c", "timestamp.h": "c", "lv_demo_benchmark.h": "c", "adcuart.h": "c", "rotaryencoder.h": "c", "statfs.h": "c", "ratio": "c", "mainwindow.h": "c", "monitor.h": "c", "yhboard.h": "c", "backlight.h": "c", "evdev.h": "c", "lv_drv_conf.h": "c", "aipu_settingswin.h": "c", "yihui_mainwindow.h": "c", "theme.h": "c", "tm1640.h": "c", "yihui_settingswindow.h": "c", "aipu_mainwindow.h": "c", "set": "c", "newuartdevice.h": "c", "ampcontroler.h": "c"}, "search.followSymlinks": false}