(function(e){function t(t){for(var i,r,o=t[0],l=t[1],c=t[2],u=0,p=[];u<o.length;u++)r=o[u],Object.prototype.hasOwnProperty.call(a,r)&&a[r]&&p.push(a[r][0]),a[r]=0;for(i in l)Object.prototype.hasOwnProperty.call(l,i)&&(e[i]=l[i]);d&&d(t);while(p.length)p.shift()();return n.push.apply(n,c||[]),s()}function s(){for(var e,t=0;t<n.length;t++){for(var s=n[t],i=!0,o=1;o<s.length;o++){var l=s[o];0!==a[l]&&(i=!1)}i&&(n.splice(t--,1),e=r(r.s=s[0]))}return e}var i={},a={index:0},n=[];function r(t){if(i[t])return i[t].exports;var s=i[t]={i:t,l:!1,exports:{}};return e[t].call(s.exports,s,s.exports,r),s.l=!0,s.exports}r.m=e,r.c=i,r.d=function(e,t,s){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:s})},r.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var s=Object.create(null);if(r.r(s),Object.defineProperty(s,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(s,i,function(t){return e[t]}.bind(null,i));return s},r.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="/";var o=window["webpackJsonp"]=window["webpackJsonp"]||[],l=o.push.bind(o);o.push=t,o=o.slice();for(var c=0;c<o.length;c++)t(o[c]);var d=l;n.push([0,"chunk-vendors"]),s()})({0:function(e,t,s){e.exports=s("56d7")},"06aa":function(e,t,s){},"06fb":function(e,t,s){},1180:function(e,t,s){"use strict";s("68f4")},"21df":function(e,t,s){"use strict";s("06fb")},"330c":function(e,t,s){"use strict";s("bedc")},"56d7":function(e,t,s){"use strict";s.r(t);var i=s("2b0e"),a=s("8c4f"),n=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[e.isLoggedIn?e._e():t("div",{attrs:{id:"login"}},[t("div",{staticClass:"login-container"},[t("form",{on:{submit:function(t){return t.preventDefault(),e.handleSubmit.apply(null,arguments)}}},[t("div",{staticClass:"login-box"},[t("div",{staticClass:"language-selector"},[t("select",{directives:[{name:"model",rawName:"v-model",value:e.selectedLanguage,expression:"selectedLanguage"}],on:{change:[function(t){var s=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){var t="_value"in e?e._value:e.value;return t}));e.selectedLanguage=t.target.multiple?s:s[0]},e.changeLanguage]}},[t("option",{attrs:{value:"zh"}},[e._v("简体中文")]),t("option",{attrs:{value:"en"}},[e._v("English")])])]),t("h2",[e._v(e._s(e.$t("login.title")))]),t("label",{attrs:{for:"username"}},[e._v(e._s(e.$t("login.username")))]),t("input",{directives:[{name:"model",rawName:"v-model",value:e.username,expression:"username"}],attrs:{type:"text",id:"username",required:""},domProps:{value:e.username},on:{input:function(t){t.target.composing||(e.username=t.target.value)}}}),t("label",{attrs:{for:"userSecret"}},[e._v(e._s(e.$t("login.password")))]),t("input",{attrs:{type:"text",id:"userSecret",autocomplete:"off",required:""},domProps:{value:e.maskedPassword},on:{input:e.updatePassword}}),t("button",{staticClass:"login-button",attrs:{type:"submit"}},[e._v(e._s(e.$t("login.loginButton")))])])])])]),e.isLoggedIn?t("div",{staticClass:"config"},[t("div",{staticClass:"tab-container"},[t("div",{staticClass:"tab-bar"},e._l(e.tabs,(function(s){return t("div",{key:s.id,class:["tab",{active:e.activeTab===s.id}],on:{click:function(t){e.activeTab=s.id}}},[t("span",{staticClass:"tab-label"},[e._v(e._s(s.label))])])})),0)]),t("div",{staticClass:"tab-content"},["deviceInfo"===e.activeTab?t("div",{staticClass:"device-info"},[t("h2",[e._v(e._s(e.$t("deviceInfo.title")))]),t("table",[t("tr",[t("th",[e._v(e._s(e.$t("deviceInfo.property")))]),t("th",[e._v(e._s(e.$t("deviceInfo.value")))])]),t("tr",[t("td",[e._v(e._s(e.$t("deviceInfo.deviceName")))]),t("td",[e._v(e._s(e.device.name))])]),t("tr",[t("td",[e._v(e._s(e.$t("deviceInfo.deviceType")))]),t("td",[e._v(e._s(e.getDeviceModelName(e.device.model)))])]),t("tr",[t("td",[e._v(e._s(e.$t("deviceInfo.deviceMac")))]),t("td",[e._v(e._s(e.device.mac))])]),t("tr",[t("td",[e._v(e._s(e.$t("deviceInfo.deviceIp")))]),t("td",[e._v(e._s(e.device.ip))])]),t("tr",[t("td",[e._v(e._s(e.$t("deviceInfo.networkMode")))]),t("td",[e._v(e._s(1===e.device.networkMode?"UDP":"TCP"))])]),t("tr",[t("td",[e._v(e._s(e.$t("deviceInfo.serverStatus")))]),t("td",[e._v(e._s(e.device.serverConnected?e.$t("deviceInfo.connected"):e.$t("deviceInfo.disconnected")))])]),t("tr",[t("td",[e._v(e._s(e.$t("deviceInfo.firmwareVersion")))]),t("td",[e._v(e._s(e.device.firmwareVersion))])])]),t("div",{staticClass:"button-container"},[t("button",{on:{click:e.refreshDevice}},[e._v(e._s(e.$t("common.refresh")))]),t("button",{on:{click:e.logoutDevice}},[e._v(e._s(e.$t("common.logout")))]),t("button",{on:{click:e.restartDevice}},[e._v(e._s(e.$t("common.restart")))]),t("button",{on:{click:e.resetDevice}},[e._v(e._s(e.$t("common.reset")))])])]):e._e(),"networkSettings"===e.activeTab?t("div",{staticClass:"network-settings"},[t("h2",[e._v(e._s(e.$t("networkSettings.title")))]),t("div",{staticClass:"network-assgin-toggle"},[t("label",{attrs:{for:"networkAssginToggle"}},[e._v(e._s(e.$t("networkSettings.ipAssignMode"))+"：")]),t("select",{directives:[{name:"model",rawName:"v-model",value:e.network.assign,expression:"network.assign"}],attrs:{id:"networkAssginToggle"},on:{change:function(t){var s=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){var t="_value"in e?e._value:e.value;return t}));e.$set(e.network,"assign",t.target.multiple?s:s[0])}}},[t("option",{domProps:{value:0}},[e._v("DHCP")]),t("option",{domProps:{value:1}},[e._v(e._s(e.$t("networkSettings.staticIp")))])])]),1===e.network.assign?t("div",{staticClass:"static-ip-settings"},[t("table",[t("tr",[t("td",[e._v(e._s(e.$t("networkSettings.ipAddress"))+"：")]),t("td",[t("input",{directives:[{name:"model",rawName:"v-model",value:e.network.ipAddress,expression:"network.ipAddress"}],staticClass:"network-parm-input",attrs:{type:"text",placeholder:e.$t("networkSettings.ipAddress")},domProps:{value:e.network.ipAddress},on:{input:function(t){t.target.composing||e.$set(e.network,"ipAddress",t.target.value)}}})])]),t("tr",[t("td",[e._v(e._s(e.$t("networkSettings.subnetMask"))+"：")]),t("td",[t("input",{directives:[{name:"model",rawName:"v-model",value:e.network.subnetMask,expression:"network.subnetMask"}],staticClass:"network-parm-input",attrs:{type:"text",placeholder:e.$t("networkSettings.subnetMask")},domProps:{value:e.network.subnetMask},on:{input:function(t){t.target.composing||e.$set(e.network,"subnetMask",t.target.value)}}})])]),t("tr",[t("td",[e._v(e._s(e.$t("networkSettings.gateway"))+"：")]),t("td",[t("input",{directives:[{name:"model",rawName:"v-model",value:e.network.gateway,expression:"network.gateway"}],staticClass:"network-parm-input",attrs:{type:"text",placeholder:e.$t("networkSettings.gateway")},domProps:{value:e.network.gateway},on:{input:function(t){t.target.composing||e.$set(e.network,"gateway",t.target.value)}}})])]),t("tr",[t("td",[e._v(e._s(e.$t("networkSettings.primaryDns"))+"：")]),t("td",[t("input",{directives:[{name:"model",rawName:"v-model",value:e.network.primaryDns,expression:"network.primaryDns"}],staticClass:"network-parm-input",attrs:{type:"text",placeholder:e.$t("networkSettings.primaryDns")},domProps:{value:e.network.primaryDns},on:{input:function(t){t.target.composing||e.$set(e.network,"primaryDns",t.target.value)}}})])]),t("tr",[t("td",[e._v(e._s(e.$t("networkSettings.secondaryDns"))+"：")]),t("td",[t("input",{directives:[{name:"model",rawName:"v-model",value:e.network.secondaryDns,expression:"network.secondaryDns"}],staticClass:"network-parm-input",attrs:{type:"text",placeholder:e.$t("networkSettings.secondaryDns")},domProps:{value:e.network.secondaryDns},on:{input:function(t){t.target.composing||e.$set(e.network,"secondaryDns",t.target.value)}}})])])])]):e._e(),t("div",{staticClass:"network-mode-toggle"},[t("label",{attrs:{for:"networkModeToggle"}},[e._v(e._s(e.$t("networkSettings.broadcastMode"))+"：")]),t("select",{directives:[{name:"model",rawName:"v-model",value:e.network.mode,expression:"network.mode"}],attrs:{id:"networkModeToggle"},on:{change:function(t){var s=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){var t="_value"in e?e._value:e.value;return t}));e.$set(e.network,"mode",t.target.multiple?s:s[0])}}},[t("option",{domProps:{value:1}},[e._v("UDP")]),t("option",{domProps:{value:2}},[e._v("TCP")])])]),2===e.network.mode?t("div",{staticClass:"static-ip-settings"},[t("table",[t("tr",[t("td",[e._v(e._s(e.$t("networkSettings.serverAddress"))+"：")]),t("td",[t("input",{directives:[{name:"model",rawName:"v-model",value:e.network.serverIP,expression:"network.serverIP"}],staticClass:"network-parm-input",attrs:{type:"text",placeholder:e.$t("networkSettings.serverAddress")},domProps:{value:e.network.serverIP},on:{input:function(t){t.target.composing||e.$set(e.network,"serverIP",t.target.value)}}})])]),t("tr",[t("td",[e._v(e._s(e.$t("networkSettings.serverPort"))+"：")]),t("td",[t("input",{directives:[{name:"model",rawName:"v-model",value:e.network.serverPort,expression:"network.serverPort"}],staticClass:"network-parm-input",attrs:{type:"text",placeholder:e.$t("networkSettings.serverPort")},domProps:{value:e.network.serverPort},on:{input:function(t){t.target.composing||e.$set(e.network,"serverPort",t.target.value)}}})])])])]):e._e(),t("div",{staticClass:"button-container"},[t("button",{on:{click:e.refreshNetworkSettings}},[e._v(e._s(e.$t("common.refresh")))]),t("button",{on:{click:e.saveNetworkSettings}},[e._v(e._s(e.$t("common.save")))])])]):e._e(),"sipSettings"===e.activeTab?t("div",{staticClass:"sip-settings"},[t("h2",[e._v(e._s(e.$t("sipSettings.title")))]),t("div",{staticClass:"sip-authorization"},[t("p",[e._v(e._s(e.$t("sipSettings.authStatus"))+"： "),t("strong",[e._v(e._s(e.sip.authorizationStatus?e.$t("sipSettings.authorized"):e.$t("sipSettings.unauthorized")))])])]),e.sip.authorizationStatus?t("div",{staticClass:"sip-details"},[t("div",{staticClass:"sip-toggle"},[t("label",{attrs:{for:"sipRegistrationToggle"}},[e._v(e._s(e.$t("sipSettings.registrationSwitch"))+"：")]),t("select",{directives:[{name:"model",rawName:"v-model",value:e.sip.registrationEnabled,expression:"sip.registrationEnabled"}],attrs:{id:"sipRegistrationToggle"},on:{change:function(t){var s=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){var t="_value"in e?e._value:e.value;return t}));e.$set(e.sip,"registrationEnabled",t.target.multiple?s:s[0])}}},[t("option",{domProps:{value:!1}},[e._v(e._s(e.$t("common.disable")))]),t("option",{domProps:{value:!0}},[e._v(e._s(e.$t("common.enable")))])])]),e.sip.registrationEnabled?t("div",{staticClass:"sip-registration-info"},[t("p",[e._v(e._s(e.$t("sipSettings.registrationStatus"))+"： "),t("strong",[e._v(e._s(e.getSipStatusStr(e.sip.registrationStatus)))])]),t("table",[t("tr",[t("th",[e._v(e._s(e.$t("deviceInfo.property")))]),t("th",[e._v(e._s(e.$t("deviceInfo.value")))])]),t("tr",[t("td",[e._v(e._s(e.$t("sipSettings.protocol")))]),t("td",[t("select",{directives:[{name:"model",rawName:"v-model",value:e.sip.transProtocol,expression:"sip.transProtocol"}],staticClass:"sip-parm-input",on:{change:function(t){var s=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){var t="_value"in e?e._value:e.value;return t}));e.$set(e.sip,"transProtocol",t.target.multiple?s:s[0])}}},[t("option",{domProps:{value:0}},[e._v("UDP")]),t("option",{domProps:{value:1}},[e._v("TCP")])])])]),t("tr",[t("td",[e._v(e._s(e.$t("sipSettings.serverAddress")))]),t("td",[t("input",{directives:[{name:"model",rawName:"v-model",value:e.sip.serverAddress,expression:"sip.serverAddress"}],staticClass:"sip-parm-input",attrs:{type:"text",placeholder:e.$t("sipSettings.serverAddress")},domProps:{value:e.sip.serverAddress},on:{input:function(t){t.target.composing||e.$set(e.sip,"serverAddress",t.target.value)}}})])]),t("tr",[t("td",[e._v(e._s(e.$t("sipSettings.serverPort")))]),t("td",[t("input",{directives:[{name:"model",rawName:"v-model",value:e.sip.serverPort,expression:"sip.serverPort"}],staticClass:"sip-parm-input",attrs:{type:"number",placeholder:e.$t("sipSettings.serverPort")},domProps:{value:e.sip.serverPort},on:{input:function(t){t.target.composing||e.$set(e.sip,"serverPort",t.target.value)}}})])]),t("tr",[t("td",[e._v(e._s(e.$t("sipSettings.account")))]),t("td",[t("input",{directives:[{name:"model",rawName:"v-model",value:e.sip.account,expression:"sip.account"}],staticClass:"sip-parm-input",attrs:{type:"text",placeholder:e.$t("sipSettings.account")},domProps:{value:e.sip.account},on:{input:function(t){t.target.composing||e.$set(e.sip,"account",t.target.value)}}})])]),t("tr",[t("td",[e._v(e._s(e.$t("sipSettings.password")))]),t("td",[t("input",{directives:[{name:"model",rawName:"v-model",value:e.sip.password,expression:"sip.password"}],staticClass:"sip-parm-input",attrs:{type:"password",placeholder:e.$t("sipSettings.password")},domProps:{value:e.sip.password},on:{input:function(t){t.target.composing||e.$set(e.sip,"password",t.target.value)}}})])])])]):e._e()]):e._e(),t("div",{staticClass:"button-container"},[t("button",{on:{click:e.refreshSipSettins}},[e._v(e._s(e.$t("common.refresh")))]),t("button",{on:{click:e.saveSipSettings}},[e._v(e._s(e.$t("common.save")))])])]):e._e(),"terminalSettings"===e.activeTab?t("div",{staticClass:"terminal-settings"},[t("h2",[e._v(e._s(e.$t("terminalSettings.title")))]),t("table",{staticClass:"terminal-settings-table"},[t("tr",[t("th",[e._v(e._s(e.$t("terminalSettings.identifier")))]),t("th",[e._v(e._s(e.$t("terminalSettings.property")))]),t("th",[e._v(e._s(e.$t("terminalSettings.value")))])]),e._l(e.terminalSettings,(function(s,i){return t("tr",{key:i},[t("td",[e._v(e._s(s.name))]),t("td",[e._v(e._s(s.desc))]),t("td",["select"===s.type?[t("select",{directives:[{name:"model",rawName:"v-model",value:s.value,expression:"setting.value"}],staticClass:"terminal-parm-select-input",on:{change:function(t){var i=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){var t="_value"in e?e._value:e.value;return t}));e.$set(s,"value",t.target.multiple?i:i[0])}}},e._l(s.options,(function(s){return t("option",{key:s.value,domProps:{value:s.value}},[e._v(" "+e._s(s.label)+" ")])})),0)]:[t("input",{directives:[{name:"model",rawName:"v-model",value:s.value,expression:"setting.value"}],staticClass:"terminal-parm-input",attrs:{type:"text",placeholder:e.$t("terminalSettings.value")},domProps:{value:s.value},on:{input:function(t){t.target.composing||e.$set(s,"value",t.target.value)}}})],t("button",{on:{click:function(t){return e.saveTerminalSetting(s.name)}}},[e._v(e._s(e.$t("common.save")))])],2)])}))],2),t("div",{staticClass:"button-container"},[t("button",{on:{click:e.refreshTerminalSettings}},[e._v(e._s(e.$t("common.refresh")))])])]):e._e(),"softwareUpdate"===e.activeTab?t("div",{staticClass:"software-update-container"},[t("h2",[e._v(e._s(e.$t("softwareUpdate.title")))]),t("div",{staticClass:"software-update-input-group"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.FirmwareUpdate.updateUrl,expression:"FirmwareUpdate.updateUrl"}],staticClass:"software-update-input",attrs:{type:"text",placeholder:e.$t("softwareUpdate.urlPlaceholder")},domProps:{value:e.FirmwareUpdate.updateUrl},on:{input:function(t){t.target.composing||e.$set(e.FirmwareUpdate,"updateUrl",t.target.value)}}}),t("button",{staticClass:"software-update-button",staticStyle:{"margin-top":"-18px"},on:{click:e.handleUrlUpgrade}},[e._v(" "+e._s(e.$t("softwareUpdate.upgrade"))+" ")])]),t("div",{staticClass:"software-update-upload-group"},[t("div",{staticClass:"custom-file-upload"},[t("input",{ref:"fileInput",staticClass:"software-update-file-input",attrs:{type:"file",accept:".tar.gz",id:"file-upload"},on:{change:e.handleFileSelection}}),t("label",{staticClass:"file-upload-label",attrs:{for:"file-upload"}},[t("span",[e._v(e._s(e.FirmwareUpdate.selectedFile?e.FirmwareUpdate.selectedFile.name:e.$t("softwareUpdate.selectFile")))])])]),t("button",{staticClass:"software-update-button",attrs:{disabled:!e.FirmwareUpdate.selectedFile},on:{click:e.handleFileUpload}},[e._v(" "+e._s(e.$t("softwareUpdate.upload"))+" ")])]),e.FirmwareUpdate.upgradeIsLoading?t("div",{staticClass:"upgrade-loading-overlay"},[t("div",{staticClass:"upgrade-spinner"}),t("p",[e._v(e._s(e.$t("softwareUpdate.upgrading")))])]):e._e(),t("div",{staticClass:"software-update-status"},[t("p",[e._v(" "+e._s(e.FirmwareUpdate.uploadStatusMessage))])])]):e._e(),"systemMaintenance"===e.activeTab?t("div",{staticClass:"system-maintenance"},[t("h2",[e._v(e._s(e.$t("systemMaintenance.title")))]),t("div",{staticClass:"ping-tool"},[t("div",{staticClass:"ping-input-group"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.systemMaintenance.pingTarget,expression:"systemMaintenance.pingTarget"}],staticClass:"ping-input",attrs:{type:"text",placeholder:e.$t("systemMaintenance.pingPlaceholder")},domProps:{value:e.systemMaintenance.pingTarget},on:{input:function(t){t.target.composing||e.$set(e.systemMaintenance,"pingTarget",t.target.value)}}}),t("div",{staticClass:"ping-count-wrapper"},[t("label",[e._v(e._s(e.$t("systemMaintenance.pingCount"))+":")]),t("input",{directives:[{name:"model",rawName:"v-model",value:e.systemMaintenance.pingCount,expression:"systemMaintenance.pingCount"}],staticClass:"ping-count-input",attrs:{type:"number",min:"1",max:"3600"},domProps:{value:e.systemMaintenance.pingCount},on:{input:function(t){t.target.composing||e.$set(e.systemMaintenance,"pingCount",t.target.value)}}})]),e.systemMaintenance.isPinging?t("button",{staticClass:"ping-button stop-button",on:{click:e.stopPing}},[e._v(" "+e._s(e.$t("systemMaintenance.stop"))+" ")]):t("button",{staticClass:"ping-button",on:{click:e.executePing}},[e._v(" "+e._s(e.$t("systemMaintenance.ping"))+" ")]),t("button",{staticClass:"ping-button export-button",attrs:{disabled:!e.systemMaintenance.pingResults},on:{click:e.exportPingResults}},[e._v(" "+e._s(e.$t("systemMaintenance.export"))+" ")])]),t("div",{staticClass:"ping-results"},[e.systemMaintenance.isPinging?t("div",{staticClass:"ping-loading"},[e._v(" "+e._s(e.$t("systemMaintenance.pinging"))+" ")]):e._e(),e.systemMaintenance.pingResults?t("pre",{staticClass:"ping-result-text"},[e._v(e._s(e.systemMaintenance.pingResults))]):e._e()])])]):e._e()])]):e._e(),t("confirm-dialog",{attrs:{show:e.showConfirmDialog,message:e.confirmMessage},on:{confirm:e.handleDialogConfirm,cancel:e.handleDialogCancel}})],1)},r=[],o=s("cee4"),l=s("a925");i["a"].use(l["a"]);const c={en:{login:{title:"IP Terminal",username:"Username",password:"Password",loginButton:"Login",userPassError:"Invalid username or password",networkError:"Network error, please try again"},common:{refresh:"Refresh",logout:"Logout",restart:"Restart",reset:"Reset",save:"Save",success:"Success",failed:"Failed",confirm:"Confirm",cancel:"Cancel",enable:"Enable",disable:"Disable"},deviceInfo:{title:"Device Information",property:"Property",value:"Value",deviceName:"Device Name",deviceType:"Device Model",deviceMac:"Device MAC",deviceIp:"Device IP",networkMode:"Network Mode",serverStatus:"Server Connection Status",connected:"Connected",disconnected:"Disconnected",firmwareVersion:"Firmware Version"},networkSettings:{title:"Network Settings",ipAssignMode:"IP Assignment Mode",staticIp:"Static IP",ipAddress:"IP Address",subnetMask:"Subnet Mask",gateway:"Gateway",primaryDns:"Primary DNS",secondaryDns:"Secondary DNS",broadcastMode:"Broadcast Mode",serverAddress:"Server Address",serverPort:"Server Port"},sipSettings:{title:"SIP Settings",authStatus:"Authorization Status",authorized:"Authorized",unauthorized:"Unauthorized",registrationSwitch:"Registration Switch",registrationStatus:"Registration Status",protocol:"Protocol",serverAddress:"Server Address",serverPort:"Server Port",account:"Account",password:"Password",regStausDes:{regFailed:"Registration failed",regSuccess:"Registered",regInProgress:"Registering",regTimeout:"Registration Timeout",authFailed:"Authorization failed",inCall:"In Call"}},terminalSettings:{title:"Terminal Settings",identifier:"Identifier",property:"Property",value:"Value",properties:{deviceName:"Device Name",auxVolume:"AUX Input Volume",sipVolume:"SIP Call Volume",systemLanguage:"System Internal Language"}},softwareUpdate:{title:"Software Update",urlPlaceholder:"upgrade package URL",upgrade:"Upgrade",uploading:"Uploading...",upgrading:"Upgrading, please wait...",selectFile:"Select File",upload:"Upload",fileSelected:"Selected file: ",noFileSelected:"No file selected"},systemMaintenance:{title:"System Maintenance",pingPlaceholder:"Enter IP or hostname to ping",ping:"Ping",pinging:"Pinging...",enterPingTarget:"Please enter an IP address or hostname",pingFailed:"Ping failed",requestFailed:"Request failed, please try again later",pingCount:"Count",stop:"Stop",pingCountExceeded:"The ping count cannot exceed 3600",export:"Export"},tips:{confirmDeviceRestart:"Are you sure to restart the device?",confirmDeviceReset:"Are you sure to reset the device to factory settings?",settingSuccess:"Setting successful",settingFailed:"Setting failed",settingParamError:"Setting failed: Invalid parameters",requestFailed:"Request failed, please try again later",selectFile:"Please select a file first",enterUrl:"Please enter the upgrade package URL",upgradeSuccess:"Upgrade successful",upgradeFailed:"Upgrade failed, please check the URL",deviceRestarting:"Device is restarting, please wait",uploadTimeout:"Upload timeout, please try again",uploadFailed:"File upload failed, please check network",confirmUpgrade:"Are you sure to upload and upgrade firmware?",networkNoChange:"Network parameters unchanged",fileSelected:"Selected file: ",noFileSelected:"No file selected"},DeviceModel:{AllDevice:"All Devices",DecodingTerminal:"Decoding Terminal",DecodingTerminalA:"Decoding Terminal A",DecodingTerminalB:"Decoding Terminal B",DecodingTerminalC:"Decoding Terminal C",DecodingTerminalD:"Decoding Terminal D",DecodingTerminalE:"Decoding Terminal E",DecodingTerminalF:"Decoding Terminal F",NetPager:"Pager",NetPagerA:"Pager A",NetPagerB:"Pager B",NetPagerC:"Pager C",NetFireCollector:"Fire Collector",NetFireCollectorA:"Fire Collector A",NetFireCollectorB:"Fire Collector B",NetFireCollectorC:"Fire Collector C",NetFireCollectorF:"Fire Collector F",NetAudioCollector:"Audio Collector",NetAudioCollectorA:"Audio Collector A",NetAudioCollectorB:"Audio Collector B",NetAudioCollectorC:"Audio Collector C",NetAudioCollectorF:"Audio Collector F",NetSequencePower:"Sequence Power",NetSequencePowerA:"Sequence Power A",NetSequencePowerB:"Sequence Power B",NetSequencePowerC:"Sequence Power C",NetSequencePowerF:"Sequence Power F",NetAudioMixer:"Audio Mixer",NetAudioMixerC:"Audio MixerC",NetRemoteControler:"Remote Controler",NetRemoteControlerC:"Remote Controler C",NetRemoteControlerF:"Remote Controler F",NetPhoneGateway:"Phone Gateway"}},zh:{login:{title:"网络终端",username:"用户名",password:"密码",loginButton:"登录",userPassError:"用户名或密码错误",networkError:"网络错误，请重试"},common:{refresh:"刷新",logout:"退出登录",restart:"重启设备",reset:"重置设备",save:"保存",success:"成功",failed:"失败",confirm:"确定",cancel:"取消",enable:"开启",disable:"关闭"},deviceInfo:{title:"设备信息",property:"属性",value:"值",deviceName:"设备名称",deviceType:"设备类型",deviceMac:"设备MAC",deviceIp:"设备IP",networkMode:"网络模式",serverStatus:"服务器连接状态",connected:"已连接",disconnected:"未连接",firmwareVersion:"固件版本"},networkSettings:{title:"网络设置",ipAssignMode:"IP分配方式",staticIp:"静态IP",ipAddress:"IP地址",subnetMask:"子网掩码",gateway:"默认网关",primaryDns:"首选DNS",secondaryDns:"备用DNS",broadcastMode:"广播通讯模式",serverAddress:"服务器地址",serverPort:"服务器端口号"},sipSettings:{title:"SIP设置",authStatus:"授权状态",authorized:"已授权",unauthorized:"未授权",registrationSwitch:"SIP注册开关",registrationStatus:"SIP注册状态",protocol:"传输协议",serverAddress:"服务器地址",serverPort:"服务器端口",account:"账号",password:"密码",regStausDes:{regFailed:"注册失败",regSuccess:"已注册",regInProgress:"注册中",regTimeout:"注册超时",authFailed:"认证失败",inCall:"通话中"}},terminalSettings:{title:"终端设置",identifier:"标识",property:"属性",value:"值",properties:{deviceName:"设备名称",auxVolume:"本地输入音量",sipVolume:"SIP通话音量",systemLanguage:"系统内部语言"}},softwareUpdate:{title:"软件更新",urlPlaceholder:"升级包的URL",upgrade:"升级",uploading:"正在上传...",upgrading:"正在升级，请稍候...",selectFile:"选择文件",upload:"上传",fileSelected:"已选择文件：",noFileSelected:"未选择任何文件"},systemMaintenance:{title:"系统维护",pingPlaceholder:"输入要ping的IP或主机名",ping:"Ping",pinging:"正在ping...",enterPingTarget:"请输入IP地址或主机名",pingFailed:"Ping失败",requestFailed:"请求失败，请稍后重试",pingCount:"次数",stop:"停止",pingCountExceeded:"Ping次数不能超过3600",export:"导出"},tips:{confirmDeviceRestart:"确认重启设备吗？",confirmDeviceReset:"确认重置设备并恢复到出厂设置吗？",settingSuccess:"设置成功",settingFailed:"设置失败",settingParamError:"设置失败：输入参数有误",requestFailed:"请求失败，请稍后重试",selectFile:"请先选择一个文件",enterUrl:"请输入升级包的URL",upgradeSuccess:"升级成功",upgradeFailed:"升级失败，请检查URL",deviceRestarting:"设备正在重启，请稍后",uploadTimeout:"上传超时，请重试",uploadFailed:"文件上传失败，请检查网络",confirmUpgrade:"确认上传升级固件并进行软件更新吗？",networkNoChange:"网络参数未变更",fileSelected:"已选择文件：",noFileSelected:"未选择任何文件"},DeviceModel:{AllDevice:"全部设备",DecodingTerminal:"解码终端",DecodingTerminalA:"解码终端A",DecodingTerminalB:"解码终端B",DecodingTerminalC:"解码终端C",DecodingTerminalD:"解码终端D",DecodingTerminalE:"解码终端E",DecodingTerminalF:"解码终端F",NetPager:"智能寻呼台",NetPagerA:"智能寻呼台A",NetPagerB:"智能寻呼台B",NetPagerC:"智能寻呼台C",NetFireCollector:"消防采集器",NetFireCollectorA:"消防采集器A",NetFireCollectorB:"消防采集器B",NetFireCollectorC:"消防采集器C",NetFireCollectorF:"消防采集器F",NetAudioCollector:"音频采集器",NetAudioCollectorA:"音频采集器A",NetAudioCollectorB:"音频采集器B",NetAudioCollectorC:"音频采集器C",NetAudioCollectorF:"音频采集器F",NetSequencePower:"电源时序器",NetSequencePowerA:"电源时序器A",NetSequencePowerB:"电源时序器B",NetSequencePowerC:"电源时序器C",NetSequencePowerF:"电源时序器F",NetAudioMixer:"音频协处理器",NetAudioMixerC:"音频协处理器C",NetRemoteControler:"远程遥控器",NetRemoteControlerC:"远程遥控器C",NetRemoteControlerF:"远程遥控器F",NetPhoneGateway:"电话网关"}}},d=new l["a"]({locale:localStorage.getItem("language")||"zh",messages:c});var u=d;const p={NetSpeakerA:5,NetSpeakerB:6,NetSpeakerC:7,NetSpeakerD:10,NetSpeakerE:16,NetPagerA:3,NetPagerB:4,NetPagerC:20,NetFireCollectorA:8,NetAudioCollectorA:9,NetSequencePowerA:11,NetFireCollectorB:12,NetAudioCollectorB:13,NetSequencePowerB:14,NetRemoteControler:17,NetAudioMixerDecoder:18,NetAudioMixerEncoder:19,NetPhoneGateway:21,NetFireCollectorC:22,NetAudioCollectorC:23,NetSequencePowerC:24,NetRemoteControlerC:25,NetAudioMixerDecoderC:26,NetAudioMixerEncoderC:27,NetSpeakerF:28,NetFireCollectorF:29,NetAudioCollectorF:30,NetSequencePowerF:31,NetRemoteControlerF:48};function g(){return new Map([[p.NetPagerA,u.t("DeviceModel.NetPagerA")],[p.NetPagerB,u.t("DeviceModel.NetPagerB")],[p.NetPagerC,u.t("DeviceModel.NetPagerC")],[p.NetSpeakerA,u.t("DeviceModel.DecodingTerminalA")],[p.NetSpeakerB,u.t("DeviceModel.DecodingTerminalB")],[p.NetSpeakerC,u.t("DeviceModel.DecodingTerminalC")],[p.NetSpeakerD,u.t("DeviceModel.DecodingTerminalD")],[p.NetSpeakerE,u.t("DeviceModel.DecodingTerminalE")],[p.NetSpeakerF,u.t("DeviceModel.DecodingTerminalF")],[p.NetFireCollectorA,u.t("DeviceModel.NetFireCollectorA")],[p.NetAudioCollectorA,u.t("DeviceModel.NetAudioCollectorA")],[p.NetSequencePowerA,u.t("DeviceModel.NetSequencePowerA")],[p.NetFireCollectorB,u.t("DeviceModel.NetFireCollectorB")],[p.NetAudioCollectorB,u.t("DeviceModel.NetAudioCollectorB")],[p.NetSequencePowerB,u.t("DeviceModel.NetSequencePowerB")],[p.NetFireCollectorC,u.t("DeviceModel.NetFireCollectorC")],[p.NetAudioCollectorC,u.t("DeviceModel.NetAudioCollectorC")],[p.NetSequencePowerC,u.t("DeviceModel.NetSequencePowerC")],[p.NetFireCollectorF,u.t("DeviceModel.NetFireCollectorF")],[p.NetAudioCollectorF,u.t("DeviceModel.NetAudioCollectorF")],[p.NetSequencePowerF,u.t("DeviceModel.NetSequencePowerF")],[p.NetAudioMixerDecoder,u.t("DeviceModel.NetAudioMixer")],[p.NetAudioMixerEncoder,u.t("DeviceModel.NetAudioMixer")],[p.NetAudioMixerDecoderC,u.t("DeviceModel.NetAudioMixerC")],[p.NetAudioMixerEncoderC,u.t("DeviceModel.NetAudioMixerC")],[p.NetRemoteControler,u.t("DeviceModel.NetRemoteControler")],[p.NetRemoteControlerC,u.t("DeviceModel.NetRemoteControlerC")],[p.NetRemoteControlerF,u.t("DeviceModel.NetRemoteControlerF")],[p.NetPhoneGateway,u.t("DeviceModel.NetPhoneGateway")]])}var m=function(){var e=this,t=e._self._c;return e.show?t("div",{staticClass:"confirm-dialog-overlay"},[t("div",{staticClass:"confirm-dialog"},[t("p",{staticClass:"confirm-message"},[e._v(e._s(e.message))]),t("div",{staticClass:"confirm-buttons"},[t("button",{staticClass:"confirm-button confirm",on:{click:e.handleConfirm}},[e._v(e._s(e.$t("common.confirm")))]),t("button",{staticClass:"confirm-button cancel",on:{click:e.handleCancel}},[e._v(e._s(e.$t("common.cancel")))])])])]):e._e()},v=[],h={name:"ConfirmDialog",props:{show:{type:Boolean,default:!1},message:{type:String,required:!0}},methods:{handleConfirm(){this.$emit("confirm")},handleCancel(){this.$emit("cancel")}}},w=h,f=(s("330c"),s("2877")),_=Object(f["a"])(w,m,v,!1,null,"0e85119d",null),S=_.exports,C={components:{ConfirmDialog:S},data(){return{selectedLanguage:localStorage.getItem("language")||"zh",httpBaseUrl:window.location.origin,username:"",password:"",maskedPassword:"",isLoggedIn:!1,activeTab:null,device:{name:"",model:10,mac:"",ip:"",networkMode:1,serverConnected:!1,firmwareVersion:""},network:{assign:0,ipAddress:"",subnetMask:"",gateway:"",primaryDns:"",secondaryDns:"",mode:1,serverIP:"",serverPort:49888},sip:{authorizationStatus:!1,registrationEnabled:!1,registrationStatus:3,serverAddress:"",serverPort:5060,account:"",password:"",transProtocol:0},sipPollingInterval:null,FirmwareUpdate:{upgradeIsLoading:!1,selectedFile:null,updateUrl:"",uploadStatusCode:0,uploadStatusMessage:""},terminalSettings:[{name:"system_language",desc:"",value:"zh",type:"select",options:[{value:"0",label:"Chinese"},{value:"1",label:"English"}]},{name:"dev_name",desc:"",value:""},{name:"aux_volume",desc:"",value:"100"},{name:"sip_output_volume",desc:"",value:"80"}],systemMaintenance:{pingTarget:"",pingResults:"",isPinging:!1,pingCount:4,pingId:"",pingPollingInterval:null,lastResultId:0},showConfirmDialog:!1,confirmMessage:"",confirmCallback:null}},computed:{tabs(){return[{id:"deviceInfo",label:this.$t("deviceInfo.title")},{id:"networkSettings",label:this.$t("networkSettings.title")},{id:"sipSettings",label:this.$t("sipSettings.title")},{id:"terminalSettings",label:this.$t("terminalSettings.title")},{id:"systemMaintenance",label:this.$t("systemMaintenance.title")},{id:"softwareUpdate",label:this.$t("softwareUpdate.title")}]},uploadMessage(){return this.FirmwareUpdate.selectedFile?this.$t("softwareUpdate.fileSelected")+this.FirmwareUpdate.selectedFile.name:this.$t("softwareUpdate.noFileSelected")}},methods:{showConfirm(e){return new Promise(t=>{this.showConfirmDialog=!0,this.confirmMessage=e,this.confirmCallback=t})},handleDialogConfirm(){this.showConfirmDialog=!1,this.confirmCallback(!0)},handleDialogCancel(){this.showConfirmDialog=!1,this.confirmCallback(!1)},updatePassword(e){const t=e.target.value,s=t[t.length-1];t.length<this.password.length?this.password=this.password.slice(0,-1):this.password+=s,this.maskedPassword=this.password.replace(/./g,"•")},handleSubmit(){const e=btoa(this.password),t={username:this.username,password:e};o["a"].post(this.httpBaseUrl+"/web/login",t,{headers:{"Content-Type":"application/json"},cache:"no-cache",timeout:2e3}).then(e=>{0===e.data.code?(this.isLoggedIn=!0,localStorage.setItem("AccessToken",e.data.token),this.activeTab="deviceInfo"):alert(this.$t("login.userPassError"))}).catch(e=>{console.error("请求出错:",e),alert(this.$t("login.networkError"))})},checkLogin(){this.ajax_get("/web/checklogin",{success:function(e){0===e.code&&(this.isLoggedIn=!0,this.activeTab="deviceInfo")}})},return_login:function(e){localStorage.removeItem("AccessToken"),this.isLoggedIn=!1,this.username="",this.password="",this.maskedPassword="",this.FirmwareUpdate={upgradeIsLoading:!1,selectedFile:null,updateUrl:"",uploadStatusCode:0,uploadStatusMessage:""},this.activeTab=null},getDeviceModelName(e){return g().get(e)},refreshDevice(){this.fetchDeviceInfo()},logoutDevice(){this.return_login()},async restartDevice(){const e=await this.showConfirm(this.$t("tips.confirmDeviceRestart"));e&&this.fetchDeviceReboot()},async resetDevice(){const e=await this.showConfirm(this.$t("tips.confirmDeviceReset"));e&&this.fetchDeviceReset()},fetchDeviceReboot(){console.log("fetchDeviceReboot..."),this.ajax_get("/web/reboot",{success:function(e){0===e.code&&(alert(this.$t("tips.deviceRestarting")),this.return_login())}})},fetchDeviceReset(){console.log("fetchDeviceReset..."),this.ajax_get("/web/reset",{success:function(e){0===e.code&&(alert(this.$t("tips.deviceRestarting")),this.return_login())}})},fetchDeviceInfo(){this.ajax_get("/web/deviceInfo",{success:function(e){0===e.code&&(this.device.name=e.device_name,this.device.model=e.device_model,this.device.mac=e.device_mac.toUpperCase(),this.device.ip=e.device_ip,this.device.networkMode=e.device_network_mode,this.device.serverConnected=e.server_connected,this.device.firmwareVersion=e.firmware_version)}})},fetchSipInfo(){this.ajax_get("/web/sipInfo",{success:function(e){0===e.code&&(this.sip.authorizationStatus=e.sip_auth_status,this.sip.authorizationStatus&&(this.sip.registrationEnabled=e.sip_reg_switch,this.sip.registrationStatus=e.sip_reg_status,this.sip.serverAddress=e.sip_server_addr,this.sip.serverPort=e.sip_server_port,this.sip.account=e.sip_account,this.sip.password=e.sip_password,this.sip.transProtocol=e.sip_transProtocol))}})},getSipStatusStr(e){switch(e){case 1:return this.$t("sipSettings.regStausDes.regSuccess");case 2:return this.$t("sipSettings.regStausDes.regInProgress");case 3:return this.$t("sipSettings.regStausDes.regFailed");case 4:return this.$t("sipSettings.regStausDes.regTimeout");case 5:return this.$t("sipSettings.regStausDes.authFailed");case 6:return this.$t("sipSettings.regStausDes.inCall")}return this.$t("sipSettings.regStausDes.regFailed")},refreshSipSettins(){this.fetchSipInfo()},saveSipSettings(){this.ajax_post("/web/sipInfo",{sip_reg_switch:Number(this.sip.registrationEnabled),sip_server_addr:this.sip.serverAddress,sip_server_port:Number(this.sip.serverPort),sip_account:this.sip.account,sip_password:this.sip.password,sip_transProtocol:Number(this.sip.transProtocol)},{success:e=>{0===e.code?alert(this.$t("tips.settingSuccess")):alert(this.$t("tips.settingFailed")+(e.msg?e.msg:""))},error:()=>{alert(this.$t("tips.requestFailed"))}})},fetchSipRegStatus(){this.ajax_get("/web/sipRegStatus",{success:function(e){0===e.code&&(this.sip.registrationStatus=e.sip_reg_status)}})},fetchNetworkInfo(){this.ajax_get("/web/networkInfo",{success:function(e){0===e.code&&(this.network.assign=e.network_assign,1===this.network.assign&&(this.network.ipAddress=e.network_ipaddr,this.network.subnetMask=e.network_sub_netmask,this.network.gateway=e.network_gateway,this.network.primaryDns=e.network_primary_dns,this.network.secondaryDns=e.network_secondary_dns),this.network.mode=e.network_mode,this.network.serverIP=e.network_server_addr,this.network.serverPort=e.network_server_port)}})},refreshNetworkSettings(){this.fetchNetworkInfo()},saveNetworkSettings(){this.ajax_post("/web/networkInfo",{network_assign:Number(this.network.assign),network_ipaddr:this.network.ipAddress,network_sub_netmask:this.network.subnetMask,network_gateway:this.network.gateway,network_primary_dns:this.network.primaryDns,network_secondary_dns:this.network.secondaryDns,network_mode:Number(this.network.mode),network_server_addr:this.network.serverIP,network_server_port:Number(this.network.serverPort)},{success:e=>{-1===e.code?alert(this.$t("tips.settingParamError")):101===e.code||102===e.code?(alert(this.$t("tips.settingSuccess")+"，"+this.$t("tips.deviceRestarting")),this.return_login()):103===e.code?alert(this.$t("tips.networkNoChange")):alert(this.$t("tips.settingSuccess"))},error:()=>{alert(this.$t("tips.requestFailed"))}})},fetchTerminalInfo(){this.ajax_get("/web/terminalInfo",{success:function(e){0===e.code&&Object.keys(e).forEach(t=>{if("code"!==t){const s=this.terminalSettings.find(e=>e.name===t);s&&(s.value=e[t])}})}})},refreshTerminalSettings(){console.log("刷新终端设置"),this.fetchTerminalInfo()},saveTerminalSetting(e){const t=this.terminalSettings.find(t=>t.name===e);let s=t.value;"aux_volume"!==t.name&&"sip_output_volume"!==t.name&&"system_language"!==t.name||(s=Number(t.value)),this.ajax_post("/web/terminalInfo",{[t.name]:s},{success:e=>{0===e.code?alert(this.$t("tips.settingSuccess")):alert(this.$t("tips.settingParamError"))},error:()=>{alert(this.$t("tips.requestFailed"))}})},handleUrlUpgrade(){this.FirmwareUpdate.updateUrl?this.ajax_post("/web/softwareUpdateByUrl",{url:this.FirmwareUpdate.updateUrl},{success:e=>{0===e.code?(alert(this.$t("tips.upgradeSuccess")),this.FirmwareUpdate.uploadStatusCode=e.code,this.FirmwareUpdate.uploadStatusMessage=this.$t("tips.upgradeSuccess")):(alert(this.$t("tips.upgradeFailed")),this.FirmwareUpdate.uploadStatusCode=e.code,this.FirmwareUpdate.uploadStatusMessage=this.$t("tips.upgradeFailed"))},error:()=>{alert(this.$t("tips.requestFailed"))}}):alert(this.$t("tips.enterUrl"))},handleFileSelection(e){const t=e.target.files[0];t?(this.FirmwareUpdate.selectedFile=t,this.FirmwareUpdate.uploadStatusCode=0,this.FirmwareUpdate.uploadStatusMessage=this.$t("softwareUpdate.fileSelected")+t.name):(this.FirmwareUpdate.selectedFile=null,this.FirmwareUpdate.uploadStatusCode=0,this.FirmwareUpdate.uploadStatusMessage=this.$t("softwareUpdate.noFileSelected"))},async handleFileUpload(){if(!this.FirmwareUpdate.selectedFile)return void alert(this.$t("tips.selectFile"));const e=await this.showConfirm(this.$t("tips.confirmUpgrade"));if(!e)return;const t=this.FirmwareUpdate.selectedFile,s=t.size,i=new FormData;i.append("file",t),i.append("size",s),this.FirmwareUpdate.upgradeIsLoading=!0,o["a"].post(this.httpBaseUrl+"/web/softwareUpdateByFile",i,{headers:{"Content-Type":"multipart/form-data",Session:localStorage.getItem("AccessToken")},timeout:3e4}).then(e=>{0===e.data.code?(this.FirmwareUpdate.uploadStatusCode=e.data.code,this.FirmwareUpdate.uploadStatusMessage=e.message||this.$t("tips.upgradeSuccess"),alert(this.$t("tips.upgradeSuccess")+"，"+this.$t("tips.deviceRestarting")),this.return_login()):(this.FirmwareUpdate.uploadStatusCode=e.data.code,this.FirmwareUpdate.uploadStatusMessage=e.message||this.$t("tips.upgradeFailed"),alert(this.$t("tips.upgradeFailed")))}).catch(e=>{"ECONNABORTED"===e.code?alert(this.$t("tips.uploadTimeout")):(console.error("请求出错:",e),alert(this.$t("tips.uploadFailed")))}).finally(()=>{this.FirmwareUpdate.upgradeIsLoading=!1})},executePing(){if(!this.systemMaintenance.pingTarget)return void alert(this.$t("systemMaintenance.enterPingTarget"));const e=parseInt(this.systemMaintenance.pingCount);e>3600?alert(this.$t("systemMaintenance.pingCountExceeded")):(this.stopPolling(),this.systemMaintenance.isPinging=!0,this.systemMaintenance.pingResults="",this.systemMaintenance.lastResultId=0,this.lastPingSeq=-1,this.ajax_post("/web/startPing",{target:this.systemMaintenance.pingTarget,count:e},{success:e=>{0===e.code?(this.systemMaintenance.pingId=e.pingId,this.startPollingResults()):(this.systemMaintenance.pingResults=this.$t("systemMaintenance.pingFailed"),this.systemMaintenance.isPinging=!1)},error:()=>{this.systemMaintenance.pingResults=this.$t("systemMaintenance.requestFailed"),this.systemMaintenance.isPinging=!1}}))},exportPingResults(){if(!this.systemMaintenance.pingResults)return;const e=new Blob([this.systemMaintenance.pingResults],{type:"text/plain"}),t=URL.createObjectURL(e),s=document.createElement("a"),i=(new Date).toISOString().replace(/[:.]/g,"-");s.href=t,s.download=`ping-results-${this.device.mac}-${this.systemMaintenance.pingTarget}-${i}.txt`,document.body.appendChild(s),s.click(),setTimeout(()=>{document.body.removeChild(s),URL.revokeObjectURL(t)},100)},startPollingResults(){this.systemMaintenance.pingPollingInterval=setInterval(()=>{this.systemMaintenance.isPinging&&this.systemMaintenance.pingId?this.ajax_post("/web/getPingResults",{pingId:this.systemMaintenance.pingId},{success:e=>{0===e.code?(e.results&&""!==e.results.trim()&&this.processPingResults(e.results),e.completed&&(this.systemMaintenance.isPinging=!1,this.stopPolling())):(this.systemMaintenance.pingResults+="\n"+this.$t("systemMaintenance.requestFailed"),this.stopPing())},error:()=>{this.systemMaintenance.pingResults+="\n"+this.$t("systemMaintenance.requestFailed"),this.stopPing()}}):this.stopPing()},500)},processPingResults(e){if(!e||!e.trim())return;const t=e.split("\n");let s="",i=!1;void 0===this.lastPingSeq&&(this.lastPingSeq=-1);for(let a of t){if(!a.trim())continue;if(a.startsWith("PING ")){this.systemMaintenance.pingResults.trim()||(s+=a+"\n",i=!0);continue}const e=a.match(/seq=(\d+)/);if(e){const t=parseInt(e[1]);t>this.lastPingSeq&&(this.lastPingSeq=t,s+=a+"\n")}else a.includes("statistics")||a.includes("packets transmitted")||a.includes("min/avg/max"),s+=a+"\n"}if(s){if(!i&&!this.systemMaintenance.pingResults.includes("PING ")&&s.trim()){const t=e.match(/^PING [^\n]+/);t&&(this.systemMaintenance.pingResults=t[0]+"\n"+this.systemMaintenance.pingResults)}this.systemMaintenance.pingResults+=s}},stopPolling(){this.systemMaintenance.pingPollingInterval&&(clearInterval(this.systemMaintenance.pingPollingInterval),this.systemMaintenance.pingPollingInterval=null)},stopPing(){if(this.systemMaintenance.isPinging=!1,this.stopPolling(),this.systemMaintenance.pingId&&this.systemMaintenance.pingId.length>0)try{this.ajax_post("/web/stopPing",{pingId:this.systemMaintenance.pingId},{success:()=>{this.systemMaintenance.pingId=""},error:()=>{this.systemMaintenance.pingId=""}})}catch(e){this.systemMaintenance.pingId="",console.error("Error stopping ping:",e)}else this.systemMaintenance.pingId=""},error_default:function(){console("Ajax request failed.")},ajax_get:function(e,t){const s=localStorage.getItem("AccessToken");let i=t.error||this.error_default,a=this;o["a"].get.call(this,`${this.httpBaseUrl}${e}`,{headers:{Session:s}}).then((function(e){999!==e.data.code?t.success.call(a,e.data):a.return_login(e.data.smg)}),(function(){i.call(a)}))},ajax_post:function(e,t,s){const i=localStorage.getItem("AccessToken");let a=s.error||this.error_default,n=this;o["a"].post(`${this.httpBaseUrl}${e}`,JSON.stringify(t),{headers:{Session:i},cache:"no-cache"}).then((function(e){999!==e.data.code?s.success.call(n,e.data):n.return_login(e.data.msg)}),(function(){a.call(n)}))},changeLanguage(){localStorage.setItem("language",this.selectedLanguage),this.$i18n.locale=this.selectedLanguage,this.updateTerminalSettingsDesc()},updateTerminalSettingsDesc(){this.terminalSettings.forEach(e=>{switch(e.name){case"dev_name":e.desc=this.$t("terminalSettings.properties.deviceName");break;case"aux_volume":e.desc=this.$t("terminalSettings.properties.auxVolume");break;case"sip_output_volume":e.desc=this.$t("terminalSettings.properties.sipVolume");break;case"system_language":e.desc=this.$t("terminalSettings.properties.systemLanguage");break}})}},mounted:function(){this.checkLogin(),this.sipPollingInterval||(this.sipPollingInterval=setInterval(()=>{this.isLoggedIn&&this.sip.authorizationStatus&&this.sip.registrationEnabled&&"sipSettings"===this.activeTab&&this.fetchSipRegStatus()},1e3)),this.updateTerminalSettingsDesc()},watch:{activeTab(e,t){"deviceInfo"===e?this.fetchDeviceInfo():"sipSettings"===e?this.fetchSipInfo():"networkSettings"===e?this.fetchNetworkInfo():"terminalSettings"===e&&this.fetchTerminalInfo()},uploadMessage:{immediate:!0,handler(e){this.FirmwareUpdate.uploadStatusMessage=e}}}},P=C,y=(s("5aae"),s("1180"),Object(f["a"])(P,n,r,!1,null,"05c68bb3",null)),k=y.exports;i["a"].use(a["a"]);var N=new a["a"]({mode:"history",routes:[{path:"/",name:"login",component:k}]}),b=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[t("router-view")],1)},M=[],$={name:"App",mounted(){document.title="IP Terminal"}},D=$,F=(s("21df"),Object(f["a"])(D,b,M,!1,null,null,null)),A=F.exports;new i["a"]({el:"#app",i18n:u,router:N,render:e=>e(A)})},"5aae":function(e,t,s){"use strict";s("06aa")},"68f4":function(e,t,s){},bedc:function(e,t,s){}});