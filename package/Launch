#!/bin/sh
APP_DIR_PATH=/customer/App

cd $APP_DIR_PATH
if [ -e $APP_DIR_PATH/PREWORK ]; then
	echo "INIT PREWORK"
	$APP_DIR_PATH/PREWORK
	rm $APP_DIR_PATH/PREWORK
else
	echo "Not Found PREWORK"
fi


echo "Init Environment"

export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$APP_DIR_PATH/Lib
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$APP_DIR_PATH/Lib/sip
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$APP_DIR_PATH/Lib/freetype
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$APP_DIR_PATH/Lib/ffmpeg
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/config/lib
echo $LD_LIBRARY_PATH

RESET_COUNT_FILE=$APP_DIR_PATH/reset_count
if [ ! -e $RESET_COUNT_FILE ]; then
	echo "reset_count is not exist!,touch it!"
	touch $RESET_COUNT_FILE
	echo 0 > $RESET_COUNT_FILE
fi

resetCount=`cat $RESET_COUNT_FILE`

echo "reset_count:$resetCount"
if expr "$resetCount" : [0-9]*$; then
	if [ "$resetCount" -lt 7 ]; then
		echo "reset_count<7,normal start!"
		echo $((++resetCount)) >reset_count
		sync
		echo "RUN NetSpeaker"
		$APP_DIR_PATH/NetSpeaker &
	else
		echo "reset_count>=6,reboot and restore!"
		echo 0 >reset_count
		rm $APP_DIR_PATH/Launch
		rm $APP_DIR_PATH/appInit
		sync
		reboot
	fi
else
	echo "read reset_count error!"
	$APP_DIR_PATH/NetSpeaker &
fi

